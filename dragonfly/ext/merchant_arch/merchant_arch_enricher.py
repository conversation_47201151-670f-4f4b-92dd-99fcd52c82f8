#!/usr/bin/env python3
# coding=utf-8
"""
filename: merchant_enricher.py
description: common_leaf dynamic_json_config DSL intelligent builder, enricher module for merchant
author: <EMAIL>
date: 2021-01-14 13:58:00
"""

from ...common_leaf_util import strict_types, check_arg, gen_attr_name_with_common_attr_channel, extract_attr_names
from ...common_leaf_processor import LeafEnricher

class MerchantDcafFeatureGeneratorEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_dcaf_feature_generator"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("biz_name"), "biz_name 不能为空")
    check_arg(self._config.get("dcaf_features"), "dcaf_features 不能为空")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("pack_user_features", []), "name")
    attrs.update(self.extract_dynamic_params(self._config["biz_name"]))
    attrs.update(self.extract_dynamic_params(self._config["sort_score"]))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("pack_context_features", []), "name")
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return set([self._config.get("dcaf_features")])


class MerchantDcafSampleGeneratorEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_dcaf_sample_generator"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("biz_name"), "biz_name 不能为空")
    check_arg(self._config.get("dcaf_features"), "dcaf_features 不能为空")
    check_arg(self._config.get("output_json"), "output_json 不能为空")
    check_arg(self._config.get("rank_score"), "rank_score 不能为空")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = extract_attr_names(self._config.get("common_attrs", []), "name")
    attrs.update(self.extract_dynamic_params(self._config.get("biz_name", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("limit_num", 5)))
    attrs.update(self.extract_dynamic_params(self._config.get("aggregator", "sum")))
    attrs.update(self.extract_dynamic_params(self._config.get("is_rerank", 0)))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("mc_truncate_seq"))
    attrs.add(self._config.get("rank_score"))
    attrs.add(self._config.get("egpm"))
    attrs.add(self._config.get("mtb_rate"))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("dcaf_features"))
    attrs.add(self._config.get("output_json"))
    return attrs


class GenMerchantAbSuffixEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gen_merchant_ab_suffix"

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("import_common_attrs"), "import_common_attrs 不能为空")
    check_arg(self._config.get("export_common_attrs"), "export_common_attrs 不能为空")
    check_arg(self._config.get("kconf_key"), "kconf_key 不能为空")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set(self._config.get("import_common_attrs", []))
    attrs.update(self.extract_dynamic_params(self._config.get("kconf_key", "")))
    app_source_type = self._config.get("app_source_type", "app_source_type")
    attrs.add(app_source_type)
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return set()

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set(self._config.get("export_common_attrs", []))
    prioritized_suffix = self._config.get("prioritized_suffix", "prioritized_suffix")
    sample_tag = self._config.get("sample_tag", "sample_tag")
    attrs.add(prioritized_suffix)
    attrs.add(sample_tag)
    return attrs
  
class MerchantArchDistributedCacheXtrScoreReaderEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_arch_distributed_cache_xtr_score_reader"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    if "attr_name_types" in self._config:
      attrs = { v.get("as", v["name"])  if isinstance(v, dict) else v \
          for v in self._config.get("attr_name_types", []) }
    if "is_cache_hit" in self._config:
      attrs.add(self._config.get("is_cache_hit"))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("attr_name_types"), "缺少 attr_name_types")
    check_arg(self._config.get("photo_store_kconf_key"), "缺少 photo_store_kconf_key")
    check_arg(self._config.get("table_kconf_path"), "缺少 table_kconf_path")
    check_arg(self._config.get("table_name"), "缺少 table_name")

class MerchantDissectProtobufToLeafEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_dissect_protobuf_to_leaf_str"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["protobuf_str_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["leaf_info_attr"])
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("protobuf_str_attr"), f"{self.get_type_alias()} 的 protobuf_str_attr 配置不可为空")
    check_arg(self._config.get("leaf_info_attr"), f"{self.get_type_alias()} 的 leaf_info_attr 配置不可为空")
    check_arg(self._config.get("class_name"), f"{self.get_type_alias()} 的 class_name 配置不可为空")
    check_arg(self._config.get("trim_user_info"), f"{self.get_type_alias()} 的 trim_user_info 配置不可为空")

class MerchantAssembleProtobufFromLeafEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_assemble_protobuf_from_leaf_str"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["leaf_info_attr"])
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config["trimmed_protobuf_str_attr"])
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("trimmed_protobuf_str_attr"), f"{self.get_type_alias()} 的 protobuf_str_attr 配置不可为空")
    check_arg(self._config.get("leaf_info_attr"), f"{self.get_type_alias()} 的 leaf_info_attr 配置不可为空")
    check_arg(self._config.get("class_name"), f"{self.get_type_alias()} 的 class_name 配置不可为空")
    check_arg(self._config.get("trim_user_info"), f"{self.get_type_alias()} 的 trim_user_info 配置不可为空")

class MerchantGenKeyWithPrefixEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_gen_key_with_prefix"

  @strict_types
  def _check_config(self) -> None:
    input_common_attr = self._config.get("input_common_attr")
    output_common_attr = self._config.get("output_common_attr")
    check_arg(output_common_attr, "`output_common_attr` 参数缺失")
    check_arg(isinstance(input_common_attr, str), "`input_common_attr` 配置需为 string 类型")
    check_arg(isinstance(output_common_attr, str), "`output_common_attr` 配置需为 string 类型")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("input_common_attr"):
      attrs.add(self._config["input_common_attr"])
    for name in ["prefix"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))

    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("output_common_attr"):
      attrs.add(self._config["output_common_attr"])
    return attrs

class MerchantGlobalDataEnricher(LeafEnricher):
  def __init__(self, config: dict):
    super().__init__(config)

  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_global_data_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("kuiba_user_attr"))
    attrs.add(self._config.get("perf_sampling_rate"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs = extract_attr_names(self._config.get("export_common_attr", []), "as")
    attrs.add(self._config.get("kuiba_user_ptr_attr"))
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("kuiba_user_attr"), str),
              "kuiba_user_attr 配置项需为 string 类型")

class MerchantArchStringListSplitEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_arch_split_string_list"

  @strict_types
  def _check_config(self) -> None:
    input_common_attr = self._config.get("input_common_attr")
    output_common_attr = self._config.get("output_common_attr")

    check_arg(output_common_attr, "`output_common_attr` 参数缺失")
    check_arg(isinstance(input_common_attr, str), "`input_common_attr` 配置需为 string 类型")
    check_arg(isinstance(output_common_attr, str), "`output_common_attr` 配置需为 string 类型")

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("input_common_attr"):
      attrs.add(self._config["input_common_attr"])
    for name in ["max_num_per_str"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    if self._config.get("output_common_attr"):
      attrs.add(self._config["output_common_attr"])
    return attrs
  
class MerchantDcafControllerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_dcaf_controller"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("partition_ab"))   
    attrs.update(self.extract_dynamic_params(self._config["biz_name"]))
    for cfg in self._config.get("controls"):
      attrs.add(cfg.get("flow_value"));
    return attrs
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    for cfg in self._config.get("controls"):
      attrs.add(cfg.get("output_quota"));
    return attrs
  
class MerchantDcafReporterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_dcaf_reporter"
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("partition_ab"))   
    attrs.update(self.extract_dynamic_params(self._config["biz_name"]))
    return attrs
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("fr_cache_flag"))
    return attrs

class MerchantArchLeafCallerEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_arch_leaf_caller"

  @strict_types
  def is_async(self) -> bool:
    return False
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config["kess_service"]))
    attrs.update(self.extract_dynamic_params(self._config.get("kess_group")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_type", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("request_num")))
    attrs.update(self.extract_dynamic_params(self._config.get("timeout_ms")))
    attrs.update(extract_attr_names(self._config.get("send_common_attrs", []), "name"))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add('merchant_show_case_response_ptr')
    attrs.add('merchant_show_case_response_bytes')
    attrs.add('merchant_show_case_response_waiter')
    return attrs
