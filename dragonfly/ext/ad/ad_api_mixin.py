#!/usr/bin/env python3
# coding=utf-8
"""
filename: ad_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, ad api mixin
author: k<PERSON><PERSON><PERSON>@kuaishou.com
date: 2021-03-16 20:45:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .ad_retriever import *
from .ad_enricher import *
from .ad_observer import *
from .ad_mixer import *

class AdApiMixin(CommonLeafBaseMixin):
  """
  商业化针对 BatchedSamples 格式日志流进行特征及 Label 提取、数据转换、样本过滤等处理相关接口的 Mixin 实现
  该 Mixin 包含以下 Processor 接口
  - AdFeatureEnricher
  - AdParseRetriever
  - AdNewFilterEnricher
  - AdTransEnricher
  - AdTdmSampleRetriever
  """

  def ad_feature_extract(self, **kwargs):
    """
    AdFeatureEnricher
    ------
    使用 feature_config_path 配置的抽取算子来抽取特征放到 AdFeatures 这个 common_attr 里

    参数配置
    ------
    `input_common_attrs`: [list] 必配项，抽取算子依赖的 common_attr, 必须依赖 AdSamples

    `feature_config_path`: [string] 必配项，传给 FeatureConfig 的配置路径

    `output_common_attrs`: [list] 必配项，抽取算子输出的 common_attrs 列表，必须输出到 AdFeatures

    调用示例
    ------
    ``` python
    .ad_feature_extract(
      input_common_attrs=["AdSamples"],
      feature_config_path="./feature_config.json",
      output_common_attrs=["AdFeatures"]
    )
    ```
    """
    self._add_processor(AdFeatureEnricher(kwargs))
    return self

  def ad_bs_parse(self, **kwargs):
    """
    AdParseRetriever
    ------
    将序列化的商业化格式的 BatchedSamples 日志 (string) 解析为解特征需要的 SampleInterface 格式数据

    参数配置
    ------
    `sample_from_attr`: [string] 必配项，原始 string 日志属性名

    `feature_config_path`: [string] 必配项，传给 FeatureConfig 的配置路径

    `output_common_attrs`: [list] 必配项，SampleInterface 和 FeatureInterface 存储的 common attrs

    调用示例
    ------
    ``` python
    .ad_bs_parse(
      sample_from_attr="input_bs_log",
      feature_config_path="./feature_config.json",
      output_common_attrs=["AdSamples", "AdFeatures"]
    )
    ```
    """
    self._add_processor(AdParseRetriever(kwargs))
    return self

  def ad_label_extract(self, **kwargs):
    """
    AdLabelEnricher
    ------
    从给定的 SampleInterface 的 common attr 中抽取样本的Label信息, 并写入回 FeatureInterface 的 common attr 中

    参数配置
    ------
    `input_common_attrs`: [list] 必配项，输入 SampleInterface 的common attr

    `label_extractor`: [String] 必配项，LabelExtractor 的名字

    `args`: [Json] optional, LabelExtractor 需要的参数

    `debug_mode`: [Boolean] optional, 是否是Debug Mode

    `output_common_attrs`: [list] 必配项，结果 FeatureInterface 的common attr

    调用示例
    ------
    ``` python
    .ad_label_extract(
      input_common_attrs=["AdSamples"],
      label_extractor="ItemLabelInfoExtractor",
      args={'label':'delivery@1,item_impression@3,item_click@7'},
      debug_mode=False,
      output_common_attrs=["AdFeatures"]
    )
    ```
    """
    self._add_processor(AdLabelEnricher(kwargs))
    return self

  def ad_picasso_extract(self, **kwargs):
    """
    AdCommonPicassoEnricher
    ------
    从给定的 Picasso 抽取特征

    参数配置
    ------
    `table_name`: [string] 必配项，输入 table_name

    `input_attr`: [str] 从 picasso 抽取特征的输入 attr, 若为空则使用 user_id

    `output_attr`: [str] 从 picasso 抽取的特征存放的 attr, 不能为空

    `scene_type`: [str] 场景 type, 枚举字符串值, 支持 "ad"/"user_realtime_action"/"lsp"/"aaa"

    `picasso_timeout_ms`: [int] 请求 picasso 服务的 timeout, 默认 20ms

    `sim_cache_capacity_`: [int] 该 processor 会 cache picasso 的结果, 此选项为 cache 大小, 默认 100000

    `cache_expire_time`: [int] cache 过期时间, 默认 120

    `use_ing_status`: [bool]

    `picasso_kess_service`: [String] 必配项，Picasso服务名
    调用示例
    ------
    ``` python
    .ad_picasso_extract(
      table_name="adPicassoFullGoodsAction",
      scene_type="aaa",
      picasso_timeout_ms=300,
      cache_capacity=5000,
      expire_time=600,
      output_attr="aaa_picasso_output",
      picasso_kess_service="grpc_adPicassoGatewayService"
    )
    ```
    """
    self._add_processor(AdCommonPicassoEnricher(kwargs))
    return self

  def ad_full_goods_gsu_extract(self, **kwargs):
    """
    AdFullGoodsGsuSplitEnricher
    ------
    从给定的 Picasso 的 common attr 中抽取特征

    参数配置
    ------
    `picasso_resp_attr`: [String] 必配项，Picasso的输出

    `slots_id`: [List] 抽取的slot特征号

    调用示例
    ------
    ``` python
    .ad_full_goods_gsu_extract(
      output_sign_attr="aaa_gsu_explicit_signs",
      output_slot_attr="aaa_gsu_explicit_slots",
      picasso_resp_attr="aaa_picasso_output",
      max_length=100,
      goods_list_num_limit=10,
      n_minute_ago=120,
      filter_type="explicit",
      search_type="buy",
      fill_flag=True,
      target_live_x7_cate1_attr="goods_x7_c1_id_list",
      target_live_x7_cate2_attr="goods_x7_c2_id_list",
      target_live_x7_cate3_attr="goods_x7_c3_id_list",
      target_live_spu_id_list_attr="goods_spu_id_list",
      target_aid_attr="aid",
      slots_id=[1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008],
      mio_slots_id=[1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008]
    )
    ```
    """
    self._add_processor(AdFullGoodsGsuSplitEnricher(kwargs))
    return self

  def ad_filter(self, **kwargs):
    """
    AdNewFilterEnricher
    ------
    对 SampleInterface 中的样本进行过滤

    参数配置
    ------
    `input_common_attrs`: [List] 必配项，SampleInterface 和 FeatureInterface 的common attr

    `success_flag_attr_var`: [string] 必配项，成功 tag 写入哪个common attr

    `time_attr`: [string] 必配项，时间属性写入哪个 common attr

    `filter_type`: [int] 必配项，过滤类型

    `output_common_attrs`: [List] 必配项，输出的common attrs

    `feature_config_path`: [string] 必配项，传给 FeatureConfig 的配置路径

    调用示例
    ------
    ``` python
    .ad_filter(
      input_common_attrs=["AdSamples", "AdFeatures"],
      success_flag_attr_var="tdm_sampling_success",
      time_attr="time_ms",
      filter_type=1,
      output_common_attrs=["AdSamples", "AdFeatures"],
      feature_config_path="./feature_config.json"
    )
    ```
    """
    self._add_processor(AdNewFilterEnricher(kwargs))
    return self

  def ad_trans_cofea(self, **kwargs):
    """
    AdTransEnricher
    ------
    将 FeatureInterface 及其他附加属性转换为BatchedSamples 数据存储到 common attr

    参数配置
    ------
    `input_common_attrs`: [List] 必配项，FeatureInterface 和 SampleInterface 的 common attr

    `output_attr`: [String] 必配项，结果 BatchedSamples 数据输出的 common attr

    调用示例
    ------
    ``` python
    .ad_trans(
      input_common_attrs=["AdFeatures", "AdSamples"]
      output_attr="ad_bs_out"
    )
    ```
    """
    self._add_processor(AdTransEnricher(kwargs))
    return self

  def retrieve_ad_tdm_sample(self, **kwargs):
    """
    AdTdmSampleRetriever
    ------
    商业化训练接入 tdm 抽样服务

    参数配置
    ------
    `kess_service`: [string] 必配项，tdm 采样服务的kess name

    `tree_name`: [string] 必配项，需要采样的 tree_name

    `request_type`: [string] optional, 采样类型

    `input_attrs`: [List] 必配项，采样依赖的 common attr, 配置 ["AdSamples", "AdFeatures"]

    `shard_num`: [int] 分 shard 数

    `time_attr`: [string] 必配项，时间戳属性输出的 common attr

    `filter_type`: [int] optional, 过滤类型，默认为 0 不过滤

    `success_flag_attr_var`: [string] 必配项，返回值，common attr, 采样成功返回 1, 否则 0, 用于判断是否丢弃 package

    `output_level_attr_var`: [string] optional, 返回样本到叶子节点的采样高度（从 0 开始）

    `output_attrs`: [List] 必配项，输出的 common attr

    `tdm_sample_type_var`: [string] optional, default="tdm_sample_type", 输出样本类型

    `feature_config_path`: [string] 必配项，特征文件 json

    `downstream_processor`: [string] optional, 下游 processor 名，同步调用时必配


    调用示例
    ------
    ``` python
    .retrieve_ad_tdm_sample(
      kess_service="grpc_TDMTreeServerOfflineOutsideAd",
      tree_name="tdm_fr_outside"
      request_type="fullrank_random",
      input_attrs=["AdSamples", "AdFeatures"],
      shard_num=1,
      time_attr="time_ms",
      filter_type=1,
      success_flag_attr_var="tdm_sampling_success",
      output_level_attr_var="tdm_level",
      output_attrs=["AdSamples", "AdFeatures", "tdm_sample_type", "tdm_sampling_sucess", "time_ms"],
      tdm_sample_type_var="tdm_sample_type",
      feature_config_path="./feature_config.json",
      downstream_processor="if_processor"
    )
    ```
    """
    self._add_processor(AdTdmSampleRetriever(kwargs))
    return self

  def ad_live_segment_gsu(self, **kwargs):
    """
    AdLiveSegmentGsuEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_id` : [int] 抽sign 使用的slot

    `mio_slots_id` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .ad_live_segment_gsu(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      )
    ```
    """
    self._add_processor(AdLiveSegmentGsuEnricher(kwargs))
    return self

  def ad_live_v1_goods_click_seq(self, **kwargs):
    """
    参数说明后续 @caikehe 补齐

    示例
    ------
    ``` python
    .ad_live_v1_goods_click_seq(
        slots_id=[1040, 1041, 1042, 1043, 1044, 1045, 1046, 1047, 1048, 1049],
        mio_slots_id=[1040, 1041, 1042, 1043, 1044, 1045, 1046, 1047, 1048, 1049],
        item_map_list = [
            ["1040", 1040, 1040, 10000001], ["1041", 1041, 1041, 1000001], ["1042", 1042, 1042, 1000001],
            ["1043", 1043, 1043, 1000001], ["1044", 1044, 1044, 1000001], ["1045", 1045, 1045, 10000001],
            ["1046", 1046, 1046, 10000001], ["1047", 1047, 1047, 1000001], ["1048", 1048, 1048, 10000001],
            ["1049", 1049, 1049, 10000001]],
        limit_user=1000,
        sign_prefix_bit_num_input=10,
        sign_prefix_bit_num_output=12,
        use_murmur3hash64=True,
        use_ad_slot_size=True,
        padding_seq_timestamp=True,
        padding_seq=False,
        slot_as_attr_name=True,
        slot_as_attr_name_prefix="all_goods_click_v1_"
    )
    """
    self._add_processor(AdLiveV1GoodsClickSeqEnricher(kwargs))
    return self

  def ad_live_v2_goods_click_seq(self, **kwargs):
    """
    参数说明后续 @caikehe 补齐
    
    示例
    ------
    ``` python
    .ad_live_v2_goods_click_seq(
        slots_id=[1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1058, 1059],
        mio_slots_id=[1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1058, 1059],
        item_map_list = [
            ["1050", 1050, 1050, 10000001], ["1051", 1051, 1051, 1000001], ["1052", 1052, 1052, 1000001],
            ["1053", 1053, 1053, 1000001], ["1054", 1054, 1054, 1000001], ["1055", 1055, 1055, 10000001],
            ["1056", 1056, 1056, 10000001], ["1057", 1057, 1057, 1000001], ["1058", 1058, 1058, 10000001],
            ["1059", 1059, 1059, 10000001]],
        limit_combine_num=50,
        sign_prefix_bit_num_input=10,
        sign_prefix_bit_num_output=12,
        use_murmur3hash64=True,
        use_ad_slot_size=True,
        padding_seq_timestamp=True,
        padding_seq=False,
        slot_as_attr_name=True,
        slot_as_attr_name_prefix="all_goods_click_v2_",
        long_item_threshold="2000",
        list_top_k=1,
        only_use_cat1=True
    )
    ```
    """
    self._add_processor(AdLiveV2OptGoodsClickSeqEnricher(kwargs))
    return self

  def ad_live_segment_gsu_u2u(self, **kwargs):
    """
    AdLiveSegmentGsuU2uEnricher
    -----
    根据 aid 进行 GSU 搜索并填充相似视频的 slot/sign 特征

    参数
    -----
    `output_sign_attr`: [str] sign 输出的 attr name

    `output_slot_attr`: [str] slot 输出的 attr name

    `colossus_resp_attr`: [str] colossus 输出的 attr name

    `limit_num_attr`: [str] 限制返回数目的 attr name

    `enable_new_product_category`: [str] enable_new_product_category 的 attr name

    `target_aids_attr`: [str] item 侧使用的 aid 的 attr name, 默认为 aid

    `slots_id`: list[int] 特征抽取使用的 slot

    `mio_slots_id`: list[int] mio 前向使用的 slot

    `fix_new_product_category_option`: [bool] 是否固定对新品类进行修正

    `only_output_basic_slots`: [bool] 是否只输出基本的 slot, 默认为 false

    `fused_slot_sign_remap`: [bool] 是否重新映射生成的 sign 的 slot

    `sign_prefix_bit_num_input`: [int] 需要 remap slot 的 sign 的 bit 数

    `sign_prefix_bit_num_output`: [int] remap 之后的 sign 的 slot 的 bit 数

    `use_djb2_hash64`: [bool] 是否使用 djb2 哈希

    `item_map_list`: [list of list] 需要 remap 的 slot 的列表

    实例
    -----
    ```python
    .ad_live_segment_gsu_u2u(
        output_sign_attr="ad_live_segment_u2u_gsu_signs",
        output_slot_attr="ad_live_segment_u2u_gsu_slots",
        colossus_resp_attr="live_seg_colossus_output",
        limit_num_attr="limit_num",
        enable_new_product_category="enable_new_product_category",
        target_aids_attr="aid",
        slots_id=[
            912, 914, 915, 916, 917, 934, 935, 936, 937, 943, 944, 945,
            946, 947, 961, 962, 963, 964, 965, 985, 986, 990, 991
        ],
        mio_slots_id=[
            912, 914, 915, 916, 917, 934, 935, 936, 937, 943, 944, 945,
            946, 947, 961, 962, 963, 964, 965, 985, 986, 990, 991
        ],
        only_output_basic_slots=True,
        # remap slot sign config
        fused_slot_sign_remap=True,
        sign_prefix_bit_num_input=10,
        sign_prefix_bit_num_output=12,
        use_djb2_hash64=True,
        item_map_list = [
            ["912", 912, 912, 20000000], ["914", 914, 914, 20000000], ["915", 915, 915, 200000],
            ["916", 916, 916, 200000], ["917", 917, 917, 200000], ["934", 934, 934, 2000],
            ["935", 935, 935, 200000], ["936", 936, 936, 200000], ["937", 937, 937, 200000],
            ["943", 943, 943, 200000], ["944", 944, 944, 20000000], ["945", 945, 945, 20000000],
            ["946", 946, 946, 200000], ["947", 947, 947, 200000], ["961", 961, 961, 2000],
            ["962", 962, 962, 2000], ["963", 963, 963, 2000], ["964", 964, 964, 200000],
            ["965", 965, 965, 200000], ["985", 985, 985, 200000], ["986", 986, 986, 200000],
            ["990", 990, 990, 200000], ["991", 991, 991, 200000]
        ]
    )

    ```
    """
    self._add_processor(AdLiveSegmentGsuU2uEnricher(kwargs))
    return self

  def ad_live_segment_colossus_e2e_retriever(self,  **kwargs):
    """
    AdLiveSegmentColossusRespFilter
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_live_segment_id_to_att`: [string]

    `save_author_id_to_attr`: [string]

    `playtime_threshold`: 30

    `face_cnt`: 1

    `yellow_cart_item_cnt`: 5

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为true

    `parse_from_pb`: [boolean] 是否从protobuf message中取colossus的返回结果，默认为true

    示例
    ------
    ``` python
    .ad_live_segment_colossus_e2e_retriever(colossus_resp_attr="colossus_output",
                            save_live_segment_id_to_att="lsp_live_segment_id_list",
                            save_author_id_to_attr="lsp_author_id_list",
                            playtime_threshold=30,
                            face_cnt=1,
                            yellow_cart_item_cnt=5
                            )
    ```
    """
    self._add_processor(AdLiveSegmentColossusE2eRetriever(kwargs))
    return self

  def commertial_live_gsu_aid2aid(self, **kwargs):
    """
    CommonCommertialLiveGsuAid2AidEnricher
    ------
    根据 liveid 所属的 aid 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdLiveItem

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_attr`: [string] item侧使用的 aid 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .ad_live_gsu_aid2aid(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_attr = 'aId',
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642]
                      )
    ```
    """
    self._add_processor(CommonCommertialLiveGsuAid2AidEnricher(kwargs))
    return self

  def ad_gsu_with_pid_cluster(self, **kwargs):
    """
    CommonAdGsuWithPidClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `use_append_cluster`  : [bool] 是否使用扩增类别, 默认为 False

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
    ------
    ``` python
    .ad_gsu_with_pid_cluster(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                           cluster_id_service_type='embedding_server',
                           kess_service='mmu_ad_aid_cluster_embedding_query_server',
                           shards=1,
                           use_append_cluster = false,
                           hetu_1k_cluster_json_value="")
    ```
    """
    self._add_processor(CommonAdGsuWithPidClusterEnricher(kwargs))
    return self

  def ad_live_gsu_with_aid_cluster(self, **kwargs):
    """
    AdLiveGsuWithAidClusterEnricher
    ------
    商业化colossus, 根据 live aid 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧author的 cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster json object

    示例
      ------
      ``` python
      .ad_live_gsu_with_aid_cluster(colossus_resp_attr='ad_colossus_resp',
                          output_sign_attr='sign',
                          output_slot_attr='slot',
                          limit_num_attr='limit_num',
                          target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                          cluster_id_service_type='embedding_server',
                          kess_service='mmu_ad_aid_cluster_embedding_query_server',
                          shards=1,
                          hetu_1k_cluster_json_value="")
      ```
      """
    self._add_processor(AdLiveGsuWithAidClusterEnricher(kwargs))
    return self

  def commertial_gsu_with_cluster_tag_conf(self, **kwargs):
    """
    CommonAdGsuWithClusterTagConfEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `search_type`: [int] 检索类型 1为根据cluster检索 2为根据tag检索

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
    ------
    ``` python
    .commertial_gsu_with_cluster_tag(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                           search_type = 1,
                           hetu_1k_cluster_json_value="")
    ```
    """
    self._add_processor(CommonAdGsuWithClusterTagConfEnricher(kwargs))
    return self

  def commertial_gsu_with_cluster_tag_v2_conf(self, **kwargs):
    """
    CommonAdGsuWithClusterTagV2ConfEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `search_type`: [int] 检索类型 1为根据cluster检索 2为根据tag检索

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
    ------
    ``` python
    .commertial_gsu_with_cluster_tag_v2_conf(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                           search_type = 1,
                           hetu_1k_cluster_json_value="")
    ```
    """
    self._add_processor(CommonAdGsuWithClusterTagV2ConfEnricher(kwargs))
    return self

  def commertial_gsu_with_cluster_tag_v2_newconf(self, **kwargs):
    """
    CommonAdGsuWithClusterTagV2NewConfEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `search_type`: [int] 检索类型 1为根据cluster检索 2为根据tag检索

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
    ------
    ``` python
    .commertial_gsu_with_cluster_tag_v2_newconf(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                           search_type = 1,
                           hetu_1k_cluster_json_value="")
    ```
    """
    self._add_processor(CommonAdGsuWithClusterTagV2NewConfEnricher(kwargs))
    return self

  def commertial_gsu_with_cluster_tag_v3_conf(self, **kwargs):
    """
    CommonAdGsuWithClusterTagV3ConfEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征，支持将 pid 的 sideinfo 信息存到 context 供其他算子使用。

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `target_cluster_attr`: [string] target cluster attr字段名

    `filter_play_time`: [int] 过滤播放时长大于filter_play_time的部分，默认0，表示不过滤

    `filter_behave`: [bool] 按照用户主动行为过滤，如like/follow等，默认False，表示不过滤

    `save_pid_map`: [bool] 是否要将colossus的pid映射存储到context中，默认false，不存储

    `photo_id_to_colossus_idx_map`: [string] 输出字段，存放 pid->colossus_idx 映射的common字段名，save_pid_map=True时必填

    `photo_id_to_side_info_map`: [string]  输出字段，存放 pid->sideinfo 映射的common字段名，save_pid_map=True时必填

    示例
    ------
    ``` python
    .commertial_gsu_with_cluster_tag_v3_conf(
                colossus_resp_attr='colossus_output',
                output_sign_attr='commertial_gsu_signs_v3',
                output_slot_attr='commertial_gsu_slots_v3',
                limit_num_attr='gsu_limit_num',
                target_cluster_attr='adClusterid',
                filter_behave=True,)
    ```
    """
    self._add_processor(CommonAdGsuWithClusterTagV3ConfEnricher(kwargs))
    return self

  def common_ad_gsu_with_pid_list_enricher(self, **kwargs):
    """
    CommonAdGsuWithPidListEnricher
    ------
    根据 photo list 取 colossus 结果下各个 item 的 sidinfo，并且尽可能利用之前的算子预存好的 map 信息来提升性能。

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] target cluster attr字段名

    `photo_id_to_colossus_idx_map`: [string] 输入字段，存放 pid->colossus_idx 映射的common字段名

    `photo_id_to_side_info_map`: [string]  输入字段，存放 pid->sideinfo 映射的common字段名

    `input_pid_list_attr`: [string] 输入item字段，存放 pid list的字段名

    示例
    ------
    .common_ad_gsu_with_pid_list_enricher(
      output_sign_attr='ia_soft_gsu_signs',
      output_slot_attr='ia_soft_gsu_slots',
      colossus_resp_attr='colossus_output',
      photo_id_to_colossus_idx_map = "photo_id_to_colossus_idx_map",
      photo_id_to_side_info_map = "photo_id_to_side_info_map",
      input_pid_list_attr="pid_list_test",
    ) 
    ```
    """
    self._add_processor(CommonAdGsuWithPidListEnricher(kwargs))
    return self

  def commertial_gsu_with_rq_vae(self, **kwargs):
    """
    CommonAdGsuWithRqVaeEnricher
    ------
    根据 photo 所属的 rq_vae code进行 gsu 搜索，并填充特征

    参数
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `semantic_ids`: [string] 用户历史对应的 semantic_id

    `target_semantic_id`: [string] 预估目标的 semantic_id

    `limit_num`: [int] 返回数目 attr

    `codebook`: [list] 量化 codebook size 大小

    `slots_id`: [list] slot id

    `fused_slot_sign_remap` : [bool] 是否remap，默认false

    `mio_slots_id`: [list] remap 的slot id

    `out_prefix`: [string] 输出特征名prefix

    `print_items`: [bool] 输出item，默认false


    示例
    ------
    ``` python
    .commertial_gsu_with_rq_vae(
                colossus_resp_attr='colossus_output',
                semantic_ids='semantic_ids',
                target_semantic_id='target_semantic_id',
                limit_num=120,
                codebook=[256, 512, 1024, 2048, 4096, 4096],
                out_prefix='ia_rq_',
                print_items=True)
    ```
    """
    self._add_processor(CommonAdGsuWithRqVaeEnricher(kwargs))
    return self


  def gsu_with_cluster_diverse_enricher(self, **kwargs):
    """
    CommonAdGsuWithClusterDiverseEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    示例
    ------
    ``` python
    .gsu_with_cluster_diverse_enricher(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster')
    ```
    """
    self._add_processor(CommonAdGsuWithClusterDiverseEnricher(kwargs))
    return self

  def commertial_gsu_with_cluster_tag(self, **kwargs):
    """
    CommonAdGsuWithClusterTagEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `search_type`: [int] 检索类型 1为根据cluster检索 2为根据tag检索

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
    ------
    ``` python
    .commertial_gsu_with_cluster_tag(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                           search_type = 1,
                           hetu_1k_cluster_json_value="")
    ```
    """
    self._add_processor(CommonAdGsuWithClusterTagEnricher(kwargs))
    return self

  def commertial_gsu_with_cluster_tag_batch(self, **kwargs):
    """
    CommonAdGsuWithClusterTagBatchEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `search_type`: [int] 检索类型 1为根据cluster检索 2为根据tag检索

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
    ------
    ``` python
    .commertial_gsu_with_cluster_tag_batch(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                           search_type = 1,
                           hetu_1k_cluster_json_value="")
    ```
    """
    self._add_processor(CommonAdGsuWithClusterTagBatchEnricher(kwargs))
    return self

  def commertial_gsu_with_cluster_tag_v4(self, **kwargs):
    """
    CommonAdGsuWithClusterTagV4Enricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `search_type`: [int] 检索类型 1为根据cluster检索 2为根据tag检索

    `fill_play_time`: [int] 是否用长播补充缺失部分 用大于fill_play_time序列补充

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
    ------
    ``` python
    .commertial_gsu_with_cluster_tag_v2(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                           search_type = 1,
                           hetu_1k_cluster_json_value="")
    ```
    """
    self._add_processor(CommonAdGsuWithClusterTagV4Enricher(kwargs))
    return self

  def commertial_gsu_with_cluster_tag_v3(self, **kwargs):
    """
    CommonAdGsuWithClusterTagV3Enricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `search_type`: [int] 检索类型 1为根据cluster检索 2为根据tag检索

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
    ------
    ``` python
    .commertial_gsu_with_cluster_tag_v2(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                           search_type = 1,
                           hetu_1k_cluster_json_value="")
    ```
    """
    self._add_processor(CommonAdGsuWithClusterTagV3Enricher(kwargs))
    return self

  def commertial_gsu_with_cluster_tag_v2(self, **kwargs):
    """
    CommonAdGsuWithClusterTagV2Enricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `search_type`: [int] 检索类型 1为根据cluster检索 2为根据tag检索

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
    ------
    ``` python
    .commertial_gsu_with_cluster_tag_v2(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                           search_type = 1,
                           hetu_1k_cluster_json_value="")
    ```
    """
    self._add_processor(CommonAdGsuWithClusterTagV2Enricher(kwargs))
    return self

  def ad_live_gsu_with_aid_cluster_cache(self, **kwargs):
    """
    AdLiveGsuWithAidClusterCacheEnricher
    ------
    商业化colossus, 根据 live aid 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征；新增了访问KRP的aid cluster的cache

    参数
    ------
    `cache_size`: [int] 缓存大小, 默认1000000

    `cache_ttl_ms`: [int] 缓存过期时间, 默认3600000(一小时)

    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧author的 cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster json object

    示例
      ------
      ``` python
      .ad_live_gsu_with_aid_cluster_cache(colossus_resp_attr='ad_colossus_resp',
                          output_sign_attr='sign',
                          output_slot_attr='slot',
                          limit_num_attr='limit_num',
                          target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                          cluster_id_service_type='embedding_server',
                          kess_service='mmu_ad_aid_cluster_embedding_query_server',
                          shards=1,
                          hetu_1k_cluster_json_value="")
      ```
      """
    self._add_processor(AdLiveGsuWithAidClusterCacheEnricher(kwargs))
    return self

  def ad_live_gsu_with_aid_cluster_cache_v2(self, **kwargs):
    """
    AdLiveGsuWithAidClusterCacheV2Enricher
    ------
    商业化colossus, 根据 live aid 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征；新增了访问KRP的aid cluster的cache; 使用parse_to_pb=False

    参数
    ------
    `cache_size`: [int] 缓存大小, 默认1000000

    `cache_ttl_ms`: [int] 缓存过期时间, 默认3600000(一小时)

    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧author的 cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster json object

    示例
      ------
      ``` python
      .ad_live_gsu_with_aid_cluster_cache(colossus_resp_attr='ad_colossus_resp',
                          output_sign_attr='sign',
                          output_slot_attr='slot',
                          limit_num_attr='limit_num',
                          target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                          cluster_id_service_type='embedding_server',
                          kess_service='mmu_ad_aid_cluster_embedding_query_server',
                          shards=1,
                          hetu_1k_cluster_json_value="")
      ```
      """
    self._add_processor(AdLiveGsuWithAidClusterCacheV2Enricher(kwargs))
    return self

  def ad_live_gsu_with_aid_cluster_v4(self, **kwargs):
    """
    AdLiveGsuWithAidClusterV4Enricher
    ------
    商业化colossus, 根据 live aid 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征；新增了访问KRP的aid cluster的cache; 使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧author的 cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster json object

    示例
      ------
      ``` python
      .ad_live_gsu_with_aid_cluster_cache(colossus_resp_attr='ad_colossus_resp',
                          output_sign_attr='sign',
                          output_slot_attr='slot',
                          limit_num_attr='limit_num',
                          target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                          cluster_id_service_type='embedding_server',
                          kess_service='mmu_ad_aid_cluster_embedding_query_server',
                          shards=1,
                          hetu_1k_cluster_json_value="")
      ```
      """
    self._add_processor(AdLiveGsuWithAidClusterV4Enricher(kwargs))
    return self

  def ad_p2l_gsu_with_aid_cluster(self, **kwargs):
    """
    AdP2lGsuWithAidClusterEnricher
    ------
    广告和电商 p2l_colossus, 根据 aid 所属的短视频 aid cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧author的 cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    示例
      ------
      ``` python
      .ad_p2l_gsu_with_aid_cluster(colossus_resp_attr='p2l_colossus_resp',
                          output_sign_attr='sign',
                          output_slot_attr='slot',
                          limit_num_attr='limit_num',
                          target_cluster_attr = 'photo_aid_cluster_id',
                        )
      ```
      """
    self._add_processor(AdP2lGsuWithAidClusterEnricher(kwargs))
    return self

  def ad_live_gsu_with_aid_cluster_v3(self, **kwargs):
    """
    AdLiveGsuWithAidClusterV3Enricher
    ------
    商业化colossus, 根据 live aid 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征；新增了访问KRP的aid cluster的cache; 使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧author的 cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster json object

    示例
      ------
      ``` python
      .ad_live_gsu_with_aid_cluster_cache(colossus_resp_attr='ad_colossus_resp',
                          output_sign_attr='sign',
                          output_slot_attr='slot',
                          limit_num_attr='limit_num',
                          target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                          cluster_id_service_type='embedding_server',
                          kess_service='mmu_ad_aid_cluster_embedding_query_server',
                          shards=1,
                          hetu_1k_cluster_json_value="")
      ```
      """
    self._add_processor(AdLiveGsuWithAidClusterV3Enricher(kwargs))
    return self

  def fused_gsu_with_index(self, **kwargs):
    """
    FusedGsuWithIndexEnricher
    ------
    根据 common_attr中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots

    参数
    ------
    `enable_cache`: [bool] 是否开启 cache, 默认 false

    `slot_as_attr_name`: [bool] 是否开启 slot 作为 attr 的 name, 默认 false

    `output_sign_attr`: [str] 生成的 sign 存放的 attr, slot_as_attr_name 为 false 时必须配置, 默认为空

    `output_slot_attr`: [str] 生成的 slot 存放的 attr, slot_as_attr_name 为 false 时必须配置, 默认为空

    `slot_as_attr_name_prefix`: [str] slot_as_attr_name_prefix, slot_as_attr_name 为 true 时生效, 默认为空

    `colossus_pid_attr`: [str] 输入 colossus_pid attr, 不可为空, 默认为空

    `author_id_attr`: [str] 输入 author_id attr, 默认为空

    `tag_attr`: [str] 输入 tag attr, 默认为空

    `play_time_attr`: [str] 输入 play_time attr, 默认为空

    `duration_attr`: [str] 输入 duration attr, 默认为空

    `timestamp_attr`: [str] 输入 timestamp attr, 默认为空

    `channel_attr`: [str] 输入 channel attr, 默认为空

    `label_attr`: [str] 输入 label attr, 默认为空

    `sorted_item_idx_attr`: [str] 排序后 top_n item index 对应的 attr, 不可为空, 默认为空

    `top_n`: [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `output_item_colossus_pid_attr`: [str] 与 top_n 对应的 colossus_pid 列表中的内容，为空时不返回

    `slots_id` [int list]

    `mio_slots_id`: [int list]

    `item_map_list`: [list]

    `sign_prefix_bit_num_input`: [int]

    `sign_prefix_bit_num_output`: [int]

    `colossus_result_as_list`: [bool] 结果是否作为 list 存放, 默认 false

    `colossus_result_list_attr`: [str] 结果作为 list 存放时, 存放结果的 attr 名字, 此时不可为空, 默认为空


    示例
    ------
    ```python

    ```
    """
    self._add_processor(FusedGsuWithIndexEnricher(kwargs))
    return self

  def fused_gsu_with_index_v2(self, **kwargs):
    """
    FusedGsuWithIndexV2Enricher
    ------
    根据 common_attr中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots

    参数
    ------
    `sorted_item_idx_attr` : [string] 排序后 top_n item index 对应的 attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `item_fields_slots` : [list of object] [{"slots_id": xxx, "mio_slots_id": xxx, "item_fields": xxx}]

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回


    示例
    ------
    ``` python
    .gsu_with_index(sorted_item_idx_attr="distance",
                    matrixcolossus_pid_attr="colossus_pid",
                    item_fields_slots = [{"slots_id": 1, "mio_slots_id": 1000, "item_fields": "ad_author_id"}]
                    output_sign_attr="output_sign",
                    output_slot_attr="output_slot",
                    top_n=50)
    ```
    """
    self._add_processor(FusedGsuWithIndexV2Enricher(kwargs))
    return self

  def ad_live_gsu_with_aid_cluster_v2_conf(self, **kwargs):
    """
    AdLiveAidClusterV2ConfEnricher
    ------
    商业化colossus, 根据 live aid 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征；新增了访问KRP的aid cluster的cache; 使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧author的 cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster json object

    示例
      ------
      ``` python
      .ad_live_gsu_with_aid_cluster_cache(colossus_resp_attr='ad_colossus_resp',
                          output_sign_attr='sign',
                          output_slot_attr='slot',
                          limit_num_attr='limit_num',
                          target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                          cluster_id_service_type='embedding_server',
                          kess_service='mmu_ad_aid_cluster_embedding_query_server',
                          shards=1,
                          hetu_1k_cluster_json_value="")
      ```
      """
    self._add_processor(AdLiveAidClusterV2ConfEnricher(kwargs))
    return self

  def ad_live_gsu_with_aid_cluster_v2(self, **kwargs):
    """
    AdLiveGsuWithAidClusterV2Enricher
    ------
    商业化colossus, 根据 live aid 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征；新增了访问KRP的aid cluster的cache; 使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧author的 cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster json object

    示例
      ------
      ``` python
      .ad_live_gsu_with_aid_cluster_cache(colossus_resp_attr='ad_colossus_resp',
                          output_sign_attr='sign',
                          output_slot_attr='slot',
                          limit_num_attr='limit_num',
                          target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                          cluster_id_service_type='embedding_server',
                          kess_service='mmu_ad_aid_cluster_embedding_query_server',
                          shards=1,
                          hetu_1k_cluster_json_value="")
      ```
      """
    self._add_processor(AdLiveGsuWithAidClusterV2Enricher(kwargs))
    return self

  def live_gsu_with_aid_cluster_cache(self, **kwargs):
    """
    LiveGsuWithAidClusterCacheEnricher
    ------
    roco colossus, 根据 live aid 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征；新增了访问KRP的aid cluster的cache

    参数
    ------
    `cache_size`: [int] 缓存大小, 默认1000000

    `cache_ttl_ms`: [int] 缓存过期时间, 默认3600000(一小时)

    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧author的 cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster json object

    示例
      ------
      ``` python
      .live_gsu_with_aid_cluster_cache(colossus_resp_attr='colossus_resp',
                          output_sign_attr='sign',
                          output_slot_attr='slot',
                          limit_num_attr='limit_num',
                          target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                          cluster_id_service_type='embedding_server',
                          kess_service='mmu_ad_aid_cluster_embedding_query_server',
                          shards=1,
                          hetu_1k_cluster_json_value="")
      ```
      """
    self._add_processor(LiveGsuWithAidClusterCacheEnricher(kwargs))
    return self

  def ad_live_gsu_aid2aid(self, **kwargs):
    """
    CommonAdLiveGsuAid2AidEnricher
    ------
    根据 liveid 所属的 aid 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdLiveItem

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_attr`: [string] item侧使用的 aid 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `filter_live_data`: [bool] 是否只保留直播数据 根据数据中的 label 第一位 photoItemimp过滤得到 默认为 false

    示例
    ------
    ``` python
    .ad_live_gsu_aid2aid(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_attr = 'aId',
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642],
                      filter_live_data=False
                      )
    ```
    """
    self._add_processor(CommonAdLiveGsuAid2AidEnricher(kwargs))
    return self

  def ad_live_gsu_aid2aid_v2(self, **kwargs):
    """
    CommonAdLiveGsuAid2AidV2Enricher
    ------
    根据 liveid 所属的 aid 进行 gsu 搜索并填充相似视频的 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdLiveItem

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_attr`: [string] item侧使用的 aid 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .ad_live_gsu_aid2aid(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_attr = 'aId',
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642]
                      )
    ```
    """
    self._add_processor(CommonAdLiveGsuAid2AidV2Enricher(kwargs))
    return self

  def p2l_colossus_gsu_aid2aid(self, **kwargs):
    """
    CommonP2lColossusGsuAid2AidEnricher
    ------
    根据 liveid 所属的 aid 进行 gsu 搜索并填充相似视频的 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdLiveItem

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_attr`: [string] item侧使用的 aid 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .p2l_colossus_gsu_aid2aid(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_attr = 'aId',
                      )
    ```
    """
    self._add_processor(CommonP2lColossusGsuAid2AidEnricher(kwargs))
    return self

  def ad_live_gsu_aid2aid_batch(self, **kwargs):
    """
    CommonAdLiveGsuAid2AidV2BatchEnricher
    ------
    根据 liveid 所属的 aid 进行 gsu 搜索并填充相似用户(u2u)的相似视频的 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdLiveItem

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_attr`: [string] item侧使用的 aid 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .ad_live_gsu_aid2aid_batch(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      personal_limit_num=50,
                      target_attr = 'aId',
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642]
                      )
    ```
    """
    self._add_processor(CommonAdLiveGsuAid2AidV2BatchEnricher(kwargs))
    return self

  def ad_live_gsu_session(self, **kwargs):
    """
    CommonAdLiveGsuSessionEnricher
    ------
    内循环直播 colossus session 特征
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr

    `if_parse_to_pb_attr`: [bool] 接收的 colossus_resp 是否是 pb 格式

    `keep_p2l`: [bool] 是否筛选出 photo2live

    `limit_num`: [int] 截断数量

    `n_minute_ago`: [int] 前移时间

    `session_time_range`: [string] 序列特征窗口 单位为分钟
    调用示例
    ------
    ``` python
    .ad_live_gsu_session(
      colossus_resp_attr = "ad_live_colossus",
      if_parse_to_pb_attr = False,
      keep_p2l = False,
      limit_num = 100,
      n_minute_ago = 1,
      session_time_range = "60;180;360;720;1440;4320;10080;43200;"
    )
    ```
    """
    self._add_processor(CommonAdLiveGsuSessionEnricher(kwargs))
    return self

  def ad_live_gsu_session_v2(self, **kwargs):
    """
    CommonAdLiveGsuSessionV2Enricher
    ------
    内循环直播 colossus session 特征  时间窗口 * play_rate 分桶 * pid/aid
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr

    `if_parse_to_pb_attr`: [bool] 接收的 colossus_resp 是否是 pb 格式

    `keep_p2l`: [bool] 是否筛选出 photo2live

    `limit_num`: [int] 截断数量

    `n_minute_ago`: [int] 前移时间

    `session_time_range`: [string] 序列特征窗口 单位为分钟
    调用示例
    ------
    ``` python
    .ad_live_gsu_session_v2(
      colossus_resp_attr = "ad_live_colossus",
      if_parse_to_pb_attr = False,
      keep_p2l = True,
      limit_num = 100,
      n_minute_ago = 1,
      session_time_range = "360;1440;10080;43200;129600"
    )
    ```
    """
    self._add_processor(CommonAdLiveGsuSessionV2Enricher(kwargs))
    return self

  def ad_live_user_neg(self, **kwargs):
    """
    CommonAdLiveUserNegEnricher
    ------
    内循环直播 colossus 隐式负反馈特征  "imp_no_p3s",        "imp_no_pend", "imp_no_ctr",
                                    "p3s_no_pend",       "p3s_no_ctr",  "p3s_no_cart",
                                    "p3s_no_itemclick",  "pend_no_ctr", "pend_no_cart",
                                    "pend_no_itemclick", "pend_no_buy", "itemclick_no_buy"
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr

    `if_parse_to_pb_attr`: [bool] 接收的 colossus_resp 是否是 pb 格式

    `keep_p2l`: [bool] 是否筛选出 photo2live

    `limit_num`: [int] 截断数量

    `n_minute_ago`: [int] 前移时间

    调用示例
    ------
    ``` python
    .ad_live_user_neg(
      colossus_resp_attr = "ad_live_colossus",
      if_parse_to_pb_attr = False,
      keep_p2l = True,
      limit_num = 100,
      n_minute_ago = 1
    )
    ```
    """
    self._add_processor(CommonAdLiveUserNegEnricher(kwargs))
    return self

  def ad_sim_item_gsu_industry2industry_batch(self, **kwargs):
    """
    AdSimItemIndustry2IndustryBatchEnricher
    ------
    从相似用户的行为序列中检索同行业的行为数据 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdSimItem

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_attr`: [string] item侧使用的 行业属性

    `fused_slot_sign_remap`: [bool] 对应是否融合remap操作

    `scene_type` : [int] 对应的场景数据选择，默认是0，0代表全场景数据，=id,则id对应的是对应场景id

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .ad_sim_item_gsu_industry2industry_batch(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='gsu_sign',
                      output_slot_attr='gsu_slot',
                      limit_num_attr='live_limit_num',
                      personal_limit_num=50,
                      target_attr = 'industry',
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639]
                      )
    ```
    """
    self._add_processor(AdSimItemIndustry2IndustryBatchEnricher(kwargs))
    return self

  def ad_sim_item_gsu_u2u_batch_industry_with_user_enricher(self, **kwargs):
    """
    AdSimItemGsuU2UBatchIndustryWithUserEnricher
    ------
    从相似用户的行为序列中检索同行业的行为数据 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdSimItem

    `similar_user_attr`: [string] u2u 输入的 attr, 挡墙用户的相似用户列表

    `industry_attr`: [string] 过滤 item 的行业类型

    `limit_num_`: [int] 返回数目, 默认 50

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `fused_slot_sign_remap`: [bool] 对应是否融合remap操作

    `scene_type` : [int] 对应的场景数据选择，默认是0，0代表全场景数据，=id,则id对应的是对应场景id

    示例
    ------
    ``` python
    .ad_sim_item_gsu_u2u_batch_industry_with_user(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='gsu_sign',
                      output_slot_attr='gsu_slot',
                      limit_num_=50,
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639]
                      )
    ```
    """
    self._add_processor(AdSimItemGsuU2UBatchIndustryWithUserEnricher(kwargs))
    return self

  def ad_sim_item_gsu_u2u_batch(self, **kwargs):
    """
    AdSimItemGsuU2UBatchEnricher
    ------
    从相似用户的行为序列中检索同行业的行为数据 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdSimItem

    `limit_num_`: [int] 返回数目, 默认 50

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `fused_slot_sign_remap`: [bool] 对应是否融合remap操作

    `scene_type` : [int] 对应的场景数据选择，默认是0，0代表全场景数据，=id,则id对应的是对应场景id

    示例
    ------
    ``` python
    .ad_sim_item_gsu_industry2industry_batch(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='gsu_sign',
                      output_slot_attr='gsu_slot',
                      limit_num_=50,
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639]
                      )
    ```
    """
    self._add_processor(AdSimItemGsuU2UBatchEnricher(kwargs))
    return self

  def ad_sim_item_gsu_u2u_batch_diversity(self, **kwargs):
    """
    AdSimItemGsuU2UBatchDiversityEnricher
    ------
    从相似用户的行为序列中检索同行业的行为数据 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdSimItem

    `limit_num_`: [int] 返回数目, 默认 50

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `fused_slot_sign_remap`: [bool] 对应是否融合remap操作

    `scene_type` : [int] 对应的场景数据选择，默认是0，0代表全场景数据，=id,则id对应的是对应场景id

    示例
    ------
    ``` python
    .ad_sim_item_gsu_industry2industry_batch_diversity(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='gsu_sign',
                      output_slot_attr='gsu_slot',
                      limit_num_=50,
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639]
                      )
    ```
    """
    self._add_processor(AdSimItemGsuU2UBatchDiversityEnricher(kwargs))
    return self

  def adsimitem_gsu_retriever_with_colossus_enricher(self, **kwargs):
    """
    AdSimItemGsuRetrieverWithColossusEnricher
    ------
    从相似用户的行为序列中检索同行业的行为数据 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdSimItem

    `limit_num_`: [int] 返回数目, 默认 50

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `fused_slot_sign_remap`: [bool] 对应是否融合remap操作

    `scene_type` : [int] 对应的场景数据选择，默认是0，0代表全场景数据

    示例
    ------
    ``` python
    .ad_sim_item_gsu_industry2industry_batch_diversity(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='gsu_sign',
                      output_slot_attr='gsu_slot',
                      limit_num_=50,
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640,
                                641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657,
                                658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640,
                                    641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657,
                                    658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669]
                      )
    ```
    """
    self._add_processor(AdSimItemGsuRetrieverWithColossusEnricher(kwargs))
    return self
  
  def adsimitem_gsu_retriever_with_colossus_enricher_v2(self, **kwargs):
    """
    AdSimItemGsuRetrieverWithColossusV2Enricher
    ------
    从相似用户的行为序列中检索同行业的行为数据 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdSimItem

    `limit_num_`: [int] 返回数目, 默认 50
    
    `fused_slot_sign_remap`: [bool] 对应是否融合remap操作

    `scene_type` : [int] 对应的场景数据选择，默认是0，0代表全场景数据

    示例
    ------
    ``` python
    .adsimitem_gsu_retriever_with_colossus_enricher_v2(colossus_resp_attr='live_colossus_resp',
                      limit_num_=50
                      )
    ```
    """
    self._add_processor(AdSimItemGsuRetrieverWithColossusV2Enricher(kwargs))
    return self

  def adsimitem_gsu_retriever_with_industry_enricher(self, **kwargs):
    """
    AdSimItemGsuRetrieverWithColossusEnricher
    ------
    从相似用户的行为序列中检索同行业的行为数据 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdSimItem

    `industry_attr`: [string] 当前 item 的行业 attr

    `limit_num_`: [int] 返回数目, 默认 50

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `fused_slot_sign_remap`: [bool] 对应是否融合remap操作

    `scene_type` : [int] 对应的场景数据选择，默认是0，0代表全场景数据

    示例
    ------
    ``` python
    .ad_sim_item_gsu_industry2industry_batch_diversity(colossus_resp_attr='live_colossus_resp',
                      industry_attr='industry_id'
                      output_sign_attr='gsu_sign',
                      output_slot_attr='gsu_slot',
                      limit_num_=50,
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640,
                                641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657,
                                658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640,
                                    641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657,
                                    658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669]
                      )
    ```
    """
    self._add_processor(AdSimItemGsuRetrieverWithIndustryEnricher(kwargs))
    return self

  def ad_sim_item_gsu_u2u_batch_diversity_with_user(self, **kwargs):
    """
    AdSimItemGsuU2UBatchDiversityWithUserEnricher
    ------
    从相似用户的行为序列中检索同行业的行为数据 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdSimItem

    `similar_user_attr`: [string] u2u 输入的 attr, 挡墙用户的相似用户列表

    `limit_num_`: [int] 返回数目, 默认 50

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `fused_slot_sign_remap`: [bool] 对应是否融合remap操作

    `scene_type` : [int] 对应的场景数据选择，默认是0，0代表全场景数据，=id,则id对应的是对应场景id

    示例
    ------
    ``` python
    .ad_sim_item_gsu_industry2industry_batch_diversity(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='gsu_sign',
                      output_slot_attr='gsu_slot',
                      limit_num_=50,
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639]
                      )
    ```
    """
    self._add_processor(AdSimItemGsuU2UBatchDiversityWithUserEnricher(kwargs))
    return self

  def ad_click_item_gsu_u2u_batch_diversity_with_user(self, **kwargs):
    """
    AdClickItemGsuU2UBatchDiversityWithUserEnricher
    ------
    从相似用户的行为序列中检索同行业的行为数据 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdSimItem

    `similar_user_attr`: [string] u2u 输入的 attr, 挡墙用户的相似用户列表

    `limit_num_`: [int] 返回数目, 默认 50

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `fused_slot_sign_remap`: [bool] 对应是否融合remap操作

    `scene_type` : [int] 对应的场景数据选择，默认是0，0代表全场景数据，=id,则id对应的是对应场景id

    示例
    ------
    ``` python
    .ad_click_item_gsu_u2u_batch_diversity_with_user(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='gsu_sign',
                      output_slot_attr='gsu_slot',
                      limit_num_=50,
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639]
                      )
    ```
    """
    self._add_processor(AdClickItemGsuU2UBatchDiversityWithUserEnricher(kwargs))
    return self

  def ad_live_gsu_with_cluster_v2(self, **kwargs):
    """
    CommonAdLiveGsuWithClusterV2Enricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `cluster_id_type`: [string] 使用哪种域cluster id进行相似度计算，包括all_domain_cluster_id, merchant_domain_cluster_id  两种类型

    示例
    ------
    ``` python
    .live_gsu_with_cluster_v2(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='ad_live_gsu_sign',
                      output_slot_attr='ad_live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      cluster_id_type = "all_domain_cluster_id"
                      )
    ```
    """
    self._add_processor(CommonAdLiveGsuWithClusterV2Enricher(kwargs))
    return self

  def adunion_live_gsu_with_cluster_v2(self, **kwargs):
    """
    AdunionLiveGsuWithClusterV2Enricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .adunion_live_gsu_with_cluster_v2(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='ad_live_gsu_sign',
                      output_slot_attr='ad_live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      cluster_id_type = "all_domain_cluster_id"
                      )
    ```
    """
    self._add_processor(AdunionLiveGsuWithClusterV2Enricher(kwargs))
    return self

  def adunion_live_v2_gsu_with_cluster_v2(self, **kwargs):
    """
    AdunionLiveV2GsuWithClusterV2Enricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .adunion_live_v2_gsu_with_cluster_v2(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='ad_live_gsu_sign',
                      output_slot_attr='ad_live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      cluster_id_type = "all_domain_cluster_id"
                      )
    ```
    """
    self._add_processor(AdunionLiveV2GsuWithClusterV2Enricher(kwargs))
    return self

  def adunion_live_gsu_aid2aid_v2(self, **kwargs):
    """
    AdunionLiveGsuAid2AidV2Enricher
    ------
    根据 liveid 所属的 aid 进行 gsu 搜索并填充相似视频的 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdLiveItem

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_attr`: [string] item侧使用的 aid 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .ad_live_gsu_aid2aid(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_attr = 'aId',
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642]
                      )
    ```
    """
    self._add_processor(AdunionLiveGsuAid2AidV2Enricher(kwargs))
    return self

  def live_gsu_retriever_with_ad_colossus_resp(self, **kwargs):
    """
    CommonAdLiveColossusRespRetriever
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_live_id_to_attr`: [string]

    `save_photo_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `save_photo_duration_to_attr`: [string]

    `save_photo_play_time_to_attr`: [string]

    `save_live_play_time_to_attr`: [string]

    `save_label_to_attr`: [string]

    `save_channel_to_attr`: [string]

    `save_cluster_id_to_attr`: [string]

    `save_tag_to_attr`: [string]

    `save_gmv_to_attr`: [string]

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    `filter_by_label` : [boolean] 是否根据label过滤数据, 默认为false. 值为true时filter_by_label_bit为默认值

    `remove_if_equal_1` : [string] 当label 等于/不等于 1 时过滤数据

    `filter_by_label_bit`: [int] 根据label二进制的第几位过滤, 默认值为-1

    示例
    ------
    ``` python
    .live_gsu_retriever_with_ad_colossus_resp(
      colossus_resp_attr="live_colossus_output",
      save_live_id_to_attr="live_colossu_live_id",
      save_photo_id_to_attr="live_colossus_pid",
      save_author_id_to_attr="live_colossus_aid",
      save_timestamp_to_attr="live_colossus_timestamp",
      save_photo_duration_to_attr="live_colossus_duration",
      save_photo_play_time_to_attr="live_colossus_photo_play_time",
      save_live_play_time_to_attr="live_colossus_live_play_time",
      save_label_to_attr="live_colossus_label",
      save_channel_to_attr="live_colossus_channel",
      save_cluster_id_to_attr="live_colossus_cluster_id",
      save_tag_to_attr="live_colossus_tag",
      save_gmv_to_attr="live_colossus_gmv",
      save_result_to_common_attr="live_colossus_pid",
      filter_by_label=True,
      remove_if_equal_1=False,
      filter_by_label_bit=1,
      filter_future_attr=True)
    ```
    """
    self._add_processor(CommonAdLiveColossusRespRetriever(kwargs))
    return self

  def ad_gsu_retriever_with_p2l_colossus_resp(self, **kwargs):
    self._add_processor(CommonP2lColossusRespV2Retriever(kwargs))
    return self

  def ad_retriever_with_goods_colossus_resp(self, **kwargs):
    """
    AdGoodsColossusRespRetriever
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_goods_id_to_attr`: [string]

    `save_goods_price_to_attr`: [string]

    `save_order_time_to_attr`: [string]

    `save_impression_time_to_attr`: [string]

    `save_label_to_attr`: [string]

    `save_spu_id_to_attr`: [string]

    `save_category_to_attr`: [string]

    `save_seller_id_to_attr`: [string]


    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false


    示例
    ------
    ``` python
    .ad_retriever_with_goods_colossus_resp(
      colossus_resp_attr="colossus_output",
      save_goods_id_to_attr="goods_id",
      save_goods_price_to_attr="goods_price",
      save_result_to_common_attr="colossus_goods_pid",
      filter_future_attr=True)
    ```
    """
    self._add_processor(AdGoodsColossusRespRetriever(kwargs))
    return self

  def live_gsu_retriever_with_ad_colossus_resp_v2(self, **kwargs):
    """
    CommonAdLiveColossusRespV2Retriever
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_live_id_to_attr`: [string]

    `save_photo_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `save_photo_duration_to_attr`: [string]

    `save_photo_play_time_to_attr`: [string]

    `save_live_play_time_to_attr`: [string]

    `save_label_to_attr`: [string]

    `save_channel_to_attr`: [string]

    `save_cluster_id_to_attr`: [string]

    `save_tag_to_attr`: [string]

    `save_gmv_to_attr`: [string]

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    `filter_by_label` : [boolean] 是否根据label过滤数据, 默认为false. 值为true时filter_by_label_bit为默认值

    `remove_if_equal_1` : [string] 当label 等于/不等于 1 时过滤数据

    `filter_by_label_bit`: [int] 根据label二进制的第几位过滤, 默认值为-1

    示例
    ------
    ``` python
    .live_gsu_retriever_with_ad_colossus_resp(
      colossus_resp_attr="live_colossus_output",
      save_live_id_to_attr="live_colossu_live_id",
      save_photo_id_to_attr="live_colossus_pid",
      save_author_id_to_attr="live_colossus_aid",
      save_timestamp_to_attr="live_colossus_timestamp",
      save_photo_duration_to_attr="live_colossus_duration",
      save_photo_play_time_to_attr="live_colossus_photo_play_time",
      save_live_play_time_to_attr="live_colossus_live_play_time",
      save_label_to_attr="live_colossus_label",
      save_channel_to_attr="live_colossus_channel",
      save_cluster_id_to_attr="live_colossus_cluster_id",
      save_tag_to_attr="live_colossus_tag",
      save_gmv_to_attr="live_colossus_gmv",
      save_result_to_common_attr="live_colossus_pid",
      filter_by_label=True,
      remove_if_equal_1=False,
      filter_by_label_bit=1,
      filter_future_attr=True)
    ```
    """
    self._add_processor(CommonAdLiveColossusRespV2Retriever(kwargs))
    return self

  def adunion_live_gsu_with_aid_cluster_cache_v2(self, **kwargs):
    """
    AdunionLiveGsuWithAidClusterCacheV2Enricher
    ------
    reco colossus, 根据 live aid 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征；新增了访问KRP的aid cluster的cache; 使用parse_to_pb=False

    参数
    ------
    `cache_size`: [int] 缓存大小, 默认1000000

    `cache_ttl_ms`: [int] 缓存过期时间, 默认3600000(一小时)

    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧author的 cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster json object

    示例
      ------
      ``` python
      .ad_live_gsu_with_aid_cluster_cache(colossus_resp_attr='ad_colossus_resp',
                          output_sign_attr='sign',
                          output_slot_attr='slot',
                          limit_num_attr='limit_num',
                          target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                          cluster_id_service_type='embedding_server',
                          kess_service='mmu_ad_aid_cluster_embedding_query_server',
                          shards=1,
                          hetu_1k_cluster_json_value="")
      ```
      """
    self._add_processor(AdunionLiveGsuWithAidClusterCacheV2Enricher(kwargs))
    return self

  def commertial_gsu_with_aid_cluster_dsp(self, **kwargs):
    """
    CommonCommertialGsuWithAidClusterDspEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 rpc 请求中的最大 aid 个数，为 0 代表不限制

    `use_append_cluster`  : [bool] 是否使用扩增类别, 默认为 False

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
      ------
      ``` python
      .commertial_gsu_with_aid_cluster_dsp(colossus_resp_attr='colossus_resp',
                          output_sign_attr='sign',
                          output_slot_attr='slot',
                          limit_num_attr='limit_num',
                          cluster_id_service_type='embedding_server',
                          kess_service='mmu_ad_aid_cluster_embedding_query_server',
                          shards=1,
                          use_append_cluster = false,
                          hetu_1k_cluster_json_value="")
      ```
      """
    self._add_processor(CommonCommertialGsuWithAidClusterDspEnricher(kwargs))
    return self

  def bucket_join(self, **kwargs):
    self._add_processor(OfflineBucketJoinEnricher(kwargs))
    return self

  def sim_extract(self, **kwargs):
    self._add_processor(OfflineSimExtractorEnricher(kwargs))
    return self

  def read_sample_interface(self, **kwargs):
    self._add_processor(SampleInterfaceRetriever(kwargs))
    return self

  def dump_sample_interface(self, **kwargs):
    self._add_processor(OfflineDumpSampleInterfaceEnricher(kwargs))
    return self

  def offline_dump_context(self, **kwargs):
    self._add_processor(DumpContextObserver(kwargs))
    return self

  def sample_interface_trans_raw_feature(self, **kwargs):
    self._add_processor(OfflineSampleInterfaceTransRawFeatureEnricher (kwargs))
    return self

  def get_redis_live_profile_by_hash_key(self, **kwargs):
    """
    FetchRedisLiveProfileByHashKeyEnricher
    ------
    用 string item attr 为 key, 且设置固定的field, 用hashget读取 redis value 写入指定的 item_attr 中。

    参数配置
    ------
    `cluster_name`: [string] redis 的 cluster name

    `timeout_ms`: [int] 获取 redis client 的超时时间，默认为 10

    `redis_hash_key_prfix`: [string] 要获取的redis数据的key_prefix, 选填 默认为""

    `redis_hash_key_suffix`: [string] 要获取的redis数据的key_suffix, 选填 默认为""

    `redis_hash_key_from`: [string] 从 string 类型的 item_attr 获取 key 中间值, 比如pid, live_id

    `redis_hash_field_from`: [string] 从 redis 的key中取对应的field值, 一般是特征名

    `save_value_to`: [string] 将 redis 获取到的值存入指定的 string 类型的 item_attr 中

    调用示例
    ------
    ``` python
    .get_redis_live_profile_by_hash_key(
      cluster_name = "redis_cluster_name",
      timeout_ms=10,
      redis_hash_key_prefix="prefix",
      redis_hash_key_suffix="suffix",
      redis_hash_key_from="key_item",
      redis_hash_field_from="field_name",
      save_value_to="redis_value",
    )
    ```
    """
    self._add_processor(FetchRedisLiveProfileByHashKeyEnricher(kwargs))
    return self

  def live_gsu_with_cluster_add_aid_cluster(self, **kwargs):
    """
    CommonLiveGsuWithClusterAddAidClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征, 并加入aid cluster id

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] aid cluster server 的 kess 服务名

    `shards`: [int] aid cluster server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] aid cluster server 的 kess 集群名，默认为 PRODUCTION

    `max_aids_per_request`: [int] 单次 aid cluster server rpc 请求中的最大 aid 个数，为 0 代表不限制

    示例
    ------
    ``` python
    .live_gsu_with_cluster_add_aid_cluster(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      cluster_id_service_type='embedding_server',
                      kess_service='mmu_ad_aid_cluster_embedding_query_server',
                      shards=1
                      )
    ```
    """
    self._add_processor(CommonLiveGsuWithClusterAddAidClusterEnricher(kwargs))
    return self

  def message_filter_remap(self, **kwargs):
    self._add_processor(MessageFilterRemapEnricher(kwargs))
    return self

  def item_key_retrieve(self, **kwargs):
    self._add_processor(AdItemKeyRetriever(kwargs))
    return self

  def ad_photo_gsu_aid2aid(self, **kwargs):
    """
    CommonAdPhotoGsuAid2AidEnricher
    ------
    根据 photo_id 所属的 aid 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdLiveItem

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_attr`: [string] item侧使用的 aid 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .ad_photo_gsu_aid2aid(colossus_resp_attr='photo_colossus_resp',
                      output_sign_attr='photo_gsu_sign',
                      output_slot_attr='photo_gsu_slot',
                      limit_num_attr='photo_limit_num',
                      target_attr = 'aId',
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639]
                      )
    ```
    """
    self._add_processor(CommonAdPhotoGsuAid2AidEnricher(kwargs))
    return self

  def ad_live_gsu_with_cluster_v3(self, **kwargs):
    """
    AdLiveGsuWithClusterV3Enricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征;  使用parse_to_pb=False; 直接remap

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `cluster_id_type`: [string] 使用哪种域cluster id进行相似度计算，包括all_domain_cluster_id, merchant_domain_cluster_id  两种类型

    示例
    ------
    ``` python
    .live_gsu_with_cluster_v2(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='ad_live_gsu_sign',
                      output_slot_attr='ad_live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      cluster_id_type = "all_domain_cluster_id"
                      )
    ```
    """
    self._add_processor(AdLiveGsuWithClusterV3Enricher(kwargs))
    return self

  def ad_grouped_reco_sim_lzc(self, **kwargs):
    """
    LzcAdGroupedRecoSimSeqEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征;  使用parse_to_pb=False; 直接remap

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `cluster_id_type`: [string] 使用哪种域cluster id进行相似度计算，包括all_domain_cluster_id, merchant_domain_cluster_id  两种类型

    示例
    ------
    ``` python
    .live_gsu_with_cluster_v2(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='ad_live_gsu_sign',
                      output_slot_attr='ad_live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      cluster_id_type = "all_domain_cluster_id"
                      )
    ```
    """
    self._add_processor(LzcAdGroupedRecoSimSeqEnricher(kwargs))
    return self

  def ad_grouped_reco_sim_lzc_v2(self, **kwargs):
      """
      LzcAdGroupedRecoSimSeqV2Enricher
      ------
      根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征;  使用parse_to_pb=False; 直接remap

      参数
      ------
      `output_sign_attr`: [string] sign 输出 attr name

      `output_slot_attr`: [string] slot 输出 attr name

      `colossus_resp_attr`: [string] colossus_resp 输入的 attr

      `limit_num_attr`: [int] 返回数目 attr

      `slots_ids` : [int] 抽sign 使用的slot

      `mio_slots_ids` : [int] mio 前向使用的slot

      `target_cluster_attr`: [string] item侧使用的 item cluster 属性

      `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

      `cluster_id_type`: [string] 使用哪种域cluster id进行相似度计算，包括all_domain_cluster_id, merchant_domain_cluster_id  两种类型

      示例
      ------
      ``` python
      .ad_grouped_reco_sim_lzc_v2(colossus_resp_attr='live_colossus_resp',
                        output_sign_attr='ad_live_gsu_sign',
                        output_slot_attr='ad_live_gsu_slot',
                        limit_num_attr='live_limit_num',
                        target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                        cluster_id_type = "all_domain_cluster_id"
                        )
      ```
      """
      self._add_processor(LzcAdGroupedRecoSimSeqV2Enricher(kwargs))
      return self

  def adunion_live_gsu_aid2aid_v3(self, **kwargs):
    """
    AdunionLiveGsuAid2AidV3Enricher
    ------
    根据 liveid 所属的 aid 进行 gsu 搜索并填充相似视频的 sign/slot 特征;  使用parse_to_pb=False

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdLiveItem

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_attr`: [string] item侧使用的 aid 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .ad_live_gsu_aid2aid(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_attr = 'aId',
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642]
                      )
    ```
    """
    self._add_processor(AdunionLiveGsuAid2AidV3Enricher(kwargs))
    return self

  def ad_live_gsu_aid2aid_v3(self, **kwargs):
    """
    AdLiveGsuAid2AidV3Enricher
    ------
    根据 liveid 所属的 aid 进行 gsu 搜索并填充相似视频的 sign/slot 特征;  使用parse_to_pb=False 带remap

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdLiveItem

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_attr`: [string] item侧使用的 aid 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .ad_live_gsu_aid2aid(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_attr = 'aId',
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642]
                      )
    ```
    """
    self._add_processor(AdLiveGsuAid2AidV3Enricher(kwargs))
    return self

  def ad_append_int_list_item_attr(self, **kwargs):
    """
    AdAppendIntListItemAttrEnricher
    ------
    对slot填充sign

    参数
    ------
    `output_sign_attr`: [string] sign 输入/输出 attr name

    `output_slot_attr`: [string] slot 输入/输出 attr name

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    示例
    ------
    ``` python
    .ad_append_int_list_item_attr(
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642],
                      signs=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642]
                      )
    ```
    """
    self._add_processor(AdAppendIntListItemAttrEnricher(kwargs))
    return self

  def adunion_live_v2_gsu_with_cluster_v3(self, **kwargs):
    """
    AdunionLiveV2GsuWithClusterV3Enricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征;  使用parse_to_pb=False 带remap

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .adunion_live_v2_gsu_with_cluster_v3(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='ad_live_gsu_sign',
                      output_slot_attr='ad_live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      cluster_id_type = "all_domain_cluster_id"
                      )
    ```
    """
    self._add_processor(AdunionLiveV2GsuWithClusterV3Enricher(kwargs))
    return self

  def fetch_tower_topn_dot_product_pxtr_for_gsim(self, **kwargs):
    """
    TowerFetchTopNDotProductAttrGsimEnricher
    ------
    user embedding 与每个item 的 list 都计算一次距离

    参数
    ------
    `kess_service`: [string] 预估服务kess 服务名

    `kess_cluster`: [string] 预估服务kess 集群名，默认 PRODUCTION

    `shards`: [int] 预估服务shard 数量

    `timeout_ms`: [int] 预估服务 rpc 超时，单位毫秒，默认 50

    `is_target_embedding_common`: [bool] user embedding 是否为 common embedding，默认 true

    `target_embedding_attr`: [string] user embedding 在 context CommonAttr 中的字段名

    `item_embedding_list_key_attr`: [string] ItemAttr 获取 embedding key 列表的key

    `server_request_type`: [string] 预估服务也是dragonfly 实现，需要request_type参数

    `req_common_embedding_attr`: [string] 从 CommonAttr 中指示 common embedding 存放的字段,默认值req_common_embedding

    `req_tower_caller_attr`: [string] 在 request common attr 中标明自己的调用身份，默认值tower_caller

    `return_pxtr_value_attr`: [string] 在 request common attr 中指定返回 pxtr 结果时存放的 response common attr字段，被调必须遵守，默认值return_pxtr_value
                              同时，如果上游没有指定 pxtr 值返回的字段(item_pxtr_value_attr)，那么该字段也是返回给上游的 attr 字段名

    `return_sorted_item_ids_attr`: [string] 在 request common attr 中指定返回排序后 topn index 时存放的 response common attr字段，被调必须遵守.

    `sorted_item_idx_attr`: [string] 输出排序后 topn item index 的 ItemAttr 名

    `sorted_item_score_attr`: [string] 输出排序后 topn item score 的 ItemAttr 名
    示例
    ------
    ``` python
    .fetch_tower_topn_dot_product_pxtr_for_gsim(
                                  target_embedding_attr = "user_output_embedding",
                                  kess_service = 'grpc_TowerPhotoEmbeddingWithPxtrCalc',
                                  shards = 8,
                                  timeout_ms = 20,
                                  server_request_type = 'rt_tower_predict_pxtr',
                                  req_common_embedding_attr='req_common_embedding',
                                  req_tower_caller_attr='tower_caller',
                                  return_pxtr_value_attr='return_pxtr_value_attr',
                                  return_sorted_item_ids_attr="sorted_item_ids_vec",
                                  sorted_item_idx_attr='sorted_item_idx',
                                  item_embedding_list_key_attr='kgnn_user_id_list',
                                  sorted_item_score_attr='sorted_item_score',
                                  emb_dim=64,
                                  top_n=200
                                  )
    ```
    """
    self._add_processor(TowerFetchTopNDotProductAttrGsimEnricher(kwargs))
    return self

  def slot_merge(self, **kwargs):
    """
    CommonGoodsSlotMergeEnricher
    ------
    将 slot 合并

    参数
    ------
    `sign_first_attr`: [string] 第一个 sign 的 attr 名

    `slot_first_attr`: [string] 第一个 slot 的 attr 名

    `sign_sec_attr`: [string] 第二个 sign 的 attr 名

    `slot_sec_attr`: [string] 第二个 slot 的 attr 名

    `output_sign_attr`: [string] 合并后 sign 的 attr 名

    `output_slot_attr`: [string] 合并后 slot 的 attr 名

    示例
    ------
    ``` python
    .slot_merge(
               sign_first_attr="sign_first_attr",
               slot_first_attr="slot_first_attr",
               sign_sec_attr="sign_sec_attr",
               slot_sec_attr="slot_sec_attr",
               output_sign_attr="output_sign_attr",
               output_slot_attr="output_slot_attr"
               )
    ```
    """
    self._add_processor(CommonGoodsSlotMergeEnricher(kwargs))
    return self

  def emb_mean(self, **kwargs):
    """
    CommonGoodsEmbMeanEnricher
    ------
    多个 emb 取平均，最多五个

    参数
    ------
    `emb1_attr`: [string] 第一个 emb 的 attr 名

    `emb2_attr`: [string] 第二个 emb 的 attr 名

    `emb3_attr`: [string] 第三个 emb 的 attr 名

    `emb4_attr`: [string] 第四个 emb 的 attr 名

    `emb5_attr`: [string] 第五个 emb 的 attr 名

    `output_attr`: [string] 合并后 slot 的 attr 名

    示例
    ------
    ``` python
    .emb_mean(
               emb1_attr="emb1_attr",
               emb2_attr="emb2_attr",
               emb3_attr="emb3_attr",
               emb4_attr="emb4_attr",
               emb5_attr="emb5_attr",
               output_attr="output_attr"
               )
    ```
    """
    self._add_processor(CommonGoodsEmbMeanEnricher(kwargs))
    return self

  def ad_goods_gsu_spu_id(self, **kwargs):
    """
    AdGoodsGsuWithCateEnricher
    ---
    商业化内循环获取商品sim的 spu id
    参数配置
    ---
    `limit_num_attr`: [string] id 列表长度的 common attr

    `goods_num_attr`: [string] target goods id 列表长度的 common attr

    `colossus_resp_attr`: [string] colossus 返回对应的 common attr

    `target_item_cate2_attr`: [string] target_item 的二级类目对应的 item attr

    `target_item_cate3_attr`: [string] target_item 的三级类目对应的 item attr

    `target_item_spu_attr`: [string] target_item 的 spu 对应的 item attr

    `output_id_attr`: [string] 返回的商品 id 列表对应的 item attr

    调用示例
    ---
    ``` python
    .ad_goods_gsu_id(
                    colossus_resp_attr="colossus_output",
                    limit_num_attr="limit_num",
                    goods_num_attr="goods_num",
                    target_item_spu_attr="target_live_spu_ids",
                    target_item_cate2_attr="target_live_cid2_ids",
                    target_item_cate3_attr="target_live_cid3_ids",
                    output_id_attr="output_id_list"
                   )
    ```
    """
    self._add_processor(AdGoodsGsuWithCateEnricher(kwargs))
    return self

  def ad_attr_index(self, **kwargs):
    """
    AdAttrIndexEnricher
    ---
    依据 index 取 attr 中的对应值
    参数配置
    ---

    `input_attr`: [string] 输入的 item attr

    `index_list`: [vec<string>] 需要的 index

    `output_attr`: [vec<string>] 返回对应的 item attr

    调用示例
    ---
    ``` python
    .ad_attr_index(
                    input_attr="c2_list",
                    index_list=[1,2,3],
                    output_attr=["c1", "c2", "c3"]
                    )
    ```
    """
    self._add_processor(AdAttrIndexEnricher(kwargs))
    return self

  def ad_goods_gsu_with_x7_cate(self, **kwargs):
    """
    AdGoodsGsuWithX7CateEnricher
    ---
    商业化内循环获取商品sim的 spu id
    参数配置
    ---
    `limit_num_attr`: [string] id 列表长度的 common attr

    `goods_num_attr`: [string] target goods id 列表长度的 common attr

    `colossus_resp_attr`: [string] colossus 返回对应的 common attr

    `target_item_cate2_attr`: [string] target_item 的二级类目对应的 item attr

    `target_item_cate3_attr`: [string] target_item 的三级类目对应的 item attr

    `target_item_spu_attr`: [string] target_item 的 spu 对应的 item attr

    `output_id_attr`: [string] 返回的商品 id 列表对应的 item attr

    调用示例
    ---
    ``` python
    .ad_goods_gsu_with_x7_cate(
                      colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      goods_num_attr="goods_num",
                      n_minute_ago=2880,
                      target_item_spu_attr="target_live_spu_ids",
                      target_item_cate2_attr="target_live_cid2_ids",
                      target_item_cate3_attr="target_live_cid3_ids",
                      slots_id=[660, 661, 662, 663, 664, 665, 666, 667, 668],
                      mio_slots_id=[660, 661, 662, 663, 664, 665, 666, 667, 668]
                     )
    ```
    """
    self._add_processor(AdGoodsGsuWithX7CateEnricher(kwargs))
    return self

  def ad_goods_gsu_spu_id_with_action(self, **kwargs):
    """
    AdGoodsWithActionEnricher
    ---
    商业化内循环获取商品sim的 spu id
    参数配置
    ---
    `limit_num_attr`: [string] id 列表长度的 common attr

    `goods_num_attr`: [string] target goods id 列表长度的 common attr

    `colossus_resp_attr`: [string] colossus 返回对应的 common attr

    `target_item_cate2_attr`: [string] target_item 的二级类目对应的 item attr

    `target_item_cate3_attr`: [string] target_item 的三级类目对应的 item attr

    `target_item_spu_attr`: [string] target_item 的 spu 对应的 item attr

    `action_ids`: [vec<string>] 需要的 action_id

    `output_id_attr`: [string] 返回的商品 id 列表对应的 item attr

    调用示例
    ---
    ``` python
    .ad_goods_gsu_id(
                    colossus_resp_attr="colossus_output",
                    limit_num_attr="limit_num",
                    goods_num_attr="goods_num",
                    target_item_spu_attr="target_live_spu_ids",
                    target_item_cate2_attr="target_live_cid2_ids",
                    target_item_cate3_attr="target_live_cid3_ids",
                    action_ids=[1,2,3],
                    output_id_attr="output_id_list"
                   )
    ```
    """
    self._add_processor(AdGoodsWithActionEnricher(kwargs))
    return self

  def common_attr_copy_to_item(self, **kwargs):
    """
    CommonAttrCopyToItemEnricher
    ---

    参数配置
    ---
    `input_common_attr`: [string] 需要写入 item 的 common attr

    `output_id_attr`: [string] 返回的 item attr

    调用示例
    ---
    ``` python
    .common_attr_copy_to_item(
                    input_common_attr="common_attr",
                    output_id_attr="output_id_list"
                   )
    ```
    """
    self._add_processor(CommonAttrCopyToItemEnricher(kwargs))
    return self

  def ad_photo2live_filter(self, **kwargs):
    """
    AdPhoto2LiveFilterEnricher
    ---
    商业化内循环Photo2Live colossus 检索
    ---
    `limit_num_attr`: [string] id 列表长度的 common attr

    `colossus_resp_attr`: [string] colossus 返回对应的 common attr

    `output_id_attr`: [string] 返回的商品 id 列表对应的 item attr

    调用示例
    ---
    ``` python
    .ad_photo2live_filter(
                      colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      n_minute_ago=2880,
                      slots_id=[660, 661, 662, 663, 664, 665, 666, 667, 668],
                      mio_slots_id=[660, 661, 662, 663, 664, 665, 666, 667, 668]
                     )
    ```
    """
    self._add_processor(AdPhoto2LiveFilterEnricher(kwargs))
    return self

  def ad_goods_user_index_slot(self, **kwargs):
    """
    AdGoodsUserIndexSlotEnricher
    ---
    商业化内循环获取对应索引的sign值
    参数配置
    ---
    `goods_id_list_attr`: [string] 商品 id 列表 对应的 item attr
    `user_id_list_attr`: [string] user id 列表 对应的 item attr
    `label_list_attr`: [string] label 列表对应的 item attr
    `sorted_index_list_attr`: [string] index 对应的 item attr
    `output_id_list_attr`: [string] 返回 user id 列表对应的 item attr

    调用示例
    ---
    ``` python
    .ad_goods_user_index_slot(
                      sample_num=10,
                      label_size=3,
                      topn = 200,
                      goods_id_list_attr="co_goods_id_list",
                      user_id_list_attr="g2u_list",
                      label_list_attr="g2u_edge_int",
                      sorted_index_list_attr="sorted_item_idx_attr",
                      output_sign_attr="gsim_output_sign",
                      output_slot_attr="gsim_output_slot",
                      output_id_list_attr="one_order_user_id_list",
                      slots_id=[1, 772, 775, 773, 774],
                      mio_slots_id=[1, 372, 375, 373, 374]
                    )
    ```
    """
    self._add_processor(AdGoodsUserIndexSlotEnricher(kwargs))
    return self

  def ad_bs_retriever(self, **kwargs):
    """
    AdBsRetriever
    -----
    用于从 bslog 获取原始数据

    参数配置
    -----
    `limit_num_attr`: [string] 输出的 limit 数量的 common attr, 默认 "limit_num"
    `limit_num_value`: [int] limit 数量，默认 50
    `attr_keys`: [list] of [dict], 从 bslog 获取的 attr 数据
      - `attr_name`: [string] 设置的 attr name
      - `enum_num`: [int] bs 字段的枚举值
      - `common`: [bool] false 表示 item attr, true 表示 common attr
      - `list`: [bool] false 表示单个值; true 表示值是数组
      - `data_type`: [int] 0 表示 int64, 1 表示 float, 2 表示 double

    调用示例
    -----
    ``` python
    .ad_bs_retriever(
              attr_keys=[
                  {
                    "attr_name": "photo_id",
                    "common": False,
                    "data_type": 0,
                    "enum_num": 22372,
                    "list": False
                  }
              ]
            )
    ```
    """
    self._add_processor(AdBsRetriever(kwargs))
    return self

  def enrich_attr_by_bslog(self, **kwargs):
    """
    AdBsAttrEnricher
    ------
    从 bslog 原始数据中填充 item attr

    参数配置
    ------
    `limit_num_attr`: [str] 输出 limit 数量的 common attr name, 默认 limit_num

    `limit_num_value:`: [int] limit 数量, 默认 50

    `input_bslog_attr`: [str] 用于读取原始 bslog 的 common attr name, 默认 BslogBsItem

    `just_set_limit`: [bool] 是否只设置 limit 数量, 不做 attr_key 读取, 默认 false

    `attr_keys`: [list of dict] 配置从原始 bslog 中获取哪些数据用于丰富 item attr
      - `attr_name`: [string] 填充的具体 item 的 attr name
      - `enum_num`: [int] 原始 bslog 中字段的枚举值
      - `common`: [bool] 是否为 item 侧 attr, true 表示 user 侧 attr, false 表示 item 侧 attr, 默认为 false
      - `list`: [bool] 是否为 list 类型数据, true 表示 list 类型数据, false 表示单值类型数据, 默认 false
      - `data_type`: [int] 数据类型, 目前支持 0: int64, 1: float, 2: double, 3: string, 默认为 0
      - `by_reverse`: [bool] 是否反转 list 数据, 仅对 list 类型数据有效, 默认为 false
      - `max_count`: [int] 对 list 类型数据限制 list 的大小, 仅对 list 类型数据有效, 默认为 -1 表示不限制

    调用示例
    ------
    ```
    .enrich_attr_by_bslog(
      attr_keys=[
          {"attr_name": "photo_id", "common": False, "data_type": 0, "enum_num": 22372, "list": False},
          {"attr_name": "user_id", "common": True, "data_type": 0, "enum_num": 26615, "list": False}
      ]
    )
    ```
    """
    self._add_processor(AdBsAttrEnricher(kwargs))
    return self

  def ad_dragon_feature(self, **kwargs):
    """
    AdDragonFeatureEnricher
    -----
    用于获取 dragon 特征值，并封装到请求 infer 的 request body 中

    参数配置
    -----
    `feature_path`: [str] 模型 feature 文件下载到容器内的本地路径, 通过 util.GetModelPathInfo 生成, 必须
    调用示例
    -----
    ``` python
    model_info = {
      "hdfs_root": "/home/<USER>/big_model_rollback/ctr_model_v1",
      "model_feature": "real_model_feature",
      "model_meta": "real_model.meta",
    }
    .ad_dragon_feature(
      feature_path=util.GetModelPathInfo(model_info, "model_feature"),
    )
    ```
    """
    self._add_processor(AdDragonFeatureEnricher(kwargs))
    return self

  def ad_merge_feature(self, **kwargs):
    """
    AdMergeFeatureEnricher
    -----
    替代AdDragonFeature, 删除了 template 的逻辑, 配置收拢于 processor 配置中
    主要用于对 Colossus、GSU、sim、kuiba 特征等结果 Merge 到 InferFeatureList
    
    参数配置
    -----
    `feature_path`: [str] 模型 feature 文件下载到容器内的本地路径, 通过 util.GetModelPathInfo 生成, 必须

    `item_sign_attrs`: [string] item 特征的 sign attr name 列表

    `item_slot_attrs`: [string] item 特征的 slot attr name 列表, user_sign_attrs 对应

    `user_sign_attrs`: [string] user 特征的 sign attr name 列表

    `user_slot_attrs`: [string] user 特征的 slot attr name 列表, 与 user_sign_attrs 对应

    `user_dense_attrs`: [string] user dense 特征 attr name 列表

    `item_dense_attrs`: [string] item dense 特征 attr name 列表

    `bs_log_attr`: [string] 指定获取 bslog 的 attr name, 默认 BslogBsItem

    `infer_feature_list_attr`: [string] 指定获取 inferfeaturelist 的 attr name, 默认 FeatureResult

    `check_duplicated_item`: [bool] 是否检查重复的 item, 默认为 true

    `duplicated_item_attr`: [string] 指定获取去重 item 映射关系的 attr name, 开启 `check_duplicated_item` 时生效, 默认 DragonDuplicatedItem

    `serialize_feature`: [bool] 是否将 merge 之后的 feature 序列化, 默认为 true

    `serialized_features_attr`: [string] 指定输出 merge 之后的 features 存放的 attr name, 若 `serialize_feature` 为 false 则不会将 merge 之后的 feature 序列化, 默认值 serialized_features

    `need_feature_extractor`: [bool] 是否需要传统抽取类, 若模型全为 dragon 特征, 置为 false, 并不再读取 infer_feature_list_attr, 默认 true,

    `compress_feature`: [bool] 是否对 merge 之后的序列化 feature 做压缩, 若开启则会使用 zstd 对序列化之后的 feature 做压缩, 默认为 false, 网卡瓶颈的服务可开启该选项减小 feature 请求 infer 的包大小

    `compress_level`: [int] 使用 zstd 对 feature 进行压缩时使用的压缩等级, 默认 -20, compress_level 最大为 22, 并且可以为负值, 较大的值可以提供更好的压缩率但会更耗时&更占内存

    `use_protocol_accessor`: [bool] 是否启用多协议 InferFeatureList, 默认 false, 开启后会将特征序列化成对应的协议，协议类型由 FLAGS_infer_feature_protocol 决定

    调用示例
    -----
    ``` python
    model_info = {
      "hdfs_root": "/home/<USER>/big_model_rollback/ctr_model_v1",
      "model_feature": "real_model_feature",
      "model_meta": "real_model.meta",
    }
    .ad_merge_feature(
      feature_path=util.GetModelPathInfo(model_info, "model_feature"),
      item_sign_attrs=["gsu_item_signs", "cross_signs"],
      item_slot_attrs=["gsu_item_slots", "cross_slots"],
      user_sign_attrs=["ad_colossus_signs"],
      user_slot_attrs=["ad_colossus_slots"]
    )
    ```
    """
    self._add_processor(AdMergeFeatureEnricher(kwargs))
    return self

  def ad_extractor(self, **kwargs):
    """
    AdExtractorFeatureEnricher
    -----
    用于提取 Extractor 特征

    参数配置
    -----
    `feature_path`: [str] 模型 feature 文件下载到容器内的本地路径, 通过 util.GetModelPathInfo 生成, 必须

    `serialize_feature`: [bool] 是否将抽取的特征序列化. 如果开启该选项则会在 dragon 的 context 中存储序列化后的 feature; 否则在 dragon 的 context 中存储 pb 格式的特征. 默认为 false

    `input_bslog_attr`: [string] 用于特征提取的 bslog 的 attr 名字, 默认 BslogBsItem

    `output_features_attr`: [string] 存储 feature 的 attr, 默认为 FeatureResult. 开启 `serialize_feature` 时存储序列化之后的 pb 字符串; 否则存储 pb 特征.

    `need_feature_processor`: [bool] 是否需要加载 feature_processor, 若已使用 AdFeatureProcessEnricher, 则需要置为false, 默认 true

    `use_protocol_accessor`: [bool] 是否启用多协议 InferFeatureList, 默认 false, 开启后会将特征序列化成对应的协议，协议类型由 FLAGS_infer_feature_protocol 决定

    调用示例
    -----
    ``` python
    model_info = {
      "hdfs_root": "/home/<USER>/big_model_rollback/ctr_model_v1",
      "model_feature": "real_model_feature",
      "model_meta": "real_model.meta",
    }
    .ad_extractor(
      feature_path=util.GetModelPathInfo(model_info, "model_feature"),
    )
    ```
    """
    self._add_processor(AdExtractorFeatureEnricher(kwargs))
    return self

  def ad_bslog(self, **kwargs):
    """
    AdBslogEnricher
    -----
    用于组装数据，封装 bslog, 输出 user 侧的 BslogBsItem  attr 和标记 item 是否确实的 item 侧的 item_miss attr

    参数配置
    -----
    `btq_incr_topic`: [string] 增量 btq topic 名称

    `lru_capacity_gb`: [int] 物料缓存容量大小, 默认 60 GB

    `item_server_cmd`: [string] 物料服务 cmd

    `item_server_shard_num`: [int] 物料服务 shard num, 默认 1

    `item_service_timeout`: [int] 访问物料服务超时时间, 单位 ms, 默认 20 ms

    `req_item_service_batch_num`: [int] 访问物料服务 batch 大小, 默认 10

    `item_server_event_loop_num`: [int] 访问物料服务 event loop 数量, 默认 8

    `item_already_ready`: [bool] 标记是否上游已经准备好 item

    `bs_item_attr`: [str] 需要读取的 bs_item 存放的 attr, 仅在 `item_already_ready` 为 True 时有效, 默认 bs_item

    `bs_log_attr`: [str] 输出的 bs_log 存放的 attr, 默认 BslogBsItem

    `item_miss_attr`: [str] 确实的 item 信息存放的 attr, 仅在 `item_already_ready` 为 True 时有效, 默认 item_miss

    `generate_bslog`: [bool] 是否产出bslog, 若需要从其他 processor 读取 bslog 则需置为false, 默认为 true

    调用示例
    -----
    ``` python
    .ad_bslog(
          btq_incr_topic: "ad_predict_item_info_v2_id_incr",
          lru_capacity_gb: 60,
 				  item_server_cmd: "/ad/ps/item:ad_predict_batched_samples_v2_5_shard",
  				item_service_timeout: 20
        )
    ```
    """
    self._add_processor(AdBslogEnricher(kwargs))
    return self

  def ad_get_feature_processor(self, **kwargs):
    """
    AdFeatureProcessEnricher
    -----
    用于将模型主流程和 rpc feature_processor 并行, 此 processor 将会产出 bslog, 若已有 bslog 将会报错

    参数配置
    -----
    `output_bslog`: [string] 用于指定 bslog 输出的名字

    调用示例
    -----
    ``` python
    .ad_get_feature_processor(
        feature_process_config=[
            {
                "AdLiveItemProcessor": {}
            }
        ],
    )
    ```
    """
    self._add_processor(AdFeatureProcessEnricher(kwargs))
    return self

  def ad_get_item_info(self, **kwargs):
    """
    AdItemEnricher
    -----
    用于获取 item 数据, 支持分域和非分域版本, 目前只支持在线模型使用
    参数配置
    -----
    `component_item`: [Json] 配置，见 [配置说明](https://docs.corp.kuaishou.com/k/home/<USER>/fcAAn0nNH0AGc2TzzNJmSfhOV)

    `use_component_item`: [bool] 是否打开分域, 默认关闭
    
    `btq_incr_topic`: [string] 增量 btq topic 名称, 分域模式时写在 component_item 中

    `capacity_gb`: [int] 物料缓存容量大小, 默认 60 GB, 分域模式时写在 component_item 中

    `item_server_cmd`: [string] 物料服务 cmd, 分域模式时写在 component_item 中

    `item_server_shard_num`: [int] 物料服务 shard num, 默认 1, 分域模式时写在 component_item 中

    `item_service_timeout`: [int] 访问物料服务超时时间, 单位 ms, 默认 20 ms, 分域模式时写在 component_item 中

    `req_item_service_batch_num`: [int] 访问物料服务 batch 大小, 默认 10, 分域模式时写在 component_item 中

    `item_server_event_loop_num`: [int] 访问物料服务 event loop 数量, 默认 8, 分域模式时写在 component_item 中

    `bs_item_attr`: [string] 输出的 bs_item 存放的 attr, 默认为 bs_item

    `skip_req_item_server`: [bool] 是否跳过 item server 访问，默认关闭

    `item_context_attr`: [string] 实时 item 信息 item_context item attr 的 key, 默认 item_context

    `miss_when_item_context_empty`: [bool] item_context 为空时是否将对应的 item 标记为 miss, 默认 false

    调用示例
    -----
    ``` python
    # 非分域模式, 根据各自场景填写
    .ad_get_item_info(
        "item_server_cmd": "/ad/ps/item:ad_predict_batched_samples_v2_4_shards"
        "btq_incr_topic": "ad_predict_item_info_v2_id_incr"
    )

    # 分域模式
    .ad_get_item_info(
        use_component_item=True,
        component_item={
        "component_item_btq_config": {
          "creative": {
            "incr_source_name": "ad_predict_item_info_v2_id_incr",
            "is_main_key": True
          },
          "live": {
            "incr_source_name": "ad_predict_live_incr_v2_batched_samples"
          },
          "photo": {
            "incr_source_name": "ad_predict_photo_info_v2_id_incr"
          }
        },
        "component_item_server_config": {
          "creative": {
            "item_server_cmd": "/ad/ps/item:ad_predict_batched_samples_v2_creative_opt"
          },
          "live": {
            "item_server_cmd": "/ad/ps/item:ad_predict_batched_samples_v2_live_opt"
          },
          "photo": {
            "item_server_cmd": "/ad/ps/item:ad_predict_batched_samples_v2_photo_opt"
          }
        }
      }
    )
    ```
    """
    self._add_processor(AdItemEnricher(kwargs))
    return self

  def ad_live_u2u_interest(self, **kwargs):
    """
    AdLiveU2uColossusInterestEnricher
    ------
    根据相似的uid list检索历史行为

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, 注意colossus_resp的proto是AdLiveItem

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `filter_uid_score`: [float] uid维度上用来过滤相似的uidlist的score阈值，默认是0.0

    `click_as_positive`: [bool] item纬度上用来过滤item的条件，默认是true

    `uid_list`: [string] kgnn中返回的最相似的uid list的attr name

    `score_list`: [string] kgnn中返回的最相似的uid 的score list的attr name

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .ad_live_u2u_interest(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',

                      slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642],
                      mio_slots_id=[630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642]
                      )
    ```
    """
    self._add_processor(AdLiveU2uColossusInterestEnricher(kwargs))
    return self

  def live_gsu_with_cluster_ad_v4(self, **kwargs):
    """
    CommonLiveV4GsuWithClusterEnricher
    ------
    上游 colossus 必须配置 parse_pb_=False, 根据 photo 所属的 cluster_id 进行 gsu 搜索并填充相似直播的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `reward_item_only`: [bool] 是否只保留打赏的item，默认false

    示例
    ------
    ``` python
    .live_gsu_with_cluster_ad_v4(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      )
    ```
    """
    self._add_processor(CommonLiveV4GsuWithClusterEnricher(kwargs))
    return self

  def common_live_colossus_liveid_user_feature_resp_enricher(self, **kwargs):
    """
    CommonLiveColossusLiveidUserFeatureRespEnricher
    ------
    获取 user 的统计特征(基于colossusV4) 只选取 CommonLiveColossusLiveidFeatureRespEnricher user 特征

    参数
    ------
    `colossus_resp_attr`: [string] colossus 视频列表

    `gap_time` : [int] 只统计时间在 request time - gap_time 之前的 colossus items

    示例
    ------
    ``` python
    .common_live_colossus_liveid_feature_resp_enricher(
      colossus_resp_attr="colossus_resp",
      gap_time=10
    )
    ```
    """
    self._add_processor(CommonLiveColossusLiveidUserFeatureRespEnricher(kwargs))
    return self

  def common_ad_live_colossus_user_rfm_enricher(self, **kwargs):
    """
    CommonAdLiveColossusUserRfmEnricher
    ------
    获取 user 的统计特征(基于AdLiveColossus) 只选取 user 特征

    参数
    ------
    `colossus_resp_attr`: [string] colossus 视频列表

    `gap_time` : [int] 只统计时间在 request time - gap_time 之前的 colossus items

    示例
    ------
    ``` python
    .common_live_colossus_liveid_feature_resp_enricher(
      colossus_resp_attr="colossus_resp",
      gap_time=10
    )
    ```
    """
    self._add_processor(CommonAdLiveColossusUserRfmEnricher(kwargs))
    return self

  def live_gsu_with_cluster_ad_part_v4(self, **kwargs):
    """
    CommonLiveV4GsuWithClusterPartEnricher
    ------
    上游 colossus 必须配置 parse_pb_=False, 根据 photo 所属的 cluster_id 进行 gsu 搜索并填充相似直播的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `reward_item_only`: [bool] 是否只保留打赏的item，默认false

    示例
    ------
    ``` python
    .live_gsu_with_cluster_ad_part_v4(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      )
    ```
    """
    self._add_processor(CommonLiveV4GsuWithClusterPartEnricher(kwargs))
    return self

  def common_reco_colossus_session_part_feature(self, **kwargs):
    """
    ColossusSessionFeaturePartEnricher
    ------
    获取 user sesion 特征(基于 colossusV4 )

    参数
    ------
    `colossus_resp_attr`: [string] colossus resp

    `session_min` : [int] session 长度定义

    示例
    ------
    ``` python
    .common_reco_colossus_session_part_feature(
      colossus_resp_attr="colossus_resp",
      session_min=360
    )
    ```
    """
    self._add_processor(ColossusSessionFeaturePartEnricher(kwargs))
    return self

  def common_p2l_colossus_gsu_with_pid_cluster(self, **kwargs):
    """
    CommonP2lColossusGsuWithPidClusterEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `target_cluster_attr`: [int] target item 的 ad pid cluster 代码里通过 int(cluster/1000000) 取值

    `cluster_config_key_name`: [string] cluster 近邻关系的 kconf 默认是 ad.model.photoAdClusterNeighbors

    示例
    ------
    ``` python
    .common_p2l_colossus_gsu_with_pid_cluster(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           target_cluster_attr = 'target_aid_cluster',
                          )
    ```
    """
    self._add_processor(CommonP2lColossusGsuWithPidClusterEnricher(kwargs))
    return self
  
  def common_p2l_colossus_gsu_with_pid_cluster_v2(self, **kwargs):
    """
    CommonP2lColossusGsuWithPidClusterV2Enricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `target_cluster_attr`: [int] target item 的 ad pid cluster 代码里通过 int(cluster/1000000) 取值

    `cluster_config_key_name`: [string] cluster 近邻关系的 kconf 默认是 ad.model.photoAdClusterNeighbors

    示例
    ------
    ``` python
    .common_p2l_colossus_gsu_with_pid_cluster_v2(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           target_cluster_attr = 'target_aid_cluster',
                          )
    ```
    """
    self._add_processor(CommonP2lColossusGsuWithPidClusterV2Enricher(kwargs))
    return self

  def common_photo_colossus_v2_retriever(self, **kwargs):
    """
    CommonPhotoColossusV2RespRetriever
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_photo_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_duration_to_attr`: [string]

    `save_play_time_to_attr`: [string]

    `save_tag_to_attr`: [string]

    `save_channel_to_attr`: [string]

    `save_label_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `save_photo_lat_attr`: [string]

    `save_photo_lon_attr`: [string]

    `save_user_lat_attr`: [string]

    `save_user_lon_attr`: [string]

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    `parse_from_pb`: [boolean] 是否从protobuf message中取colossus的返回结果，默认为true

    示例
    ------
    ``` python
    .common_photo_colossus_v2_retriever(
                            colossus_resp_attr="colossus_output",
                            parse_from_pb = False,
                            filter_future_attr = True,
                            limit_num = 200,
                            play_time_threshold = 18,
                            save_photo_id_to_attr="reco_photo_id",
                            save_author_id_to_attr="reco_author_id",
                            save_duration_to_attr="duration",
                            save_play_time_to_attr="reco_play_time",
                            save_tag_to_attr="reco_tag",
                            save_channel_to_attr="channel",
                            save_label_to_attr="label",
                            save_timestamp_to_attr="new_timestamp",
                            save_photo_lat_attr="photo_lat_list",
                            save_photo_lon_attr="photo_lon_list",
                            save_user_lat_attr="user_lat",
                            save_user_lon_attr="user_longitude"
                            )
    ```
    """
    self._add_processor(CommonPhotoColossusV2RespRetriever(kwargs))
    return self

  def gsu_retriever_with_colossus_resp_v3(self, **kwargs):
    """
    CommonAdColossusRespRetriever
    ------
    从 colossus 中提取长期兴趣
    ------
    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `output_slot_attr`: [string] slot 输出 attr name. common 类型

    `output_sign_attr`: [string] sign 输出 attr name. common 类型

    `limit_num`: [int] [动态参数]

    调用示例
    ------
    示例
    ------
    ``` python
    .gsu_retriever_with_colossus_resp_v3(colossus_resp_attr="colossus_output",
                                         output_sign_attr='sign',
                                         output_slot_attr='slot',
                                         limit_num=5000,
                                        slots_id = [26, 128, 1003, 1004, 1005],
                                        mio_slots_id = [1001, 1002, 1003, 1004, 1005])
    ```
    """
    self._add_processor(CommonAdColossusRespRetriever(kwargs))
    return self
  
  def common_ad_colossus_resp_retriever_v2(self, **kwargs):
      """
      CommonAdColossusRespV2Retriever
      ------
      从 colossus 中提取长期兴趣
      ------
      `colossus_resp_attr`: [string] colossus_resp 输入的 attr

      `output_pid_attr`: [string] pid 输出 attr name. common 类型

      `output_pid_attr`: [string] aid 输出 attr name. common 类型
    
      `output_play_attr`: [string] play 输出 attr name. common 类型

      `output_tag_attr`: [string] tag 输出 attr name. common 类型

      `output_time_attr`: [string] time 输出 attr name. common 类型

      `output_label_attr`: [string] label 输出 attr name. common 类型

      `limit_num`: [int] [动态参数]

      调用示例
      ------
      示例
      ------
      ``` python
      .common_ad_colossus_resp_retriever_v2(colossus_resp_attr="colossus_output",
                                          output_pid_attr='sim_pid_list',
                                          output_aid_attr='sim_aid_list',
                                          output_play_attr='sim_play_list',
                                          output_tag_attr='sim_tag_list',
                                          output_time_attr='sim_time_list',
                                          output_label_attr='sim_label_list',
                                          limit_num=5000)
      ```
      """
      self._add_processor(CommonAdColossusRespV2Retriever(kwargs))
      return self
    
  def common_ad_colossus_resp_retriever_v3(self, **kwargs):
      """
      CommonAdColossusRespV2Retriever
      ------
      从 colossus 中提取长期兴趣
      ------
      `colossus_resp_attr`: [string] colossus_resp 输入的 attr

      `output_pid_attr`: [string] pid 输出 attr name. common 类型

      `output_pid_attr`: [string] aid 输出 attr name. common 类型
    
      `output_play_attr`: [string] play 输出 attr name. common 类型

      `output_tag_attr`: [string] tag 输出 attr name. common 类型

      `output_time_attr`: [string] time 输出 attr name. common 类型

      `output_label_attr`: [string] label 输出 attr name. common 类型

      `limit_num`: [int] [动态参数]

      调用示例
      ------
      示例
      ------
      ``` python
      .common_ad_colossus_resp_retriever_v2(colossus_resp_attr="colossus_output",
                                          output_pid_attr='sim_pid_list',
                                          output_aid_attr='sim_aid_list',
                                          output_play_attr='sim_play_list',
                                          output_tag_attr='sim_tag_list',
                                          output_time_attr='sim_time_list',
                                          output_label_attr='sim_label_list',
                                          limit_num=5000)
      ```
      """
      self._add_processor(CommonAdColossusRespV3Retriever(kwargs))
      return self
    
  def common_ad_colossus_resp_retriever_v4(self, **kwargs):
      """
      CommonAdColossusRespV4Retriever
      ------
      从 colossus 中提取长期兴趣
      ------
      `colossus_resp_attr`: [string] colossus_resp 输入的 attr

      `output_pid_attr`: [string] pid 输出 attr name. common 类型

      `output_pid_attr`: [string] aid 输出 attr name. common 类型
    
      `output_play_attr`: [string] play 输出 attr name. common 类型

      `output_tag_attr`: [string] tag 输出 attr name. common 类型

      `output_time_attr`: [string] time 输出 attr name. common 类型

      `output_label_attr`: [string] label 输出 attr name. common 类型
      
      `output_channel_attr`: [string] label 输出 attr name. common 类型

      `limit_num`: [int] [动态参数]

      调用示例
      ------
      示例
      ------
      ``` python
      .common_ad_colossus_resp_retriever_v2(colossus_resp_attr="colossus_output",
                                          output_pid_attr='sim_pid_list',
                                          output_aid_attr='sim_aid_list',
                                          output_play_attr='sim_play_list',
                                          output_tag_attr='sim_tag_list',
                                          output_time_attr='sim_time_list',
                                          output_label_attr='sim_label_list',
                                          output_channel_attr='sim_channel_list',
                                          limit_num=5000)
      ```
      """
      self._add_processor(CommonAdColossusRespV4Retriever(kwargs))
      return self
    

  def common_ad_colossus_resp_retriever_v5(self, **kwargs):
      """
      CommonAdColossusRespV5Retriever
      ------
      从 colossus 中提取长期兴趣
      ------
      `colossus_resp_attr`: [string] colossus_resp 输入的 attr

      `output_pid_attr`: [string] pid 输出 attr name. common 类型

      `output_pid_attr`: [string] aid 输出 attr name. common 类型
    
      `output_play_attr`: [string] play 输出 attr name. common 类型

      `output_tag_attr`: [string] tag 输出 attr name. common 类型

      `output_time_attr`: [string] time 输出 attr name. common 类型

      `output_label_attr`: [string] label 输出 attr name. common 类型
      
      `output_channel_attr`: [string] label 输出 attr name. common 类型

      `limit_num`: [int] [动态参数]

      调用示例
      ------
      示例
      ------
      ``` python
      .common_ad_colossus_resp_retriever_v2(colossus_resp_attr="colossus_output",
                                          output_pid_attr='sim_pid_list',
                                          output_aid_attr='sim_aid_list',
                                          output_play_attr='sim_play_list',
                                          output_tag_attr='sim_tag_list',
                                          output_time_attr='sim_time_list',
                                          output_label_attr='sim_label_list',
                                          output_channel_attr='sim_channel_list',
                                          limit_num=5000)
      ```
      """
      self._add_processor(CommonAdColossusRespV5Retriever(kwargs))
      return self
    
  
  def common_gsu_with_rq_vae_enricher_v2(self, **kwargs):
    """
    CommonGsuWithRqVaeV2Enricher
    ------
    根据 target semantic id 对用户交互过的 photo id 做匹配，同时计算 match count

    参数
    ------
    `output_filter_first`: [string] 第一层 semantic id 匹配的 photo id

    `output_filter_second`: [string] 前两层 semantic id 匹配的 photo id

    `output_filter_third`: [string] 前三层 semantic id 匹配的 photo id

    `output_match_cnt`: [string] 三层 semantic id 匹配的数量
    
    `output_match_cnt_first`: [string] 一层 semantic id 匹配的数量 (match_cnt_combine为True时设置)

    `output_match_cnt_second`: [string] 二层 semantic id 匹配的数量 (match_cnt_combine为True时设置)

    `output_match_cnt_third`: [string] 三层 semantic id 匹配的数量 (match_cnt_combine为True时设置)

    `limit_num_attr`: [int] 返回数目 attr

    `target_semantic_id`: [int] target photo id 的 RQVAE 编码

    `photo_ids` : [int] 用户交互过的 photo id

    `semantic_ids` : [int] 用户交互过的视频 RQVAE 编码
    
    `match_cnt_combine` : [bool] 是否将match_cnr的返回结果合并，默认False

    示例
    ------
    ``` python
    .common_gsu_with_rq_vae_enricher_v2(photo_ids='photo_ids',
                             semantic_ids='semantic_ids',
                             match_cnt_combine=True,
                             target_semantic_id='target_semantic_id',
                             limit_num_attr='limit_num_attr',
                             output_filter_first='output_filter_first',
                             output_filter_second='output_filter_second',
                             output_filter_third='output_filter_third',
                             output_match_cnt_first = 'output_match_cnt_first',
                             output_match_cnt_second = 'output_match_cnt_second',
                             output_match_cnt_third = 'output_match_cnt_third',

                            )
    ```
    """
    self._add_processor(CommonGsuWithRqVaeV2Enricher(kwargs))
    return self

  def ad_request_infer(self, **kwargs):
    """
    AdDelegateEnricher
    ------
    用于请求 infer server 并返回结果给上游

    参数
    ------
    `timeout_ms`: [int] 调用 infer 的超时, 默认 47ms
    `kess_service`: [string] infer 的 kess name, 不填会自动生成, 没有特殊需求不要填
    `recv_common_attrs`: [string list] 必填, 只能填 ["debug_info"]
    `recv_item_attrs`: [string list] 必填, 只能填 ["predict_value", "result_stat"]
    `send_common_attrs`: [string list] 必填, 只能填 ["cmdkey_list","cmdkey_end_pos","serialized_features"]
    `send_item_attrs`: [string list] 必填, 只能填 ["photo_id", "item_miss"]

    示例
    ------
    ``` python
    .ad_request_infer(
      kess_service="",
      timeout_ms=47,
      recv_common_attrs=["debug_info"],
      recv_item_attrs=["predict_value", "result_stat"],
      send_common_attrs=["cmdkey_list","cmdkey_end_pos","serialized_features"],
      send_item_attrs=["photo_id", "item_miss"]
    )
    ```
    """
    self._add_processor(AdDelegateEnricher(kwargs))
    return self

  def ad_infer_feature(self, **kwargs):
    """
    AdInferFeatureEnricher
    ------
    用于将上游请求发送的序列化之后的 feature 反序列化

    参数
    ------
    `input_serialized_features_attr`: [str] 请求中序列化的 feature, 默认为 serialized_features.

    `output_features_attr`: [str] 反序列化之后的 feature 存储的 attr, 默认为 ad_infer_feature_list

    `feature_conpressed`: [bool] 需要反序列化的 feature 是否被压缩, 默认为 false

    `use_protocol_accessor`: [bool] 是否启用多协议 InferFeatureList, 默认 false, 开启后会使用对应的协议反序列化特征请求，协议类型由 FLAGS_infer_feature_protocol 决定

    示例
    ------
    ``` python
    .ad_infer_feature()
    ```
    """
    self._add_processor(AdInferFeatureEnricher(kwargs))
    return self

  def ad_infer_model(self, **kwargs):
    """
    AdUniPredictEnricher
    ------
    用于模型推理, 部署是推理相关配置自动生成, 目前不需要进行配置

    参数
    ------
    配置过于复杂, KAI-serving 后端根据图相关配置自动生成

    示例
    ------
    ``` python
    .ad_infer_model()
    ```
    """
    self._add_processor(AdUniPredictEnricher(kwargs))
    return self

  def ad_uni_predict_fused(self, **kwargs):
    """
    AdUniPredictFusedEnricher
    ------
    使用 uni-predict 作为底层推理引擎的预估 processor

    参数配置
    ------
    `hdfs_root`: [str] 模型 HDFS 路径, 模型 meta、特征文件、checkpoint 等都存放在该路径下。 \
      比如: /home/<USER>/big_model_rollback/ctr_model_v1

    `model_meta_path`: [str] 模型 meta 文件下载到容器内的本地路径, 通过 util.GetModelPathInfo 生成, 必须

    `feature_path`: [str] 模型 feature 文件下载到容器内的本地路径, 通过 util.GetModelPathInfo 生成, 必须

    `ps_root`: [str] 模型相关文件的本地容器内存储目录, 通过 util.GetModelDirPath 生成

    `queue_prefix`: [str] 模型的 btq 名字, 必须

    `key`: [str] 用于唯一标识模型的 key, 默认与 queue_prefix 一致, 当 pipeline 中有多个模型使用同一个 btq 的时候用于区别每个模型

    `use_bs_fast_feature`: [bool] 是否使用 bs fast feature 抽取特征, 默认值为 false

    `use_fused_infer_and_feature`: [bool] 是否在同一个 pipeline 中进行特征抽取和预估, 默认为 false

    `use_protocol_accessor`: [bool] 是否启用多协议 InferFeatureList, 默认 false, 开启后会调用对应接口读取 InferFeatureList, 需要与 ad_infer_feature 的配置统一

    `dragon_input_feature_list`: [str] 模型输入特征存放的 attr name, 默认为 ad_infer_feature_list

    `outputs`: [list of dict] 模型的输出, 必须
      - `tensor_name`: [str] 模型输出的 tensor 名字
      - `attr_name`: [str] 模型输出存放在在 dragon context 中的 attr 名字

    `emp_service`: [dict] 模型 embedding server 相关配置, 通常是自动生成的
      - `local`: [list of dict] 本地 embedding 存储相关配置
        - `capacity`:
        - `embedding_len`:
        - `field`:
        - `mio_bucket_size`:
        - `prefix`:
        - `size`:
      - `0`: [list of dict] 其他远端 embedding server 相关配置, 可以为空, 表示所有 embedding 都存储在本地
      - ...

    `embedding_storage_config`: [dict] embedding 存储相关配置, 可以为空, 为空时取默认值
      - `embedding_node_prefix`: [str] embedding 存储前缀, 默认值为 'embedding'
      - `remove_sign_prefix`: [bool] 默认值为 false
      - `kai_nohash_model`: [bool] 默认值为 false
      - `remap_slot_sign_feature`: [dict] slot, sign 特征抽取 remap 相关配置
        - `output_slot_bit_num`: [int] 默认值为12, 取值范围为 (0, 64), 0 和 64 都不包含
        - `ad_sign_prefix_bit_num`: [int]
        - `reco_gsu_sign_prefix_bit_num`: [int] reco  GSU 特征抽取 sign 中 prefix 所占的 bit 数
        - `reco_sign_prefix_bit_num`: [int] reco 特征抽取 sign 中 prefix 所占的 bit 数
      - `embedidng_capacity_gb`: [float] embedding cache 存储容量大小, 单位 GB, 默认值为 128
      - `embedding_cache_type`: [str] embedding cache 类型, 支持 `shm`, `file` 和空字符串; 'shm': 表示使用共享内存类型的 cache; 'file': 表示使用 Mmap 映射文件类型的 cache, 空字符串表示使用内存类型的 cache. 默认为'shm'
      - `embedding_min_fill_ratio`: [float] emebdding cache 最小填充率, 默认值为 0.65

    `embedding_fetcher_config`: [dict] 远程 embedding 拉取相关配置, 为空时取默认值
      - `in_fea_org_layout`: [int], 默认值为 1
      - `reordering_feature`: [bool] 默认为 false
      - `expont_per_bucket`: [int] 默认值为 12
      - `weights_in_tensor`: [bool] 默认值为 false
      - `combine_type`: [int] 默认值为 0

    `btq_reader_config`: [dict] btq 读取相关的配置, 为空时取默认值
      - `btq_model_reader_thread_num`: [int] btq 读取模型的线程数, 默认值为 3
      - `btq_model_read_qps`: [int] btq 读取模型的 QPS 设置, 默认值为 100
      - `btq_model_worker_num`: [int] btq 工作线程数, 默认值为 8

    `idt_config`: [dict] idt 相关配置
      - `use_common_idt_service`: [bool] 是否使用 common idt 服务, 默认为 false
      - `common_idt_config`: [dict] common idt 配置, 若 `use_common_idt_service` 为 true, 但没有该配置则会从 key 为 `ad.predict.common_idt_config` 的 kconf 获取默认配置

    `instant_rollback`: [bool] 是否实例化模型 rollback 功能, 默认值为 true

    `use_double_buffer_loader`: [bool] 是否双 Buffer 天级别更新模型（通过 HDFS 参数文件更新模型参数）, 默认为 false

    `model_loader_config`: [dict] 模型加载配置
      - `rowmajor`: [bool] 模型 param 参数是否为 rowmajor, kai 训练的模型一般为 true, 默认值为 true
      - `dense_format`: [str] 支持 btq/kai/btq_v2, btq 格式是指不带有 dense 参数名字的格式, 对应 ad 模型中 mio_model 为 True 的情况; kai 格式为 kai 训练框架打的 dense 参数, 对应 ad 模型中 kai_model 为 True 的情况; btq_v2 是指带有 dense 参数名字的格式, 默认为 btq.
      - `dynamic_shape`: [bool] 是否开启 dynamic shape 功能进行预估, 不开启时使用 explicit 模型预估, 默认为 false
      - `workspace_size`: [float] TensorRT 在加载模型时使用的工作空间大小, 单位为 GB, 通常不需要自己设置, 默认为 0
      - `executor_batchsizes`: [list of int] 加载模型时设置的 batch size 大小, 可设置多个, 表示加载多个 batch size 的模型, 实际预估时会选择大于实际 batch 的最小值, 默认为空. 当为空时会设置 `{batching_config::max_batch_size / 2, batching_config::max_batch_size}` 两个, 不为空是最大值需要和 `batching_config::max_batch_size` 一致.
      - `type`: [str] 模型记载类型, 当前支持 `KaiTFExecutedByTensorRTModelLoader`, 表示 Kai 训练的模型使用 TensorRT 进行加载, 必须

    `optimizers`: [list of str] 对图使用的优化方法

    `batching_config`: [dict] batching 相关配置, 在 batching 的过程中 max_batch_size 和 max_enqueued_batches 任意一个达到最大都会发车, 必须
      - `batch_timeout_micros`: [int] batching 时收集 task 的等待时间, 默认值为 8000 也即 8 毫秒, 若该值为 0 表示不开启 batching
      - `max_batch_size`: [int] batching 时最多的 item 数, 默认值为 512. 若请求中包含的 item 数大于该值将无法 batching 该请求将会被丢弃
      - `max_enqueued_batches`:[int] batching 时最多收集的请求数, 默认为 16, 若该值为 1 表示不开启 batching

    示例
    ------
    ```python
    model_info = {
      "hdfs_root": "/home/<USER>/big_model_rollback/ctr_model_v1",
      "model_feature": "real_model_feature",
      "model_meta": "real_model.meta",
    }
    .ad_uni_predict_fused(
      hdfs_root=model_info["hdfs_root"],
      ps_root=util.GetModelDirPath(model_info["hdfs_root"]),
      model_meta_path=util.GetModelPathInfo(model_info, "model_meta"),
      feature_path=util.GetModelPathInfo(model_info, "model_feature"),
      outputs=[ {"tensor_name": "prob", "attr_name": "prob"} ]
    )
    ```
    """
    self._add_processor(AdUniPredictFusedEnricher(kwargs))
    return self

  def ad_infer_result(self, **kwargs):
    """
    AdInferResultEnricher
    ------
    用于获取预估的结果, 并封装结果返回给上游

    参数
    ------
    `enable_model_validate`: [bool] 使用启动 dense 模型校验, 默认使用

    示例
    ------
    ``` python
    .ad_infer_result(
      enable_model_validate=True
    )
    ```
    """
    self._add_processor(AdInferResultEnricher(kwargs))
    return self

  def ad_infer_result_v2(self, **kwargs):
    """
    AdInferResultV2Enricher
    ------
    用于获取预估的结果, 并封装结果返回给上游, 支持概率结果输出, int 类型输出和 embedding 输出

    参数
    ------

    `neg_sample_rate`: [float] 默认值为 1, 若大于 0, 将会对最终的预估值做如下处理 `value = output_value / (neg_sample_rate - (neg_sample_rate - 1) * output_value)`

    `is_negative_valid`: [bool] 输出层产生负值时是否有效, 仅对 `outputs` 中的输出有效, 默认为 false

    `outputs`: [list of dict] 模型的 probability 类型输出, 需要和预估 processor 中 `outputs` 的配置一致, 默认为空 list, 不能为空
      - `attr_name`: [str] 输出对应的 attr name
      - `tensor_name`: [str] 输出对应的图中的 tensor_name
      - `output_value_width`: [int] 模型 probability 类型结果输出每个分组的宽度, 当输出 tensor 的 dim 为 1 时该配置无效, 默认为 2, eg. output dim 为 8, output_value_width 为 2 的情况下, 将会有 4 个分组
      - `predict_value_num`: [int] 模型 probability 类型结果输出最终预估值的个数, 当输出 tensor 的 dim 为 1 时该配置无效, 默认值为 1, eg. 当 output dim 为 8, output_value_width 为 2 时, 将会有 4 个分组, 每个分组取后一个值, 将会产生 4 个预估值, 这种情况下 predict_value_num 为 4

    `outputs_v2`: [list of dict] 用于存放模型的 int 类型的输出和 embedding 类型的输出. 需要和预估 processor 中的 `outputs` 的配置一致, 默认为空 list, 可以为空
      - `attr_name`: [str] 输出对应的 attr name
      - `tensor_name`: [str] 输出对应的图中的 tensor_name
      - `predict_value_num`: [int] 仅对 embedding 类型的输出有效, 输出最终预估值的个数, 需要和当前 output 的 dim 一致, 默认为 1
      - `enable_validate`: [bool] 仅对 embedding 类型的输出有效, 输出是否用于结果的验证, 默认为 true

    `output_prob_attr`: [str] 模型的 probability 类型输出结果 pack 之后输出的的 item attr 名字, 默认为 predict_value

    `output_prob_attr_v2`: [str] 模型的 int 和 embedding 类型输出存放的 item attr 名字, 默认为 predict_value_v2

    `output_result_stat_attr`: [str] 模型对每个 item 的预估结果的状态输出的 attr 名字, 默认为 result_stat

    `key`: [str] 唯一标识每个模型的 key, 当 pipeline 中有多个模型使用同一个 btq 的时候用于区别每个模型, 需要和预估 processor 中的 `key` 一致

    示例
    ------
    ``` python
    .ad_infer_result_v2(
      is_negative_valid=False,
      outputs=outputs,
      outputs_v2=outputs_v2,
      key="model_key",
      output_prob_attr="predict_value",
      output_prob_attr_v2="predict_value_v2",
      output_result_stat_attr="result_stat",
    )
    ```
    """
    self._add_processor(AdInferResultV2Enricher(kwargs))
    return self

  def common_idt(self, **kwargs):
    """
    CommonIdtObserver
    ------
    用于使用 idt 能力

    参数
    ------
    无

    示例
    ------
    ``` python
    .common_idt()
    ```
    """
    self._add_processor(CommonIdtObserver(kwargs))
    return self

  def kscale(self, **kwargs):
    """
    CommonKScaleObserver
    ------
    用于 kscale 弹性的能力

    参数
    ------
    无

    示例
    ------
    ``` python
    .kscale()
    ```
    """
    self._add_processor(CommonKScaleObserver(kwargs))
    return self

  def ad_colossusv2_goods_click_seq(self, **kwargs):
    """
    AdColossusV2GoodsClickSeqEnricher
    ---
    商业化内循环访问V2版本GoodsClickItem的colossus取序列特征

    参数配置
    -----
    `colossus_goods_id_attr`: [string] 存商品 goods_id 列表的 common attr, 默认 item_id_list

    `colossus_timestamp_attr`: [string] 存时间戳 timestamp 列表的 common attr, 默认 click_timestamp_list

    `colossus_category_attr`: [string] 存商品类目 category 列表的 common attr, 默认 category_a_list

    `colossus_host_attr`: [string] 存主播 host 列表的 common attr, 默认 seller_id_list

    `colossus_shop_attr`: [string] 存商家 shop 列表的 common attr, 默认 real_seller_id_list

    `request_time_attr`: [string] 存生成特征的请求时间的 common attr, 默认为空则取当前时间

    `limit_num`: [int] 返回的商品列表的长度限制，默认 500

    `min_seconds_ago`: [int] colossus 时间过滤的近值，默认 60

    `max_seconds_ago`: [int] colossus 时间过滤的远值，默认 365*24*3600

    `use_ad_slot_size`: [bool] 特征前缀采用 ad 的配置，默认 true

    `parameter_sign_bits`: [int] 特征值的比特位数，默认 52

    `mio_slots_id`: [list] sparse 特征对应的 mio_slots_id 列表，默认 [4000, 4001, 4002, 4003, 4004, 4005, 4006]

    `slots_id`: [list] sparse 特征对应的 slots_id 列表，默认 [4000, 4001, 4002, 4003, 4004, 4005, 4006]

    `output_slot_attr`: [string] sparse 特征 slot 存放的 common attr, 默认 ad_colocussv2_goods_click_seq_slot

    `output_sign_attr`: [string] sparse 特征 sign 存放的 common attr, 默认 ad_colocussv2_goods_click_seq_sign

    `padding_seq`: [bool] 返回的 id 特征列表是否补全，默认 false

    `padding_seq_timestamp`: [bool] 返回的时间戳列表是否补全，默认 false

    `output_seq_timestamp_attr`: [string] dense 特征时间戳序列存放的 common attr, 默认 ad_colocussv2_goods_click_seq_timestamp

    `output_seq_length_attr`: [string] dense 特征序列有效长度存放的 common attr, 默认 ad_colocussv2_goods_click_seq_length

    `slot_as_attr_name_prefix`: [string] sparse 特征分开存放的 common attr 前缀, 默认为空不分开存

    调用示例
    ------
    ``` python
    .ad_colossusv2_goods_click_seq()
    ```
    """
    self._add_processor(AdColossusV2GoodsClickSeqEnricher(kwargs))
    return self

  def ad_live_hash_slot(self, **kwargs):
    """
    AdColossusV2GoodsClickSeqEnricher
    ---
    商业化内循环访问V2版本auto_live_item的colossus取match特征

    参数配置
    ---
    `colossus_author_id_attr`: [string][动态参数] 存作者 author_id 的 common attr, 默认 author_id

    `colossus_timestamp_attr`: [string][动态参数] 存时间戳 timestamp 的 common attr, 默认 timestamp

    `colossus_play_time_attr`: [string][动态参数] 存观看时间的 common attr, 默认 play_time

    `min_seconds_ago`: [int][动态参数] colossus 时间过滤的近值，默认 0

    `max_seconds_ago`: [int][动态参数] colossus 时间过滤的远值，默认 20 * 60

    `target_ids_attr`: [list][动态参数] sparse 特征对应的用户侧字段，默认 author_id

    `output_attr`: [string][动态参数] sparse 特征存放的 item attr, 默认 ad_live_v2_author_label_qy

    调用示例
    ------
    示例
    ------
    ``` python
    .ad_live_v2_author_label()
    ```
    """
    self._add_processor(AdLiveHashSlotEnricher(kwargs))
    return self

  def ad_live_v2_clk_cate(self, **kwargs):
    """
    AdColossusV2GoodsClickSeqEnricher
    ---
    商业化内循环访问V2版本auto_live_item的colossus取match特征

    参数配置
    ---
    `colossus_author_id_attr`: [string][动态参数] 存作者 author_id 的 common attr, 默认 author_id

    `colossus_timestamp_attr`: [string][动态参数] 存时间戳 timestamp 的 common attr, 默认 timestamp

    `colossus_play_time_attr`: [string][动态参数] 存观看时间的 common attr, 默认 play_time

    `min_seconds_ago`: [int][动态参数] colossus 时间过滤的近值，默认 0

    `max_seconds_ago`: [int][动态参数] colossus 时间过滤的远值，默认 20 * 60

    `target_ids_attr`: [list][动态参数] sparse 特征对应的用户侧字段，默认 author_id

    `output_attr`: [string][动态参数] sparse 特征存放的 item attr, 默认 ad_live_v2_author_label_qy

    调用示例
    ------
    示例
    ------
    ``` python
    .ad_live_v2_author_label()
    ```
    """
    self._add_processor(AdLiveV2ClkCateEnricher(kwargs))
    return self
  
  def ad_llm_ui_match(self, **kwargs):
    """
    AdColossusV2GoodsClickSeqEnricher
    ---
    商业化内循环访问V2版本auto_live_item的colossus取match特征

    参数配置
    ---
    `colossus_author_id_attr`: [string][动态参数] 存作者 author_id 的 common attr, 默认 author_id

    `colossus_timestamp_attr`: [string][动态参数] 存时间戳 timestamp 的 common attr, 默认 timestamp

    `colossus_play_time_attr`: [string][动态参数] 存观看时间的 common attr, 默认 play_time

    `min_seconds_ago`: [int][动态参数] colossus 时间过滤的近值，默认 0

    `max_seconds_ago`: [int][动态参数] colossus 时间过滤的远值，默认 20 * 60

    `target_ids_attr`: [list][动态参数] sparse 特征对应的用户侧字段，默认 author_id

    `output_attr`: [string][动态参数] sparse 特征存放的 item attr, 默认 ad_live_v2_author_label_qy

    调用示例
    ------
    示例
    ------
    ``` python
    .ad_live_v2_author_label()
    ```
    """
    self._add_processor(AdLlmUiMatchEnricher(kwargs))
    return self
  
  def ad_live_v2_author_label(self, **kwargs):
    """
    AdColossusV2GoodsClickSeqEnricher
    ---
    商业化内循环访问V2版本auto_live_item的colossus取match特征

    参数配置
    ---
    `colossus_author_id_attr`: [string][动态参数] 存作者 author_id 的 common attr, 默认 author_id

    `colossus_timestamp_attr`: [string][动态参数] 存时间戳 timestamp 的 common attr, 默认 timestamp

    `colossus_play_time_attr`: [string][动态参数] 存观看时间的 common attr, 默认 play_time

    `min_seconds_ago`: [int][动态参数] colossus 时间过滤的近值，默认 0

    `max_seconds_ago`: [int][动态参数] colossus 时间过滤的远值，默认 20 * 60

    `target_ids_attr`: [list][动态参数] sparse 特征对应的用户侧字段，默认 author_id

    `output_attr`: [string][动态参数] sparse 特征存放的 item attr, 默认 ad_live_v2_author_label_qy

    调用示例
    ------
    示例
    ------
    ``` python
    .ad_live_v2_author_label()
    ```
    """
    self._add_processor(AdLiveV2AuthorLabelEnricher(kwargs))
    return self

  def ad_twin_towers_retrieval(self, **kwargs):
    """
    TwinTowersRetrieval
    -----
    商业化双塔召回服务

    参数配置
    -----
    `none`: [string][动态参数] trigger类型 common attr, 默认 5(realtime_calculation)

    调用示例
    -----
    ``` python
    .ad_twin_towers_retrieval()
    ```
    """
    self._add_processor(TwinTowersRetrieval(kwargs))
    return self

  def ad_twin_towers_initializer(self, **kwargs):
    """
    TwinTowersInitializer
    -----
    商业化双塔初始化processor

    参数配置
    -----
    `load_model`: [bool][动态参数] 是否需要加载模型，用于计算user embedding, 默认 True

    调用示例
    -----
    ``` python
    .ad_twin_towers_initializer(load_model=True)
    ```
    """
    self._add_processor(TwinTowersInitializer(kwargs))
    return self

  def twin_towers_prepare_context(self, **kwargs):
    """
    PrepareContext
    -----
    商业化双塔构造请求context接口

    参数配置
    -----
    `recall_type`: [int][动态参数] recall的类型,是否使用app_id, 默认 1(user_id)

    调用示例
    -----
    ``` python
    .twin_towers_prepare_context(recall_type=1, enable_target_filter=False, enable_region_filter=False)
    ```
    """
    self._add_processor(PrepareContext(kwargs))
    return self

  def twin_towers_result_from_cache(self, **kwargs):
    """
    ResultFromCache
    -----
    商业化双塔cache(user维度)，命中cache的请求会直接返回结果

    参数配置
    -----
    `result_cache_ttl`: [int][动态参数] user维度缓存时间，单位：second, 默认 80s

    `result_cache_capacity`: [int][动态参数] user维度cache最大缓存user结果数量，默认 150000

    `result_cache_max_num`: [int][动态参数] user维度cache每个user最多缓存item分数数量，默认 30000

    `result_count`: [int][动态参数] cache缓存结果个数，需要和结果处理对齐，例如创意模型返回1个creative id,聚合召回返回photo idh和unit id等,默认 1

    `return_score`: [int][动态参数] 查询到缓存之后，是否将分数返回到上游

    `result_cache_keys`: [int][动态参数] 联盟专用，只有类型为string的key才需要填改值

    调用示例
    -----
    ``` python
    .twin_towers_result_from_cache()
    ```
    """
    self._add_processor(ResultFromCache(kwargs))
    return self

  def twin_towers_trigger_init(self, **kwargs):
    """
    TriggerInit，初始化构造trigger
    -----
    商业化双塔trigger构造

    参数配置
    -----
    `trigger_type`: [string] trigger类型, 默认"realtime_calculation",列表参考teams/ad/ad_nn/kconf_manager/trigger_config.cc

    调用示例
    -----
    ``` python
    .twin_towers_trigger_init(trigger_type="realtime_calculation")
    ```
    """
    self._add_processor(TriggerInit(kwargs))
    return self

  def construct_bslog(self, **kwargs):
    """
    Construct batched sample log.
    -----
    商业化双塔用于构造bslog，填写user info、reco user info等到bslog中

    参数配置
    -----
    `none`: [int] 返回给target的结果个数,对于创意模型只返回一个creative id,对于聚合模型一般会返回photo id和unit id 2个结果, 默认1

    调用示例
    -----
    ``` python
    .construct_bslog()
    ```
    """
    self._add_processor(ConstructBSLog(kwargs))
    return self

  def construct_adlog(self, **kwargs):
    """
    Construct AdLog
    -----
    商业化双塔用于构造adlog，填写user info、reco user info等到adlog中

    参数配置
    -----
    `none`: [int] 返回结果的个数, 默认1

    调用示例
    -----
    ``` python
    .construct_adlog()
    ```
    """
    self._add_processor(ConstructADLog(kwargs))
    return self

  def extract_bs_feature(self, **kwargs):
    """
    ExtractBSFeature
    -----
    商业化双塔用于求解输入为batch sample格式的特征

    参数配置
    -----
    `none`: [int] 返回结果的个数, 默认1

    调用示例
    -----
    ``` python
    .extract_bs_feature()
    ```
    """
    self._add_processor(ExtractBSFeature(kwargs))
    return self

  def construct_dnn_input(self, **kwargs):
    """
    Construct DNNInput
    -----
    商业化双塔用于构造DNN的输入，包括sparse user（计算embedding pooling等） + dense user

    参数配置
    -----
    `none`: [int] 返回结果的个数, 默认1

    调用示例
    -----
    ``` python
    .construct_dnn_input()
    ```
    """
    self._add_processor(ConstructDNNInput(kwargs))
    return self


  def calculate_embedding(self, **kwargs):
    """
    CalculateEmbedding
    -----
    商业化双塔用于计算trigger/user embedding

    参数配置
    -----
    `none`: [int] 返回结果的个数, 默认1

    调用示例
    -----
    ``` python
    .calculate_embedding()
    ```
    """
    self._add_processor(CalculateEmbedding(kwargs))
    return self

  def twin_towers_handle_result(self, **kwargs):
    """
    HandleResult
    -----
    商业化双塔用于计算trigger embedding

    参数配置 -----
    `result_count`: [int] 返回给target的结果个数,对于创意模型只返回一个creative id,对于聚合模型一般会返回photo id和unit id 2个结果, 默认1

    调用示例
    -----
    ``` python
    .twin_towers_handle_result()
    ```
    """
    self._add_processor(HandleResult(kwargs))
    return self

  def sim_action_list(self, **kwargs):
    """
    SimActionList
    -----
    商业化用于获取sim数据processor

    参数配置
    -----
    `feature_process_config`: [int] 获取sim数据配置, 默认为空，直接跳过该processor

    `use_async_processor`: [int] 是否异步执行获取ad sim数据, 默认为false

    调用示例
    -----
    ``` python
    .sim_action_list(feature_process_config = {
      "BSSimProcessor": {
        "cache_capacity": 50000,
        "cache_expire_time": 120,
        "scene_type": "ad",
        "table_name": "adSimDataCHash",
        "use_picasso_chash": true
      }
    })
    ```
    """
    self._add_processor(SimActionList(kwargs))
    return self

  def build_user_target(self, **kwargs):
    """
    BuildUserTarget
    -----
    商业化定向过滤，用于召回每个user对应的定向信息

    参数配置
    -----
    `bs_log`: [bool] 数据源是否为batched sample格式，默认true

    `is_i18n`: [bool] 是否为海外业务，默认false

    `enable_region_target`: [bool] 是否打开地域定向,省级别,默认true

    `enable_city_filter`: [bool] 是否打开地域定向,city级别,默认false

    `enable_resource_filter`: [bool] 是否广告位,默认false

    `enable_crowd_pack_filter`: [bool] 是否打人群包定向,默认false

    `enable_platform_version_filter`: [bool] 是否打开平台定向,默认false

    `prophet_config_name`: [string] 先知项目配置，从ad.algorithm.ProphetRecallConfig读取数据

    `i18n_target_map_kconf`: [string] 海外业务配置定向map，kconf名称，默认为空字符串

    调用示例
    -----
    ``` python
    .build_user_target()
    ```
    """
    self._add_processor(BuildUserTarget(kwargs))
    return self

  def feature_initializer(self, **kwargs):
    """
    FeatureInitializer
    -----
    商业化获取特征相关信息，提供feature_specs

    参数配置
    -----
    `feature_specs`: [array] 每个特征信息描述

    `feature_format`: [dict] 特征全局信息描述，包括hash方式、prefix长度等

    调用示例
    -----
    ``` python
    .feature_initializer(
      feature_specs = [{
         "feature_name": "long_term_live_pids",
         "extractor_type": "dragon_processor",
         "feature_id": 0,
         "is_sparse": False,
         "bucket_size": 250000002,
         "bucket_id": 771,
         "embedding_len": 16,
         "lru_cache_capacity": 250000002,
         "split_prefix": 0,
         "dragon_feature_id": 100,
         "dragon_attr_name": "",
         "rewrite_prefix": 371,
         "category": "combine",
         "op": "concat",
         "concat_len": 800,
     }, {
         "feature_name": "BSExtractUserRealtimeActionAdImpNoStdlpsLiveId",
         "extractor_type": "bs_extractor",
         "feature_id": 1,
         "bucket_size": 250000002,
         "bucket_id": 771,
         "embedding_len": 16,
         "lru_cache_capacity": 250000002,
         "split_prefix": 0,
         "rewrite_prefix": 371,
         "is_sparse": False,
         "category": "user",
         "op": "concat",
         "concat_len": 800,
     }],
     feature_format = {
         "prefix_len": 12,
         "hash_type": "djb2_hash64"
     })
    ```
    """
    self._add_processor(FeatureInitializer(kwargs))
    return self

  def ad_sampling_center_retriever(self, **kwargs):
    """
    AdSamplingCenterRetriever
    ------
    从采样中心获取样本
    ------
    `pos_sample_ids_attr`: [string] 输入的正样本 common attr name

    `neg_sample_ids_attr`: [string] 输入的负样本 common attr name

    `sample_info_attr`: [string] 输出 sample info 的 item attr name

    调用示例
    ------
    示例
    ------
    ``` python
    .ad_sampling_center_retriever(pos_sample_ids_attr="pos_sample_ids",
                                  neg_sample_ids_attr="neg_sample_ids",
                                  sample_info_attr="sample_info_str")
    ```
    """
    self._add_processor(AdSamplingCenterRetriever(kwargs))
    return self

  def ad_gsu_retriever_with_p2l_colossus_resp_v1(self, **kwargs):
    self._add_processor(CommonP2lColossusRetriever(kwargs))
    return self

  def ad_ecom_gsu(self, **kwargs):
    self._add_processor(AdEcomGsuEnricher(kwargs))
    return self

  def ad_ecom_gsu_v2(self, **kwargs):
    self._add_processor(AdEcomGsuV2Enricher(kwargs))
    return self

  def ad_ecom_gsu_v3(self, **kwargs):
    self._add_processor(AdEcomGsuV3Enricher(kwargs))
    return self

  def ad_ecom_match_cnt(self, **kwargs):
    self._add_processor(AdEcomMatchCntEnricher(kwargs))
    return self

  def ad_ecom_aid2aid(self, **kwargs):
    self._add_processor(AdEcomAid2AidEnricher(kwargs))
    return self

  def ad_goods_click(self, **kwargs):
    self._add_processor(AdGoodsClickEnricher(kwargs))
    return self
  
  def ad_livev4_all_seq(self, **kwargs):
    """
    AdLiveV1Livev4AllSeqEnricher
    ------
    从 liveitem v4 中解析 list
    ------
    `limit_num`: [int] [动态参数] 序列长度，默认 1000
    `filter_play_time`: [bool] [动态参数] 最小播放时间过滤，单位为 s
    `only_merchant_live_attr`: [bool] [动态参数] 电商直播过滤，默认 false
    `min_seconds_ago & max_seconds_ago`: [int] [动态参数] 播放时间戳过滤，单位为 s
    调用示例
    ------
    ``` python
    .ad_livev4_all_seq(
        colossus_live_id_attr="live_id_list",
        colossus_timestamp_attr="timestamp_list",
        colossus_author_id_attr="author_id_list",
        colossus_play_time_attr="play_time_list",
        colossus_cluster_id_attr="cluster_id_list",
        colossus_label_attr="label_list",
        colossus_audience_count_attr="audience_count_list",
        colossus_order_price_attr="order_price_list",
        mio_slots_id=list(range(800, 809)),
        slots_id=list(range(800, 809)),
        output_slot_attr='ad_live_v1_livev4_click_seq_slot',
        output_sign_attr='ad_live_v1_livev4_click_seq_sign',
        filter_play_time=0,
        only_merchant_live_attr_=True,
        only_click_live_attr_=True,
        limit_num=1000,
        slot_as_attr_name_prefix='ad_livev4_click_',
        slot_as_attr_name=True,
        item_map_list=[[str(i), i, i, 1000001] for i in range(800, 809)],
        sign_prefix_bit_num_input=10,
        sign_prefix_bit_num_output=12,
        use_murmur3hash64=True,
        use_ad_slot_size=True,
    ) \
    ```
    """
    self._add_processor(AdLiveV1Livev4AllSeqEnricher(kwargs))
    return self

  def ad_livev4_all_seq_v2(self, **kwargs):
    """
    AdLiveV1Livev4AllSeqEnricher
    ------
    从 liveitem v4 中解析 list v2 : 增加 sideinfo , 输入适配新版 colossus, 输出保留原始值
    ------
    `limit_num`: [int] [动态参数] 序列长度，默认 1000
    `filter_play_time & filter_auto_play_time`: [bool] [动态参数] 播放时间过滤，默认 false
    `only_merchant_live_attr`: [bool] [动态参数] 电商直播过滤，默认 false
    `min_seconds_ago & max_seconds_ago`: [int] [动态参数] 播放时间戳过滤，单位为 s
    调用示例
    ------
    ``` python
    .ad_livev4_all_seq_v2(
        colossus_auto_play_time_attr="auto_play_time_list",
        colossus_timestamp_attr="timestamp_list",
        colossus_author_id_attr="author_id_list",
        colossus_play_time_attr="play_time_list",
        colossus_cluster_id_attr="cluster_id_list",
        colossus_label_attr="label_list",
        colossus_audience_count_attr="audience_count_list",
        colossus_order_price_attr="order_price_list",
        filter_play_time=True,
        filter_play_time_min=0,
        only_merchant_live_attr=True,
        limit_num=1000,
        author_id_list_attr="dup_live_author_id_list",
        label_list_attr="dup_live_label_list",
        lag_day_list_attr="dup_live_lag_day_list",
        lag_hour_list_attr="dup_live_lag_hour_list",
        play_time_list_attr="dup_live_play_time_list",
        auto_play_time_list_attr="dup_auto_play_time_list",
        cluster_id_list_attr="dup_cluster_id_list",
        audience_count_list_attr="dup_audience_count_list",
        order_price_list_attr="dup_order_price_list",
    ) \
    ```
    """
    self._add_processor(AdLiveV2Livev4AllSeqEnricher(kwargs))
    return self

  def ad_livev4_aid2aid(self, **kwargs):
    """
    AdLiveV1Livev4AllSeqEnricher
    ------
    从 liveitem v4 中解析 list , 以 author_id 进行 hard search
    ------
    `limit_num`: [int] [动态参数] 序列长度，默认 1000
    `filter_play_time`: [bool] [动态参数] 最小播放时间过滤，单位为 s
    `only_merchant_live_attr`: [bool] [动态参数] 电商直播过滤，默认 false
    `min_seconds_ago & max_seconds_ago`: [int] [动态参数] 播放时间戳过滤，单位为 s
    `target_aids_attr`: [int] [动态参数] 目标 author_id
    调用示例
    ------
    ``` python
    .ad_livev4_aid2aid(
        colossus_live_id_attr="live_id_list",
        colossus_timestamp_attr="timestamp_list",
        colossus_author_id_attr="author_id_list",
        colossus_play_time_attr="play_time_list",
        colossus_cluster_id_attr="cluster_id_list",
        colossus_label_attr="label_list",
        colossus_audience_count_attr="audience_count_list",
        colossus_order_price_attr="order_price_list",
        mio_slots_id=list(range(800, 809)),
        slots_id=list(range(800, 809)),
        output_slot_attr='ad_live_v1_livev4_aid2aid_slot',
        output_sign_attr='ad_live_v1_livev4_aid2aid_sign',
        filter_play_time=0,
        limit_num=10000,
        slot_as_attr_name_prefix='ad_livev4_aid2aid_',
        slot_as_attr_name=True,
        item_map_list=[[str(i),i,i,1000001] for i in range(800,809)],
        sign_prefix_bit_num_input=10,
        sign_prefix_bit_num_output=12,
        use_murmur3hash64= True,
        use_ad_slot_size= True,
        target_aid_attr=0,
        only_merchant_live_attr=False,
        only_click_live_attr=False,
        target_aids_attr="aid"
    ) \
    ```
    """
    self._add_processor(AdLiveV2Livev4Aid2aidEnricher(kwargs))
    return self

  def eshop_video_gsu(self, **kwargs):
    self._add_processor(EshopVideoGsuEnricher(kwargs))
    return self

  def eshop_merchant_merge(self, **kwargs):
    self._add_processor(EshopMerchantMergeEnricher(kwargs))
    return self
  def merchant_good_gsu_with_idx_list(self, **kwargs):
    self._add_processor(MerchantGoodGsuWithIdxListEnricher(kwargs))
    return self
  
  def goods_click_gsu_with_idx_list(self, **kwargs):
    self._add_processor(GoodsClickGsuWithIdxListEnricher(kwargs))
    return self

  def id_count_enricher(self, **kwargs):
    self._add_processor(IdCountEnricher(kwargs))
    return self

  def eshop_video_gsu_with_idx_list(self, **kwargs):
    self._add_processor(EshopVideoGsuWithIdxListEnricher(kwargs))
    return self

  def eshop_video_gsu_with_pid_list(self, **kwargs):
    self._add_processor(EshopVideoGsuWithPidListEnricher(kwargs))
    return self

  def eshop_video_aid2aid(self, **kwargs):
    self._add_processor(EshopVideoAid2AidEnricher(kwargs))
    return self

  def merchant_good_gsu(self, **kwargs):
    self._add_processor(MerchantGoodGsuEnricher(kwargs))
    return self

  def live_item_gsu_with_aid_list(self, **kwargs):
    self._add_processor(LiveItemGsuWithAidListEnricher(kwargs))
    return self
  
  def live_item_gsu_with_idx_list(self, **kwargs):
    self._add_processor(LiveItemGsuWithIdxListEnricher(kwargs))
    return self

  def ad_rewrite_topk_index_enricher(self, **kwargs):
    self._add_processor(AdRewriteTopkIndexEnricher(kwargs))
    return self
  
  def ad_remote_emb_check_save_time_valid(self, **kwargs):
    """
    AdRemoteEmbCheckSaveTimeValidEnricher
    ------
    商业化内循环检查 EmbSvr 返回的 Emb 是否发生了特征穿越

    参数配置
    ------
    `request_time_attr`: [string][动态参数] 存生成特征的请求时间的 common attr, 默认为空则取当前时间

    `from_common_attr`: [string] 如果配置，则从 common attr 获取 key 进行 sign 变换, 优先级高于 from_item_attr

    `from_item_attr`: [string] 如果配置，则从 item attr 获取 key 进行 sign 变换, 否则用 item_key 进行变换

    `time_size`: [int][动态参数] EmbSvr 返回的 Emb 前 time_size 个数为时间戳，默认 4

    `emb_size`: [int][动态参数] EmbSvr 返回的 Emb 后 emb_size 个数为 Emb，默认 64

    `time_seconds`: [bool][动态参数] 时间戳单位是秒，否则为毫秒，默认 false

    `safe_gap_seconds`: [int][动态参数] 防止特征穿越的安全时间间隔 单位秒，默认 10

    `padding_size`: [int][动态参数] 大于 0 时保证返回结果长度等于 padding_size ，不够补零，多则截断，默认 0

    `output_attr`: [string] 结果 Emb 存放的 attr，from_common_attr 不为空则存 common attr， 否则存 item attr

    调用示例
    ------
    示例
    ------
    ``` python
    .ad_remote_emb_check_save_time_valid(
      from_common_attr="remote_embedding",
      output_attr="valid_embedding"
    )
    ```
    """
    self._add_processor(AdRemoteEmbCheckSaveTimeValidEnricher(kwargs))
    return self

  def ad_slot_sign_plain(self, **kwargs):
    """
    AdSlotSignPlainEnricher
    ------
    商业化将一些字段合并成 sign 不做哈希

    参数配置
    ------
    `slot_id`: [int][动态参数] 特征桶号 slot_id ，在最高位

    `slot_bits`: [int][动态参数] 特征桶号最高位占用比特数 slot_bits ，填 0 则不占用，默认 12

    `id_attrs`: [list] of [dict][动态参数]，构成 sign 的 attr 配置
      - `attr_name`: [string] 设置的 attr name ，默认空
      - `common`: [bool] false 表示 item attr, true 表示 common attr ，默认 false
      - `bits`: [int] 字段占用的比特位 bits ，所有 attr 及 slot 占用比特位加起来应等于 64

    `output_attr`: [string] 结果 sign 存放的 attr， id_attrs 全 common 则存 common attr， 否则存 item attr

    调用示例
    ------
    示例
    ------
    ``` python
    .ad_slot_sign_plain(
      slot_id=750,
      slot_bits=12,
      id_attrs=[
        {
            "attr_name": "the_user_id",
            "common": True,
            "bits": 32,
        },
        {
            "attr_name": "aid",
            "common": False,
            "bits": 20,
        }
      ],
      output_attr="slot_sign"
    )
    ```
    """
    self._add_processor(AdSlotSignPlainEnricher(kwargs))
    return self

  def ad_colossus_time_filter(self, **kwargs):
    """
    AdColossusTimeFilterEnricher
    ------
    商业化按时间戳过滤 colossus 中的交互

    参数配置
    ------
    `input_attrs`: [list] of [string] 必填，输入的 common 序列字段，每个序列长度需要和时间戳序列长度对应

    `output_attrs`: [list] of [string] 必填，过滤后输出的 common 字段，需要和 input_attrs 一一对应

    `colossus_time_attr`: [string] 必填， colossus 时间戳序列的 common 字段名

    `request_time_attr`: [string] 当前请求时间的 common 字段名，默认空

    `default_request_time`: [bool] 是否通过 GetRequestTime() 获取默认的请求时间，默认 true

    `default_current_time`: [bool] 当请求时间获取为空时，是否通过 std::time(0) 获取当前时间以兜底，默认 false

    `min_seconds_ago`: [int] colossus 时间需要至少早于当前请求时间 min_seconds_ago 以防穿越，默认 -1 不生效

    `max_seconds_ago`: [int] colossus 时间最多早于当前请求时间 max_seconds_ago 以过滤太旧的交互，默认 -1 不生效

    `limit_num`: [int] 最多保留多少个 colossus 的交互，默认 -1 不生效

    调用示例
    ------
    示例
    ------
    ``` python
    .ad_colossus_time_filter(
      input_attrs=['item_id_list', 'category_a_list'],
      output_attrs=['item_id_list', 'category_a_list'],
      colossus_time_attr='click_timestamp_list',
      min_seconds_ago=600,
      limit_num=500
    )
    ```
    """
    self._add_processor(AdColossusTimeFilterEnricher(kwargs))
    return self

  def ad_list_attr_pad(self, **kwargs):
    """
    AdListAttrPadEnricher
    ------
    商业化 pad list attr

    参数配置
    ------
    `pad_length`: [int] pad 目标长度

    `common_attr`: [bool] 是否输入输出为 common attr

    `double_attr`: [bool] 是否输入输出 attr list 元素类型是 double

    `pad_value`: [double|int] pad 值，默认 0
    
    `input_attr`: [string] 输入 attr

    `output_attr`: [string] 结果存放的 attr， input 为 common 则存 common attr， 否则存 item attr

    调用示例
    ------
    示例
    ------
    ``` python
    .ad_list_attr_pad(
      pad_length=64,
      common_attr=true,
      double_attr=true,
      input_attr="user_emb"
      output_attr="user_emb"
    )
    ```
    """
    self._add_processor(AdListAttrPadEnricher(kwargs))
    return self

  def ad_userinfo_debug_log(self, **kwargs):
    """
    AdBsUserInfoValueObserver
    ------
    打印 bs 类型的 UserInfo 的值到日志中

    参数
    ------
    `user_attrs`: [list] 可缺省，打印填入的 attr id 或者 attr key 对应 userinf 的 value 值。debug_all 参数为 True 时，此参数不起作用。
    `debug_all`: [bool] 可缺省，默认为 False，是否在日志中打印全量 bs userinfo 数据，为 True 时会忽略以上两个参数。默认为 False。
    `to`: [string] 输出的位置，支持 glog、stdout、stderr、file，默认值为 glog。注意：glog 对单次日志的长度有限制，若因内容太多被截断展示不全，可改成输出到 stdout/stderr/file 来避免。
    `to_file_folder`: [string] 当 to="file" 时，需要提供该参数，用于指定 debug info 输出文件所在的目录
    `to_file_name`: [string] [动态参数] 当 to="file" 时，需要提供该参数，用于指定 debug info 输出文件名，默认为 debug_info.log，注意避免不同 processor 同时向一个文件写入数据。
    `append_to_file`: [bool] 当 to="file" 时，该参数有效，用于指定 debug info 写入日志文件的方式，默认为 False，表示覆盖写入文件；如果配置成 True，表示追加写入文件

    示例
    ------
    ``` python
    .ad_userinfo_debug_log(
      user_attrs = ["adlog.user_info.rank_cmd.exists", "60258", "0", "adlog.user_info.common_info_attr.key:60255"],
      to = "file",
      to_file_folder = "../debug/",
      append_to_file = True,
    )
    or
    .ad_userinfo_debug_log(
      debug_all = True,
    )
    ```
    """
    self._add_processor(AdBsUserInfoValueObserver(kwargs))
    return self

  def ad_iteminfo_debug_log(self, **kwargs):
    """
    AdBsItemInfoValueObserver
    ------
    打印 bs 类型的 ItemInfo 的值到日志中

    参数
    ------
    `common_attr`: [string] 可缺省，指定从哪个 common attr 中取出要打印的 item attrs，默认值是 BslogBsItem。
    `item_attrs`: [list] 可缺省，打印填入的 item attrs 对应的 value 值。 debug_all 参数为 True 时，此参数不起作用。
    `debug_all`: [bool] 可缺省，是否在日志中打印全量 item attrs 数据，为 True 时会忽略 item_attrs 参数。默认为 False。
    `item_num_limit`: [int] 可缺省，默认为 10。
    `to`: [string] 输出的位置，支持 glog、stdout、stderr、file，默认值为 glog。注意：glog 对单次日志的长度有限制，若因内容太多被截断展示不全，可改成输出到 stdout/stderr/file 来避免。
    `to_file_folder`: [string] 当 to="file" 时，需要提供该参数，用于指定 debug info 输出文件所在的目录
    `to_file_name`: [string] [动态参数] 当 to="file" 时，需要提供该参数，用于指定 debug info 输出文件名，默认为 debug_info.log，注意避免不同 processor 同时向一个文件写入数据。
    `append_to_file`: [bool] 当 to="file" 时，该参数有效，用于指定 debug info 写入日志文件的方式，默认为 False，表示覆盖写入文件；如果配置成 True，表示追加写入文件

    示例
    ------
    ``` python
    .ad_iteminfo_debug_log(
      common_attr = "BslogBsItem",
      item_attrs=["adlog.item.build_time", "17869"],
      item_num_limit=4,
    )
    ```
    """
    self._add_processor(AdBsItemInfoValueObserver(kwargs))
    return self

  def ad_feature_debug_log(self, **kwargs):
    """
    AdInferFeatureValueObserver
    ------
    打印已经被抽取好的特征到日志中

    参数
    ------
    `common_attr`: [string] 可缺省，指定从哪个 common attr 中取出要打印的特征值，默认值是 FeatureResult。
    `feature_names`: [list] 可缺省，指定打印哪些 feature。
    `item_num_limit`: [int] 可缺省，最多输出多少个 item 的 item attr，默认值为 10
    `to`: [string] 输出的位置，支持 glog、stdout、stderr、file，默认值为 glog。注意：glog 对单次日志的长度有限制，若因内容太多被截断展示不全，可改成输出到 stdout/stderr/file 来避免。
    `to_file_folder`: [string] 当 to="file" 时，需要提供该参数，用于指定 debug info 输出文件所在的目录
    `to_file_name`: [string] [动态参数] 当 to="file" 时，需要提供该参数，用于指定 debug info 输出文件名，默认为 debug_info.log，注意避免不同 processor 同时向一个文件写入数据。
    `append_to_file`: [bool] 当 to="file" 时，该参数有效，用于指定 debug info 写入日志文件的方式，默认为 False，表示覆盖写入文件；如果配置成 True，表示追加写入文件

    示例
    ------
    ``` python
    .ad_feature_debug_log(
      common_attr="FeatureResult",
      feature_names=["ExtractUserAdItemClickNoLps", "ExtractCombineUserAdActionListSimLps", "long_term_tags", "ExtractPhotoBaseUriLps", "ExtractDenseAdQueueType"],
      item_num_limit=4,
      to="stdout",
    )
    ```
    """
    self._add_processor(AdFeatureValueObserver(kwargs))
    return self

  def common_ad_gsu_with_cluster_tag_v2_rfm_conf_enricher(self, **kwargs):
    """
    CommonAdGsuWithClusterTagV2RfmConfEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `search_type`: [int] 检索类型 1为根据cluster检索 2为根据tag检索

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
    ------
    ``` python
    .common_ad_gsu_with_cluster_tag_v2_rfm_conf_enricher(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           rfm_limit_num=2000,
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                           search_type = 1,
                           hetu_1k_cluster_json_value="")
    ```
    """
    self._add_processor(CommonAdGsuWithClusterTagV2RfmConfEnricher(kwargs))
    return self

  def common_ad_gsu_with_cluster_tag_v3_rfm_conf_enricher(self, **kwargs):
    """
    CommonAdGsuWithClusterTagV3RfmConfEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `search_type`: [int] 检索类型 1为根据cluster检索 2为根据tag检索

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
    ------
    ``` python
    .common_ad_gsu_with_cluster_tag_v3_rfm_conf_enricher(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           rfm_limit_num=2000,
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                           search_type = 1,
                           hetu_1k_cluster_json_value="")
    ```
    """
    self._add_processor(CommonAdGsuWithClusterTagV3RfmConfEnricher(kwargs))
    return self

  def common_ad_gsu_with_cluster_tag_v4_rfm_conf_enricher(self, **kwargs):
    """
    CommonAdGsuWithClusterTagV3RfmConfEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `search_type`: [int] 检索类型 1为根据cluster检索 2为根据tag检索

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
    ------
    ``` python
    .common_ad_gsu_with_cluster_tag_v4_rfm_conf_enricher(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           rfm_limit_num=2000,
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                           search_type = 1,
                           hetu_1k_cluster_json_value="")
    ```
    """
    self._add_processor(CommonAdGsuWithClusterTagV4RfmConfEnricher(kwargs))

  def ad_colossus(self, **kwargs):
    """
    AdCommonColossusEnricher
    ------
    拉取 colossus 数据

    参数
    ------
    `service_name`: [str] colossus 服务的 kess name

    `client_type`: [str] 请求 colossus 服务的 client 类型, 支持 sim_client/snack_client/common_item_client

    `is_batch_query`: [bool] 是否开启 batch 请求模式

    `parse_to_pb`: [bool] 是否将 colossus 数据解析到 pb 中, 默认 true

    `print_items`: [bool] 是否打印 colossus 获取的 item 数据, 默认 false

    `cache_capacity`: [int] cache 大小

    `max_resp_item_num`: [int] 从 colossus 返回的 item 数的最大值, 默认为 0 表示不限制

    `debug_uids`: [str] 逗号分隔的 uid list

    `output_attr`: [str] 拉取的 colossus 数据的输出 attr, 不可为空

    `input_attr`: [str] 开启 batch 时 input_attr 不可为空

    `input_colossus_resp_attr`: [str]

    `expire_time`: [int] cache 过期时间

    `use_ing_status`: [bool]

    示例
    ------
    ```python
    .ad_colossus(
        name="colossus_recosim",
        cache_capacity=200000,
        client_type="common_item_client",
        expire_time=600,
        output_attr="colossus_output",
        parse_to_pb=False,
        service_name="grpc_colossusSimAdStateItemNew",
    )
      ```
    """
    self._add_processor(AdCommonColossusEnricher(kwargs))
    return self

  def ad_get_remote_embedding(self, **kwargs):
    """
    AdCommonRemoteEmbeddingAttrEnricher
    ------
    拉取远程 embedding

    参数
    ------
    `kess_cluster`: [str] kess 集群, 默认 PRODUCTION

    `shard_num`: [int] 默认为 1

    `client_side_shard`: [bool] 是否 client 端分 shard, 默认为 false

    `max_signs_per_request`: [int] 请求中 sign 的最大数量, 默认为 0, 表示不限制

    `shard_prefix`: [str] 分 shard 的前缀, 默认为 s

    `save_to_common_attr`: [bool] 是否保存到 common attr 中, 默认为 false

    `query_source_type`: [str] 查询的 source 类型, 关闭 save_to_common_attr 时, 支持 item_attr/item_key/item_id; 开启 save_to_common_attr 时 支持 user_id/device_id/user_id_and_device_id, 默认为 item_key

    `query_source_item_attr`: [str] query_source_type 为 item_key 时 需要配置此选项

    `id_converter`: [dict] id converter 相关配置

      - `type_name`: [str] id converter 类型, 不可为空

    `slot`: [int] slot 配置, 默认为 0

    `output_item_list_attr`: [str] save_to_common_attr 时 item_list 存放的 attr, 默认为空, 会丢弃 item list

    `output_embedding_list_attr`: [str] save_to_common_attr 时 embedding_list 存放的 attr, 不可为空, 默认为空

    `output_attr_name`: [str] 输出 attr, 不可为空, 默认为空

    `is_raw_data`: [bool] 是否为 raw_data, 默认为 false

    `raw_data_type`: [str] 如果为 raw_data, 则 raw_data 的类型, 支持 uint8/uint16/uint32/uint64/int8/int16/int32/int64/float32/string, 默认为 uint16

    `is_raw_data_list`: [bool] row data 是否为 list, 默认为 true

    `cache_capacity`: [int] cache 容量, 默认为 100000

    `expire_time`: [int] cache 过期时间, 默认为 0

    `use_cache`: [bool] 是否开启 cache, 默认为 false
    """
    self._add_processor(AdCommonRemoteEmbeddingAttrEnricher(kwargs))
    return self

  def common_ad_gsu_with_cluster_tag_v5_rfm_conf_enricher(self, **kwargs):
    """
    CommonAdGsuWithClusterTagV5RfmConfEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `search_type`: [int] 检索类型 1为根据cluster检索 2为根据tag检索

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
    ------
    ``` python
    .common_ad_gsu_with_cluster_tag_v5_rfm_conf_enricher(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           rfm_limit_num=2000,
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                           search_type = 1,
                           hetu_1k_cluster_json_value="")
    ```
    """
    self._add_processor(CommonAdGsuWithClusterTagV5RfmConfEnricher(kwargs))
    return self
  
  def commertial_gsu_with_hour_cluster_conf(self, **kwargs):
    """
    CommonAdGsuWithHourClusterConfEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `limit_num_attr`: [int] 返回数目 attr

    `search_type`: [int] 检索类型 1为根据cluster检索 2为根据tag检索

    `hetu_1k_cluster_json_value`: [string] hetu 1k cluster string 值

    示例
    ------
    ``` python
    .commertial_gsu_with_hour_cluster_conf(colossus_resp_attr='colossus_resp',
                           output_sign_attr='sign',
                           output_slot_attr='slot',
                           limit_num_attr='limit_num',
                           target_cluster_attr = 'liveAidHetuCoverEmbeddingCluster',
                           search_type = 1,
                           hetu_1k_cluster_json_value="")
    ```
    """
    self._add_processor(CommonAdGsuWithHourClusterConfEnricher(kwargs))
    return self

  def multimodal_seq_soft_search_extract_enricher(self, **kwargs):
    self._add_processor(MultimodalSeqSoftSearchExtractEnricher(kwargs))
    return self

  def multimodal_seq_enricher(self, **kwargs):
    self._add_processor(MultimodalSeqEnricher(kwargs))
    return self

  def goods_click_soft_search_enricher(self, **kwargs):
    self._add_processor(GoodsClickSoftSearchEnricher(kwargs))
    return self
  
  def goods_click_soft_search_with_id_list_enricher(self, **kwargs):
    self._add_processor(GoodsClickSoftSearchWithIdListEnricher(kwargs))
    return self

  def ad_live_user_goods_click_times(self, **kwargs):
    """
    AdLiveUserGoodsClickTimesEnricher
    ------
    示例
    ------
    ① 常规用法：该方法效率最高
    ```
    .gsu_common_colossusv2_enricher(
        name='colossusv2_good_click_item',
        query_key="user_id",
        kconf="colossus.kconf_client.good_click_item",
        filter_future_items=True,
        seconds_to_lookback=60,
        # photo_id 和 timestamp 必须配，其他字段用不上就别配，可以节省CPU和网卡带宽
        item_fields=dict(
            click_timestamp="click_timestamp_list",
            item_id="item_id_list",
            category_a="category_a_list",
            real_seller_id="real_seller_id_list",
            seller_id="seller_id_list",
            real_price="real_price_list",
            click_flow_type="click_flow_type_list",
            click_from="click_from_list",
            label="label_list",
            detail_page_view_time="detail_page_view_time_list"
        ),
        limit=5000
    ) \
    .ad_live_user_goods_click_times(
      slots_id= list(range(1080,1085)),
      mio_slots_id= list(range(1080,1085)),
      item_map_list=[[str(i),i,i,5001]for i in range(1080,1085)],
      sign_prefix_bit_num_input=10,
      sign_prefix_bit_num_output=12,
      colossus_goods_id_attr= "item_id_list",
      colossus_timestamp_attr= "click_timestamp_list",
      detail_page_view_time_attr= "detail_page_view_time_list",
      output_slot_attr= "ad_live_user_goods_click_times_slot",
      output_sign_attr= "ad_live_user_goods_click_times_sign",
      output_user_mean_time_by_hours= "goods_click_mean_detail_time_hours",
      output_user_max_time_by_hours= "goods_click_max_detail_time_hours",
      output_user_min_time_by_hours= "goods_click_min_detail_time_hours",
      output_user_cnt_time_by_hours= "goods_click_cnt_detail_time_hours",
      output_user_mean_time_by_weekdays= "goods_click_mean_detail_time_weekdays",
      output_user_max_time_by_weekdays= "goods_click_max_detail_time_weekdays",
      output_user_min_time_by_weekdays= "goods_click_min_detail_time_weekdays",
      output_user_cnt_time_by_weekdays= "goods_click_cnt_detail_time_weekdays",
      output_user_mean_time_by_months= "goods_click_mean_detail_time_months",
      output_user_max_time_by_months= "goods_click_max_detail_time_months",
      output_user_min_time_by_months= "goods_click_min_detail_time_months",
      output_user_cnt_time_by_months= "goods_click_cnt_detail_time_months",
      output_user_mean_time_by_monthdays= "goods_click_mean_detail_time_monthdays",
      output_user_max_time_by_monthdays= "goods_click_max_detail_time_monthdays",
      output_user_min_time_by_monthdays= "goods_click_min_detail_time_monthdays",
      output_user_cnt_time_by_monthdays= "goods_click_cnt_detail_time_monthdays",
      output_user_cnt_time_by_bins = "goods_click_cnt_detail_time_bins",
      slot_as_attr_name= True,
      use_murmur3hash64= True,
      use_ad_slot_size= True,
      slot_as_attr_name_prefix="all_goods_click_times_",
      limit=5000
    ) \
    .log_debug_info(
      print_all_common_attrs=True,
      print_all_item_attrs=True,
      for_debug_request_only=False,
      to="stdout"
    )
    ```
    会将获取的 item list 分字段存储在对应的 commonAttr 中，例如 item 有 1500 条，而 photo_id 是整数类型，那么会将这 1500 个 item 中的 photo_id 存在 my_photo_id 的c ommonAttr 里，类型是 int list
    """
    self._add_processor(AdLiveUserGoodsClickTimesEnricher(kwargs))
    return self

  def ad_live_user_goods_click_v1(self, **kwargs):
    """
    AdLiveUserGoodsClickV1Enricher
    ------
    示例
    ------
    ① 常规用法：该方法效率最高
    ```
    .gsu_common_colossusv2_enricher(
      name='colossusv2_good_order_item',
      # query_key="user_id",
      kconf="colossus.kconf_client.good_order_item",
      filter_future_items=True,
      seconds_to_lookback=60,
      item_fields={
              "create_order_time": "create_order_time_order_list",
              "item_id": "item_id_order_list",
              "pay_order_time":"pay_order_time_list"},
      query_key="user_id",
      limit=1
    ) \
    .gsu_common_colossusv2_enricher(
      name='colossusv2_good_click_item',
      query_key="user_id",
      kconf="colossus.kconf_client.good_click_item",
      filter_future_items=True,
      seconds_to_lookback=60,
      item_fields={
              "category_a": "goods_click_category_list",
              "click_flow_type": "goods_click_flow_type_list",
              "click_from": "goods_click_click_from_list",
              "click_timestamp": "goods_click_timestamp_list",
              "item_id": "goods_click_item_id_list",
              "real_price": "goods_click_price_list",
              "real_seller_id": "goods_click_real_seller_id_list",
              "detail_page_view_time": "goods_detail_page_view_time_list",
              "seller_id": "goods_click_seller_id_list",
              "label": "goods_click_label_list",
              "uniform_spu_id": "goods_click_uniform_spu_id_list"},
      limit=1000
    ) \
    .ad_live_user_goods_click_v1(
      colossus_goods_id_attr="goods_click_item_id_list",
      colossus_timestamp_attr="goods_click_timestamp_list",
      colossus_category_attr="goods_click_category_list",
      colossus_host_attr="goods_click_seller_id_list",
      colossus_shop_attr="goods_click_real_seller_id_list",
      colossus_real_price_attr="goods_click_price_list",
      detail_page_view_time_attr="goods_detail_page_view_time_list",
      colossus_order_goods_id_attr="item_id_order_list",
      colossus_submit_timestamp_attr="create_order_time_order_list",
      colossus_pay_timestamp_attr= "pay_order_time_list",
      colossus_label_attr="goods_click_label_list",
      colossus_uniform_spu_id_attr="goods_click_uniform_spu_id_list",
      click_flow_type_attr="goods_click_flow_type_list",
      click_from_attr = "goods_click_click_from_list",
      output_slot_attr="ad_live_user_goods_click_v1_slot",
      output_sign_attr="ad_live_user_goods_click_v1_sign",
      slot_as_attr_name= True,
      use_murmur3hash64= True,
      use_ad_slot_size= True,
      limit_user_num=1000,
      slots_id= list(range(2001,2021)),
      mio_slots_id= list(range(2001,2021)),
      item_map_list=[[str(i),i,i,sizes[i-2001]]for i in range(2001,2021)],
      sign_prefix_bit_num_input=10,
      sign_prefix_bit_num_output=12,
              slot_as_attr_name_prefix= "ad_live_click_order_v1_"
    ) \
    .log_debug_info(
      print_all_common_attrs=True,
      print_all_item_attrs=True,
      for_debug_request_only=False,
      to="stdout"
    )
    ```
    会将获取的 item list 分字段存储在对应的 commonAttr 中
    """
    self._add_processor(AdLiveUserGoodsClickV1Enricher(kwargs))
    return self

  def ad_live_user_cycle_click_v1(self, **kwargs):
    """
    AdLiveUserCycleClickV1Enricher
    ------
    示例
    ------
    ① 常规用法：该方法效率最高
    ```
    .gsu_common_colossusv2_enricher(
      name='colossusv2_good_click_item',
      query_key="user_id",
      kconf="colossus.kconf_client.good_click_item",
      filter_future_items=True,
      seconds_to_lookback=60,
      item_fields={
              "category_a": "goods_click_category_list",
              "click_flow_type": "goods_click_flow_type_list",
              "click_from": "goods_click_click_from_list",
              "click_timestamp": "goods_click_timestamp_list",
              "item_id": "goods_click_item_id_list",
              "real_price": "goods_click_price_list",
              "real_seller_id": "goods_click_real_seller_id_list",
              "detail_page_view_time": "goods_detail_page_view_time_list",
              "seller_id": "goods_click_seller_id_list",
              "label": "goods_click_label_list",
              "uniform_spu_id": "goods_click_uniform_spu_id_list"},
      limit=1000
    ) \
    .ad_live_user_cycle_click_v1(
      colossus_goods_id_attr="goods_click_item_id_list",
      colossus_timestamp_attr="goods_click_timestamp_list",
      colossus_category_attr="goods_click_category_list",
      colossus_host_attr="goods_click_seller_id_list",
      colossus_shop_attr="goods_click_real_seller_id_list",
      colossus_real_price_attr="goods_click_price_list",
      detail_page_view_time_attr="goods_detail_page_view_time_list",
      colossus_label_attr="goods_click_label_list",
      colossus_uniform_spu_id_attr="goods_click_uniform_spu_id_list",
      click_flow_type_attr="goods_click_flow_type_list",
      click_from_attr = "goods_click_click_from_list",
      output_slot_attr="ad_live_user_cycle_click_v1_slot",
      output_sign_attr="ad_live_user_cycle_click_v1_sign",
      slot_as_attr_name= True,
      use_murmur3hash64= True,
      use_ad_slot_size= True,
      slots_id= list(range(2001,2015)),
      mio_slots_id= list(range(2001,2015)),
      item_map_list=[[str(i),i,i,10001]for i in range(2001,2015)],
      sign_prefix_bit_num_input=10,
      sign_prefix_bit_num_output=12,
      slot_as_attr_name_prefix= "ad_live_click_cycle_v1_"
    ) \
    .log_debug_info(
      print_all_common_attrs=True,
      print_all_item_attrs=True,
      for_debug_request_only=False,
      to="stdout"
    )
    ```
    会将获取的 item list 分字段存储在对应的 commonAttr 中
    """
    self._add_processor(AdLiveUserCycleClickV1Enricher(kwargs))
    return self

  def ad_live_user_goods_click_v2(self, **kwargs):
    """
    AdLiveUserGoodsClickV21Enricher
    ------
    示例
    ------
    ① 常规用法：该方法效率最高
    ```
    .gsu_common_colossusv2_enricher(
      name='colossusv2_good_order_item',
      # query_key="user_id",
      kconf="colossus.kconf_client.good_order_item",
      filter_future_items=True,
      seconds_to_lookback=60,
      item_fields={
              "create_order_time": "create_order_time_order_list",
              "item_id": "item_id_order_list",
              "pay_order_time":"pay_order_time_list"},
      query_key="user_id",
      limit=1
    ) \
    .gsu_common_colossusv2_enricher(
      name='colossusv2_good_click_item',
      query_key="user_id",
      kconf="colossus.kconf_client.good_click_item",
      filter_future_items=True,
      seconds_to_lookback=60,
      item_fields={
              "category_a": "goods_click_category_list",
              "click_flow_type": "goods_click_flow_type_list",
              "click_from": "goods_click_click_from_list",
              "click_timestamp": "goods_click_timestamp_list",
              "item_id": "goods_click_item_id_list",
              "real_price": "goods_click_price_list",
              "real_seller_id": "goods_click_real_seller_id_list",
              "detail_page_view_time": "goods_detail_page_view_time_list",
              "seller_id": "goods_click_seller_id_list",
              "label": "goods_click_label_list",
              "uniform_spu_id": "goods_click_uniform_spu_id_list"},
      limit=1000
    ) \
    .ad_live_user_goods_click_v2(
      colossus_goods_id_attr="goods_click_item_id_list",
      colossus_timestamp_attr="goods_click_timestamp_list",
      colossus_category_attr="goods_click_category_list",
      colossus_host_attr="goods_click_seller_id_list",
      colossus_shop_attr="goods_click_real_seller_id_list",
      colossus_real_price_attr="goods_click_price_list",
      detail_page_view_time_attr="goods_detail_page_view_time_list",
      colossus_order_goods_id_attr="item_id_order_list",
      colossus_submit_timestamp_attr="create_order_time_order_list",
      colossus_pay_timestamp_attr= "pay_order_time_list",
      colossus_label_attr="goods_click_label_list",
      colossus_uniform_spu_id_attr="goods_click_uniform_spu_id_list",
      click_flow_type_attr="goods_click_flow_type_list",
      click_from_attr = "goods_click_click_from_list",
      output_slot_attr="ad_live_user_goods_click_v1_slot",
      output_sign_attr="ad_live_user_goods_click_v1_sign",
      slot_as_attr_name= True,
      use_murmur3hash64= True,
      use_ad_slot_size= True,
      limit_user_num=1000,
      slots_id= list(range(2001,2021)),
      mio_slots_id= list(range(2001,2021)),
      item_map_list=[[str(i),i,i,sizes[i-2001]]for i in range(2001,2021)],
      sign_prefix_bit_num_input=10,
      sign_prefix_bit_num_output=12,
      output_dense_timestamp_attr="ad_live_user_goods_click_dense_timestamp",
              slot_as_attr_name_prefix= "ad_live_click_order_v1_"
    ) \
    .log_debug_info(
      print_all_common_attrs=True,
      print_all_item_attrs=True,
      for_debug_request_only=False,
      to="stdout"
    )
    ```
    会将获取的 item list 分字段存储在对应的 commonAttr 中
    """
    self._add_processor(AdLiveUserGoodsClickV2Enricher(kwargs))
    return self

  def ad_live_user_goods_click_v3(self, **kwargs):
    """
    AdLiveUserGoodsClickV21Enricher
    ------
    示例
    ------
    ① 常规用法：该方法效率最高
    ```
    .fake_retrieve(num=1, reason=999) \
    .enrich_attr_by_lua(
        function_for_item = "calculate",
        export_item_attr = ["explain_item_id",
        "explain_item_spu_id", "explain_item_brand_id", "explain_item_category_id","explain_item_category1_id","explain_item_category2_id","explain_item_category3_id","explain_item_category4_id","explain_item_min_price", "explain_item_quoted_price"],
        lua_script = '''
          function calculate(seq, item_key, reason, score)
            local explain_item_id = 1000
            local explain_item_spu_id = 1009
            local explain_item_brand_id = 1001
            local explain_item_category_id= 1002
            local explain_item_category1_id = 1003
            local explain_item_category2_id = 1004
            local explain_item_category3_id = 1005
            local explain_item_category4_id = 1006
            local explain_item_min_price = 1007
            local explain_item_quoted_price = 1008
            return explain_item_id,explain_item_spu_id, explain_item_brand_id, explain_item_category_id,explain_item_category1_id,explain_item_category2_id,explain_item_category3_id,explain_item_category4_id,explain_item_min_price, explain_item_quoted_price
          end
        '''
    ) \
    .gsu_common_colossusv2_enricher(
          name='colossusv2_good_order_item',
          # query_key="user_id",
          kconf="colossus.kconf_client.good_order_item",
          filter_future_items=True,
          seconds_to_lookback=60,
          item_fields={
                  "create_order_time": "create_order_time_order_list",
                  "item_id": "item_id_order_list",
                  "pay_order_time":"pay_order_time_list"},
          query_key="user_id",
          limit=1000
    ) \
    .gsu_common_colossusv2_enricher(
          name='colossusv2_good_click_item',
          query_key="user_id",
          kconf="colossus.kconf_client.good_click_item",
          filter_future_items=True,
          seconds_to_lookback=60,
          item_fields={
                  "category_a": "goods_click_category_list",
                  "click_flow_type": "goods_click_flow_type_list",
                  "click_from": "goods_click_click_from_list",
                  "click_timestamp": "goods_click_timestamp_list",
                  "item_id": "goods_click_item_id_list",
                  "real_price": "goods_click_price_list",
                  "real_seller_id": "goods_click_real_seller_id_list",
                  "detail_page_view_time": "goods_detail_page_view_time_list",
                  "seller_id": "goods_click_seller_id_list",
                  "label": "goods_click_label_list",
                  "uniform_spu_id": "goods_click_uniform_spu_id_list"},
          limit=1000
    ) \
    .ad_live_user_goods_click_v3(
          colossus_goods_id_attr="goods_click_item_id_list",
          colossus_timestamp_attr="goods_click_timestamp_list",
          colossus_category_attr="goods_click_category_list",
          colossus_host_attr="goods_click_seller_id_list",
          colossus_shop_attr="goods_click_real_seller_id_list",
          colossus_real_price_attr="goods_click_price_list",
          detail_page_view_time_attr="goods_detail_page_view_time_list",
          colossus_order_goods_id_attr="item_id_order_list",
          colossus_submit_timestamp_attr="create_order_time_order_list",
          colossus_pay_timestamp_attr= "pay_order_time_list",
          colossus_label_attr="goods_click_label_list",
          colossus_uniform_spu_id_attr="goods_click_uniform_spu_id_list",
          click_flow_type_attr="goods_click_flow_type_list",
          click_from_attr = "goods_click_click_from_list",
          output_slot_attr="ad_live_user_goods_click_v1_slot",
          output_sign_attr="ad_live_user_goods_click_v1_sign",
          explain_item_id_attr="explain_item_id",
          explain_item_spu_id_attr="explain_item_spu_id",
          explain_item_brand_id_attr="explain_item_brand_id",
          explain_item_category_id_attr="explain_item_category_id",
          explain_item_category1_id_attr="explain_item_category1_id",
          explain_item_category2_id_attr="explain_item_category2_id",
          explain_item_category3_id_attr="explain_item_category3_id",
          explain_item_category4_id_attr="explain_item_category4_id",
          explain_item_min_price_attr="explain_item_min_price",
          explain_item_quoted_price_attr="explain_item_quoted_price",
          output_dense_hour_weight_attr="ad_live_click_order_dense_hourweight",
          slot_as_attr_name= True,
          use_murmur3hash64= True,
          use_ad_slot_size= True,
          interest_weight=[10,10,1,5,2],
          limit_user_num=100,
          slots_id= list(range(2001,2022)),
          mio_slots_id= list(range(2001,2022)),
          item_map_list=[[str(i),i,i,sizes[i-2001]]for i in range(2001,2022)],
          sign_prefix_bit_num_input=10,
          sign_prefix_bit_num_output=12,
          output_dense_timestamp_attr="ad_live_user_goods_click_dense_timestamp",
                  slot_as_attr_name_prefix= "ad_live_click_order_v1_"
    ) \
    .log_debug_info(
      print_all_common_attrs=True,
      print_all_item_attrs=True,
      for_debug_request_only=False,
      to="stdout"
    )
    ```
    会将获取的 item list 分字段存储在对应的 commonAttr 中
    """
    self._add_processor(AdLiveUserGoodsClickV3Enricher(kwargs))
    return self

  def ad_live_user_goods_click_opt_v3(self, **kwargs):
    """
    AdLiveUserGoodsClickOptV3Enricher
    ------
    示例
    ------
    ① 常规用法：该方法效率最高
    ```
    .fake_retrieve(num=1, reason=999) \
    .enrich_attr_by_lua(
        function_for_item = "calculate",
        export_item_attr = ["explain_item_id",
        "explain_item_spu_id", "explain_item_brand_id", "explain_item_category_id","explain_item_category1_id","explain_item_category2_id","explain_item_category3_id","explain_item_category4_id","explain_item_min_price", "explain_item_quoted_price"],
        lua_script = '''
          function calculate(seq, item_key, reason, score)
            local explain_item_id = 1000
            local explain_item_spu_id = 1009
            local explain_item_brand_id = 1001
            local explain_item_category_id= 1002
            local explain_item_category1_id = 1003
            local explain_item_category2_id = 1004
            local explain_item_category3_id = 1005
            local explain_item_category4_id = 1006
            local explain_item_min_price = 1007
            local explain_item_quoted_price = 1008
            return explain_item_id,explain_item_spu_id, explain_item_brand_id, explain_item_category_id,explain_item_category1_id,explain_item_category2_id,explain_item_category3_id,explain_item_category4_id,explain_item_min_price, explain_item_quoted_price
          end
        '''
    ) \
    .gsu_common_colossusv2_enricher(
          name='colossusv2_good_order_item',
          # query_key="user_id",
          kconf="colossus.kconf_client.good_order_item",
          filter_future_items=True,
          seconds_to_lookback=60,
          item_fields={
                  "create_order_time": "create_order_time_order_list",
                  "item_id": "item_id_order_list",
                  "pay_order_time":"pay_order_time_list"},
          query_key="user_id",
          limit=1000
    ) \
    .gsu_common_colossusv2_enricher(
          name='colossusv2_good_click_item',
          query_key="user_id",
          kconf="colossus.kconf_client.good_click_item",
          filter_future_items=True,
          seconds_to_lookback=60,
          item_fields={
                  "category_a": "goods_click_category_list",
                  "click_flow_type": "goods_click_flow_type_list",
                  "click_from": "goods_click_click_from_list",
                  "click_timestamp": "goods_click_timestamp_list",
                  "item_id": "goods_click_item_id_list",
                  "real_price": "goods_click_price_list",
                  "real_seller_id": "goods_click_real_seller_id_list",
                  "detail_page_view_time": "goods_detail_page_view_time_list",
                  "seller_id": "goods_click_seller_id_list",
                  "label": "goods_click_label_list",
                  "uniform_spu_id": "goods_click_uniform_spu_id_list"},
          limit=1000
    ) \
    .ad_live_user_goods_click_opt_v3(
          colossus_goods_id_attr="goods_click_item_id_list",
          colossus_timestamp_attr="goods_click_timestamp_list",
          colossus_category_attr="goods_click_category_list",
          colossus_host_attr="goods_click_seller_id_list",
          colossus_shop_attr="goods_click_real_seller_id_list",
          colossus_real_price_attr="goods_click_price_list",
          detail_page_view_time_attr="goods_detail_page_view_time_list",
          colossus_order_goods_id_attr="item_id_order_list",
          colossus_submit_timestamp_attr="create_order_time_order_list",
          colossus_pay_timestamp_attr= "pay_order_time_list",
          colossus_label_attr="goods_click_label_list",
          colossus_uniform_spu_id_attr="goods_click_uniform_spu_id_list",
          click_flow_type_attr="goods_click_flow_type_list",
          click_from_attr = "goods_click_click_from_list",
          output_slot_attr="ad_live_user_goods_click_v1_slot",
          output_sign_attr="ad_live_user_goods_click_v1_sign",
          explain_item_id_attr="explain_item_id",
          explain_item_spu_id_attr="explain_item_spu_id",
          explain_item_brand_id_attr="explain_item_brand_id",
          explain_item_category_id_attr="explain_item_category_id",
          explain_item_category1_id_attr="explain_item_category1_id",
          explain_item_category2_id_attr="explain_item_category2_id",
          explain_item_category3_id_attr="explain_item_category3_id",
          explain_item_category4_id_attr="explain_item_category4_id",
          explain_item_min_price_attr="explain_item_min_price",
          explain_item_quoted_price_attr="explain_item_quoted_price",
          output_dense_hour_weight_attr="ad_live_click_order_dense_hourweight",
          slot_as_attr_name= True,
          use_murmur3hash64= True,
          use_ad_slot_size= True,
          interest_weight=[10,10,1,5,2],
          limit_user_num=100,
          slots_id= list(range(2001,2022)),
          mio_slots_id= list(range(2001,2022)),
          item_map_list=[[str(i),i,i,sizes[i-2001]]for i in range(2001,2022)],
          sign_prefix_bit_num_input=10,
          sign_prefix_bit_num_output=12,
          output_dense_timestamp_attr="ad_live_user_goods_click_dense_timestamp",
                  slot_as_attr_name_prefix= "ad_live_click_order_v1_"
    ) \
    .log_debug_info(
      print_all_common_attrs=True,
      print_all_item_attrs=True,
      for_debug_request_only=False,
      to="stdout"
    )
    ```
    会将获取的 item list 分字段存储在对应的 commonAttr 中
    """
    self._add_processor(AdLiveUserGoodsClickOptV3Enricher(kwargs))
    return self

  def ad_live_user_goods_click_opt_v2(self, **kwargs):
    """
    AdLiveUserGoodsClickOptV2Enricher
    ------
    示例
    ------
    ① 常规用法：该方法效率最高
    ```
    .fake_retrieve(num=1, reason=999) \
    .enrich_attr_by_lua(
        function_for_item = "calculate",
        export_item_attr = ["explain_item_id",
        "explain_item_spu_id", "explain_item_brand_id", "explain_item_category_id","explain_item_category1_id","explain_item_category2_id","explain_item_category3_id","explain_item_category4_id","explain_item_min_price", "explain_item_quoted_price"],
        lua_script = '''
          function calculate(seq, item_key, reason, score)
            local explain_item_id = 1000
            local explain_item_spu_id = 1009
            local explain_item_brand_id = 1001
            local explain_item_category_id= 1002
            local explain_item_category1_id = 1003
            local explain_item_category2_id = 1004
            local explain_item_category3_id = 1005
            local explain_item_category4_id = 1006
            local explain_item_min_price = 1007
            local explain_item_quoted_price = 1008
            return explain_item_id,explain_item_spu_id, explain_item_brand_id, explain_item_category_id,explain_item_category1_id,explain_item_category2_id,explain_item_category3_id,explain_item_category4_id,explain_item_min_price, explain_item_quoted_price
          end
        '''
    ) \
    .gsu_common_colossusv2_enricher(
          name='colossusv2_good_order_item',
          # query_key="user_id",
          kconf="colossus.kconf_client.good_order_item",
          filter_future_items=True,
          seconds_to_lookback=60,
          item_fields={
                  "create_order_time": "create_order_time_order_list",
                  "item_id": "item_id_order_list",
                  "pay_order_time":"pay_order_time_list"},
          query_key="user_id",
          limit=1000
    ) \
    .gsu_common_colossusv2_enricher(
          name='colossusv2_good_click_item',
          query_key="user_id",
          kconf="colossus.kconf_client.good_click_item",
          filter_future_items=True,
          seconds_to_lookback=60,
          item_fields={
                  "category_a": "goods_click_category_list",
                  "click_flow_type": "goods_click_flow_type_list",
                  "click_from": "goods_click_click_from_list",
                  "click_timestamp": "goods_click_timestamp_list",
                  "item_id": "goods_click_item_id_list",
                  "real_price": "goods_click_price_list",
                  "real_seller_id": "goods_click_real_seller_id_list",
                  "detail_page_view_time": "goods_detail_page_view_time_list",
                  "seller_id": "goods_click_seller_id_list",
                  "label": "goods_click_label_list",
                  "uniform_spu_id": "goods_click_uniform_spu_id_list"},
          limit=1000
    ) \
    .ad_live_user_goods_click_opt_v2(
          colossus_goods_id_attr="goods_click_item_id_list",
          colossus_timestamp_attr="goods_click_timestamp_list",
          colossus_category_attr="goods_click_category_list",
          colossus_host_attr="goods_click_seller_id_list",
          colossus_shop_attr="goods_click_real_seller_id_list",
          colossus_real_price_attr="goods_click_price_list",
          detail_page_view_time_attr="goods_detail_page_view_time_list",
          colossus_order_goods_id_attr="item_id_order_list",
          colossus_submit_timestamp_attr="create_order_time_order_list",
          colossus_pay_timestamp_attr= "pay_order_time_list",
          colossus_label_attr="goods_click_label_list",
          colossus_uniform_spu_id_attr="goods_click_uniform_spu_id_list",
          click_flow_type_attr="goods_click_flow_type_list",
          click_from_attr = "goods_click_click_from_list",
          output_slot_attr="ad_live_user_goods_click_v1_slot",
          output_sign_attr="ad_live_user_goods_click_v1_sign",
          explain_item_id_attr="explain_item_id",
          explain_item_spu_id_attr="explain_item_spu_id",
          explain_item_brand_id_attr="explain_item_brand_id",
          explain_item_category_id_attr="explain_item_category_id",
          explain_item_category1_id_attr="explain_item_category1_id",
          explain_item_category2_id_attr="explain_item_category2_id",
          explain_item_category3_id_attr="explain_item_category3_id",
          explain_item_category4_id_attr="explain_item_category4_id",
          explain_item_min_price_attr="explain_item_min_price",
          explain_item_quoted_price_attr="explain_item_quoted_price",
          output_dense_hour_weight_attr="ad_live_click_order_dense_hourweight",
          output_dense_cat_score_attr="ad_live_click_order_dense_catscore",
          slot_as_attr_name= True,
          use_murmur3hash64= True,
          use_ad_slot_size= True,
          interest_weight=[10,10,1,5,2],
          limit_user_num=100,
          slots_id= list(range(2001,2022)),
          mio_slots_id= list(range(2001,2022)),
          item_map_list=[[str(i),i,i,sizes[i-2001]]for i in range(2001,2022)],
          sign_prefix_bit_num_input=10,
          sign_prefix_bit_num_output=12,
          output_dense_timestamp_attr="ad_live_user_goods_click_dense_timestamp",
                  slot_as_attr_name_prefix= "ad_live_click_order_v1_"
    ) \
    .log_debug_info(
      print_all_common_attrs=True,
      print_all_item_attrs=True,
      for_debug_request_only=False,
      to="stdout"
    )
    ```
    会将获取的 item list 分字段存储在对应的 commonAttr 中
    """
    self._add_processor(AdLiveUserGoodsClickOptV2Enricher(kwargs))
    return self

  def ad_live_user_goods_click_opt_v1(self, **kwargs):
    """
    AdLiveUserGoodsClickOptV1Enricher
    """
    self._add_processor(AdLiveUserGoodsClickOptV1Enricher(kwargs))
    return self

  def ad_live_user_goods_click_opt_v4(self, **kwargs):
    """
    AdLiveUserGoodsClickOptV4Enricher
    """
    self._add_processor(AdLiveUserGoodsClickOptV4Enricher(kwargs))
    return self

  def ad_live_user_goods_click_clock(self, **kwargs):
    """
    AdLiveUserGoodsClickClockEnricher
    """
    self._add_processor(AdLiveUserGoodsClickClockEnricher(kwargs))
    return self

  def seq_sideinfo_enricher(self, **kwargs):
    """
    SeqSideinfoEnricher
    ------
    序列侧信息enricher，用于处理用户行为序列的侧信息，包括类目、停留时间、价格等特征的提取和分桶

    参数配置
    ------
    输入属性配置:
    `input_category`: [string] 类目列表属性名，用于提取1-3级类目
    `input_detail_content_stay_time`: [string] 详情页停留时间列表属性名
    `input_timestamp`: [string] 时间戳列表属性名，用于计算时间间隔
    `input_price`: [string] 价格列表属性名，用于价格分桶
    `input_click_flow_type`: [string] 点击流类型属性名，用于提取流类型特征
    `input_label`: [string] 标签属性名，用于提取订单、购物车、交互等标签
    `input_item_id`: [string] 物品ID列表属性名，用于计算count_index，默认为空

    输出属性配置:
    `output_cate1`: [string] 1级类目输出属性名，默认"cate1_list"
    `output_cate2`: [string] 2级类目输出属性名，默认"cate2_list"
    `output_cate3`: [string] 3级类目输出属性名，默认"cate3_list"
    `output_stay_time_bucket`: [string] 停留时间分桶输出属性名，默认"stay_time_bucket_list"
    `output_day_gap`: [string] 天数间隔输出属性名，默认为空，未分桶
    `output_hour_gap`: [string] 小时间隔输出属性名，默认为空，未分桶
    `output_hour_gap_v1`: [string] 小时间隔V1版本输出属性名，默认为空，非线性分桶
    `output_minute_gap`: [string] 分钟间隔输出属性名，默认为空，非线性分桶
    `output_price_bucket`: [string] 价格分桶输出属性名，默认"price_bucket_list"
    `output_flow_type`: [string] 流类型输出属性名，默认"flow_type_list"
    `output_order`: [string] 订单标签输出属性名，默认"order_list"
    `output_shopping`: [string] 购物车标签输出属性名，默认"shopping_list"
    `output_interaction`: [string] 交互标签输出属性名，默认"interaction_list"
    `output_index`: [string] 索引序列输出属性名，根据category_list长度生成1到N的索引，默认为空
    `output_count_index`: [string] 计数索引输出属性名，计算每个位置的count_index，默认为空

    调用示例
    ------
    ``` python
    .seq_sideinfo_enricher(
        input_category="category_list",
        input_detail_content_stay_time="stay_time_list",
        input_timestamp="timestamp_list",
        input_price="price_list",
        input_click_flow_type="flow_type_list",
        input_label="label_list",
        input_item_id="item_id_list",
        output_cate1="cate1_features",
        output_cate2="cate2_features",
        output_cate3="cate3_features",
        output_stay_time_bucket="stay_time_buckets",
        output_day_gap="day_gaps",
        output_hour_gap="hour_gaps",
        output_hour_gap_v1="hour_gaps_v1",
        output_minute_gap="minute_gaps",
        output_price_bucket="price_buckets",
        output_flow_type="flow_types",
        output_order="order_labels",
        output_shopping="shopping_labels",
        output_interaction="interaction_labels",
        output_index="index_list",
        output_count_index="count_index_list"
    )
    ```
    """
    self._add_processor(SeqSideinfoEnricher(kwargs))
    return self

  def seq_cate_gsu_enricher(self, **kwargs):
    """
    SeqCateGsuEnricher
    ------
    根据item的category属性从sideinfo属性列表中提取对应的GSU特征

    参数配置
    ------
    `category_list`: [str] 必配项，category列表的属性名

    `sideinfo_attrs`: [list] 必配项，输入的sideinfo属性名列表

    `output_sideinfo_attrs`: [list] 必配项，输出的sideinfo属性名列表，与sideinfo_attrs一一对应

    `limit_num`: [int] 可选项，限制提取的元素数量，默认50

    `cate1_attr`: [str] 可选项，item的cate1属性名，默认"iCate1Id"

    `cate2_attr`: [str] 可选项，item的cate2属性名，默认"iCate2Id"

    `cate3_attr`: [str] 可选项，item的cate3属性名，默认"iCate3Id"

    `cate1_match_attr`: [bool] 可选项，是否匹配cate1，默认true

    `cate2_match_attr`: [bool] 可选项，是否匹配cate2，默认true

    `cate3_match_attr`: [bool] 可选项，是否匹配cate3，默认true

    调用示例
    ------
    ``` python
    .seq_cate_gsu_enricher(
      category_list="category_list",
      sideinfo_attrs=["timestamp_list", "seller_id_list", "price_list"],
      output_sideinfo_attrs=["gsu_timestamp_list", "gsu_seller_id_list", "gsu_price_list"],
      limit_num=100,
      cate1_match_attr=True,
      cate2_match_attr=True,
      cate3_match_attr=True
    )
    ```
    """
    self._add_processor(SeqCateGsuEnricher(kwargs))
    return self
