#!/usr/bin/env python3
# coding=utf-8

import operator
import itertools

from ...common_leaf_util import strict_types, extract_common_attrs, gen_attr_name_with_common_attr_channel
from ...common_leaf_processor import LeafEnricher


class CommonLiveColossusGiftAuthorFeatureRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_colossus_gift_author_feature_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        #"author_id", "vec_time", "vec_reward", "author_size", "user_author_size",
        "u_total_reward_amt_attr", "u_total_reward_times_attr", "u_total_reward_days_attr",
        "ua_first_play_day_lag_attr", "ua_last_play_day_lag_attr", "ua_first_reward_day_lag_attr",
        "ua_last_reward_day_lag_attr", "ua_total_reward_times_attr", "ua_total_reward_days_attr",
        "ua_total_reward_amt_attr", "ua_avg_live_reward_amt_attr", "ua_max_live_reward_amt_attr",
        "ua_min_live_reward_amt_attr", "ua_avg_daily_reward_amt_attr", "ua_max_daily_reward_amt_attr",
        "ua_min_daily_reward_amt_attr", "ua_watch_live_cnt_attr", "ua_watch_live_day_attr",
        "ua_live_reward_rate_attr",
    }

class CommonLiveA2AFeatureV1RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_a2a_feature_v1_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    pre_str = "" 
    if "pre_str" in self._config:
      pre_str = self._config["pre_str"]
    return {
        pre_str + key for key in 
        ["ua_a2a_reward_amt_1d_attr", "ua_a2a_play_times_1d_attr", "ua_a2a_watch_live_times_1d_attr",
        "ua_a2a_reward_amt_3d_attr", "ua_a2a_play_times_3d_attr", "ua_a2a_watch_live_times_3d_attr",
        "ua_a2a_reward_amt_7d_attr", "ua_a2a_play_times_7d_attr", "ua_a2a_watch_live_times_7d_attr",
        "ua_a2a_reward_amt_14d_attr", "ua_a2a_play_times_14d_attr", "ua_a2a_watch_live_times_14d_attr",
        "ua_a2a_reward_amt_28d_attr", "ua_a2a_play_times_28d_attr", "ua_a2a_watch_live_times_28d_attr",
        "ua_vec_a2a_author_id_attr", "ua_vec_a2a_reward_amt_attr", "ua_vec_a2a_play_times_attr"]
    }

class CommonLiveColossusGiftAuthorFeatureV2RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_colossus_gift_author_feature_v2_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        "ua_first_play_day_lag_attr", "ua_last_play_day_lag_attr", "ua_first_reward_day_lag_attr",
        "ua_last_reward_day_lag_attr", "ua_total_reward_times_attr", "ua_total_reward_days_attr",
        "ua_total_reward_amt_attr", "ua_avg_live_reward_amt_attr", "ua_max_live_reward_amt_attr",
        "ua_min_live_reward_amt_attr", "ua_avg_daily_reward_amt_attr", "ua_max_daily_reward_amt_attr",
        "ua_min_daily_reward_amt_attr", "ua_watch_live_cnt_attr", "ua_watch_live_day_attr",
        "ua_live_reward_rate_attr",
        "ua_reward_amt_1d_attr", "ua_play_times_1d_attr", "ua_watch_live_times_1d_attr",
        "ua_reward_amt_3d_attr", "ua_play_times_3d_attr", "ua_watch_live_times_3d_attr",
        "ua_reward_amt_7d_attr", "ua_play_times_7d_attr", "ua_watch_live_times_7d_attr",
        "ua_reward_amt_14d_attr", "ua_play_times_14d_attr", "ua_watch_live_times_14d_attr",
        "ua_reward_amt_28d_attr", "ua_play_times_28d_attr", "ua_watch_live_times_28d_attr",
        "ua_reward_rate_in_all_attr",
        "u_total_reward_amt_attr", "u_total_reward_times_attr", "u_total_reward_days_attr",
        "u_total_reward_amt_1d_attr", "u_total_play_times_1d_attr", "u_total_watch_live_times_1d_attr",
        "u_total_reward_amt_3d_attr", "u_total_play_times_3d_attr", "u_total_watch_live_times_3d_attr",
        "u_total_reward_amt_7d_attr", "u_total_play_times_7d_attr", "u_total_watch_live_times_7d_attr",
        "u_total_reward_amt_14d_attr", "u_total_play_times_14d_attr", "u_total_watch_live_times_14d_attr",
        "u_total_reward_amt_28d_attr", "u_total_play_times_28d_attr", "u_total_watch_live_times_28d_attr",
        "ua_first_reward_hour_lag_attr", "ua_last_reward_hour_lag_attr",
        "u_vec_reward_author_id_attr", "u_vec_reward_amt_attr","u_vec_play_duration_attr",
        "u_vec_time_lag_attr", "u_vec_hetu_tag_attr",
        "u_vec_play_duration_v2_attr", "u_vec_hetu_tag_v2_attr",
        "u_reward_1d_attr", "u_reward_2d_attr", "u_reward_3d_attr", "u_reward_4d_attr",
        "u_reward_5d_attr", "u_reward_6d_attr", "u_reward_7d_attr", "u_reward_8d_attr",
        "u_reward_9d_attr", "u_reward_10d_attr", "u_reward_11d_attr", "u_reward_12d_attr",
        "u_reward_13d_attr", "u_reward_14d_attr",
    }


class LiveSegmentExtractEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_segment_extract_enricher"

  @strict_types
  def depend_on_items(self) -> bool:
    return True

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["input_int_attr"])
    ret.add(self._config["pid_list"])
    return ret
  
  @property
  @strict_types
  def output_item_v2_attrs(self) -> set:
    return {
        "live_id_tiny",
        "timestamp_tiny",
        "cluster_2_3_tiny",
        "cluster_4_tiny"
    }


class UserAuthorFollowtimeRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "user_author_followtime_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["author_id_attr"])
    ret.add(self._config["user_id_attr"])

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        "followtime",
    }

class CommonLiveColossusGiftAuthorFeatureV3RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_colossus_gift_author_feature_v3_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])

    for attr_name in ['ua_ltv_reward_decay_rate', 'ua_ltv_time_decay_rate', 
                      'ua_ltv_watch_time_window','ua_ltv_reward_time_window']:
      if attr_name in self._config:
        ret.add(self._config[attr_name])

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        #"author_id", "vec_time", "vec_reward", "author_size", "user_author_size",
        "u_total_reward_amt_attr", "u_total_reward_times_attr", "u_total_reward_days_attr",
        "u_max_reward_attr", "u_min_reward_attr", "u_avg_reward_attr",
        "ua_first_play_day_lag_attr", "ua_last_play_day_lag_attr", "ua_first_reward_day_lag_attr",
        "ua_last_reward_day_lag_attr", "ua_total_reward_times_attr", "ua_total_reward_days_attr",
        "ua_total_reward_amt_attr", "ua_avg_live_reward_amt_attr", "ua_max_live_reward_amt_attr",
        "ua_min_live_reward_amt_attr", "ua_avg_daily_reward_amt_attr", "ua_max_daily_reward_amt_attr",
        "ua_min_daily_reward_amt_attr", "ua_watch_live_cnt_attr", "ua_watch_live_day_attr",
        "ua_live_reward_rate_attr",
        "u_total_reward_amt_1d_attr", "u_total_play_times_1d_attr", "u_total_watch_live_times_1d_attr",
        "u_total_reward_amt_3d_attr", "u_total_play_times_3d_attr", "u_total_watch_live_times_3d_attr",
        "u_total_reward_amt_7d_attr", "u_total_play_times_7d_attr", "u_total_watch_live_times_7d_attr",
        "u_total_reward_amt_14d_attr", "u_total_play_times_14d_attr", "u_total_watch_live_times_14d_attr",
        "u_total_reward_amt_28d_attr", "u_total_play_times_28d_attr", "u_total_watch_live_times_28d_attr",
        "ua_reward_amt_1d_attr", "ua_play_times_1d_attr", "ua_watch_live_times_1d_attr",
        "ua_reward_amt_3d_attr", "ua_play_times_3d_attr", "ua_watch_live_times_3d_attr",
        "ua_reward_amt_7d_attr", "ua_play_times_7d_attr", "ua_watch_live_times_7d_attr",
        "ua_reward_amt_14d_attr", "ua_play_times_14d_attr", "ua_watch_live_times_14d_attr",
        "ua_reward_amt_28d_attr", "ua_play_times_28d_attr", "ua_watch_live_times_28d_attr",
        "ua_reward_rate_in_all_attr", "u_total_watch_live_day_1d_attr",
        "u_total_watch_live_day_3d_attr", "u_total_watch_live_day_7d_attr",
        "u_total_watch_live_day_14d_attr", "u_total_watch_live_day_28d_attr",
        "u_reward_days_1d_attr", "u_reward_days_3d_attr", "u_reward_days_7d_attr",
        "u_reward_days_14d_attr", "u_reward_days_28d_attr", "ua_watch_live_times_attr",
        "ua_avg_watch_live_times_attr", "ua_max_watch_live_times_attr",
        "ua_min_watch_live_times_attr", "ua_avg_daily_watch_live_times_attr",
        "ua_max_daily_watch_live_times_attr", "ua_min_daily_watch_live_times_attr",
        "ua_first_reward_hour_lag_attr", "ua_last_reward_hour_lag_attr",
        # 合并v2新增特征
        "u_reward_10d_attr", "u_reward_11d_attr", "u_reward_12d_attr", "u_reward_13d_attr",
        "u_reward_14d_attr", "u_reward_1d_attr", "u_reward_2d_attr", "u_reward_3d_attr",
        "u_reward_4d_attr", "u_reward_5d_attr", "u_reward_6d_attr", "u_reward_7d_attr",
        "u_reward_8d_attr", "u_reward_9d_attr", "u_vec_hetu_tag_attr",
        "u_vec_play_duration_attr", "u_vec_reward_amt_attr", "u_vec_reward_author_id_attr",
        "u_vec_time_lag_attr",
        # 新增更长期的打赏金额和付费用户字段
        "u_total_reward_amt_42d_attr", "u_total_reward_amt_56d_attr", "u_total_reward_amt_70d_attr",
        "u_total_reward_amt_84d_attr", "gift_user_flag_attr",
        # 增加短期打赏list特征
        "u_vec_play_duration_v2_attr", "u_vec_hetu_tag_v2_attr", "u_vec_reward_count_attr",
        "u_vec_page_type_attr", "u_vec_frame_cluster_id_attr",
        # 新增个性化label
        "u_reward_amt_p50_attr", "u_reward_amt_p75_attr", "u_reward_amt_list_size_attr",
        "u_play_time_p50_attr", "u_play_time_p75_attr", "u_play_time_list_size_attr", "u_reward_amt_pn_90d_attr",
        # 当天是否观看直播和当天photolive曝光条数
        "u_is_liveplay_today_gmt8_attr", "u_photolive_play_cnt_today_gmt8_attr",
        "ua_ltv_windowed_watch_time_attr", "ua_ltv_windowed_play_times_attr",
        "ua_ltv_windowed_reward_gt60_times_attr", "ua_ltv_windowed_reward_times_attr",
        "ua_ltv_windowed_reward_amt_attr", "ua_ltv_windowed_reward_rate_attr", "ua_ltv_windowed_avg_watch_time_attr"
    }

class CommonLiveLifelongSeqEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_lifelong_seq_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])

    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
       "user_latest_aid_list_attr",
       "user_latest_play_time_seconds_list_attr", "user_latest_play_time_minutes_list_attr",
       "user_latest_reward_amt_list_attr", "user_latest_reward_count_list_attr",
       "user_latest_time_gap_list_attr", "user_latest_time_gap_v2_list_attr",
       "user_latest_hetu_tag_list_attr", "user_latest_aid_padding_list_attr",
       "user_latest_page_type_list_attr", "user_latest_action_list_attr",
       "user_latest_aid_and_action_list_attr", "user_latest_action_hour_list_attr",
       "user_latest_action_padding_list_attr", "user_latest_hour_list_attr"
    }


  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        "ua_latest_aid_list_attr",
        "ua_latest_play_time_seconds_list_attr", "ua_latest_play_time_minutes_list_attr",
        "ua_latest_reward_amt_list_attr", "ua_latest_reward_count_list_attr",
        "ua_latest_time_gap_list_attr", "ua_latest_time_gap_v2_list_attr",
        "ua_latest_hetu_tag_list_attr",
        "ua_latest_page_type_list_attr", "ua_latest_action_list_attr",
        "ua_latest_aid_and_action_list_attr", "ua_latest_action_hour_list_attr",
        "ua_latest_action_padding_list_attr", "ua_latest_hour_list_attr"
    }

class CommonLiveLifelongSeqV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_lifelong_seq_v2_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        "ua_latest_aid_list_v2_attr", "ua_latest_time_gap_list_v2_attr",
        "ua_latest_play_time_seconds_list_v2_attr", "ua_latest_play_time_minutes_list_v2_attr",
        "ua_latest_reward_amt_list_v2_attr", "ua_latest_reward_count_list_v2_attr",
        "ua_latest_resume_action_padding_list_v2_attr", "ua_latest_revenue_action_padding_list_v2_attr",
        "ua_latest_seq_length_v2_attr", "ua_synthesis_time_gap_list_v2_attr",
        "ua_latest_aid_count_list_v2_attr", "ua_is_the_first_item_v2_attr",
        "ua_latest_is_llvtr_list_v2_attr", "ua_latest_is_lvtr_list_v2_attr",
        "ua_latest_is_evtr_list_v2_attr", "ua_latest_is_svr_list_v2_attr"
    }


class CommonLiveLifelongSeqV4Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_lifelong_seq_v4_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])

    return ret


  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        "ua_latest_aid_list_v4_attr", "ua_latest_time_gap_list_v4_attr",
        "ua_latest_play_time_seconds_list_v4_attr", "ua_latest_play_time_minutes_list_v4_attr",
        "ua_latest_reward_amt_list_v4_attr", "ua_latest_reward_count_list_v4_attr",
        "ua_latest_resume_action_padding_list_v4_attr", "ua_latest_revenue_action_padding_list_v4_attr",
        "ua_latest_seq_length_v4_attr", "ua_is_the_first_item_v4_attr", "ua_latest_is_like_list_v4_attr", 
        "ua_latest_is_from_follow_page_list_v4_attr", "ua_latest_is_comment_list_v4_attr",
        "ua_latest_is_gift_list_v4_attr", "ua_latest_is_profile_list_v4_attr", "ua_latest_is_forward_list_v4_attr",
        "ua_latest_is_report_list_v4_attr", "ua_latest_is_follow_list_v4_attr", "ua_latest_is_unlike_list_v4_attr",
        "ua_latest_is_unfollow_list_v4_attr", "ua_latest_is_click_gift_button_list_v4_attr", 
        "ua_action_first_time_count_v4_attr", "ua_action_first_action_count_v4_attr",
        "ua_latest_is_llvtr_list_v4_attr", "ua_latest_is_lvtr_list_v4_attr",
        "ua_latest_is_evtr_list_v4_attr", "ua_latest_is_svr_list_v4_attr", "ua_latest_is_midvr_list_v4_attr",
        "ua_latest_aid_count_list_v4_attr", "ua_latest_is_watch_pk_list_v4_attr"
    }


class CommonLiveUserSeqEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_user_seq"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret


  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
        "u_latest_play_time_list_attr", "u_latest_reward_amt_list_attr",
        "u_latest_reward_count_list_attr", "u_latest_time_lag_hour_list_attr", "u_latest_time_lag_day_list_attr",
        "u_latest_channel_list_attr", "u_latest_aid_list_attr",
        "u_latest_pid_list_attr", "u_latest_pid_list_attr_v2", "u_furthest_time_lag_attr",
        "u_latest_time_lag_day_list_attr", "u_latest_tag_list_attr",
        "u_latest_auto_play_time_list_attr", "u_latest_time_stamp_list_attr",
        "u_latest_time_lag_hour_attr", "u_latest_time_lag_day_attr", "u_latest_time_lag_list_attr_v2"
    }


class CommonLiveMultiFeedbackRFMRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_multi_feedback_rfm_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        "u_total_reward_times_1h_mf_attr", "u_total_reward_times_3h_mf_attr",
        "u_total_reward_times_6h_mf_attr", "u_total_reward_times_12h_mf_attr",
        "u_total_reward_times_nd_mf_attr", "u_total_reward_times_1d_mf_attr",
        "u_total_reward_times_3d_mf_attr", "u_total_reward_times_7d_mf_attr",
        "u_total_reward_times_14d_mf_attr", "u_total_reward_times_28d_mf_attr",
        "u_total_big_reward_times_1h_mf_attr", "u_total_big_reward_times_3h_mf_attr",
        "u_total_big_reward_times_6h_mf_attr", "u_total_big_reward_times_12h_mf_attr",
        "u_total_big_reward_times_nd_mf_attr", "u_total_big_reward_times_1d_mf_attr",
        "u_total_big_reward_times_3d_mf_attr", "u_total_big_reward_times_7d_mf_attr",
        "u_total_big_reward_times_14d_mf_attr", "u_total_big_reward_times_28d_mf_attr",
        "u_total_long_view_1h_mf_attr", "u_total_long_view_3h_mf_attr",
        "u_total_long_view_6h_mf_attr", "u_total_long_view_12h_mf_attr",
        "u_total_long_view_nd_mf_attr", "u_total_long_view_1d_mf_attr",
        "u_total_long_view_3d_mf_attr", "u_total_long_view_7d_mf_attr",
        "u_total_long_view_14d_mf_attr", "u_total_long_view_28d_mf_attr",
        "u_total_short_view_1h_mf_attr", "u_total_short_view_3h_mf_attr",
        "u_total_short_view_6h_mf_attr", "u_total_short_view_12h_mf_attr",
        "u_total_short_view_nd_mf_attr", "u_total_short_view_1d_mf_attr",
        "u_total_short_view_3d_mf_attr", "u_total_short_view_7d_mf_attr",
        "u_total_short_view_14d_mf_attr", "u_total_short_view_28d_mf_attr",
        "u_total_reward_amt_1h_mf_attr", "u_total_reward_amt_3h_mf_attr",
        "u_total_reward_amt_6h_mf_attr", "u_total_reward_amt_12h_mf_attr",
        "u_total_reward_amt_nd_mf_attr", "u_total_reward_amt_1d_mf_attr",
        "u_total_reward_amt_3d_mf_attr", "u_total_reward_amt_7d_mf_attr",
        "u_total_reward_amt_14d_mf_attr", "u_total_reward_amt_28d_mf_attr",
        "u_total_watch_time_1h_mf_attr", "u_total_watch_time_3h_mf_attr",
        "u_total_watch_time_6h_mf_attr", "u_total_watch_time_12h_mf_attr",
        "u_total_watch_time_nd_mf_attr", "u_total_watch_time_1d_mf_attr",
        "u_total_watch_time_3d_mf_attr", "u_total_watch_time_7d_mf_attr",
        "u_total_watch_time_14d_mf_attr", "u_total_watch_time_28d_mf_attr",
        "u_total_play_times_1h_mf_attr", "u_total_play_times_3h_mf_attr",
        "u_total_play_times_6h_mf_attr", "u_total_play_times_12h_mf_attr",
        "u_total_play_times_nd_mf_attr", "u_total_play_times_1d_mf_attr",
        "u_total_play_times_3d_mf_attr", "u_total_play_times_7d_mf_attr",
        "u_total_play_times_14d_mf_attr", "u_total_play_times_28d_mf_attr",

        "u_total_gtr_1h_pxtr_attr", "u_total_gtr_3h_pxtr_attr",
        "u_total_gtr_6h_pxtr_attr", "u_total_gtr_12h_pxtr_attr",
        "u_total_gtr_nd_pxtr_attr", "u_total_gtr_1d_pxtr_attr",
        "u_total_gtr_3d_pxtr_attr", "u_total_gtr_7d_pxtr_attr",
        "u_total_gtr_14d_pxtr_attr", "u_total_gtr_28d_pxtr_attr",
        "u_total_gtr_big_1h_pxtr_attr", "u_total_gtr_big_3h_pxtr_attr",
        "u_total_gtr_big_6h_pxtr_attr", "u_total_gtr_big_12h_pxtr_attr",
        "u_total_gtr_big_nd_pxtr_attr", "u_total_gtr_big_1d_pxtr_attr",
        "u_total_gtr_big_3d_pxtr_attr", "u_total_gtr_big_7d_pxtr_attr",
        "u_total_gtr_big_14d_pxtr_attr", "u_total_gtr_big_28d_pxtr_attr",
        "u_total_lvtr_1h_pxtr_attr", "u_total_lvtr_3h_pxtr_attr",
        "u_total_lvtr_6h_pxtr_attr", "u_total_lvtr_12h_pxtr_attr",
        "u_total_lvtr_nd_pxtr_attr", "u_total_lvtr_1d_pxtr_attr",
        "u_total_lvtr_3d_pxtr_attr", "u_total_lvtr_7d_pxtr_attr",
        "u_total_lvtr_14d_pxtr_attr", "u_total_lvtr_28d_pxtr_attr",
        "u_total_svr_1h_pxtr_attr", "u_total_svr_3h_pxtr_attr",
        "u_total_svr_6h_pxtr_attr", "u_total_svr_12h_pxtr_attr",
        "u_total_svr_nd_pxtr_attr", "u_total_svr_1d_pxtr_attr",
        "u_total_svr_3d_pxtr_attr", "u_total_svr_7d_pxtr_attr",
        "u_total_svr_14d_pxtr_attr", "u_total_svr_28d_pxtr_attr",
        "u_total_gvalue_1h_pxtr_attr", "u_total_gvalue_3h_pxtr_attr",
        "u_total_gvalue_6h_pxtr_attr", "u_total_gvalue_12h_pxtr_attr",
        "u_total_gvalue_nd_pxtr_attr", "u_total_gvalue_1d_pxtr_attr",
        "u_total_gvalue_3d_pxtr_attr", "u_total_gvalue_7d_pxtr_attr",
        "u_total_gvalue_14d_pxtr_attr", "u_total_gvalue_28d_pxtr_attr",
        "u_total_watch_time_1h_pxtr_attr", "u_total_watch_time_3h_pxtr_attr",
        "u_total_watch_time_6h_pxtr_attr", "u_total_watch_time_12h_pxtr_attr",
        "u_total_watch_time_nd_pxtr_attr", "u_total_watch_time_1d_pxtr_attr",
        "u_total_watch_time_3d_pxtr_attr", "u_total_watch_time_7d_pxtr_attr",
        "u_total_watch_time_14d_pxtr_attr", "u_total_watch_time_28d_pxtr_attr",

        "ua_reward_times_1h_mf_attr", "ua_reward_times_3h_mf_attr",
        "ua_reward_times_6h_mf_attr", "ua_reward_times_12h_mf_attr",
        "ua_reward_times_nd_mf_attr", "ua_reward_times_1d_mf_attr",
        "ua_reward_times_3d_mf_attr", "ua_reward_times_7d_mf_attr",
        "ua_reward_times_14d_mf_attr", "ua_reward_times_28d_mf_attr",
        "ua_big_reward_times_1h_mf_attr", "ua_big_reward_times_3h_mf_attr",
        "ua_big_reward_times_6h_mf_attr", "ua_big_reward_times_12h_mf_attr",
        "ua_big_reward_times_nd_mf_attr", "ua_big_reward_times_1d_mf_attr",
        "ua_big_reward_times_3d_mf_attr", "ua_big_reward_times_7d_mf_attr",
        "ua_big_reward_times_14d_mf_attr", "ua_big_reward_times_28d_mf_attr",
        "ua_long_view_1h_mf_attr", "ua_long_view_3h_mf_attr",
        "ua_long_view_6h_mf_attr", "ua_long_view_12h_mf_attr",
        "ua_long_view_nd_mf_attr", "ua_long_view_1d_mf_attr",
        "ua_long_view_3d_mf_attr", "ua_long_view_7d_mf_attr",
        "ua_long_view_14d_mf_attr", "ua_long_view_28d_mf_attr",
        "ua_short_view_1h_mf_attr", "ua_short_view_3h_mf_attr",
        "ua_short_view_6h_mf_attr", "ua_short_view_12h_mf_attr",
        "ua_short_view_nd_mf_attr", "ua_short_view_1d_mf_attr",
        "ua_short_view_3d_mf_attr", "ua_short_view_7d_mf_attr",
        "ua_short_view_14d_mf_attr", "ua_short_view_28d_mf_attr",
        "ua_reward_amt_1h_mf_attr", "ua_reward_amt_3h_mf_attr",
        "ua_reward_amt_6h_mf_attr", "ua_reward_amt_12h_mf_attr",
        "ua_reward_amt_nd_mf_attr", "ua_reward_amt_1d_mf_attr",
        "ua_reward_amt_3d_mf_attr", "ua_reward_amt_7d_mf_attr",
        "ua_reward_amt_14d_mf_attr", "ua_reward_amt_28d_mf_attr",
        "ua_watch_time_1h_mf_attr", "ua_watch_time_3h_mf_attr",
        "ua_watch_time_6h_mf_attr", "ua_watch_time_12h_mf_attr",
        "ua_watch_time_nd_mf_attr", "ua_watch_time_1d_mf_attr",
        "ua_watch_time_3d_mf_attr", "ua_watch_time_7d_mf_attr",
        "ua_watch_time_14d_mf_attr", "ua_watch_time_28d_mf_attr",
        "ua_play_times_1h_mf_attr", "ua_play_times_3h_mf_attr",
        "ua_play_times_6h_mf_attr", "ua_play_times_12h_mf_attr",
        "ua_play_times_nd_mf_attr", "ua_play_times_1d_mf_attr",
        "ua_play_times_3d_mf_attr", "ua_play_times_7d_mf_attr",
        "ua_play_times_14d_mf_attr", "ua_play_times_28d_mf_attr",

        "ua_gtr_1h_pxtr_attr", "ua_gtr_3h_pxtr_attr",
        "ua_gtr_6h_pxtr_attr", "ua_gtr_12h_pxtr_attr",
        "ua_gtr_nd_pxtr_attr", "ua_gtr_1d_pxtr_attr",
        "ua_gtr_3d_pxtr_attr", "ua_gtr_7d_pxtr_attr",
        "ua_gtr_14d_pxtr_attr", "ua_gtr_28d_pxtr_attr",
        "ua_gtr_big_1h_pxtr_attr", "ua_gtr_big_3h_pxtr_attr",
        "ua_gtr_big_6h_pxtr_attr", "ua_gtr_big_12h_pxtr_attr",
        "ua_gtr_big_nd_pxtr_attr", "ua_gtr_big_1d_pxtr_attr",
        "ua_gtr_big_3d_pxtr_attr", "ua_gtr_big_7d_pxtr_attr",
        "ua_gtr_big_14d_pxtr_attr", "ua_gtr_big_28d_pxtr_attr",
        "ua_lvtr_1h_pxtr_attr", "ua_lvtr_3h_pxtr_attr",
        "ua_lvtr_6h_pxtr_attr", "ua_lvtr_12h_pxtr_attr",
        "ua_lvtr_nd_pxtr_attr", "ua_lvtr_1d_pxtr_attr",
        "ua_lvtr_3d_pxtr_attr", "ua_lvtr_7d_pxtr_attr",
        "ua_lvtr_14d_pxtr_attr", "ua_lvtr_28d_pxtr_attr",
        "ua_svr_1h_pxtr_attr", "ua_svr_3h_pxtr_attr",
        "ua_svr_6h_pxtr_attr", "ua_svr_12h_pxtr_attr",
        "ua_svr_nd_pxtr_attr", "ua_svr_1d_pxtr_attr",
        "ua_svr_3d_pxtr_attr", "ua_svr_7d_pxtr_attr",
        "ua_svr_14d_pxtr_attr", "ua_svr_28d_pxtr_attr",
        "ua_gvalue_1h_pxtr_attr", "ua_gvalue_3h_pxtr_attr",
        "ua_gvalue_6h_pxtr_attr", "ua_gvalue_12h_pxtr_attr",
        "ua_gvalue_nd_pxtr_attr", "ua_gvalue_1d_pxtr_attr",
        "ua_gvalue_3d_pxtr_attr", "ua_gvalue_7d_pxtr_attr",
        "ua_gvalue_14d_pxtr_attr", "ua_gvalue_28d_pxtr_attr",
        "ua_watch_time_1h_pxtr_attr", "ua_watch_time_3h_pxtr_attr",
        "ua_watch_time_6h_pxtr_attr", "ua_watch_time_12h_pxtr_attr",
        "ua_watch_time_nd_pxtr_attr", "ua_watch_time_1d_pxtr_attr",
        "ua_watch_time_3d_pxtr_attr", "ua_watch_time_7d_pxtr_attr",
        "ua_watch_time_14d_pxtr_attr", "ua_watch_time_28d_pxtr_attr",
    }

class CommonLiveMultiFeedbackRFMV2RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_multi_feedback_rfm_v2_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])
    return ret

  @property
  @strict_types
  def output_item_v2_attrs(self) -> set:
    return {
        "u_total_reward_times_1h_mf_v2_attr", "u_total_reward_times_3h_mf_v2_attr",
        "u_total_reward_times_6h_mf_v2_attr", "u_total_reward_times_12h_mf_v2_attr",
        "u_total_reward_times_nd_mf_v2_attr", "u_total_reward_times_1d_mf_v2_attr",
        "u_total_reward_times_3d_mf_v2_attr", "u_total_reward_times_7d_mf_v2_attr",
        "u_total_reward_times_14d_mf_v2_attr", "u_total_reward_times_28d_mf_v2_attr",
        "u_total_big_reward_times_1h_mf_v2_attr", "u_total_big_reward_times_3h_mf_v2_attr",
        "u_total_big_reward_times_6h_mf_v2_attr", "u_total_big_reward_times_12h_mf_v2_attr",
        "u_total_big_reward_times_nd_mf_v2_attr", "u_total_big_reward_times_1d_mf_v2_attr",
        "u_total_big_reward_times_3d_mf_v2_attr", "u_total_big_reward_times_7d_mf_v2_attr",
        "u_total_big_reward_times_14d_mf_v2_attr", "u_total_big_reward_times_28d_mf_v2_attr",
        "u_total_long_view_1h_mf_v2_attr", "u_total_long_view_3h_mf_v2_attr",
        "u_total_long_view_6h_mf_v2_attr", "u_total_long_view_12h_mf_v2_attr",
        "u_total_long_view_nd_mf_v2_attr", "u_total_long_view_1d_mf_v2_attr",
        "u_total_long_view_3d_mf_v2_attr", "u_total_long_view_7d_mf_v2_attr",
        "u_total_long_view_14d_mf_v2_attr", "u_total_long_view_28d_mf_v2_attr",
        "u_total_short_view_1h_mf_v2_attr", "u_total_short_view_3h_mf_v2_attr",
        "u_total_short_view_6h_mf_v2_attr", "u_total_short_view_12h_mf_v2_attr",
        "u_total_short_view_nd_mf_v2_attr", "u_total_short_view_1d_mf_v2_attr",
        "u_total_short_view_3d_mf_v2_attr", "u_total_short_view_7d_mf_v2_attr",
        "u_total_short_view_14d_mf_v2_attr", "u_total_short_view_28d_mf_v2_attr",
        "u_total_reward_amt_1h_mf_v2_attr", "u_total_reward_amt_3h_mf_v2_attr",
        "u_total_reward_amt_6h_mf_v2_attr", "u_total_reward_amt_12h_mf_v2_attr",
        "u_total_reward_amt_nd_mf_v2_attr", "u_total_reward_amt_1d_mf_v2_attr",
        "u_total_reward_amt_3d_mf_v2_attr", "u_total_reward_amt_7d_mf_v2_attr",
        "u_total_reward_amt_14d_mf_v2_attr", "u_total_reward_amt_28d_mf_v2_attr",
        "u_total_watch_time_1h_mf_v2_attr", "u_total_watch_time_3h_mf_v2_attr",
        "u_total_watch_time_6h_mf_v2_attr", "u_total_watch_time_12h_mf_v2_attr",
        "u_total_watch_time_nd_mf_v2_attr", "u_total_watch_time_1d_mf_v2_attr",
        "u_total_watch_time_3d_mf_v2_attr", "u_total_watch_time_7d_mf_v2_attr",
        "u_total_watch_time_14d_mf_v2_attr", "u_total_watch_time_28d_mf_v2_attr",
        "u_total_play_times_1h_mf_v2_attr", "u_total_play_times_3h_mf_v2_attr",
        "u_total_play_times_6h_mf_v2_attr", "u_total_play_times_12h_mf_v2_attr",
        "u_total_play_times_nd_mf_v2_attr", "u_total_play_times_1d_mf_v2_attr",
        "u_total_play_times_3d_mf_v2_attr", "u_total_play_times_7d_mf_v2_attr",
        "u_total_play_times_14d_mf_v2_attr", "u_total_play_times_28d_mf_v2_attr",

        "u_total_gtr_1h_pxtr_v2_attr", "u_total_gtr_3h_pxtr_v2_attr",
        "u_total_gtr_6h_pxtr_v2_attr", "u_total_gtr_12h_pxtr_v2_attr",
        "u_total_gtr_nd_pxtr_v2_attr", "u_total_gtr_1d_pxtr_v2_attr",
        "u_total_gtr_3d_pxtr_v2_attr", "u_total_gtr_7d_pxtr_v2_attr",
        "u_total_gtr_14d_pxtr_v2_attr", "u_total_gtr_28d_pxtr_v2_attr",
        "u_total_gtr_big_1h_pxtr_v2_attr", "u_total_gtr_big_3h_pxtr_v2_attr",
        "u_total_gtr_big_6h_pxtr_v2_attr", "u_total_gtr_big_12h_pxtr_v2_attr",
        "u_total_gtr_big_nd_pxtr_v2_attr", "u_total_gtr_big_1d_pxtr_v2_attr",
        "u_total_gtr_big_3d_pxtr_v2_attr", "u_total_gtr_big_7d_pxtr_v2_attr",
        "u_total_gtr_big_14d_pxtr_v2_attr", "u_total_gtr_big_28d_pxtr_v2_attr",
        "u_total_lvtr_1h_pxtr_v2_attr", "u_total_lvtr_3h_pxtr_v2_attr",
        "u_total_lvtr_6h_pxtr_v2_attr", "u_total_lvtr_12h_pxtr_v2_attr",
        "u_total_lvtr_nd_pxtr_v2_attr", "u_total_lvtr_1d_pxtr_v2_attr",
        "u_total_lvtr_3d_pxtr_v2_attr", "u_total_lvtr_7d_pxtr_v2_attr",
        "u_total_lvtr_14d_pxtr_v2_attr", "u_total_lvtr_28d_pxtr_v2_attr",
        "u_total_svr_1h_pxtr_v2_attr", "u_total_svr_3h_pxtr_v2_attr",
        "u_total_svr_6h_pxtr_v2_attr", "u_total_svr_12h_pxtr_v2_attr",
        "u_total_svr_nd_pxtr_v2_attr", "u_total_svr_1d_pxtr_v2_attr",
        "u_total_svr_3d_pxtr_v2_attr", "u_total_svr_7d_pxtr_v2_attr",
        "u_total_svr_14d_pxtr_v2_attr", "u_total_svr_28d_pxtr_v2_attr",
        "u_total_gvalue_1h_pxtr_v2_attr", "u_total_gvalue_3h_pxtr_v2_attr",
        "u_total_gvalue_6h_pxtr_v2_attr", "u_total_gvalue_12h_pxtr_v2_attr",
        "u_total_gvalue_nd_pxtr_v2_attr", "u_total_gvalue_1d_pxtr_v2_attr",
        "u_total_gvalue_3d_pxtr_v2_attr", "u_total_gvalue_7d_pxtr_v2_attr",
        "u_total_gvalue_14d_pxtr_v2_attr", "u_total_gvalue_28d_pxtr_v2_attr",
        "u_total_watch_time_1h_pxtr_v2_attr", "u_total_watch_time_3h_pxtr_v2_attr",
        "u_total_watch_time_6h_pxtr_v2_attr", "u_total_watch_time_12h_pxtr_v2_attr",
        "u_total_watch_time_nd_pxtr_v2_attr", "u_total_watch_time_1d_pxtr_v2_attr",
        "u_total_watch_time_3d_pxtr_v2_attr", "u_total_watch_time_7d_pxtr_v2_attr",
        "u_total_watch_time_14d_pxtr_v2_attr", "u_total_watch_time_28d_pxtr_v2_attr",

        "ua_reward_times_1h_mf_v2_attr", "ua_reward_times_3h_mf_v2_attr",
        "ua_reward_times_6h_mf_v2_attr", "ua_reward_times_12h_mf_v2_attr",
        "ua_reward_times_nd_mf_v2_attr", "ua_reward_times_1d_mf_v2_attr",
        "ua_reward_times_3d_mf_v2_attr", "ua_reward_times_7d_mf_v2_attr",
        "ua_reward_times_14d_mf_v2_attr", "ua_reward_times_28d_mf_v2_attr",
        "ua_big_reward_times_1h_mf_v2_attr", "ua_big_reward_times_3h_mf_v2_attr",
        "ua_big_reward_times_6h_mf_v2_attr", "ua_big_reward_times_12h_mf_v2_attr",
        "ua_big_reward_times_nd_mf_v2_attr", "ua_big_reward_times_1d_mf_v2_attr",
        "ua_big_reward_times_3d_mf_v2_attr", "ua_big_reward_times_7d_mf_v2_attr",
        "ua_big_reward_times_14d_mf_v2_attr", "ua_big_reward_times_28d_mf_v2_attr",
        "ua_long_view_1h_mf_v2_attr", "ua_long_view_3h_mf_v2_attr",
        "ua_long_view_6h_mf_v2_attr", "ua_long_view_12h_mf_v2_attr",
        "ua_long_view_nd_mf_v2_attr", "ua_long_view_1d_mf_v2_attr",
        "ua_long_view_3d_mf_v2_attr", "ua_long_view_7d_mf_v2_attr",
        "ua_long_view_14d_mf_v2_attr", "ua_long_view_28d_mf_v2_attr",
        "ua_short_view_1h_mf_v2_attr", "ua_short_view_3h_mf_v2_attr",
        "ua_short_view_6h_mf_v2_attr", "ua_short_view_12h_mf_v2_attr",
        "ua_short_view_nd_mf_v2_attr", "ua_short_view_1d_mf_v2_attr",
        "ua_short_view_3d_mf_v2_attr", "ua_short_view_7d_mf_v2_attr",
        "ua_short_view_14d_mf_v2_attr", "ua_short_view_28d_mf_v2_attr",
        "ua_reward_amt_1h_mf_v2_attr", "ua_reward_amt_3h_mf_v2_attr",
        "ua_reward_amt_6h_mf_v2_attr", "ua_reward_amt_12h_mf_v2_attr",
        "ua_reward_amt_nd_mf_v2_attr", "ua_reward_amt_1d_mf_v2_attr",
        "ua_reward_amt_3d_mf_v2_attr", "ua_reward_amt_7d_mf_v2_attr",
        "ua_reward_amt_14d_mf_v2_attr", "ua_reward_amt_28d_mf_v2_attr",
        "ua_watch_time_1h_mf_v2_attr", "ua_watch_time_3h_mf_v2_attr",
        "ua_watch_time_6h_mf_v2_attr", "ua_watch_time_12h_mf_v2_attr",
        "ua_watch_time_nd_mf_v2_attr", "ua_watch_time_1d_mf_v2_attr",
        "ua_watch_time_3d_mf_v2_attr", "ua_watch_time_7d_mf_v2_attr",
        "ua_watch_time_14d_mf_v2_attr", "ua_watch_time_28d_mf_v2_attr",
        "ua_play_times_1h_mf_v2_attr", "ua_play_times_3h_mf_v2_attr",
        "ua_play_times_6h_mf_v2_attr", "ua_play_times_12h_mf_v2_attr",
        "ua_play_times_nd_mf_v2_attr", "ua_play_times_1d_mf_v2_attr",
        "ua_play_times_3d_mf_v2_attr", "ua_play_times_7d_mf_v2_attr",
        "ua_play_times_14d_mf_v2_attr", "ua_play_times_28d_mf_v2_attr",

        "ua_gtr_1h_pxtr_v2_attr", "ua_gtr_3h_pxtr_v2_attr",
        "ua_gtr_6h_pxtr_v2_attr", "ua_gtr_12h_pxtr_v2_attr",
        "ua_gtr_nd_pxtr_v2_attr", "ua_gtr_1d_pxtr_v2_attr",
        "ua_gtr_3d_pxtr_v2_attr", "ua_gtr_7d_pxtr_v2_attr",
        "ua_gtr_14d_pxtr_v2_attr", "ua_gtr_28d_pxtr_v2_attr",
        "ua_gtr_big_1h_pxtr_v2_attr", "ua_gtr_big_3h_pxtr_v2_attr",
        "ua_gtr_big_6h_pxtr_v2_attr", "ua_gtr_big_12h_pxtr_v2_attr",
        "ua_gtr_big_nd_pxtr_v2_attr", "ua_gtr_big_1d_pxtr_v2_attr",
        "ua_gtr_big_3d_pxtr_v2_attr", "ua_gtr_big_7d_pxtr_v2_attr",
        "ua_gtr_big_14d_pxtr_v2_attr", "ua_gtr_big_28d_pxtr_v2_attr",
        "ua_lvtr_1h_pxtr_v2_attr", "ua_lvtr_3h_pxtr_v2_attr",
        "ua_lvtr_6h_pxtr_v2_attr", "ua_lvtr_12h_pxtr_v2_attr",
        "ua_lvtr_nd_pxtr_v2_attr", "ua_lvtr_1d_pxtr_v2_attr",
        "ua_lvtr_3d_pxtr_v2_attr", "ua_lvtr_7d_pxtr_v2_attr",
        "ua_lvtr_14d_pxtr_v2_attr", "ua_lvtr_28d_pxtr_v2_attr",
        "ua_svr_1h_pxtr_v2_attr", "ua_svr_3h_pxtr_v2_attr",
        "ua_svr_6h_pxtr_v2_attr", "ua_svr_12h_pxtr_v2_attr",
        "ua_svr_nd_pxtr_v2_attr", "ua_svr_1d_pxtr_v2_attr",
        "ua_svr_3d_pxtr_v2_attr", "ua_svr_7d_pxtr_v2_attr",
        "ua_svr_14d_pxtr_v2_attr", "ua_svr_28d_pxtr_v2_attr",
        "ua_gvalue_1h_pxtr_v2_attr", "ua_gvalue_3h_pxtr_v2_attr",
        "ua_gvalue_6h_pxtr_v2_attr", "ua_gvalue_12h_pxtr_v2_attr",
        "ua_gvalue_nd_pxtr_v2_attr", "ua_gvalue_1d_pxtr_v2_attr",
        "ua_gvalue_3d_pxtr_v2_attr", "ua_gvalue_7d_pxtr_v2_attr",
        "ua_gvalue_14d_pxtr_v2_attr", "ua_gvalue_28d_pxtr_v2_attr",
        "ua_watch_time_1h_pxtr_v2_attr", "ua_watch_time_3h_pxtr_v2_attr",
        "ua_watch_time_6h_pxtr_v2_attr", "ua_watch_time_12h_pxtr_v2_attr",
        "ua_watch_time_nd_pxtr_v2_attr", "ua_watch_time_1d_pxtr_v2_attr",
        "ua_watch_time_3d_pxtr_v2_attr", "ua_watch_time_7d_pxtr_v2_attr",
        "ua_watch_time_14d_pxtr_v2_attr", "ua_watch_time_28d_pxtr_v2_attr",
    }

class CommonLiveMultiFeedbackRFMV3RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_multi_feedback_rfm_v3_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        "u_total_eff_10s_click_view_1h_mf_v3_attr", "u_total_eff_10s_click_view_3h_mf_v3_attr",
        "u_total_eff_10s_click_view_6h_mf_v3_attr", "u_total_eff_10s_click_view_12h_mf_v3_attr",
        "u_total_eff_10s_click_view_nd_mf_v3_attr", "u_total_eff_10s_click_view_1d_mf_v3_attr",
        "u_total_eff_10s_click_view_3d_mf_v3_attr", "u_total_eff_10s_click_view_7d_mf_v3_attr",
        "u_total_eff_10s_click_view_14d_mf_v3_attr", "u_total_eff_10s_click_view_28d_mf_v3_attr",
        "u_total_eff_60s_click_view_1h_mf_v3_attr", "u_total_eff_60s_click_view_3h_mf_v3_attr",
        "u_total_eff_60s_click_view_6h_mf_v3_attr", "u_total_eff_60s_click_view_12h_mf_v3_attr",
        "u_total_eff_60s_click_view_nd_mf_v3_attr", "u_total_eff_60s_click_view_1d_mf_v3_attr",
        "u_total_eff_60s_click_view_3d_mf_v3_attr", "u_total_eff_60s_click_view_7d_mf_v3_attr",
        "u_total_eff_60s_click_view_14d_mf_v3_attr", "u_total_eff_60s_click_view_28d_mf_v3_attr",
        "u_total_play_times_1h_mf_v3_attr", "u_total_play_times_3h_mf_v3_attr",
        "u_total_play_times_6h_mf_v3_attr", "u_total_play_times_12h_mf_v3_attr",
        "u_total_play_times_nd_mf_v3_attr", "u_total_play_times_1d_mf_v3_attr",
        "u_total_play_times_3d_mf_v3_attr", "u_total_play_times_7d_mf_v3_attr",
        "u_total_play_times_14d_mf_v3_attr", "u_total_play_times_28d_mf_v3_attr",

        "u_total_long_view_1h_mf_v3_attr", "u_total_long_view_3h_mf_v3_attr",
        "u_total_long_view_6h_mf_v3_attr", "u_total_long_view_12h_mf_v3_attr",
        "u_total_long_view_nd_mf_v3_attr", "u_total_long_view_1d_mf_v3_attr",
        "u_total_long_view_3d_mf_v3_attr", "u_total_long_view_7d_mf_v3_attr",
        "u_total_long_view_14d_mf_v3_attr", "u_total_long_view_28d_mf_v3_attr",
        "u_total_short_view_1h_mf_v3_attr", "u_total_short_view_3h_mf_v3_attr",
        "u_total_short_view_6h_mf_v3_attr", "u_total_short_view_12h_mf_v3_attr",
        "u_total_short_view_nd_mf_v3_attr", "u_total_short_view_1d_mf_v3_attr",
        "u_total_short_view_3d_mf_v3_attr", "u_total_short_view_7d_mf_v3_attr",
        "u_total_short_view_14d_mf_v3_attr", "u_total_short_view_28d_mf_v3_attr",
        "u_total_watch_time_1h_mf_v3_attr", "u_total_watch_time_3h_mf_v3_attr",
        "u_total_watch_time_6h_mf_v3_attr", "u_total_watch_time_12h_mf_v3_attr",
        "u_total_watch_time_nd_mf_v3_attr", "u_total_watch_time_1d_mf_v3_attr",
        "u_total_watch_time_3d_mf_v3_attr", "u_total_watch_time_7d_mf_v3_attr",
        "u_total_watch_time_14d_mf_v3_attr", "u_total_watch_time_28d_mf_v3_attr",

        "u_total_like_times_1h_mf_v3_attr", "u_total_like_times_3h_mf_v3_attr",
        "u_total_like_times_6h_mf_v3_attr", "u_total_like_times_12h_mf_v3_attr",
        "u_total_like_times_nd_mf_v3_attr", "u_total_like_times_1d_mf_v3_attr",
        "u_total_like_times_3d_mf_v3_attr", "u_total_like_times_7d_mf_v3_attr",
        "u_total_like_times_14d_mf_v3_attr", "u_total_like_times_28d_mf_v3_attr",
        "u_total_comment_times_1h_mf_v3_attr", "u_total_comment_times_3h_mf_v3_attr",
        "u_total_comment_times_6h_mf_v3_attr", "u_total_comment_times_12h_mf_v3_attr",
        "u_total_comment_times_nd_mf_v3_attr", "u_total_comment_times_1d_mf_v3_attr",
        "u_total_comment_times_3d_mf_v3_attr", "u_total_comment_times_7d_mf_v3_attr",
        "u_total_comment_times_14d_mf_v3_attr", "u_total_comment_times_28d_mf_v3_attr",
        "u_total_follow_times_1h_mf_v3_attr", "u_total_follow_times_3h_mf_v3_attr",
        "u_total_follow_times_6h_mf_v3_attr", "u_total_follow_times_12h_mf_v3_attr",
        "u_total_follow_times_nd_mf_v3_attr", "u_total_follow_times_1d_mf_v3_attr",
        "u_total_follow_times_3d_mf_v3_attr", "u_total_follow_times_7d_mf_v3_attr",
        "u_total_follow_times_14d_mf_v3_attr", "u_total_follow_times_28d_mf_v3_attr",

        "u_total_reward_amt_1h_mf_v3_attr", "u_total_reward_amt_3h_mf_v3_attr",
        "u_total_reward_amt_6h_mf_v3_attr", "u_total_reward_amt_12h_mf_v3_attr",
        "u_total_reward_amt_nd_mf_v3_attr", "u_total_reward_amt_1d_mf_v3_attr",
        "u_total_reward_amt_3d_mf_v3_attr", "u_total_reward_amt_7d_mf_v3_attr",
        "u_total_reward_amt_14d_mf_v3_attr", "u_total_reward_amt_28d_mf_v3_attr",
        "u_total_reward_times_1h_mf_v3_attr", "u_total_reward_times_3h_mf_v3_attr",
        "u_total_reward_times_6h_mf_v3_attr", "u_total_reward_times_12h_mf_v3_attr",
        "u_total_reward_times_nd_mf_v3_attr", "u_total_reward_times_1d_mf_v3_attr",
        "u_total_reward_times_3d_mf_v3_attr", "u_total_reward_times_7d_mf_v3_attr",
        "u_total_reward_times_14d_mf_v3_attr", "u_total_reward_times_28d_mf_v3_attr",
        "u_total_big_reward_times_1h_mf_v3_attr", "u_total_big_reward_times_3h_mf_v3_attr",
        "u_total_big_reward_times_6h_mf_v3_attr", "u_total_big_reward_times_12h_mf_v3_attr",
        "u_total_big_reward_times_nd_mf_v3_attr", "u_total_big_reward_times_1d_mf_v3_attr",
        "u_total_big_reward_times_3d_mf_v3_attr", "u_total_big_reward_times_7d_mf_v3_attr",
        "u_total_big_reward_times_14d_mf_v3_attr", "u_total_big_reward_times_28d_mf_v3_attr",

        "ua_eff_10s_click_view_1h_mf_v3_attr", "ua_eff_10s_click_view_3h_mf_v3_attr",
        "ua_eff_10s_click_view_6h_mf_v3_attr", "ua_eff_10s_click_view_12h_mf_v3_attr",
        "ua_eff_10s_click_view_nd_mf_v3_attr", "ua_eff_10s_click_view_1d_mf_v3_attr",
        "ua_eff_10s_click_view_3d_mf_v3_attr", "ua_eff_10s_click_view_7d_mf_v3_attr",
        "ua_eff_10s_click_view_14d_mf_v3_attr", "ua_eff_10s_click_view_28d_mf_v3_attr",
        "ua_eff_60s_click_view_1h_mf_v3_attr", "ua_eff_60s_click_view_3h_mf_v3_attr",
        "ua_eff_60s_click_view_6h_mf_v3_attr", "ua_eff_60s_click_view_12h_mf_v3_attr",
        "ua_eff_60s_click_view_nd_mf_v3_attr", "ua_eff_60s_click_view_1d_mf_v3_attr",
        "ua_eff_60s_click_view_3d_mf_v3_attr", "ua_eff_60s_click_view_7d_mf_v3_attr",
        "ua_eff_60s_click_view_14d_mf_v3_attr", "ua_eff_60s_click_view_28d_mf_v3_attr",
        "ua_play_times_1h_mf_v3_attr", "ua_play_times_3h_mf_v3_attr",
        "ua_play_times_6h_mf_v3_attr", "ua_play_times_12h_mf_v3_attr",
        "ua_play_times_nd_mf_v3_attr", "ua_play_times_1d_mf_v3_attr",
        "ua_play_times_3d_mf_v3_attr", "ua_play_times_7d_mf_v3_attr",
        "ua_play_times_14d_mf_v3_attr", "ua_play_times_28d_mf_v3_attr",

        "ua_long_view_1h_mf_v3_attr", "ua_long_view_3h_mf_v3_attr",
        "ua_long_view_6h_mf_v3_attr", "ua_long_view_12h_mf_v3_attr",
        "ua_long_view_nd_mf_v3_attr", "ua_long_view_1d_mf_v3_attr",
        "ua_long_view_3d_mf_v3_attr", "ua_long_view_7d_mf_v3_attr",
        "ua_long_view_14d_mf_v3_attr", "ua_long_view_28d_mf_v3_attr",
        "ua_short_view_1h_mf_v3_attr", "ua_short_view_3h_mf_v3_attr",
        "ua_short_view_6h_mf_v3_attr", "ua_short_view_12h_mf_v3_attr",
        "ua_short_view_nd_mf_v3_attr", "ua_short_view_1d_mf_v3_attr",
        "ua_short_view_3d_mf_v3_attr", "ua_short_view_7d_mf_v3_attr",
        "ua_short_view_14d_mf_v3_attr", "ua_short_view_28d_mf_v3_attr",
        "ua_watch_time_1h_mf_v3_attr", "ua_watch_time_3h_mf_v3_attr",
        "ua_watch_time_6h_mf_v3_attr", "ua_watch_time_12h_mf_v3_attr",
        "ua_watch_time_nd_mf_v3_attr", "ua_watch_time_1d_mf_v3_attr",
        "ua_watch_time_3d_mf_v3_attr", "ua_watch_time_7d_mf_v3_attr",
        "ua_watch_time_14d_mf_v3_attr", "ua_watch_time_28d_mf_v3_attr",

        "ua_like_times_1h_mf_v3_attr", "ua_like_times_3h_mf_v3_attr",
        "ua_like_times_6h_mf_v3_attr", "ua_like_times_12h_mf_v3_attr",
        "ua_like_times_nd_mf_v3_attr", "ua_like_times_1d_mf_v3_attr",
        "ua_like_times_3d_mf_v3_attr", "ua_like_times_7d_mf_v3_attr",
        "ua_like_times_14d_mf_v3_attr", "ua_like_times_28d_mf_v3_attr",
        "ua_comment_times_1h_mf_v3_attr", "ua_comment_times_3h_mf_v3_attr",
        "ua_comment_times_6h_mf_v3_attr", "ua_comment_times_12h_mf_v3_attr",
        "ua_comment_times_nd_mf_v3_attr", "ua_comment_times_1d_mf_v3_attr",
        "ua_comment_times_3d_mf_v3_attr", "ua_comment_times_7d_mf_v3_attr",
        "ua_comment_times_14d_mf_v3_attr", "ua_comment_times_28d_mf_v3_attr",
        "ua_follow_times_1h_mf_v3_attr", "ua_follow_times_3h_mf_v3_attr",
        "ua_follow_times_6h_mf_v3_attr", "ua_follow_times_12h_mf_v3_attr",
        "ua_follow_times_nd_mf_v3_attr", "ua_follow_times_1d_mf_v3_attr",
        "ua_follow_times_3d_mf_v3_attr", "ua_follow_times_7d_mf_v3_attr",
        "ua_follow_times_14d_mf_v3_attr", "ua_follow_times_28d_mf_v3_attr",

        "ua_reward_amt_1h_mf_v3_attr", "ua_reward_amt_3h_mf_v3_attr",
        "ua_reward_amt_6h_mf_v3_attr", "ua_reward_amt_12h_mf_v3_attr",
        "ua_reward_amt_nd_mf_v3_attr", "ua_reward_amt_1d_mf_v3_attr",
        "ua_reward_amt_3d_mf_v3_attr", "ua_reward_amt_7d_mf_v3_attr",
        "ua_reward_amt_14d_mf_v3_attr", "ua_reward_amt_28d_mf_v3_attr",
        "ua_reward_times_1h_mf_v3_attr", "ua_reward_times_3h_mf_v3_attr",
        "ua_reward_times_6h_mf_v3_attr", "ua_reward_times_12h_mf_v3_attr",
        "ua_reward_times_nd_mf_v3_attr", "ua_reward_times_1d_mf_v3_attr",
        "ua_reward_times_3d_mf_v3_attr", "ua_reward_times_7d_mf_v3_attr",
        "ua_reward_times_14d_mf_v3_attr", "ua_reward_times_28d_mf_v3_attr",
        "ua_big_reward_times_1h_mf_v3_attr", "ua_big_reward_times_3h_mf_v3_attr",
        "ua_big_reward_times_6h_mf_v3_attr", "ua_big_reward_times_12h_mf_v3_attr",
        "ua_big_reward_times_nd_mf_v3_attr", "ua_big_reward_times_1d_mf_v3_attr",
        "ua_big_reward_times_3d_mf_v3_attr", "ua_big_reward_times_7d_mf_v3_attr",
        "ua_big_reward_times_14d_mf_v3_attr", "ua_big_reward_times_28d_mf_v3_attr",

        "u_latest_show_time_gap_mf_v3_attr", "ua_latest_show_time_gap_mf_v3_attr"
    }


class CommonLiveM3RFMRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_m3_rfm_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])
    return ret

  @property
  @strict_types
  def output_item_v2_attrs(self) -> set:
    return {
        "u_auto_long_view_nd_m3_attr", "u_auto_long_view_30m_m3_attr", 
        "u_auto_long_view_1h_m3_attr", "u_auto_long_view_3h_m3_attr", 
        "u_auto_long_view_6h_m3_attr", "u_auto_long_view_12h_m3_attr", 
        "u_auto_long_view_1d_m3_attr", "u_auto_long_view_3d_m3_attr", 
        "u_auto_long_view_7d_m3_attr", "u_auto_long_view_14d_m3_attr", 
        "u_auto_long_view_28d_m3_attr", 
        "u_auto_watch_time_nd_m3_attr", "u_auto_watch_time_30m_m3_attr", 
        "u_auto_watch_time_1h_m3_attr", "u_auto_watch_time_3h_m3_attr", 
        "u_auto_watch_time_6h_m3_attr", "u_auto_watch_time_12h_m3_attr", 
        "u_auto_watch_time_1d_m3_attr", "u_auto_watch_time_3d_m3_attr", 
        "u_auto_watch_time_7d_m3_attr", "u_auto_watch_time_14d_m3_attr", 
        "u_auto_watch_time_28d_m3_attr", 
        "u_in_long_view_nd_m3_attr", "u_in_long_view_30m_m3_attr", 
        "u_in_long_view_1h_m3_attr", "u_in_long_view_3h_m3_attr", 
        "u_in_long_view_6h_m3_attr", "u_in_long_view_12h_m3_attr", 
        "u_in_long_view_1d_m3_attr", "u_in_long_view_3d_m3_attr", 
        "u_in_long_view_7d_m3_attr", "u_in_long_view_14d_m3_attr", 
        "u_in_long_view_28d_m3_attr", 
        "u_in_watch_time_nd_m3_attr", "u_in_watch_time_30m_m3_attr", 
        "u_in_watch_time_1h_m3_attr", "u_in_watch_time_3h_m3_attr", 
        "u_in_watch_time_6h_m3_attr", "u_in_watch_time_12h_m3_attr", 
        "u_in_watch_time_1d_m3_attr", "u_in_watch_time_3d_m3_attr", 
        "u_in_watch_time_7d_m3_attr", "u_in_watch_time_14d_m3_attr", 
        "u_in_watch_time_28d_m3_attr", 
        "u_in_play_times_nd_m3_attr", "u_in_play_times_30m_m3_attr", 
        "u_in_play_times_1h_m3_attr", "u_in_play_times_3h_m3_attr", 
        "u_in_play_times_6h_m3_attr", "u_in_play_times_12h_m3_attr", 
        "u_in_play_times_1d_m3_attr", "u_in_play_times_3d_m3_attr", 
        "u_in_play_times_7d_m3_attr", "u_in_play_times_14d_m3_attr", 
        "u_in_play_times_28d_m3_attr", 
        "u_short_view_nd_m3_attr", "u_short_view_30m_m3_attr", 
        "u_short_view_1h_m3_attr", "u_short_view_3h_m3_attr", 
        "u_short_view_6h_m3_attr", "u_short_view_12h_m3_attr", 
        "u_short_view_1d_m3_attr", "u_short_view_3d_m3_attr", 
        "u_short_view_7d_m3_attr", "u_short_view_14d_m3_attr", 
        "u_short_view_28d_m3_attr", 
        "u_long_view_nd_m3_attr", "u_long_view_30m_m3_attr", 
        "u_long_view_1h_m3_attr", "u_long_view_3h_m3_attr", 
        "u_long_view_6h_m3_attr", "u_long_view_12h_m3_attr", 
        "u_long_view_1d_m3_attr", "u_long_view_3d_m3_attr", 
        "u_long_view_7d_m3_attr", "u_long_view_14d_m3_attr", 
        "u_long_view_28d_m3_attr", 
        "u_watch_times_nd_m3_attr", "u_watch_times_30m_m3_attr", 
        "u_watch_times_1h_m3_attr", "u_watch_times_3h_m3_attr", 
        "u_watch_times_6h_m3_attr", "u_watch_times_12h_m3_attr", 
        "u_watch_times_1d_m3_attr", "u_watch_times_3d_m3_attr", 
        "u_watch_times_7d_m3_attr", "u_watch_times_14d_m3_attr", 
        "u_watch_times_28d_m3_attr",
        "ua_auto_long_view_nd_m3_attr", "ua_auto_long_view_30m_m3_attr", 
        "ua_auto_long_view_1h_m3_attr", "ua_auto_long_view_3h_m3_attr", 
        "ua_auto_long_view_6h_m3_attr", "ua_auto_long_view_12h_m3_attr", 
        "ua_auto_long_view_1d_m3_attr", "ua_auto_long_view_3d_m3_attr", 
        "ua_auto_long_view_7d_m3_attr", "ua_auto_long_view_14d_m3_attr", 
        "ua_auto_long_view_28d_m3_attr", 
        "ua_auto_watch_time_nd_m3_attr", "ua_auto_watch_time_30m_m3_attr", 
        "ua_auto_watch_time_1h_m3_attr", "ua_auto_watch_time_3h_m3_attr", 
        "ua_auto_watch_time_6h_m3_attr", "ua_auto_watch_time_12h_m3_attr", 
        "ua_auto_watch_time_1d_m3_attr", "ua_auto_watch_time_3d_m3_attr", 
        "ua_auto_watch_time_7d_m3_attr", "ua_auto_watch_time_14d_m3_attr", 
        "ua_auto_watch_time_28d_m3_attr", 
        "ua_in_long_view_nd_m3_attr", "ua_in_long_view_30m_m3_attr", 
        "ua_in_long_view_1h_m3_attr", "ua_in_long_view_3h_m3_attr", 
        "ua_in_long_view_6h_m3_attr", "ua_in_long_view_12h_m3_attr", 
        "ua_in_long_view_1d_m3_attr", "ua_in_long_view_3d_m3_attr", 
        "ua_in_long_view_7d_m3_attr", "ua_in_long_view_14d_m3_attr", 
        "ua_in_long_view_28d_m3_attr", 
        "ua_in_watch_time_nd_m3_attr", "ua_in_watch_time_30m_m3_attr", 
        "ua_in_watch_time_1h_m3_attr", "ua_in_watch_time_3h_m3_attr", 
        "ua_in_watch_time_6h_m3_attr", "ua_in_watch_time_12h_m3_attr", 
        "ua_in_watch_time_1d_m3_attr", "ua_in_watch_time_3d_m3_attr", 
        "ua_in_watch_time_7d_m3_attr", "ua_in_watch_time_14d_m3_attr", 
        "ua_in_watch_time_28d_m3_attr", 
        "ua_in_play_times_nd_m3_attr", "ua_in_play_times_30m_m3_attr", 
        "ua_in_play_times_1h_m3_attr", "ua_in_play_times_3h_m3_attr", 
        "ua_in_play_times_6h_m3_attr", "ua_in_play_times_12h_m3_attr", 
        "ua_in_play_times_1d_m3_attr", "ua_in_play_times_3d_m3_attr", 
        "ua_in_play_times_7d_m3_attr", "ua_in_play_times_14d_m3_attr", 
        "ua_in_play_times_28d_m3_attr", 
        "ua_short_view_nd_m3_attr", "ua_short_view_30m_m3_attr", 
        "ua_short_view_1h_m3_attr", "ua_short_view_3h_m3_attr", 
        "ua_short_view_6h_m3_attr", "ua_short_view_12h_m3_attr", 
        "ua_short_view_1d_m3_attr", "ua_short_view_3d_m3_attr", 
        "ua_short_view_7d_m3_attr", "ua_short_view_14d_m3_attr", 
        "ua_short_view_28d_m3_attr", 
        "ua_long_view_nd_m3_attr", "ua_long_view_30m_m3_attr", 
        "ua_long_view_1h_m3_attr", "ua_long_view_3h_m3_attr", 
        "ua_long_view_6h_m3_attr", "ua_long_view_12h_m3_attr", 
        "ua_long_view_1d_m3_attr", "ua_long_view_3d_m3_attr", 
        "ua_long_view_7d_m3_attr", "ua_long_view_14d_m3_attr", 
        "ua_long_view_28d_m3_attr", 
        "ua_watch_times_nd_m3_attr", "ua_watch_times_30m_m3_attr", 
        "ua_watch_times_1h_m3_attr", "ua_watch_times_3h_m3_attr", 
        "ua_watch_times_6h_m3_attr", "ua_watch_times_12h_m3_attr", 
        "ua_watch_times_1d_m3_attr", "ua_watch_times_3d_m3_attr", 
        "ua_watch_times_7d_m3_attr", "ua_watch_times_14d_m3_attr", 
        "ua_watch_times_28d_m3_attr",
    }



class CommonLiveSameClusterAuthorListRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_same_cluster_author_list_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_v2_attrs(self) -> set:
    return {
        "live_same_cluster_author_list_attr"
    }


class CommonVideoLiveMixedRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_video_live_mixed_sequence_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["video_colossus_resp_attr"])
    ret.add(self._config["live_colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])
    ret.add(self._config["use_pb_format"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "u_lasted_video_live_mixed_type_list_attr", "u_lasted_video_live_mixed_aid_list_attr",
      "u_lasted_video_live_mixed_pid_list_attr", "u_lasted_video_live_mixed_playtime_list_attr",
      "u_lasted_video_live_mixed_reward_list_attr", "u_lasted_video_live_mixed_tag_list_attr",
      "u_lasted_video_live_mixed_label_list_attr", "u_lasted_video_live_mixed_mlag_list_attr",
      "u_lasted_video_live_mixed_hlag_list_attr", "u_lasted_video_live_mixed_dlag_list_attr",
    }
    
  @property
  @strict_types
  def output_item_v2_attrs(self) -> set:
    return {
      "ua_lasted_video_live_mixed_type_list_attr", "ua_lasted_video_live_mixed_aid_list_attr",
      "ua_lasted_video_live_mixed_pid_list_attr", "ua_lasted_video_live_mixed_playtime_list_attr",
      "ua_lasted_video_live_mixed_reward_list_attr", "ua_lasted_video_live_mixed_tag_list_attr",
      "ua_lasted_video_live_mixed_label_list_attr", "ua_lasted_video_live_mixed_mlag_list_attr",
      "ua_lasted_video_live_mixed_hlag_list_attr", "ua_lasted_video_live_mixed_dlag_list_attr",
    }

class CommonVideoLiveMixedV2RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_video_live_mixed_sequence_v2_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()

    ret.add(self._config.get("video_colossus_resp_attr"))
    ret.add(self._config.get("live_colossus_resp_attr"))
    ret.add(self._config.get("author_id_attr"))
    ret.add(self._config.get("use_pb_format"))
    ret.add(self._config.get("enable_filter_e_shop"))
    ret.add(self._config.get("enable_filter_outside_item"))
    ret.add(self._config.get("enable_filter_live_long_view_item"))
    ret.add(self._config.get("enable_filter_channel"))
    ret.add(self._config.get("u_valid_author_seq_len"))
    ret.add(self._config.get("live_long_view_threshold"))
    ret.add(self._config.get("filter_time_len"))
    ret.add(self._config.get("day_of_seq"))
    ret.add(self._config.get("sort_type"))

    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "u_lasted_mixed_type_list_attr", "u_lasted_mixed_aid_list_attr",
      "u_lasted_mixed_playtime_list_attr", "u_lasted_mixed_reward_list_attr",
      "u_lasted_mixed_glabel_list_attr", "u_lasted_mixed_clicks_list_attr",
      "u_lasted_mixed_avg_dlag_list_attr", "u_lasted_mixed_latest_mlag_list_attr",
      "u_lasted_mixed_latest_hlag_list_attr", "u_lasted_mixed_latest_dlag_list_attr",
      "u_lasted_mixed_latest_timestamp_list_attr",
      "u_lasted_mixed_video_playtime_list_attr", "u_lasted_mixed_video_glabel_list_attr",
      "u_lasted_mixed_video_clicks_list_attr",
      "u_lasted_mixed_live_playtime_list_attr", "u_lasted_mixed_live_glabel_list_attr",
      "u_lasted_mixed_live_reward_list_attr", "u_lasted_mixed_live_clicks_list_attr",
    }

class CommonLiveClickRFMRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_click_rfm_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])
    return ret

  @property
  @strict_types
  def output_item_v2_attrs(self) -> set:
    return {
        "u_auto_click_long_view_nd_attr", "u_auto_click_long_view_30m_attr", 
        "u_auto_click_long_view_1h_attr", "u_auto_click_long_view_3h_attr", 
        "u_auto_click_long_view_6h_attr", "u_auto_click_long_view_12h_attr", 
        "u_auto_click_long_view_1d_attr", "u_auto_click_long_view_3d_attr", 
        "u_auto_click_long_view_7d_attr", "u_auto_click_long_view_14d_attr", 
        "u_auto_click_long_view_28d_attr", 
        "u_auto_like_long_view_nd_attr", "u_auto_like_long_view_30m_attr", 
        "u_auto_like_long_view_1h_attr", "u_auto_like_long_view_3h_attr", 
        "u_auto_like_long_view_6h_attr", "u_auto_like_long_view_12h_attr", 
        "u_auto_like_long_view_1d_attr", "u_auto_like_long_view_3d_attr", 
        "u_auto_like_long_view_7d_attr", "u_auto_like_long_view_14d_attr", 
        "u_auto_like_long_view_28d_attr", 
        "u_auto_comment_long_view_nd_attr", "u_auto_comment_long_view_30m_attr", 
        "u_auto_comment_long_view_1h_attr", "u_auto_comment_long_view_3h_attr", 
        "u_auto_comment_long_view_6h_attr", "u_auto_comment_long_view_12h_attr", 
        "u_auto_comment_long_view_1d_attr", "u_auto_comment_long_view_3d_attr", 
        "u_auto_comment_long_view_7d_attr", "u_auto_comment_long_view_14d_attr", 
        "u_auto_comment_long_view_28d_attr", 
        "u_click_long_view_nd_attr", "u_click_long_view_30m_attr", 
        "u_click_long_view_1h_attr", "u_click_long_view_3h_attr", 
        "u_click_long_view_6h_attr", "u_click_long_view_12h_attr", 
        "u_click_long_view_1d_attr", "u_click_long_view_3d_attr", 
        "u_click_long_view_7d_attr", "u_click_long_view_14d_attr", 
        "u_click_long_view_28d_attr", 
        "u_like_long_view_nd_attr", "u_like_long_view_30m_attr", 
        "u_like_long_view_1h_attr", "u_like_long_view_3h_attr", 
        "u_like_long_view_6h_attr", "u_like_long_view_12h_attr", 
        "u_like_long_view_1d_attr", "u_like_long_view_3d_attr", 
        "u_like_long_view_7d_attr", "u_like_long_view_14d_attr", 
        "u_like_long_view_28d_attr", 
        "u_comment_long_view_nd_attr", "u_comment_long_view_30m_attr", 
        "u_comment_long_view_1h_attr", "u_comment_long_view_3h_attr", 
        "u_comment_long_view_6h_attr", "u_comment_long_view_12h_attr", 
        "u_comment_long_view_1d_attr", "u_comment_long_view_3d_attr", 
        "u_comment_long_view_7d_attr", "u_comment_long_view_14d_attr", 
        "u_comment_long_view_28d_attr", 
        "ua_auto_click_long_view_nd_attr", "ua_auto_click_long_view_30m_attr", 
        "ua_auto_click_long_view_1h_attr", "ua_auto_click_long_view_3h_attr", 
        "ua_auto_click_long_view_6h_attr", "ua_auto_click_long_view_12h_attr", 
        "ua_auto_click_long_view_1d_attr", "ua_auto_click_long_view_3d_attr", 
        "ua_auto_click_long_view_7d_attr", "ua_auto_click_long_view_14d_attr", 
        "ua_auto_click_long_view_28d_attr", 
        "ua_auto_like_long_view_nd_attr", "ua_auto_like_long_view_30m_attr", 
        "ua_auto_like_long_view_1h_attr", "ua_auto_like_long_view_3h_attr", 
        "ua_auto_like_long_view_6h_attr", "ua_auto_like_long_view_12h_attr", 
        "ua_auto_like_long_view_1d_attr", "ua_auto_like_long_view_3d_attr", 
        "ua_auto_like_long_view_7d_attr", "ua_auto_like_long_view_14d_attr", 
        "ua_auto_like_long_view_28d_attr", 
        "ua_auto_comment_long_view_nd_attr", "ua_auto_comment_long_view_30m_attr", 
        "ua_auto_comment_long_view_1h_attr", "ua_auto_comment_long_view_3h_attr", 
        "ua_auto_comment_long_view_6h_attr", "ua_auto_comment_long_view_12h_attr", 
        "ua_auto_comment_long_view_1d_attr", "ua_auto_comment_long_view_3d_attr", 
        "ua_auto_comment_long_view_7d_attr", "ua_auto_comment_long_view_14d_attr", 
        "ua_auto_comment_long_view_28d_attr", 
        "ua_click_long_view_nd_attr", "ua_click_long_view_30m_attr", 
        "ua_click_long_view_1h_attr", "ua_click_long_view_3h_attr", 
        "ua_click_long_view_6h_attr", "ua_click_long_view_12h_attr", 
        "ua_click_long_view_1d_attr", "ua_click_long_view_3d_attr", 
        "ua_click_long_view_7d_attr", "ua_click_long_view_14d_attr", 
        "ua_click_long_view_28d_attr", 
        "ua_like_long_view_nd_attr", "ua_like_long_view_30m_attr", 
        "ua_like_long_view_1h_attr", "ua_like_long_view_3h_attr", 
        "ua_like_long_view_6h_attr", "ua_like_long_view_12h_attr", 
        "ua_like_long_view_1d_attr", "ua_like_long_view_3d_attr", 
        "ua_like_long_view_7d_attr", "ua_like_long_view_14d_attr", 
        "ua_like_long_view_28d_attr", 
        "ua_comment_long_view_nd_attr", "ua_comment_long_view_30m_attr", 
        "ua_comment_long_view_1h_attr", "ua_comment_long_view_3h_attr", 
        "ua_comment_long_view_6h_attr", "ua_comment_long_view_12h_attr", 
        "ua_comment_long_view_1d_attr", "ua_comment_long_view_3d_attr", 
        "ua_comment_long_view_7d_attr", "ua_comment_long_view_14d_attr", 
        "ua_comment_long_view_28d_attr", 
    }

class CommonLiveClusterRFMRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_cluster_rfm_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])
    return ret

  @property
  @strict_types
  def output_item_v2_attrs(self) -> set:
    return {
      "uc_long_view_1h_attr", "uc_long_view_1d_attr", 
      "uc_long_view_7d_attr", "uc_long_view_nd_attr", 
      "uc_watch_time_1h_attr", "uc_watch_time_1d_attr", 
      "uc_watch_time_7d_attr", "uc_watch_time_nd_attr", 
      "uc_play_times_1h_attr", "uc_play_times_1d_attr", 
      "uc_play_times_7d_attr", "uc_play_times_nd_attr", 
      "uc_auto_long_view_1h_attr", "uc_auto_long_view_1d_attr", 
      "uc_auto_long_view_7d_attr", "uc_auto_long_view_nd_attr", 
      "uc_auto_watch_time_1h_attr", "uc_auto_watch_time_1d_attr", 
      "uc_auto_watch_time_7d_attr", "uc_auto_watch_time_nd_attr", 
      "uc_auto_play_times_1h_attr", "uc_auto_play_times_1d_attr", 
      "uc_auto_play_times_7d_attr", "uc_auto_play_times_nd_attr", 
      "uc_in_long_view_1h_attr", "uc_in_long_view_1d_attr", 
      "uc_in_long_view_7d_attr", "uc_in_long_view_nd_attr", 
      "uc_in_watch_time_1h_attr", "uc_in_watch_time_1d_attr", 
      "uc_in_watch_time_7d_attr", "uc_in_watch_time_nd_attr", 
      "uc_in_play_times_1h_attr", "uc_in_play_times_1d_attr", 
      "uc_in_play_times_7d_attr", "uc_in_play_times_nd_attr", 
      "uc_click_long_view_1h_attr", "uc_click_long_view_1d_attr", 
      "uc_click_long_view_7d_attr", "uc_click_long_view_nd_attr", 
      "uc_like_long_view_1h_attr", "uc_like_long_view_1d_attr", 
      "uc_like_long_view_7d_attr", "uc_like_long_view_nd_attr", 
      "uc_comment_long_view_1h_attr", "uc_comment_long_view_1d_attr", 
      "uc_comment_long_view_7d_attr", "uc_comment_long_view_nd_attr", 
    }


class CommonLiveRFMSeqRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_rfm_seq_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "hour_u_live_show_attr_list", "hour_u_live_effective_view_attr_list", "hour_u_live_eff_10s_click_view_attr_list",
      "day_u_live_show_attr_list", "day_u_live_effective_view_attr_list", "day_u_live_eff_10s_click_view_attr_list",
      "month_u_live_show_attr_list", "month_u_live_effective_view_attr_list", "month_u_live_eff_10s_click_view_attr_list"
    }
  
class CommonLiveTAGSeqRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_tag_seq_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "highfreq_live_glabel_tag_list",
      "highfreq_live_glabel_count_list",
      "highfreq_live_giftlabel_tag_list",
      "highfreq_live_giftlabel_count_list",
      "highfreq_live_efview_tag_list",
      "highfreq_live_efview_count_list",
      "highfreq_live_like_tag_list",
      "highfreq_live_like_count_list",
      "highfreq_live_comment_tag_list",
      "highfreq_live_comment_count_list",
      "highfreq_live_follow_tag_list",
      "highfreq_live_follow_count_list",
      "highfreq_live_show_tag_list",
      "highfreq_live_show_count_list",
      "highfreq_live_longview_tag_list",
      "highfreq_live_longview_count_list"
    }
  
class CommonLiveTAGSeqRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_tag_seq_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "highfreq_live_glabel_tag_list",
      "highfreq_live_glabel_count_list",
      "highfreq_live_giftlabel_tag_list",
      "highfreq_live_giftlabel_count_list",
      "highfreq_live_efview_tag_list",
      "highfreq_live_efview_count_list",
      "highfreq_live_like_tag_list",
      "highfreq_live_like_count_list",
      "highfreq_live_comment_tag_list",
      "highfreq_live_comment_count_list",
      "highfreq_live_follow_tag_list",
      "highfreq_live_follow_count_list",
      "highfreq_live_show_tag_list",
      "highfreq_live_show_count_list",
      "highfreq_live_longview_tag_list",
      "highfreq_live_longview_count_list"
    }

class CommonLiveTAGSeqV2RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_tag_seqv2_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "highfreq_live_cluster_list",
      "highfreq_live_cluster_glabel_count_list",
      "highfreq_live_cluster_giftlabel_count_list",
      "highfreq_live_cluster_efview_count_list",
      "highfreq_live_cluster_like_count_list",
      "highfreq_live_cluster_comment_count_list",
      "highfreq_live_cluster_follow_count_list",
      "highfreq_live_cluster_show_count_list",
      "highfreq_live_cluster_longview_count_list"
    }


class CommonLiveColossusGiftAuthorRankRfmRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_colossus_gift_author_rank_rfm_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        "ua_reward_amt_1d_rank_attr", "ua_play_times_1d_rank_attr", "ua_watch_live_times_1d_rank_attr",
        "ua_reward_amt_3d_rank_attr", "ua_play_times_3d_rank_attr", "ua_watch_live_times_3d_rank_attr",
        "ua_reward_amt_7d_rank_attr", "ua_play_times_7d_rank_attr", "ua_watch_live_times_7d_rank_attr",
        "ua_reward_amt_14d_rank_attr", "ua_play_times_14d_rank_attr", "ua_watch_live_times_14d_rank_attr",
        "ua_reward_amt_28d_rank_attr", "ua_play_times_28d_rank_attr", "ua_watch_live_times_28d_rank_attr",
    }

class CommonLiveColossusGiftAuthorRankRfmV2RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_colossus_gift_author_rank_rfm_v2_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        "ua_watch_live_times_1_rank_v2_attr", "ua_watch_live_times_6_rank_v2_attr",
        "ua_watch_live_times_2_rank_v2_attr", "ua_watch_live_times_7_rank_v2_attr",
        "ua_watch_live_times_3_rank_v2_attr", "ua_watch_live_times_8_rank_v2_attr",
        "ua_watch_live_times_4_rank_v2_attr", "ua_watch_live_times_9_rank_v2_attr",
        "ua_watch_live_times_5_rank_v2_attr", "ua_watch_live_times_10_rank_v2_attr",
        "ua_play_times_1_rank_v2_attr", "ua_play_times_6_rank_v2_attr",
        "ua_play_times_2_rank_v2_attr", "ua_play_times_7_rank_v2_attr",
        "ua_play_times_3_rank_v2_attr", "ua_play_times_8_rank_v2_attr",
        "ua_play_times_4_rank_v2_attr", "ua_play_times_9_rank_v2_attr",
        "ua_play_times_5_rank_v2_attr", "ua_play_times_10_rank_v2_attr",
        "ua_reward_amt_1_rank_v2_attr", "ua_reward_amt_6_rank_v2_attr",
        "ua_reward_amt_2_rank_v2_attr", "ua_reward_amt_7_rank_v2_attr",
        "ua_reward_amt_3_rank_v2_attr", "ua_reward_amt_8_rank_v2_attr",
        "ua_reward_amt_4_rank_v2_attr", "ua_reward_amt_9_rank_v2_attr",
        "ua_reward_amt_5_rank_v2_attr", "ua_reward_amt_10_rank_v2_attr",
        "ua_watch_live_times_1_value_v2_attr", "ua_watch_live_times_6_value_v2_attr",
        "ua_watch_live_times_2_value_v2_attr", "ua_watch_live_times_7_value_v2_attr",
        "ua_watch_live_times_3_value_v2_attr", "ua_watch_live_times_8_value_v2_attr",
        "ua_watch_live_times_4_value_v2_attr", "ua_watch_live_times_9_value_v2_attr",
        "ua_watch_live_times_5_value_v2_attr", "ua_watch_live_times_10_value_v2_attr",
        "ua_play_times_1_value_v2_attr", "ua_play_times_6_value_v2_attr",
        "ua_play_times_2_value_v2_attr", "ua_play_times_7_value_v2_attr",
        "ua_play_times_3_value_v2_attr", "ua_play_times_8_value_v2_attr",
        "ua_play_times_4_value_v2_attr", "ua_play_times_9_value_v2_attr",
        "ua_play_times_5_value_v2_attr", "ua_play_times_10_value_v2_attr",
        "ua_reward_amt_1_value_v2_attr", "ua_reward_amt_6_value_v2_attr",
        "ua_reward_amt_2_value_v2_attr", "ua_reward_amt_7_value_v2_attr",
        "ua_reward_amt_3_value_v2_attr", "ua_reward_amt_8_value_v2_attr",
        "ua_reward_amt_4_value_v2_attr", "ua_reward_amt_9_value_v2_attr",
        "ua_reward_amt_5_value_v2_attr", "ua_reward_amt_10_value_v2_attr"
    }

class CommonLiveColossusGiftAuthorFeatureV4RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_colossus_gift_author_feature_v4_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        #"author_id", "vec_time", "author_size", "user_author_size",
        "ua_first_play_day_lag_attr_iso", "ua_last_play_day_lag_attr_iso",
        "ua_watch_live_cnt_attr_iso", "ua_watch_live_day_attr_iso",
        "ua_play_times_1d_attr_iso", "ua_watch_live_times_1d_attr_iso",
        "ua_play_times_3d_attr_iso", "ua_watch_live_times_3d_attr_iso",
        "ua_play_times_7d_attr_iso", "ua_watch_live_times_7d_attr_iso",
        "ua_play_times_14d_attr_iso", "ua_watch_live_times_14d_attr_iso",
        "ua_play_times_28d_attr_iso", "ua_watch_live_times_28d_attr_iso",
        "ua_watch_live_times_attr_iso",
        "ua_avg_watch_live_times_attr_iso", "ua_max_watch_live_times_attr_iso",
        "ua_min_watch_live_times_attr_iso", "ua_avg_daily_watch_live_times_attr_iso",
        "ua_max_daily_watch_live_times_attr_iso", "ua_min_daily_watch_live_times_attr_iso",
    }


class CommonLiveColossusGiftAuthorFeatureV5RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_colossus_gift_author_feature_v5_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["author_id_attr"])

    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        "vec_reward_author_id","vec_reward_amt","vec_play_duration", "vec_time_lag","vec_hetu_tag",
        "ua_first_play_day_lag_attr", "ua_last_play_day_lag_attr", "ua_first_reward_day_lag_attr",
        "ua_last_reward_day_lag_attr", "ua_total_reward_times_attr", "ua_total_reward_days_attr",
        "ua_total_reward_amt_attr", "ua_avg_live_reward_amt_attr", "ua_max_live_reward_amt_attr",
        "ua_min_live_reward_amt_attr", "ua_avg_daily_reward_amt_attr", "ua_max_daily_reward_amt_attr",
        "ua_min_daily_reward_amt_attr", "ua_watch_live_cnt_attr", "ua_watch_live_day_attr",
        "ua_live_reward_rate_attr",
        "ua_reward_amt_1d_attr", "ua_play_times_1d_attr", "ua_watch_live_times_1d_attr",
        "ua_reward_amt_3d_attr", "ua_play_times_3d_attr", "ua_watch_live_times_3d_attr",
        "ua_reward_amt_7d_attr", "ua_play_times_7d_attr", "ua_watch_live_times_7d_attr",
        "ua_reward_amt_14d_attr", "ua_play_times_14d_attr", "ua_watch_live_times_14d_attr",
        "ua_reward_amt_28d_attr", "ua_play_times_28d_attr", "ua_watch_live_times_28d_attr",
        "ua_reward_rate_in_all_attr",
        "u_total_reward_amt_attr", "u_total_reward_times_attr", "u_total_reward_days_attr",
        "u_total_reward_amt_1d_attr", "u_total_play_times_1d_attr", "u_total_watch_live_times_1d_attr",
        "u_total_reward_amt_3d_attr", "u_total_play_times_3d_attr", "u_total_watch_live_times_3d_attr",
        "u_total_reward_amt_7d_attr", "u_total_play_times_7d_attr", "u_total_watch_live_times_7d_attr",
        "u_total_reward_amt_14d_attr", "u_total_play_times_14d_attr", "u_total_watch_live_times_14d_attr",
        "u_total_reward_amt_28d_attr", "u_total_play_times_28d_attr", "u_total_watch_live_times_28d_attr",
        "ua_first_reward_hour_lag_attr", "ua_last_reward_hour_lag_attr",
    }


class CommonLiveColossusGiftAuthorFeatureV6RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_reco_colossus_user_feature_v6_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["filter_future_attr"])
    ret.add(self._config["kess_service"])
    ret.add(self._config["shards"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "u_last_action_hour_lag_attr_1d_", "u_last_action_hour_lag_attr_3d_", "u_last_action_hour_lag_attr_7d_",
      "u_last_action_hour_lag_attr_14d_", "u_last_action_hour_lag_attr_28d_", "u_last_action_hour_lag_attr_",
      "u_total_play_cnt_attr_1d_", "u_total_play_cnt_attr_3d_", "u_total_play_cnt_attr_7d_",
      "u_total_play_cnt_attr_14d_", "u_total_play_cnt_attr_28d_", "u_total_play_cnt_attr_",
      "u_total_play_time_attr_1d_", "u_total_play_time_attr_3d_", "u_total_play_time_attr_7d_",
      "u_total_play_time_attr_14d_", "u_total_play_time_attr_28d_", "u_total_play_time_attr_",
      "u_total_like_attr_1d_", "u_total_like_attr_3d_", "u_total_like_attr_7d_", "u_total_like_attr_14d_",
      "u_total_like_attr_28d_", "u_total_like_attr_", "u_total_hate_attr_1d_", "u_total_hate_attr_3d_",
      "u_total_hate_attr_7d_", "u_total_hate_attr_14d_", "u_total_hate_attr_28d_", "u_total_hate_attr_",
      "u_total_forward_attr_1d_", "u_total_forward_attr_3d_", "u_total_forward_attr_7d_",
      "u_total_forward_attr_14d_", "u_total_forward_attr_28d_", "u_total_forward_attr_",
      "u_total_comment_attr_1d_", "u_total_comment_attr_3d_", "u_total_comment_attr_7d_",
      "u_total_comment_attr_14d_", "u_total_comment_attr_28d_", "u_total_comment_attr_",
      "u_total_enter_profile_attr_1d_", "u_total_enter_profile_attr_3d_", "u_total_enter_profile_attr_7d_",
      "u_total_enter_profile_attr_14d_", "u_total_enter_profile_attr_28d_", "u_total_enter_profile_attr_",
      "u_cluster_last_action_hour_lag_attr_1d_", "u_cluster_last_action_hour_lag_attr_3d_",
      "u_cluster_last_action_hour_lag_attr_7d_", "u_cluster_last_action_hour_lag_attr_14d_",
      "u_cluster_last_action_hour_lag_attr_28d_", "u_cluster_last_action_hour_lag_attr_",
      "u_cluster_play_cnt_attr_1d_", "u_cluster_play_cnt_attr_3d_", "u_cluster_play_cnt_attr_7d_",
      "u_cluster_play_cnt_attr_14d_", "u_cluster_play_cnt_attr_28d_", "u_cluster_play_cnt_attr_",
      "u_cluster_play_time_attr_1d_", "u_cluster_play_time_attr_3d_", "u_cluster_play_time_attr_7d_",
      "u_cluster_play_time_attr_14d_", "u_cluster_play_time_attr_28d_", "u_cluster_play_time_attr_",
      "u_cluster_like_attr_1d_", "u_cluster_like_attr_3d_", "u_cluster_like_attr_7d_",
      "u_cluster_like_attr_14d_", "u_cluster_like_attr_28d_", "u_cluster_like_attr_",
      "u_cluster_hate_attr_1d_", "u_cluster_hate_attr_3d_", "u_cluster_hate_attr_7d_",
      "u_cluster_hate_attr_14d_", "u_cluster_hate_attr_28d_", "u_cluster_hate_attr_",
      "u_cluster_forward_attr_1d_", "u_cluster_forward_attr_3d_", "u_cluster_forward_attr_7d_",
      "u_cluster_forward_attr_14d_", "u_cluster_forward_attr_28d_", "u_cluster_forward_attr_",
      "u_cluster_comment_attr_1d_", "u_cluster_comment_attr_3d_", "u_cluster_comment_attr_7d_",
      "u_cluster_comment_attr_14d_", "u_cluster_comment_attr_28d_", "u_cluster_comment_attr_",
      "u_cluster_enter_profile_attr_1d_", "u_cluster_enter_profile_attr_3d_",
      "u_cluster_enter_profile_attr_7d_", "u_cluster_enter_profile_attr_14d_",
      "u_cluster_enter_profile_attr_28d_", "u_cluster_enter_profile_attr_",
    }


class ColossusUserStatsFeatureRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_user_stats_feature_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_list_from_attr"])
    ret.update(self.extract_dynamic_params(self._config.get("filter_older_actions_seconds")))
    ret.update(self.extract_dynamic_params(self._config.get("max_action_cnt_after_filter")))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_duration_attr"])
    ret.add(self._config["colossus_play_attr"])
    ret.add(self._config["colossus_label_attr"])
    ret.add(self._config["colossus_channel_attr"])
    ret.add(self._config["colossus_time_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      self._config[key] for key in [
        "save_play_count_to_attr",
        "save_play_time_to_attr", "save_play_time_avg_to_attr",
        "save_like_count_to_attr", "save_like_rate_to_attr",
        "save_follow_count_to_attr", "save_follow_rate_to_attr",
        "save_forward_count_to_attr", "save_forward_rate_to_attr",
        "save_hate_count_to_attr", "save_hate_rate_to_attr",
        "save_comment_count_to_attr", "save_comment_rate_to_attr",
        "save_enter_profile_count_to_attr", "save_enter_profile_rate_to_attr",
        "save_enter_comment_count_to_attr", "save_enter_comment_rate_to_attr",
        "save_short_view_count_to_attr", "save_short_view_rate_to_attr",
        "save_effective_view_count_to_attr", "save_effective_view_rate_to_attr",
        "save_long_view_count_to_attr", "save_long_view_rate_to_attr",
        "save_long_long_view_count_to_attr", "save_long_long_view_rate_to_attr",
        "save_play_complete_count_to_attr", "save_play_complete_rate_to_attr",
        "save_pic_effective_view_count_to_attr", "save_pic_effective_view_rate_to_attr",
        "save_commertial_cnt_to_attr", "save_commertial_rate_to_attr"
      ] if key in self._config
    }


class ColossusUserCateStatsFeatureRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_user_cate_stats_feature_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_list_from_attr"])
    ret.update(self.extract_dynamic_params(self._config.get("filter_older_actions_seconds")))
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_duration_attr"])
    ret.add(self._config["colossus_play_attr"])
    ret.add(self._config["colossus_label_attr"])
    ret.add(self._config["colossus_channel_attr"])
    ret.add(self._config["colossus_time_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      self._config[key] for key in [
        "save_play_count_to_attr",
        "save_play_time_to_attr", "save_play_time_avg_to_attr",
        "save_like_count_to_attr", "save_like_rate_to_attr",
        "save_follow_count_to_attr", "save_follow_rate_to_attr",
        "save_forward_count_to_attr", "save_forward_rate_to_attr",
        "save_hate_count_to_attr", "save_hate_rate_to_attr",
        "save_comment_count_to_attr", "save_comment_rate_to_attr",
        "save_enter_profile_count_to_attr", "save_enter_profile_rate_to_attr",
        "save_enter_comment_count_to_attr", "save_enter_comment_rate_to_attr",
        "save_short_view_count_to_attr", "save_short_view_rate_to_attr",
        "save_effective_view_count_to_attr", "save_effective_view_rate_to_attr",
        "save_long_view_count_to_attr", "save_long_view_rate_to_attr",
        "save_long_long_view_count_to_attr", "save_long_long_view_rate_to_attr",
        "save_play_complete_count_to_attr", "save_play_complete_rate_to_attr",
        "save_pic_effective_view_count_to_attr", "save_pic_effective_view_rate_to_attr",
      ] if key in self._config
    }

class CommonRecoColossusUserFeatureRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_reco_colossus_user_feature_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["filter_future_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
        "u_last_action_hour_lag_attr", "u_total_play_cnt_attr", "u_total_play_time_attr",
        "u_total_like_attr", "u_total_hate_attr", "u_total_forward_attr", "u_total_comment_attr",
        "u_total_enter_profile_attr", "u_session_acts_attr", "u_author_last_action_hour_lag_attr",
        "u_author_play_cnt_attr", "u_author_play_time_attr", "u_author_like_attr", "u_author_hate_attr",
        "u_author_forward_attr", "u_author_comment_attr", "u_author_enter_profile_attr",
        "u_author_session_acts_attr"
    }


class CommonRecoColossusUserFeatureV2RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_reco_colossus_user_feature_v2_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["colossus_aid_list_attr"])
    ret.add(self._config["sorted_item_idx_attr"])
    ret.add(self._config["top_n"])
    ret.add(self._config["filter_future_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
        "u_last_action_hour_lag_attr", "u_total_play_cnt_attr", "u_total_play_time_attr",
        "u_total_like_attr", "u_total_hate_attr", "u_total_forward_attr", "u_total_comment_attr",
        "u_total_enter_profile_attr", "u_session_acts_attr", "output_aid_list_attr",
        "u_author_last_action_hour_lag_attr", "u_author_play_cnt_attr", "u_author_play_time_attr",
        "u_author_like_attr", "u_author_hate_attr", "u_author_forward_attr", "u_author_comment_attr",
        "u_author_enter_profile_attr", "u_author_session_acts_attr"
    }


class CommonRecoColossusUserFeatureV3RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_reco_colossus_user_feature_v3_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["filter_future_attr"])
    ret.add(self._config["kess_service"])
    ret.add(self._config["shards"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
        "u_last_action_hour_lag_attr", "u_total_play_cnt_attr", "u_total_play_time_attr",
        "u_total_like_attr", "u_total_hate_attr", "u_total_forward_attr", "u_total_comment_attr",
        "u_total_enter_profile_attr", "u_cluster_last_action_hour_lag_attr", "u_cluster_play_cnt_attr",
        "u_cluster_play_time_attr", "u_cluster_like_attr", "u_cluster_hate_attr", "u_cluster_forward_attr",
        "u_cluster_comment_attr", "u_cluster_enter_profile_attr",
    }

class CommonLiveColossusUserConsumFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_colossus_user_consum_feature_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["time_ms_common_attr"])
    ret.add(self._config["week_ctr_threshold"])
    ret.add(self._config["month_ctr_threshold"])
    ret.add(self._config["week_eff_click_threshold"])
    ret.add(self._config["month_eff_click_threshold"])

    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attrs = [
      "week_live_u_shows", "week_live_u_clicks", 
      "week_live_u_ctr", "week_live_u_ctr_level", "week_live_u_eff_click_level",
      "month_live_u_shows", "month_live_u_clicks",
      "month_live_u_ctr", "month_live_u_ctr_level", "month_live_u_eff_click_level"
      ]

    for attr in attrs:
      ret.add(attr)
    return ret

class CommonVideoColossusUserConsumFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_video_colossus_user_consum_feature_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_photos_attr"])
    ret.add(self._config["time_ms_common_attr"])
    ret.add(self._config["week_ctr_threshold"])
    ret.add(self._config["month_ctr_threshold"])
    ret.add(self._config["week_eff_click_threshold"])
    ret.add(self._config["month_eff_click_threshold"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attrs = [
      "week_video_u_shows", "week_video_u_clicks", 
      "week_video_u_ctr", "week_video_u_ctr_level", "week_video_u_eff_click_level",
      "month_video_u_shows", "month_video_u_clicks",
      "month_video_u_ctr", "month_video_u_ctr_level", "month_video_u_eff_click_level"
      ]
    
    for attr in attrs:
      ret.add(attr)
    return ret

class ColossusUserAuthorFeatureRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_user_author_feature_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("filter_older_actions_seconds")))
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["filter_future_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_author_id_attr"])
    return ret


  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config[key] for key in [
        "u_author_last_action_hour_lag_attr", "u_author_play_cnt_attr", "u_author_effective_view_attr",
        "u_author_play_time_attr", "u_author_play_rate_attr", "u_author_like_attr", "u_author_hate_attr",
        "u_author_enter_comment_attr", "u_author_enter_profile_attr",
      ] if key in self._config
    }

class ColossusUgGsuWithClusterEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_ug_gsu_with_cluster"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["photo_id_attr"])
    ret.add(self._config["author_id_attr"])
    ret.add(self._config["tag_id_attr"])
    ret.add(self._config["timestamp_attr"])
    ret.add(self._config["playtime_id_attr"])
    ret.add(self._config["duration_attr"])
    ret.add(self._config["limit_num_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_pid_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config["output_cluster_attr"]
    }

class DualLiveSortItemListAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "dual_live_sort_item_list_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["limit"])
    ret.add(self._config["weight_thresh"])
    ret.add(self._config["weight_bucket"])
    ret.add(self._config["diff_bucket"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_dual_uid"])
    ret.add(self._config["item_dual_ts"])
    ret.add(self._config["item_dual_weight"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["fplay_uid_list"])
    ret.add(self._config["fplay_time_list"])
    ret.add(self._config["fplay_diff_list"])
    ret.add(self._config["fplay_playing_list"])
    return ret

class CommonLiveColossusLiveidFeatureRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_colossus_liveid_feature_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "uLatestHateTimeLagAttr", "uLatestHateLiveIdAttr", "uLatestHateAuthorIdAttr",
      "uLatestHateClusterIdAttr", "uHourHateCntAttr", "uDayHateCntAttr", "uWeekHateCntAttr",
      "uHourHateRateAttr", "uDayHateRateAttr", "uWeekHateRateAttr", "uLatestShortViewTimeLagAttr",
      "uLatestShortViewLiveIdAttr", "uLatestShortViewAuthorIdAttr", "uLatestShortViewClusterIdAttr",
      "uHourShortViewCntAttr", "uDayShortViewCntAttr", "uWeekShortViewCntAttr", "uHourShortViewRateAttr",
      "uDayShortViewRateAttr", "uWeekShortViewRateAttr", "uHourShowCntAttr", "uHourShowNoCLickCntAttr",
      "uHourShowNoClickRateAttr", "uDayShowCntAttr", "uDayShowNoClickCntAttr", "uDayShowNoClickRateAttr",
      "uWeekShowCntAttr", "uWeekShowNoClickCntAttr", "uWeekShowNoClickRateAttr", "showLatestTimeLagLiveAttr",
      "showLatestAutoWatchTimeLiveAttr", "showLatestRealWatchTimeLiveAttr", "showHourCntLiveAttr",
      "showHourTotalAutoWatchTimeLiveAttr", "showHourTotalRealWatchTimeLiveAttr", "shortViewCntLiveAttr",
      "showLatestTimeLagAuthorAttr", "showLatestAutoWatchTimeAuthorAttr", "showLatestRealWatchTimeAuthorAttr",
      "showTotalAutoWatchTimeAuthorAttr", "showHourCntAuthorAttr", "showHourTotalAutoWatchTimeAuthorAttr",
      "showHourTotalRealWatchTimeAuthorAttr", "showDayCntAuthorAttr", "showDayTotalAutoWatchTimeAuthorAttr",
      "showDayTotalRealWatchTimeAuthorAttr", "showWeekCntAuthorAttr", "showWeekTotalAutoWatchTimeAuthorAttr",
      "showWeekTotalRealWatchTimeAuthorAttr", "showCntAuthorAttr",
      "showDayTotalCommentAuthorAttr", "showThreeDayTotalCommentAuthorAttr", "showSevenDayTotalCommentAuthorAttr",
      "showFourteenDayTotalCommentAuthorAttr", "showThirtyDayTotalCommentAuthorAttr", 
      "showDayCommentDayAuthorAttr", "showThreeDayCommentDayAuthorAttr", "showSevenDayCommentDayAuthorAttr",
      "showFourteenDayCommentDayAuthorAttr", "showThirtyDayCommentDayAuthorAttr",
      "showTotalRealWatchTimeAuthorAttr", "shortViewCntAuthorAttr", "showLatestTimeLagClusterAttr",
      "showLatestAutoWatchTimeClusterAttr", "showLatestRealWatchTimeClusterAttr",
      "showTotalAutoWatchTimeClusterAttr", "showHourCntClusterAttr", "showHourTotalAutoWatchTimeClusterAttr",
      "showHourTotalRealWatchTimeClusterAttr", "showDayCntClusterAttr",
      "showDayTotalAutoWatchTimeClusterAttr", "showDayTotalRealWatchTimeClusterAttr",
      "showWeekCntClusterAttr", "showWeekTotalAutoWatchTimeClusterAttr",
      "showWeekTotalRealWatchTimeClusterAttr", "showCntClusterAttr",
      "showTotalRealWatchTimeClusterAttr", "shortViewCntClusterAttr",
      "uLatestShortViewTimeLagAttr_scenario", "uLatestShortViewLiveIdAttr_scenario",
      "uLatestShortViewAuthorIdAttr_scenario", "uLatestShortViewClusterIdAttr_scenario",
      "uHourShortViewCntAttr_scenario","uDayShortViewCntAttr_scenario","uWeekShortViewCntAttr_scenario",
      "uHourShortViewRateAttr_scenario","uDayShortViewRateAttr_scenario","uWeekShortViewRateAttr_scenario",
      "uHourShowCntAttr_scenario","uHourShowNoCLickCntAttr_scenario","uHourShowNoClickRateAttr_scenario",
      "uDayShowCntAttr_scenario","uDayShowNoClickCntAttr_scenario","uDayShowNoClickRateAttr_scenario",
      "uWeekShowCntAttr_scenario","uWeekShowNoClickCntAttr_scenario","uWeekShowNoClickRateAttr_scenario",
      "uHourTotalWatchTimeAttr_scenario","uDayTotalWatchTimeAttr_scenario","uWeekTotalWatchTimeAttr_scenario",
      "uHourShowCLickCntAttr_scenario","uHourShowClickRateAttr_scenario",
      "uDayShowClickCntAttr_scenario","uDayShowClickRateAttr_scenario",
      "uWeekShowClickCntAttr_scenario","uWeekShowClickRateAttr_scenario",
      "uHourShowCLickCntAttr","uHourShowClickRateAttr",
      "uDayShowClickCntAttr","uDayShowClickRateAttr",
      "uWeekShowClickCntAttr","uWeekShowClickRateAttr",
      "uTodayIsClickLiveAttr_scenario", "uTodayClickLiveCntAttr_scenario",
      "uTodayIsClickLiveAttr", "uTodayClickLiveCntAttr",
      "uTodayIsClickLiveAttrV2_scenario", "uTodayClickLiveCntAttrV2_scenario",
      "uTodayIsClickLiveAttrV2", "uTodayClickLiveCntAttrV2",
      "uTodayIsClickLiveAttrV3_scenario", "uTodayClickLiveCntAttrV3_scenario",
      "uTodayIsClickLiveAttrV3", "uTodayClickLiveCntAttrV3",
      "showWeekTotalEffClickCntAttr_scenario", "showWeekTotalRealWatchTimeAuthorAttr_scenario",
      "showWeekTotalEffClickCntAttr", "showWeekTotalRealWatchTimeAuthorAttr",
      "showFourteenDayTotalGiftAuthorAttr_scenario", "showFourteenDayTotalFollowAuthorAttr_scenario",
      "showFourteenDayTotalLikeAuthorAttr_scenario", "showFourteenDayTotalHateAuthorAttr_scenario",
      "showDayCntAuthorAttr_scenario", "showFourteenDayAttr", "showDayEffClickAttr_scenario"
    }
  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = [
      "showFourteenDayTotalEffClickAuthorAttr",
      "showThirtyDayTotalEffClickAuthorAttr"
    ]
    return set(attrs)

class CommonUserTimelyFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_user_timely_feature_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["lasted_num"])
    ret.add(self._config["lasted_play_over_threshold"])
    ret.add(self._config["lasted_play_less_threshold"])
    ret.add(self._config["filter_channel"])
    ret.add(self._config["filter_e_shop"])

    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    if "author_id_attr" in self._config:
      ret.add(self._config["author_id_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "user_lasted_valid_play_pids",
      "user_lasted_valid_play_aids",
      "user_lasted_valid_play_lag",
      "user_lasted_valid_play_channel",
      "user_lasted_valid_play_watch",
      "user_lasted_valid_play_tag",
      "user_lasted_valid_play_cluster",
      "user_lasted_valid_play_label",
      "user_lasted_invalid_play_aids",
      "user_lasted_invalid_play_lag",
      "user_lasted_invalid_play_channel",
      "user_lasted_invalid_play_watch",
      "user_lasted_invalid_play_tag",
      "user_lasted_invalid_play_cluster",
      "user_lasted_invalid_play_label",
      "userLuckColossusCommentList",
      "userLuckColossusLikeList",
      "userLuckColossusForwardList",
      "userLuckColossusProfileList",
      "userLuckColossusFollowList",
      "userLuckColossusGiftList"
    }

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {'ua_live_play_aids', 'ua_live_play_lags', 'ua_live_play_channels', 'ua_live_play_watchs', 
            'ua_live_play_tags', 'ua_live_play_labels', 'ua_live_play_clusters'}

class CommonLiveUserFollowSeqEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_user_follow_seq_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["lasted_num"])
    ret.add(self._config["filter_channel"])
    ret.add(self._config["filter_e_shop"])
    ret.add(self._config["use_filter_time"])
    ret.add(self._config["filter_time"])

    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "user_lasted_follow_aids",
      "user_lasted_follow_live_ids",
      "user_lasted_follow_lag",
      "user_lasted_follow_watch",
      "user_lasted_follow_play_tag",
      "user_lasted_follow_play_cluster",
      "user_lasted_follow_play_label",
      "user_lasted_follow_total_click",
      "user_lasted_follow_total_like",
      "user_lasted_follow_total_comment",
      "user_lasted_follow_total_gift_amt",
      "user_lasted_follow_total_gift_count",
      "user_lasted_follow_total_forward",
      "user_lasted_follow_total_profile",
      "user_lasted_follow_total_inwatch_time",
      "user_lasted_follow_total_watch_time"
    }
    
class CommonLiveUserEffectiveClickSeqEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_user_eff_click_seq_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["lasted_num"])
    ret.add(self._config["filter_channel"])
    ret.add(self._config["filter_e_shop"])
    ret.add(self._config["use_filter_time"])
    ret.add(self._config["filter_time"])

    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "user_lasted_eff_click_aids",
      "user_lasted_eff_click_live_ids",
      "user_lasted_eff_click_lag",
      "user_lasted_eff_click_watch",
      "user_lasted_eff_click_tag",
      "user_lasted_eff_click_cluster",
      "user_lasted_eff_click_label",
      "user_lasted_eff_click_total_click",
      "user_lasted_eff_click_total_like",
      "user_lasted_eff_click_total_comment",
      "user_lasted_eff_click_total_gift_amt",
      "user_lasted_eff_click_total_gift_count",
      "user_lasted_eff_click_total_forward",
      "user_lasted_eff_click_total_profile",
      "user_lasted_eff_click_total_inwatch_time",
      "user_lasted_eff_click_total_watch_time"
    }
    
class CommonLiveUserMixFollowSeqEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_user_mix_follow_seq_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["pick_num"])
    ret.add(self._config["filter_channel"])
    ret.add(self._config["filter_e_shop"])
    ret.add(self._config["use_filter_time"])
    ret.add(self._config["filter_time"])

    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {
      "user_lasted_mix_follow_aids",
      "user_lasted_mix_follow_lag",
      "user_lasted_mix_follow_play_tag",
      "user_lasted_mix_follow_play_cluster",
      "user_lasted_mix_follow_total_click",
      "user_lasted_mix_follow_total_like",
      "user_lasted_mix_follow_total_comment",
      "user_lasted_mix_follow_total_gift_amt",
      "user_lasted_mix_follow_total_gift_count",
      "user_lasted_mix_follow_total_inwatch_time",
      "user_lasted_mix_follow_total_watch_time",
      "user_lasted_mix_follow_total_watch_time_7d",
      "user_lasted_mix_follow_total_inwatch_time_7d",
      "user_lasted_mix_follow_total_gift_amt_7d",
      "user_lasted_mix_follow_avg_playtime_7d"
    }
    
class ColossusUnitLiveidSideinfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_unit_liveid_sideinfo_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["begin_time"])
    ret.add(self._config["end_time"])

    return ret
  
  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["live_id_list"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      "unit_item_colossusV4_author_id",
      "unit_item_colossusV4_gift_amt",
      "unit_item_colossusV4_avg_playtime"
    }

class CommonAdLiveColossusLiveidFeatureRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_ad_live_colossus_liveid_feature_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      "uLatestHateTimeLagAttrAD",
      "uLatestHateLiveIdAttrAD",
      "uLatestHateAuthorIdAttrAD",
      "uLatestHateClusterIdAttrAD",
      "uHourHateCntAttrAD",
      "uDayHateCntAttrAD",
      "uWeekHateCntAttrAD",
      "uHourHateRateAttrAD",
      "uDayHateRateAttrAD",
      "uWeekHateRateAttrAD",
      "uLatestShortViewTimeLagAttrAD",
      "uLatestShortViewLiveIdAttrAD",
      "uLatestShortViewAuthorIdAttrAD",
      "uLatestShortViewClusterIdAttrAD",
      "uHourShortViewCntAttrAD",
      "uDayShortViewCntAttrAD",
      "uWeekShortViewCntAttrAD",
      "uHourShortViewRateAttrAD",
      "uDayShortViewRateAttrAD",
      "uWeekShortViewRateAttrAD",
      "uLatestLongViewTimeLagAttrAD",
      "uLatestLongViewLiveIdAttrAD",
      "uLatestLongViewAuthorIdAttrAD",
      "uLatestLongViewClusterIdAttrAD",
      "uHourLongViewCntAttrAD",
      "uDayLongViewCntAttrAD",
      "uWeekLongViewCntAttrAD",
      "uHourLongViewRateAttrAD",
      "uDayLongViewRateAttrAD",
      "uWeekLongViewRateAttrAD",
      "uHourShowCntAttrAD",
      "uHourShowNoCLickCntAttrAD",
      "uHourShowNoClickRateAttrAD",
      "uHourShowCLickCntAttrAD",
      "uHourShowClickRateAttrAD",
      "uDayShowCntAttrAD",
      "uDayShowNoClickCntAttrAD",
      "uDayShowNoClickRateAttrAD",
      "uDayShowClickCntAttrAD",
      "uDayShowClickRateAttrAD",
      "uWeekShowCntAttrAD",
      "uWeekShowNoClickCntAttrAD",
      "uWeekShowNoClickRateAttrAD",
      "uWeekShowClickCntAttrAD",
      "uWeekShowClickRateAttrAD",
      "showLatestTimeLagLiveAttrAD",
      "showLatestRealWatchTimeLiveAttrAD",
      "showHourCntLiveAttrAD",
      "showHourTotalRealWatchTimeLiveAttrAD",
      "shortViewCntLiveAttrAD",
      "longViewCntLiveAttrAD",
      "showLatestTimeLagAuthorAttrAD",
      "showLatestRealWatchTimeAuthorAttrAD",
      "showHourCntAuthorAttrAD",
      "showHourTotalRealWatchTimeAuthorAttrAD",
      "showDayCntAuthorAttrAD",
      "showDayTotalRealWatchTimeAuthorAttrAD",
      "showWeekCntAuthorAttrAD",
      "showWeekTotalRealWatchTimeAuthorAttrAD",
      "showCntAuthorAttrAD",
      "showTotalRealWatchTimeAuthorAttrAD",
      "shortViewCntAuthorAttrAD",
      "longViewCntAuthorAttrAD",
      "showLatestTimeLagClusterAttrAD",
      "showLatestRealWatchTimeClusterAttrAD",
      "showHourCntClusterAttrAD",
      "showHourTotalRealWatchTimeClusterAttrAD",
      "showDayCntClusterAttrAD",
      "showDayTotalRealWatchTimeClusterAttrAD",
      "showWeekCntClusterAttrAD",
      "showWeekTotalRealWatchTimeClusterAttrAD",
      "showCntClusterAttrAD",
      "showTotalRealWatchTimeClusterAttrAD",
      "shortViewCntClusterAttrAD",
      "longViewCntClusterAttrAD"
    }


class LiveColossusUserShortTermAttrEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_colossus_user_short_term_attr_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["live_colossus_resp_attr"])
    ret.add(self._config["video_colossus_resp_attr"])
    ret.add(self._config["live_list_len"])
    ret.add(self._config["video_list_len"])
    ret.add(self._config["day_live_num_limit"])
    ret.add(self._config["day_video_num_limit"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {"u_today_live_effect_view_time", "u_today_live_effect_view_cnt",
            "u_today_live_long_view_cnt", "u_today_live_short_view_cnt",
            "u_today_live_show_cnt", "u_today_video_effect_view_time",
            "u_today_video_effect_view_cnt", "u_today_video_show_cnt",
            "u_live_last_long_view_interval", "u_live_last_short_view_interval",
            "u_live_last_effect_view_interval", "u_avg_live_effect_view_time",
            "u_avg_live_effect_view_cnt", "u_avg_video_effect_view_time",
            "u_avg_video_effect_view_cnt", "u_recent_live_author_id",
            "u_recent_video_author_id", "u_recent_video_photo_id",
            "u_recent_live_tag", "u_recent_video_tag",
            "u_recent_video_time_interval", "u_recent_live_time_interval",
            "u_recent_video_playtime", "u_recent_live_playtime",}

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {"u_today_similar_live_effect_view_time", "u_today_similar_live_effect_view_cnt",
            "u_today_similar_live_long_view_cnt", "u_today_similar_live_short_view_cnt",
            "u_today_similar_live_show_cnt", "u_similar_last_short_view_interval",
            "u_similar_last_long_view_interval", "u_similar_last_effect_view_interval",}

class ColossusLiteFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_reco_colossus_lite_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

class ColossusSessionFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_reco_colossus_session_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

class LiveRevenueColossusAuthorFeatureV5RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "live_revenue_colossus_author_feature_v5_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        #gift,
        "u_vec_long_reward_author_id_attr", 
        "u_vec_long_reward_amt_attr",
        "u_vec_long_reward_count_attr",
        "u_vec_long_follow_author_id_attr", 
        "u_vec_long_like_author_id_attr", 
        "u_vec_long_comment_author_id_attr",
    }
class LiveRevenueColossusAuthorFeatureDislikeRespEnricher(LeafEnricher):
    @classmethod
    @strict_types
    def get_type_alias(cls) -> str:
        return "live_revenue_colossus_author_feature_dislike_resp"

    @property
    @strict_types
    def input_common_attrs(self) -> set:
        ret = set()
        ret.add(self._config["colossus_resp_attr"])
        ret.add(self._config["u_long_reward_author_id_attr"])
        return ret

    @property
    @strict_types
    def output_common_attrs(self) -> set:
        return {
            "u_vec_dislike_author_id_attr",
            "u_vec_unfollow_author_id_attr",
            "u_vec_report_author_id_attr",
            "u_vec_redirect_and_skip_author_id_attr",
            "u_vec_auto_skip_author_id_attr",
            "u_vec_short_play_3s_author_id_attr",
            "u_vec_enter_not_click_gift_panel_author_id_attr",
            "u_vec_play_1min_not_gift_author_id_attr",
        }

class CommonAdLiveColossusPxsAuthorFeatureRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_ad_live_colossus_pxs_author_feature_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if "pxsP2lCtrAttr" in self._config:
      ret.add(self._config["pxsP2lCtrAttr"])
    else:
      ret.add("pxsP2lCtrAttr")

    if "pxsP2lP3sAttr" in self._config:
      ret.add(self._config["pxsP2lP3sAttr"])
    else:
      ret.add("pxsP2lP3sAttr")

    if "pxsP2lP5sAttr" in self._config:
      ret.add(self._config["pxsP2lP5sAttr"])
    else:
      ret.add("pxsP2lP5sAttr")

    if "pxsP2lP1sAttr" in self._config:
      ret.add(self._config["pxsP2lP1sAttr"])
    else:
      ret.add("pxsP2lP1sAttr")

    if "pxsP2lP2sAttr" in self._config:
      ret.add(self._config["pxsP2lP2sAttr"])
    else:
      ret.add("pxsP2lP2sAttr")

    return ret

class CommonVideoColossusUserLivingSeqRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_video_colossus_user_living_seq_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_photos_attr"])
    ret.add(self._config["time_ms_common_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add("u_lasted_living_aids")
    ret.add("u_lasted_living_pts")
    ret.add("u_lasted_living_lags")
    ret.add("u_lasted_living_pages")
    ret.add("u_lasted_living_labels")
    ret.add("u_lasted_living_n_tags")
    ret.add("u_lasted_living_ptds")
    ret.add("u_lasted_living_slags")
    ret.add("u_lasted_living_mlags")
    ret.add("u_lasted_living_olabels")

    return ret

class CommonVideoColossusUserLivingVideoClickSeqRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_video_colossus_user_living_video_click_seq_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_photos_attr"])
    ret.add(self._config["time_ms_common_attr"])
    ret.add(self._config["living_video_click_pid_list_attr"])
    ret.add(self._config["living_video_click_ts_list_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add("u_lasted_living_video_click_pids")
    ret.add("u_lasted_living_video_click_aids")
    ret.add("u_lasted_living_video_click_pts")
    ret.add("u_lasted_living_video_click_lags")
    ret.add("u_lasted_living_video_click_pages")
    ret.add("u_lasted_living_video_click_labels")
    ret.add("u_lasted_living_video_click_n_tags")
    ret.add("u_lasted_living_video_click_ptds")
    ret.add("u_lasted_living_video_click_slags")
    ret.add("u_lasted_living_video_click_mlags")
    ret.add("u_lasted_living_video_click_olabels")

    return ret

class CommonVideoColossusUserAuthorFeatureRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_video_colossus_user_author_feature_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    attrs = ["u_total_play_cnt_3min", 
      "u_total_play_cnt_30min", 
      "u_total_play_cnt_4h",
      "u_total_play_cnt_24h", 
      "u_total_play_cnt_3d", 
      "u_total_play_cnt_7d", 
      "u_total_play_cnt_14d", 
      "u_total_play_cnt_28d",
      "u_total_play_time_3min", 
      "u_total_play_time_30min",
      "u_total_play_time_4h",
      "u_total_play_time_24h", 
      "u_total_play_time_3d", 
      "u_total_play_time_7d", 
      "u_total_play_time_14d", 
      "u_total_play_time_28d",
      "u_total_play_ratio_3min", 
      "u_total_play_ratio_30min",
      "u_total_play_ratio_4h",
      "u_total_play_ratio_24h", 
      "u_total_play_ratio_3d", 
      "u_total_play_ratio_7d", 
      "u_total_play_ratio_14d", 
      "u_total_play_ratio_28d"]
    channels = ["", "_jx", "_js"]
    
    for channel in channels:
      for attr in attrs:
        ret.add(self._config.get(str(attr + channel)))
        
    ret.add(self._config.get("u_lasted_video_aids"))
    ret.add(self._config.get("u_lasted_video_pts"))
    ret.add(self._config.get("u_lasted_video_lags"))
    ret.add(self._config.get("u_lasted_video_pages"))
    ret.add(self._config.get("u_lasted_video_labels"))

    ret.add(self._config.get("u_lasted_video_n_pids"))
    ret.add(self._config.get("u_lasted_video_n_tags"))
    ret.add(self._config.get("u_lasted_video_ptds"))
    ret.add(self._config.get("u_lasted_video_slags"))
    ret.add(self._config.get("u_lasted_video_mlags"))
    ret.add(self._config.get("u_lasted_video_olabels"))
    
    ret.add(self._config["colossus_photos_attr"])
    ret.add(self._config.get("use_pb_format"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attrs = ["u_total_play_cnt_3min", 
      "u_total_play_cnt_30min", 
      "u_total_play_cnt_4h",
      "u_total_play_cnt_24h", 
      "u_total_play_cnt_3d", 
      "u_total_play_cnt_7d", 
      "u_total_play_cnt_14d", 
      "u_total_play_cnt_28d",
      "u_total_play_time_3min", 
      "u_total_play_time_30min",
      "u_total_play_time_4h",
      "u_total_play_time_24h", 
      "u_total_play_time_3d", 
      "u_total_play_time_7d", 
      "u_total_play_time_14d", 
      "u_total_play_time_28d",
      "u_total_play_ratio_3min", 
      "u_total_play_ratio_30min",
      "u_total_play_ratio_4h",
      "u_total_play_ratio_24h", 
      "u_total_play_ratio_3d", 
      "u_total_play_ratio_7d", 
      "u_total_play_ratio_14d", 
      "u_total_play_ratio_28d"]
    
    attrs_jx = ["u_total_play_cnt_3min_jx", 
      "u_total_play_cnt_30min_jx", 
      "u_total_play_cnt_4h_jx",
      "u_total_play_cnt_24h_jx", 
      "u_total_play_cnt_3d_jx", 
      "u_total_play_cnt_7d_jx", 
      "u_total_play_cnt_14d_jx", 
      "u_total_play_cnt_28d_jx",
      "u_total_play_time_3min_jx", 
      "u_total_play_time_30min_jx",
      "u_total_play_time_4h_jx",
      "u_total_play_time_24h_jx", 
      "u_total_play_time_3d_jx", 
      "u_total_play_time_7d_jx", 
      "u_total_play_time_14d_jx", 
      "u_total_play_time_28d_jx",
      "u_total_play_ratio_3min_jx", 
      "u_total_play_ratio_30min_jx",
      "u_total_play_ratio_4h_jx",
      "u_total_play_ratio_24h_jx", 
      "u_total_play_ratio_3d_jx", 
      "u_total_play_ratio_7d_jx", 
      "u_total_play_ratio_14d_jx", 
      "u_total_play_ratio_28d_jx"]
    
    attrs_js = ["u_total_play_cnt_3min_js", 
      "u_total_play_cnt_30min_js", 
      "u_total_play_cnt_4h_js",
      "u_total_play_cnt_24h_js", 
      "u_total_play_cnt_3d_js", 
      "u_total_play_cnt_7d_js", 
      "u_total_play_cnt_14d_js", 
      "u_total_play_cnt_28d_js",
      "u_total_play_time_3min_js", 
      "u_total_play_time_30min_js",
      "u_total_play_time_4h_js",
      "u_total_play_time_24h_js", 
      "u_total_play_time_3d_js", 
      "u_total_play_time_7d_js", 
      "u_total_play_time_14d_js", 
      "u_total_play_time_28d_js",
      "u_total_play_ratio_3min_js", 
      "u_total_play_ratio_30min_js",
      "u_total_play_ratio_4h_js",
      "u_total_play_ratio_24h_js", 
      "u_total_play_ratio_3d_js", 
      "u_total_play_ratio_7d_js", 
      "u_total_play_ratio_14d_js", 
      "u_total_play_ratio_28d_js"]
    for attr in attrs:
      ret.add(attr)
    for attr in attrs_jx:
      ret.add(attr)
    for attr in attrs_js:
      ret.add(attr)
    ret.add("u_lasted_video_aids")
    ret.add("u_lasted_video_pts")
    ret.add("u_lasted_video_lags")
    ret.add("u_lasted_video_pages")
    ret.add("u_lasted_video_labels")

    ret.add("u_lasted_video_n_pids")
    ret.add("u_lasted_video_n_tags")
    ret.add("u_lasted_video_ptds")
    ret.add("u_lasted_video_slags")
    ret.add("u_lasted_video_mlags")
    ret.add("u_lasted_video_hlags")
    ret.add("u_lasted_video_hdlags")
    ret.add("u_lasted_video_dlags")
    ret.add("u_lasted_video_olabels")
    ret.add("u_lasted_video_indexs")
    ret.add("u_lasted_video_playtimes")
    ret.add("u_lasted_video_hours")

    return ret

class CommonVideoColossusUserAuthorFeatureV6RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_video_colossus_user_author_feature_v6_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()

    ret.add(self._config.get("valid_play_time"))
    ret.add(self._config.get("valid_seq_len"))
    ret.add(self._config.get("gen_lasted_valid_video"))
    ret.add(self._config.get("valid_seq_is_reverse"))
    ret.add(self._config.get("colossus_photos_attr"))
    ret.add(self._config.get("use_pb_format"))
    ret.add(self._config.get("time_ms_common_attr"))
    ret.add(self._config.get("filter_time_len"))
    ret.add(self._config.get("limit_num"))

    ret.add(self._config.get("u_lasted_valid_video_aids"))
    ret.add(self._config.get("u_lasted_valid_video_pts"))
    ret.add(self._config.get("u_lasted_valid_video_lags"))
    ret.add(self._config.get("u_lasted_valid_video_pages"))
    ret.add(self._config.get("u_lasted_valid_video_labels"))

    ret.add(self._config.get("u_lasted_valid_video_n_pids"))
    ret.add(self._config.get("u_lasted_valid_video_n_tags"))
    ret.add(self._config.get("u_lasted_valid_video_ptds"))
    ret.add(self._config.get("u_lasted_valid_video_slags"))
    ret.add(self._config.get("u_lasted_valid_video_mlags"))
    ret.add(self._config.get("u_lasted_valid_video_olabels"))

    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()

    ret.add("u_lasted_valid_video_aids")
    ret.add("u_lasted_valid_video_pts")
    ret.add("u_lasted_valid_video_lags")
    ret.add("u_lasted_valid_video_pages")
    ret.add("u_lasted_valid_video_labels")
    ret.add("u_lasted_valid_video_n_pids")
    ret.add("u_lasted_valid_video_n_tags")
    ret.add("u_lasted_valid_video_ptds")
    ret.add("u_lasted_valid_video_slags")
    ret.add("u_lasted_valid_video_mlags")
    ret.add("u_lasted_valid_video_hlags")
    ret.add("u_lasted_valid_video_hdlags")
    ret.add("u_lasted_valid_video_dlags")
    ret.add("u_lasted_valid_video_olabels")
    ret.add("u_lasted_valid_video_indexs")
    ret.add("u_lasted_valid_video_playtimes")
    ret.add("u_lasted_valid_video_hours")

    return ret

class CommonVideoColossusUserAuthorFeatureV2RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_video_colossus_user_author_feature_v2_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_photos_attr"])
    ret.add(self._config["time_ms_common_attr"])
    ret.add(self._config["u_latest_video_seq_len"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()

    ret.add("u_lasted_video_aids_new_v2")
    ret.add("u_lasted_video_pts_new_v2")
    ret.add("u_lasted_video_lags_new_v2")
    ret.add("u_lasted_video_pages_new_v2")
    ret.add("u_lasted_video_labels_new_v2")
    ret.add("u_lasted_video_n_pids_v2")
    ret.add("u_lasted_video_n_tags_v2")
    ret.add("u_lasted_video_ptds_v2")
    ret.add("u_lasted_video_slags_v2")
    ret.add("u_lasted_video_mlags_v2")
    ret.add("u_lasted_video_olabels_v2")

    ret.add("u_ev_video_aids_v2")
    ret.add("u_ev_video_pts_v2")
    ret.add("u_ev_video_lags_v2")
    ret.add("u_ev_video_pages_v2")
    ret.add("u_ev_video_labels_v2")
    ret.add("u_ev_video_n_pids_v2")
    ret.add("u_ev_video_n_tags_v2")
    ret.add("u_ev_video_ptds_v2")
    ret.add("u_ev_video_slags_v2")
    ret.add("u_ev_video_mlags_v2")
    ret.add("u_ev_video_olabels_v2")

    ret.add("u_sv_video_aids_v2")
    ret.add("u_sv_video_pts_v2")
    ret.add("u_sv_video_lags_v2")
    ret.add("u_sv_video_pages_v2")
    ret.add("u_sv_video_labels_v2")
    ret.add("u_sv_video_n_pids_v2")
    ret.add("u_sv_video_n_tags_v2")
    ret.add("u_sv_video_ptds_v2")
    ret.add("u_sv_video_slags_v2")
    ret.add("u_sv_video_mlags_v2")
    ret.add("u_sv_video_olabels_v2")

    ret.add("ua_lasted_video_pts_v2")
    ret.add("ua_lasted_video_lags_v2")
    ret.add("ua_lasted_video_pages_v2")
    ret.add("ua_lasted_video_labels_v2")
    ret.add("ua_lasted_video_n_pids_v2")
    ret.add("ua_lasted_video_n_tags_v2")
    ret.add("ua_lasted_video_ptds_v2")
    ret.add("ua_lasted_video_slags_v2")
    ret.add("ua_lasted_video_mlags_v2")
    ret.add("ua_lasted_video_olabels_v2")

    attrs = ["ua_video_show_1h_v2", "ua_video_show_7h_v2", "ua_video_show_1d_v2", "ua_video_show_7d_v2",
    "ua_video_show_14d_v2", "ua_video_show_28d_v2", "ua_video_show_nd_v2",
    "ua_video_effective_view_1h_v2", "ua_video_effective_view_7h_v2", "ua_video_effective_view_1d_v2", "ua_video_effective_view_7d_v2",
    "ua_video_effective_view_14d_v2", "ua_video_effective_view_28d_v2", "ua_video_effective_view_nd_v2",
    "ua_video_watch_time_1h_v2", "ua_video_watch_time_7h_v2", "ua_video_watch_time_1d_v2", "ua_video_watch_time_7d_v2",
    "ua_video_watch_time_14d_v2", "ua_video_watch_time_28d_v2", "ua_video_watch_time_nd_v2"] + ["ua_video_glabel_1h_v2",
    "ua_video_glabel_7h_v2", "ua_video_glabel_1d_v2", "ua_video_glabel_7d_v2", "ua_video_glabel_14d_v2", "ua_video_glabel_28d_v2",
    "ua_video_glabel_nd_v2", "ua_last_video_palytime_v2", "ua_last_video_glabel_v2", "ua_last_video_label_v2", "ua_last_video_watch_day_lag_v2",
    "ua_last_video_watch_hour_lag_v2", "ua_last_video_watch_min_lag_v2", "ua_total_video_watch_day_count_v2"]

    for attr in attrs:
      ret.add(attr)

    return ret

class CommonVideoColossusUserAuthorFeatureV4RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_video_colossus_user_author_feature_v4_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_photos_attr"])
    ret.add(self._config["time_ms_common_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attrs = [
      "highfreq_glabel_tag_list", "highfreq_glabel_count_list",
      "highfreq_efview_tag_list", "highfreq_efview_count_list",
      "highfreq_like_tag_list",   "highfreq_like_count_list",
      "highfreq_comment_tag_list","highfreq_comment_count_list",
      "highfreq_follow_tag_list", "highfreq_follow_count_list",
      "u_hour_ev_video_aids", "u_hour_ev_video_pts", "u_hour_ev_video_pages", "u_hour_ev_video_labels",
      "u_hour_ev_video_pids", "u_hour_ev_video_tags", "u_hour_ev_video_ptds", "u_hour_ev_video_slags",
      "u_hour_ev_video_mlags", "u_hour_ev_video_olabels"
    ]
    for attr in attrs:
      ret.add(attr)
    return ret
  
class CommonVideoColossusUserAuthorFeatureV5RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_video_colossus_user_author_feature_v5_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()  
    ret.add(self._config["colossus_photos_attr"])
    ret.add(self._config["time_ms_common_attr"])
    ret.add(self._config.get("use_pb_format"))

    if "gen_u_long_play_video_seq" in self._config:
      ret.add(self._config["u_long_play_video_seq_len"])
      ret.add(self._config["u_long_play_video_playtime_threshold"])
      ret.add(self._config["u_latest_video_seq_len"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    if "gen_u_long_play_video_seq" in self._config:
      ret.add("u_long_play_video_pids")
      ret.add("u_long_play_video_aids")
      ret.add("u_long_play_video_tags")
      ret.add("u_long_play_video_channels")
      ret.add("u_long_play_video_labels")
      ret.add("u_long_play_video_lags")
      ret.add("u_long_play_video_pts")
      ret.add("u_long_play_video_pt_bucket")
    return ret



class CommonVideoColossusUserAuthorFeatureV7RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_video_colossus_user_author_feature_v7_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_photos_attr"))
    ret.add(self._config.get("time_ms_common_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()

    attrs = ["ut_lasted_video_pts_hetu_1", "ut_lasted_video_lags_hetu_1", "ut_lasted_video_pages_hetu_1", "ut_lasted_video_labels_hetu_1",
  "ut_lasted_video_pids_hetu_1", "ut_lasted_video_aids_hetu_1", "ut_lasted_video_ptds_hetu_1", "ut_lasted_video_slags_hetu_1",
  "ut_lasted_video_mlags_hetu_1", "ut_lasted_video_olabels_hetu_1", "ut_lasted_video_pts_hetu_2", "ut_lasted_video_lags_hetu_2",
  "ut_lasted_video_pages_hetu_2", "ut_lasted_video_labels_hetu_2", "ut_lasted_video_pids_hetu_2", "ut_lasted_video_aids_hetu_2",
  "ut_lasted_video_ptds_hetu_2", "ut_lasted_video_slags_hetu_2", "ut_lasted_video_mlags_hetu_2", "ut_lasted_video_olabels_hetu_2",
  "ut_lasted_video_pts_hetu_3", "ut_lasted_video_lags_hetu_3", "ut_lasted_video_pages_hetu_3", "ut_lasted_video_labels_hetu_3",
  "ut_lasted_video_pids_hetu_3", "ut_lasted_video_aids_hetu_3", "ut_lasted_video_ptds_hetu_3", "ut_lasted_video_slags_hetu_3",
  "ut_lasted_video_mlags_hetu_3", "ut_lasted_video_olabels_hetu_3",]

    for attr in attrs:
      ret.add(attr)

    return ret

class CommonLiveHetuHardSearchRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_colossus_hetu_search_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("colossus_resp_attr"))
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    attrs = ["ut_search_pid_list_attr", "ut_search_aid_list_attr", "ut_search_time_lag_hour_list_attr", "ut_search_time_lag_day_list_attr",
            "ut_search_channel_list_attr", "ut_search_auto_play_time_list_attr", "ut_search_play_time_list_attr",
            "ut_search_label_list_attr", "ut_search_reward_amt_list_attr", "ut_search_cluster_list_attr", "ut_search_tag_list_attr"]
    for attr in attrs:
      ret.add(attr)

    return ret


class UserVideoInterestClusterFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "user_video_interest_cluster_feature_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_photos_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attrs = [
      "highfreq_video_tag_list_attr",
      "highfreq_video_show_list_attr",
      "highfreq_video_effective_view_count_list_attr",
      "highfreq_video_interact_list_attr",
      "highfreq_video_watch_time_list_attr"
    ]
    for attr in attrs:
      ret.add(attr)
    return ret


class CommonVideoColossusUserAuthorFeatureV3RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_video_colossus_user_author_feature_v3_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_photos_attr"])
    ret.add(self._config["time_ms_common_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attrs = [
      "month_hour_u_show_attr_list", "month_hour_u_effective_view_attr_list", "month_hour_u_watch_time_attr_list", "month_hour_u_glabel_attr_list",
      "min_u_show_attr_list", "min_u_effective_view_attr_list", "min_u_watch_time_attr_list", "min_u_glabel_attr_list",
      "hour_u_show_attr_list", "hour_u_effective_view_attr_list", "hour_u_watch_time_attr_list", "hour_u_glabel_attr_list",
      "day_u_show_attr_list", "day_u_effective_view_attr_list", "day_u_watch_time_attr_list", "day_u_glabel_attr_list",
      "month_u_show_attr_list", "month_u_effective_view_attr_list", "month_u_watch_time_attr_list", "month_u_glabel_attr_list",

      "noncum_min_u_show_attr_list",   "noncum_min_u_effective_view_attr_list",   "noncum_min_u_watch_time_attr_list",   "noncum_min_u_glabel_attr_list",
      "noncum_hour_u_show_attr_list",  "noncum_hour_u_effective_view_attr_list",  "noncum_hour_u_watch_time_attr_list",  "noncum_hour_u_glabel_attr_list",
      "noncum_day_u_show_attr_list",   "noncum_day_u_effective_view_attr_list",   "noncum_day_u_watch_time_attr_list",   "noncum_day_u_glabel_attr_list",
      "noncum_month_u_show_attr_list", "noncum_month_u_effective_view_attr_list", "noncum_month_u_watch_time_attr_list", "noncum_month_u_glabel_attr_list",
    ]
    for attr in attrs:
      ret.add(attr)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    attrs = [
      "noncum_min_ua_show_attr_list",    "noncum_min_ua_effective_view_attr_list",   "noncum_min_ua_watch_time_attr_list",   "noncum_min_ua_glabel_attr_list",
      "noncum_hour_ua_show_attr_list",   "noncum_hour_ua_effective_view_attr_list",  "noncum_hour_ua_watch_time_attr_list",  "noncum_hour_ua_glabel_attr_list",
      "noncum_day_ua_show_attr_list",    "noncum_day_ua_effective_view_attr_list",   "noncum_day_ua_watch_time_attr_list",   "noncum_day_ua_glabel_attr_list",
      "noncum_month_ua_show_attr_list",  "noncum_month_ua_effective_view_attr_list", "noncum_month_ua_watch_time_attr_list", "noncum_month_ua_glabel_attr_list",
    ]
    for attr in attrs:
      ret.add(attr)
    return ret



class CommonVideoColossusLiveSideInfoRFMRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_video_colossus_live_sideinfo_rfm_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_photos_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attrs = [
      "accum_hour_u_show_attr_list", "accum_hour_u_effective_view_attr_list", "accum_hour_u_watch_time_attr_list", "accum_hour_u_glabel_attr_list",
      "accum_day_u_show_attr_list", "accum_day_u_effective_view_attr_list", "accum_day_u_watch_time_attr_list", "accum_day_u_glabel_attr_list",
      "accum_week_u_show_attr_list", "accum_week_u_effective_view_attr_list", "accum_week_u_watch_time_attr_list", "accum_week_u_glabel_attr_list"
    ]
    for attr in attrs:
      ret.add(attr)
    return ret



class UserInterestClockEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "user_interest_clock_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_photos_attr"])
    ret.add(self._config["time_ms_common_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attrs = [
      "u_total_hour_show_counts_1d_ic_attr", "u_total_hour_eff_view_counts_1d_ic_attr", "u_total_hour_watch_time_counts_1d_ic_attr", "u_total_hour_glabel_counts_1d_ic_attr",
      "u_total_hour_show_counts_3d_ic_attr", "u_total_hour_eff_view_counts_3d_ic_attr", "u_total_hour_watch_time_counts_3d_ic_attr", "u_total_hour_glabel_counts_3d_ic_attr",
      "u_total_hour_show_counts_7d_ic_attr", "u_total_hour_eff_view_counts_7d_ic_attr", "u_total_hour_watch_time_counts_7d_ic_attr", "u_total_hour_glabel_counts_7d_ic_attr",
      "u_total_hour_show_counts_14d_ic_attr", "u_total_hour_eff_view_counts_14d_ic_attr", "u_total_hour_watch_time_counts_14d_ic_attr", "u_total_hour_glabel_counts_14d_ic_attr",
      "u_total_hour_show_counts_28d_ic_attr", "u_total_hour_eff_view_counts_28d_ic_attr", "u_total_hour_watch_time_counts_28d_ic_attr", "u_total_hour_glabel_counts_28d_ic_attr",

      "u_total_week_show_counts_1w_ic_attr", "u_total_week_eff_view_counts_1w_ic_attr", "u_total_week_watch_time_counts_1w_ic_attr", "u_total_week_glabel_counts_1w_ic_attr",
      "u_total_week_show_counts_2w_ic_attr", "u_total_week_eff_view_counts_2w_ic_attr", "u_total_week_watch_time_counts_2w_ic_attr", "u_total_week_glabel_counts_2w_ic_attr",
      "u_total_week_show_counts_4w_ic_attr", "u_total_week_eff_view_counts_4w_ic_attr", "u_total_week_watch_time_counts_4w_ic_attr", "u_total_week_glabel_counts_4w_ic_attr",
      "u_total_week_show_counts_6w_ic_attr", "u_total_week_eff_view_counts_6w_ic_attr", "u_total_week_watch_time_counts_6w_ic_attr", "u_total_week_glabel_counts_6w_ic_attr",
      "u_total_week_show_counts_8w_ic_attr", "u_total_week_eff_view_counts_8w_ic_attr", "u_total_week_watch_time_counts_8w_ic_attr", "u_total_week_glabel_counts_8w_ic_attr",
    ]
    for attr in attrs:
      ret.add(attr)
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    attrs = [
      "ua_hour_show_counts_1d_ic_attr", "ua_hour_eff_view_counts_1d_ic_attr", "ua_hour_watch_time_counts_1d_ic_attr", "ua_hour_glabel_counts_1d_ic_attr",
      "ua_hour_show_counts_3d_ic_attr", "ua_hour_eff_view_counts_3d_ic_attr", "ua_hour_watch_time_counts_3d_ic_attr", "ua_hour_glabel_counts_3d_ic_attr",
      "ua_hour_show_counts_7d_ic_attr", "ua_hour_eff_view_counts_7d_ic_attr", "ua_hour_watch_time_counts_7d_ic_attr", "ua_hour_glabel_counts_7d_ic_attr",
      "ua_hour_show_counts_14d_ic_attr", "ua_hour_eff_view_counts_14d_ic_attr", "ua_hour_watch_time_counts_14d_ic_attr", "ua_hour_glabel_counts_14d_ic_attr",
      "ua_hour_show_counts_28d_ic_attr", "ua_hour_eff_view_counts_28d_ic_attr", "ua_hour_watch_time_counts_28d_ic_attr", "ua_hour_glabel_counts_28d_ic_attr",

      "ua_week_show_counts_1w_ic_attr", "ua_week_eff_view_counts_1w_ic_attr", "ua_week_watch_time_counts_1w_ic_attr", "ua_week_glabel_counts_1w_ic_attr",
      "ua_week_show_counts_2w_ic_attr", "ua_week_eff_view_counts_2w_ic_attr", "ua_week_watch_time_counts_2w_ic_attr", "ua_week_glabel_counts_2w_ic_attr",
      "ua_week_show_counts_4w_ic_attr", "ua_week_eff_view_counts_4w_ic_attr", "ua_week_watch_time_counts_4w_ic_attr", "ua_week_glabel_counts_4w_ic_attr",
      "ua_week_show_counts_6w_ic_attr", "ua_week_eff_view_counts_6w_ic_attr", "ua_week_watch_time_counts_6w_ic_attr", "ua_week_glabel_counts_6w_ic_attr",
      "ua_week_show_counts_8w_ic_attr", "ua_week_eff_view_counts_8w_ic_attr", "ua_week_watch_time_counts_8w_ic_attr", "ua_week_glabel_counts_8w_ic_attr",
    ]
    for attr in attrs:
      ret.add(attr)
    return ret

class CommonVideoColossusUserInterestsSideInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_video_colossus_user_interests_sideinfo_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_photos_attr"])
    ret.add(self._config.get("use_pb_format"))
    ret.add(self._config.get("time_ms_common_attr"))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attrs_lasted = [
      "u_lasted_video_aids_v2",
      "u_lasted_video_pts_v2",
      "u_lasted_video_lags_v2",
      "u_lasted_video_pages_v2",
      "u_lasted_video_labels_v2",
      "u_lasted_video_pids",
      "u_lasted_video_tags",
      "u_lasted_video_3d_ua_pts",
      "u_lasted_video_3d_ua_cnts",
      "u_lasted_video_3d_uat_pts",
      "u_lasted_video_3d_uat_cnts",
      "u_lasted_video_3d_utag_pts",
      "u_lasted_video_3d_utag_cnts"]

    for attr in attrs_lasted:
      ret.add(attr)
    return ret

class CommonLiveColossusAuthorFeatureLightRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_colossus_author_feature_light_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config.get("ua_live_total_watch_time_attr"))
    ret.add(self._config.get("u_live_total_watch_time_28d_attr"))
    ret.add(self._config.get("ua_live_total_watch_time_28d_attr"))
    ret.add(self._config.get("ua_reward_cnt_attr"))
    ret.add(self._config.get("u_reward_amt_3d_attr"))
    ret.add(self._config.get("u_reward_cnt_attr"))
    ret.add(self._config.get("ua_live_total_watch_day_attr"))
    ret.add(self._config.get("ua_live_avg_day_watch_time_attr"))
    ret.add(self._config.get("ua_live_max_watch_time_attr"))
    ret.add(self._config.get("ua_live_total_watch_day_attr_fix"))
    ret.add(self._config.get("ua_live_avg_day_watch_time_attr_fix"))
    ret.add(self._config.get("ua_is_follow_attr"))
    return ret
                  
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = [
      "ua_live_total_watch_time",
      "u_live_total_watch_time_28d",
      "ua_live_total_watch_time_28d",
      "ua_reward_cnt",
      "u_reward_amt_3d",
      "u_reward_cnt",
      "ua_live_total_watch_day",
      "ua_live_avg_day_watch_time",
      "ua_live_max_watch_time"
    ]
    return set(attrs)

class CommonLiveColossusAuthorFeatureRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_colossus_author_feature_resp_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    fix_attrs = ["ua_live_total_watch_cnt_attr_fix", "ua_live_total_watch_day_attr_fix", "ua_live_avg_day_watch_time_attr_fix",\
                 "ua_live_avg_cnt_watch_time_attr_fix", "ua_live_total_watch_cnt_jx_attr_fix", "ua_live_total_watch_day_jx_attr_fix",\
                 "ua_live_avg_day_watch_time_jx_attr_fix", "ua_live_avg_cnt_watch_time_jx_attr_fix", "ua_live_total_watch_cnt_js_attr_fix",\
                 "ua_live_total_watch_day_js_attr_fix", "ua_live_avg_day_watch_time_js_attr_fix", "ua_live_avg_cnt_watch_time_js_attr_fix"]
    attrs = ["ua_is_follow_attr_"]
    for attr in fix_attrs:
      ret.add(self._config.get(attr))
    for attr in attrs:
      ret.add(self._config.get(attr))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    return {}

class ColossusMultiSessionFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_reco_colossus_multi_session_feature"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

class PhotolivePhotoColossusLiveaidSearchPidEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "photo_gsu_with_live_author_list"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["uClickLiveAuthorListHot_attr"])
    ret.add(self._config["uClickLiveAuthorListFollow_attr"])
    ret.add(self._config["uClickLiveAuthorListNear_attr"])
    ret.add(self._config["uClickLiveAuthorList_attr"])
    ret.add(self._config["uClickLiveAuthorListSlHot_attr"])
    ret.add(self._config["uClickLiveAuthorListSlFollow_attr"])
    ret.add(self._config["uClickLiveAuthorListSlNear_attr"])
    ret.add(self._config["uClickLiveAuthorListJXLiveTabSingle_attr"])
    ret.add(self._config["uClickLiveAuthorListBlHot_attr"])
    ret.add(self._config["uClickLiveAuthorListBlNear_attr"])
    ret.add(self._config["uClickLiveAuthorListBlFollow_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonLiveColossusAuthorMergeFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_colossus_author_merge_feature_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["live_colossus_attr"])
    for attr_name in ['filter_time_len', 'enable_filter_e_shop', 'enable_filter_outside_item', 'enable_filter_long_view_item', 'long_view_threshold', 'enable_time_seq']:
      if attr_name in self._config:
        ret.add(self._config[attr_name])
    for attr_name in ['latest_n_live', 'latest_valid_n_live']:
      if attr_name in self._config:
        ret.update(self.extract_dynamic_params(self._config[attr_name]))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attrs = [
      "u_recent_live_author_list_attr",
      "u_recent_live_reward_list_attr",
      "u_recent_live_cluster_list_attr",
      "u_recent_live_playtime_list_attr",
      "u_recent_live_position_list_attr",
      "u_recent_live_consume_reward_list_attr",
      "u_recent_live_mix_reward_list_attr",
      "u_recent_live_recent_clk_ts_list_attr"
    ]
    for attr in attrs:
      ret.add(attr)
    return ret

class CommonLiveColossusAuthorGiftMergeFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_colossus_author_gift_merge_feature_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["live_colossus_attr"])
    for attr_name in ['filter_time_len', 'enable_filter_e_shop', 'enable_filter_outside_item', 'enable_filter_long_view_item', 'long_view_threshold', 'enable_time_seq']:
      if attr_name in self._config:
        ret.add(self._config[attr_name])
    for attr_name in ['latest_n_live', 'latest_valid_n_live']:
      if attr_name in self._config:
        ret.update(self.extract_dynamic_params(self._config[attr_name]))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attrs = [
      "u_recent_live_author_gift_list_attr",
      "u_recent_live_reward_gift_list_attr",
      "u_recent_live_cluster_gift_list_attr",
      "u_recent_live_playtime_gift_list_attr",
      "u_recent_live_position_gift_list_attr",
      "u_recent_live_consume_reward_gift_list_attr",
      "u_recent_live_mix_reward_gift_list_attr",
      "u_recent_live_recent_clk_ts_gift_list_attr"
    ]
    for attr in attrs:
      ret.add(attr)
    return ret

class CommonVideoColossusActionInfoEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_video_colossus_action_info_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["video_colossus_attr"])
    for attr_name in ['filter_time_len', 'u_latest_video_seq_len', 'limit_num', 'time_ms_common_attr', 'use_pb_format']:
      if attr_name in self._config:
        ret.add(self._config[attr_name])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attrs = [
      "u_recent_video_author_list_attr",
      "u_recent_video_label_list_attr",
      "u_recent_video_lag_list_attr",
      "u_recent_video_pts_list_attr",
      "u_recent_video_page_list_attr",
      "u_recent_video_photo_list_attr",
      "u_recent_video_tag_list_attr",
    ]
    for attr in attrs:
      ret.add(attr)
    return ret  

class CommonLiveColossusUaLifelongFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_live_colossus_ua_lifelong_feature_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["live_colossus_attr"])
    for attr_name in ['filter_time_len', 'enable_filter_e_shop', 'enable_filter_outside_item']:
      if attr_name in self._config:
        ret.add(self._config[attr_name])
    for attr_name in ['latest_n_live', 'latest_valid_n_live', 'ua_group_num', 'user_author_latest_n_action']:
      if attr_name in self._config:
        ret.update(self.extract_dynamic_params(self._config[attr_name]))
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attrs = [
      "u_recent_live_author_list_attr",
      "u_recent_live_cluster_list_attr",
      # "u_recent_live_showcnt_list_attr",
      "u_recent_live_clickcnt_list_attr",
      "u_recent_live_likecnt_list_attr",
      "u_recent_live_commentcnt_list_attr",
      "u_recent_live_playtime_list_attr",
      "u_user_authors_group_action_list_attr",
      "u_user_author_mask_num_list_attr",
    ]
    for attr in attrs:
      ret.add(attr)
    return ret

class CommonP2lRecoColossusUserFeatureEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_p2l_reco_colossus_user_feature_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attrs = [
      "u_minute_play_cnt_attr", "u_minute_play_time_attr", "u_minute_play_playend_cnt_attr",
      "u_minute_sum_label_attr", "u_minute_count_profile_attr", "u_minute_count_forward_attr",
      "u_minute_count_like_attr", "u_minute_count_comment_attr", "u_minute_count_hate_attr",
      "u_minute_count_follow_attr",
      "u_hour_play_cnt_attr", "u_hour_play_time_attr", "u_hour_play_playend_cnt_attr",
      "u_hour_sum_label_attr", "u_hour_count_profile_attr", "u_hour_count_forward_attr",
      "u_hour_count_like_attr", "u_hour_count_comment_attr", "u_hour_count_hate_attr",
      "u_hour_count_follow_attr",
      "u_day_play_cnt_attr", "u_day_play_time_attr", "u_day_play_playend_cnt_attr",
      "u_day_sum_label_attr", "u_day_count_profile_attr", "u_day_count_forward_attr",
      "u_day_count_like_attr", "u_day_count_comment_attr", "u_day_count_hate_attr",
      "u_day_count_follow_attr"
    ]
    for attr in attrs:
      ret.add(attr)
    return ret

class ColossusUserAuthorCrossStaticRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_user_author_cross_static_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("filter_older_actions_seconds")))
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["filter_future_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_author_id_attr"])
    return ret


  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config[key] for key in [
        "u_author_play_cnt_attr", "u_author_effective_view_attr",
        "u_author_play_time_attr", "u_author_play_rate_attr",
      ] if key in self._config
    }
    
class ColossusUserAuthorCrossStaticRespV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_user_author_cross_static_resp_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("filter_older_actions_seconds")))
    # ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["filter_future_attr"])
    for attr in ['reflection_input_attr', 'item_datas_input_attr', 'merged_colossusv2_data_from']:
      if attr in self._config:
        ret.add(self._config[attr])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_author_id_attr"])
    return ret


  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config[key] for key in [
        "u_author_play_cnt_attr", "u_author_effective_view_attr",
        "u_author_play_time_attr", "u_author_play_rate_attr",
      ] if key in self._config
    }

class CommonP2lRecoColossusUserFeatureV1Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_p2l_reco_colossus_user_feature_v1_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attrs = [
      "u_minute_play_cnt_attr", "u_minute_play_time_attr", "u_minute_play_playend_cnt_attr",
      "u_minute_sum_label_attr", "u_minute_count_profile_attr",
      "u_minute_is_effective_play1_attr", "u_minute_is_effective_play2_attr",
      "u_hour_play_cnt_attr", "u_hour_play_time_attr", "u_hour_play_playend_cnt_attr",
      "u_hour_sum_label_attr", "u_hour_count_profile_attr",
      "u_day_play_cnt_attr", "u_day_play_time_attr", "u_day_play_playend_cnt_attr",
      "u_day_sum_label_attr", "u_day_count_profile_attr",
      "u_minute_play_time_ratio_attr", "u_minute_play_playend_cnt_ratio_attr",
      "u_minute_is_effective_play1_ratio_attr", "u_minute_is_effective_play2_ratio_attr"
    ]
    for attr in attrs:
      ret.add(attr)
    return ret
  
class ColossusUserTagCrossStaticRespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "colossus_user_tag_cross_static_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.update(self.extract_dynamic_params(self._config.get("filter_older_actions_seconds")))
    ret.add(self._config["colossus_resp_attr"])
    ret.add(self._config["filter_future_attr"])
    return ret

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    ret = set()
    ret.add(self._config["item_tag_attr"])
    return ret


  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
      self._config[key] for key in [
        "u_tag_play_cnt_attr", "u_tag_effective_view_attr",
        "u_tag_play_time_attr", "u_tag_play_rate_attr",
        "u_tag_like_attr", "u_tag_hate_attr",
        "u_tag_enter_comment_attr","u_tag_enter_profile_attr",
      ] if key in self._config
    }

class CommonP2lRecoColossusUserFeatureV2Enricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "common_p2l_reco_colossus_user_feature_v2_enricher"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret
  
  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    attrs = [
      "u_minute_is_effective_play1_attr", "u_minute_is_effective_play2_attr",
      "u_hour_is_effective_play1_attr", "u_hour_is_effective_play2_attr",
      "u_day_is_effective_play1_attr", "u_day_is_effective_play2_attr",
      "u_minute_play_time_ratio_attr", "u_minute_play_playend_cnt_ratio_attr",
      "u_minute_is_effective_play1_ratio_attr", "u_minute_is_effective_play2_ratio_attr",
      "u_minute_sum_label_ratio_attr", "u_minute_count_profile_ratio_attr", "u_minute_count_forward_ratio_attr",
      "u_minute_count_like_ratio_attr", "u_minute_count_comment_ratio_attr", "u_minute_count_hate_ratio_attr",
      "u_minute_count_follow_ratio_attr",
      "u_hour_play_time_ratio_attr", "u_hour_play_playend_cnt_ratio_attr",
      "u_hour_is_effective_play1_ratio_attr", "u_hour_is_effective_play2_ratio_attr",
      "u_hour_sum_label_ratio_attr", "u_hour_count_profile_ratio_attr", "u_hour_count_forward_ratio_attr",
      "u_hour_count_like_ratio_attr", "u_hour_count_comment_ratio_attr", "u_hour_count_hate_ratio_attr",
      "u_hour_count_follow_ratio_attr",
      "u_day_play_time_ratio_attr", "u_day_play_playend_cnt_ratio_attr",
      "u_day_is_effective_play1_ratio_attr", "u_day_is_effective_play2_ratio_attr",
      "u_day_sum_label_ratio_attr", "u_day_count_profile_ratio_attr", "u_day_count_forward_ratio_attr",
      "u_day_count_like_ratio_attr", "u_day_count_comment_ratio_attr", "u_day_count_hate_ratio_attr",
      "u_day_count_follow_ratio_attr"
    ]
    for attr in attrs:
      ret.add(attr)
    return ret

class CommonLiveRfmWithLiveFollowEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "gsu_with_follow_seq"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["id_list_attr"])
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["output_sign_attr"])
    ret.add(self._config["output_slot_attr"])
    return ret

class CommonVideoColossusUserAuthorFeatureV8RespEnricher(LeafEnricher):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "video_high_play_rate_topn"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_photos_attr"])
    ret.add(self._config["use_pb_format"])
    ret.add(self._config["limit_num"])
    ret.add(self._config["time_ms_common_attr"])
    ret.add(self._config["filter_time_len"])
    ret.add(self._config["limit_play_ratio"])
    ret.add(self._config["gen_u_high_play_rate_video_seq"])
    ret.add(self._config["u_high_play_rate_video_seq_len"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    if "gen_u_high_play_rate_video_seq" in self._config:
      ret.add("u_lasted_video_aids_v3")
      ret.add("u_lasted_video_pts_v3")
      ret.add("u_lasted_video_lags_v3")
      ret.add("u_lasted_video_pages_v3")
      ret.add("u_lasted_video_labels_v3")
      ret.add("u_lasted_video_n_pids_v3")
      ret.add("u_lasted_video_n_tags_v3")
      ret.add("u_lasted_video_ptds_v3")
      ret.add("u_lasted_video_slags_v3")
      ret.add("u_lasted_video_mlags_v3")
      ret.add("u_lasted_video_hlags_v3")
      ret.add("u_lasted_video_hdlags_v3")
      ret.add("u_lasted_video_dlags_v3")
      ret.add("u_lasted_video_olabels_v3")
      ret.add("u_lasted_video_indexs_v3")
      ret.add("u_lasted_video_playtimes_v3")
      ret.add("u_lasted_video_hours_v3")

    return ret
