#!/usr/bin/env python3
# coding=utf-8
"""
filename: uni_predict_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, uni_predict_ad api mixin
author: <EMAIL>
date: 2024-09-12 11:34:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .uni_predict_ad_enricher import *

# uni_predict 要分离 fetch embedding 和 predict ，那么就只能跳过 uni_predict 的 batching system，
# uni_predict batching system 的一个作用就可以统筹 fetch batching tasks 的 input embeddings，不然的话可以直接服用 tensorflow 的 batching 机制
# 一旦需要优化 batching fetch embedding，基本就得走 uni_predict_fused 的老路：用 batching system schedule task，预设好 fetch_input 和 process_results

class AdTwinTowerApiMixin(CommonLeafBaseMixin):
  def ad_twin_tower_startup(self, **kwargs):
    """
    AdTwinTowerStartupEnricher
    ------
    商业化双塔初始化

    参数
    ------
    无

    示例
    ------
    ``` python
    .ad_twin_tower_startup()
    ```
    """
    self._add_processor(AdTwinTowerStartupEnricher(kwargs))
    return self

  def ad_embedding_producer_startup(self, **kwargs):
    """
    AdEmbeddingProducerStartupEnricher
    ------
    商业化双塔 embedding producer 初始化

    参数
    ------
    无

    示例
    ------
    ``` python
    .ad_embedding_producer_startup()
    ```
    """
    self._add_processor(AdEmbeddingProducerStartupEnricher(kwargs))
    return self

  def ad_embedding_producer_retriever(self, **kwargs):
    """
    AdEmbeddingProducerRetriever
    ------
    商业化双塔 embedding producer 物料 retriever

    参数
    ------
    无

    示例
    ------
    ``` python
    .ad_embedding_producer_retriever()
    ```
    """
    self._add_processor(AdEmbeddingProducerRetriever(kwargs))
    return self

class AdFeatureExtractApiMixin(CommonLeafBaseMixin):
  def ad_feature_diff(self, **kwargs):
    """
    构造 BsLog, 用于抽取特征, 参见 ConstructBatchedSampleOpt

    参数配置
    ------
    `base_uni_response`: [string] 输入，用于从 context 获取 llsid，一般来源于 request

    `sparse_feature`: [string] 输入，用于从 context 获取序列化的 user_info，一般来源于 request

    `dense_feature`: [string] true 表示 user_info 需要解压在反序列化，false 则不需要解压直接反序列

    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(AdFeatureDiffEnricher(kwargs))
    return self

  def construct_bs_log_opt(self, **kwargs):
    """
    构造 BsLog, 用于抽取特征, 参见 ConstructBatchedSampleOpt

    参数配置
    ------
    `llsid`: [string] 输入，用于从 context 获取 llsid，一般来源于 request

    `user_info`: [string] 输入，用于从 context 获取序列化的 user_info，一般来源于 request

    `compressed`: [bool] true 表示 user_info 需要解压在反序列化，false 则不需要解压直接反序列

    `bs_opt_user_info`: [string] 输入，用于从 context 获取优化后的 BsLog user info，一般来源于 request

    `output_bs_log`: [string] 输出，构造好的 BsLog 对象指针在 context 里 attr_name

    `attr_id_manager`: [dict] AttrIdManager 相关的配置信息

    `......`: [TODO] TODO 待定细节

    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(AdConstructBsLogEnricher(kwargs))
    return self

  def construct_bs_log_i18n(self, **kwargs):
    """
    暂不实现？？？
    构造 BsLog, 用于抽取特征, 参见 ConstructBatchedSampleForI18N

    参数配置
    ------

    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(ConstructBatchedSampleI18NEnricher(kwargs))
    return self

  def construct_colossus_info(self, **kwargs):
    """
    设置 BsLog 里的 colossus 特征，参见 SimActionList::Fetch，
    也是一个 processor list 式的抽取特征的列表，应该也能拆解成多个 processor ？？？

    参数配置
    ------
    `bs_log`: [string] 需要设置的 BsLog 的 attr name

    `....`: [TODO] TODO

    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(ConstructColossusInfoEnricher(kwargs))
    return self

  def extract_ad_feature_by_bslog(self, **kwargs):
    """
    基于 BsLog 抽取特征，参见 TwinTowersModel::ExtractUserFeature

    参数配置
    ------
    `bs_log`: [string] 输入，抽特征必须的 BsLog

    `output_signs`: [string] 输出，抽取到的 sparse 特征 sign vector 在 context 里的 attr name

    `output_values`: [string] 输出，抽取到的 dense 特征 value vector 在 context 里的 attr name

    `output_fields`: [string] 输出，每个特征 sign/value 对应的 field id 组成的 vector 在 context 里的 attr name

    `common`: [bool] true 抽取的 sign/value/field 信息写入 common attr，false 写入 item attr

    `....`: [TODO] TODO

    调用示例
    ------
    ```
    ad_result_cache_retriever(
        result_cache_ttl = 100,
        result_cache_capacity = 160000,
        is_read = True,
        secondary_id_attr = "secondary_id",
        request_ptr = "uni_request_ptr",
        hit_cache_attr = "hit_cache")
    ```
    """
    self._add_processor(AdExtractUserFeatureEnricher(kwargs))
    return self

  def extract_ad_feature_by_bslog_dag(self, **kwargs):
    """
    国际化在用，暂时先不用实现
    基于 BsLog 抽取特征，参见 RealtimeCalculation::ExtractBsUserFeatures
    """
    self._add_processor(ExtractAdFeatureByBsLogI18NEnricher(kwargs))
    return self

  def prepare_ad_log(self, **kwargs):
    """
    暂时先不用实现
    """
    pass

  def extract_ad_feature_by_adlog(self, **kwargs):
    """
    暂时先不用实现
    """
    pass

  def extract_ad_feature_by_dragon(self, **kwargs):
    """
    这个 processor 不应该实现，而是把现阶段 TemplateDragon 的 processor 直接引入进来
    """
    pass

  def ad_embedding_producer_feature_enricher(self, **kwargs):
    """
    AdEmbeddingProducerFeatureEnricher
    ------
    商业化双塔 embedding producer feature enricher

    参数
    ------
    无

    示例
    ------
    ``` python
    .ad_embedding_producer_feature_enricher(
      sparse_fields_attr = "embedding_fetcher_sparse_fields_attr",
      sparse_signs_attr = "embedding_fetcher_sparse_signs_attr",
      dense_fields_attr = "embedding_fetcher_dense_fields_attr",
      dense_values_attr = "embedding_fetcher_dense_values_attr",
      feature_miss_attr = "feature_miss_attr"
    )
    ```
    """
    self._add_processor(AdEmbeddingProducerFeatureEnricher(kwargs))
    return self


class AdBizStrategyApiMixin(CommonLeafBaseMixin):
  def build_user_target_index(self, **kwargs):
    """
    设置一些标志位，这个好像是给 topk 结果处理用的（细节待定）
    参见 BuildUserTarget::BSBuild

    参数配置
    ------
    `request_ptr`: [string] ad Universe 的 request 信息

    `bs_log`: [string] 需要设置的 BsLog 的 attr name

    `target_index`: [string] 输出，设置 context 里的 target_index

    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(AdBuildUserTargetEnricher(kwargs))
    return self

  def retrieve_topk_by_cache(self, **kwargs):
    '''
     仅限召回场景，set hit cache 是否成功的比较, 成功会跳过预估逻辑，不成功则继续预估
    '''

  def update_topk_cache(self, **kwargs):
    pass

  # 各项召回 topk 策略
  def retrieve_by_result_handler(self, **kwargs):
    """
    将模型预估结果转化成召回结果(不同的过滤逻辑各成一个 retriever)
    对应 FullSearchCombineKey teams/ad/ad_nn/twin_towers_ps/result_handler/full_search_combine_key.h

    参数配置
    ------
    `topk_results`: [string] 模型预估的 topk item ids 在 context 里的 common attr name

    `topk_score`: [string] 模型预估的 topk item score 在 context 里的 common attr name

    `....`: [TODO] TODO

    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(AdResultHandlerRetriever(kwargs))
    return self



  def retrieve_by_full_search_combine_key_half(self, **kwargs):
    pass

  def retrieve_by_full_search_combine_key_half_diversity(self, **kwargs):
    pass


class UniPredictAdApiMixin(CommonLeafBaseMixin):
  """
  统一用 uni_predict 框架来完成当前 trigger 和 compute_engine 所做的预估功能。
  一些后续可以调整的细节：
  1. 目前 frozen graph 的一堆改图逻辑是和输入强绑定的，这种逻辑应该可以离线完成
  2. hybrid embedding 查询逻辑：目前 hybrid embedding 支持本地和远程混部，其查询逻辑也是基于此实现
  2. GPU Cache 切换 lixinhua 的 GPU Cache 封装：沟通确认 ad 场景的使用方式是 reco GPU cache 的一个特 例，不过这个不涉及 processor 上下游接口的设计
  """
  def uni_predict_fused_ad(self, **kwargs):
    """
    AdUniPredictFusedAttrEnricher, 与 uni_predict_fused 基本保持一致，embedding_fetchers 的实现不一样

    参数配置
    ------
    `embedding_fetchers`: [list] of [dict] 输入 tensor 构造的相关配置

    `embedding_fetchers::fetcher_type`: [string] 支持多种构造方式：
        DenseEmbeddingFetcher，用 common/item attr value 直接构造, 无查询 kv table/dict 过程过;
        SparseHybridEmbeddingFetcher，用 sign 查询本地的 HybridEmbedding，一般用于构造 user 特征 tensor, 输出 <key, embedding_ptr> 集合;
        EmbeddingStoreFetcher(TODO)，用 item id/key 查询本地 EmbeddingStore，用于构造 item tensor，输出 <key, embedding_ptr> 集合;
        EmbeddingStoreWithGpuCacheFetcher(TODO)，先用 item id/key 查询本地 GPUCache，输出 hit_cache_item_tensor +
          hit_cache_item_index + miss_item_ids + miss_item_index, miss_item_ids 再查询 EmbeddingStore,
          再次得到 hit_store_item_tensor + hit_store_item_index + real_miss_item_ids
          + real_miss_item_index（hit_cache_item_tensor + hit_cache_item_index + hit_store_item_tensor
          + hit_store_item_index + real_miss_item_index  用一个 gpu kernel 合并形成一个完整的 item 特征输入)

    `embedding_fetchers::common`: [bool] true 表示从 common attr 获取 signs/fields，false 则从 item attr 获取

    `embedding_fetchers::fields`: [list] of [string] 输入，查询 embedding 的 sign（或者 dense value）所属于的 field 在 context 中的 common/item attr name, sign(or value) 和 field 以数组形式一一映射, field 有什么用这个需要知道 ad 特征以及 embedding 存储与查询设计

    `embedding_fetchers::signs`: [list] of [string] 输入，查询 embedding 的 sign 在 context 中的 common/item attr name, dense 特征无视该项配置

    `embedding_fetchers::values`: [list] of [string] 输入，dense 特征用该项 common/item attr 的值来构造输入 tensor, sparse 特征无视该项配置

    `embedding_fetchers::compress_fields`: [bool] true，那么 fields 压缩存储相同且连续的值，以 [(field, num),(field, num), ...]的格式存储

    `embedding_fetchers::fields_config`: [list] of [dict] 用于描述如何构造ad 模型输入:
        user 侧计算时各个 sparse、dense 特征是对应有自己独立输入 tensor 的, 即一个 field 绑定一个 tensor，
        但 item 侧计算时，由于 producer 把所有的 item sparse、dense、以及 top embedding 都 concat 到一起了（以保障 item 特征和模型一致性），
        所以 item 侧只有一个输入 tensor，需要框架对业务提供的训练 infer 图进行自动改图（个人建议这个改图适合放到离线用 python 处理）

    `embedding_fetchers::fields_config::tensor_name`: [string] 特征的 input tensor node name

    `embedding_fetchers::fields_config::field`: [int] 特征的 field index

    `embedding_fetchers::fields_config::emb_dim`: [int] 特征单个 sign 对应 embedding  size

    `embedding_fetchers::fields_config::emb_dtype(TODO)`: [string] 特征单个 sign 对应 embedding data type: float32/float16

    `embedding_fetchers::fields_config::operation(TODO)`: [string] 特征多个 sign 对应 embedding 如何组成 tensor.
        SUM，将多个 sign 对应的 embedding 进行 sum;
        CONCAT，将多个 sign 对应的 embedding concat 起来，超过 shape 可容纳空间的 embedding 报错;
        TRUNCATE，将多个 sign 对应的 embedding concat 起来，超过 shape 可容纳空间的 embedding 则丢弃

    `inputs`: [list] of [dict] 模型输入配置，描述 input tensor 如何从 context 中获取，以及对应的 shape 约束

    `inputs::tensor_name`: [string] 输入 tensor name，与 embedding_fetchers::fields_config::tensor_name 对应

    `inputs::common`: [bool] true 则 input tensor batch维度为 user_batch_num，false 则必须为 item_batch_num

    `inputs::dim`: [int] input tensor 只支持 2 维， 以 dims 表示维度数组、则dim 指 dims[1]

    `inputs::dtype(TODO)`: [string] 指定输入模型的 input tensor 的数据类型，支持 float32/float16

    `outputs`: [list] of [dict] 模型预估输出至 context 的配置

    `outputs::common`: [bool] common 则输出 tensor 写入 common attr，false 则写入 item attr。召回的 result_handler 需要从对应的 attr 中获取 topk 结果进行处理

    `outputs::tensor_name`: [string] 模型输出 tensor name, output tensor 的 shape 由预估引擎推到，不在此配置

    `outputs::output_attr`: [string] 输出 tensor 写入 context 的 attr name

    `param`: [list] of [dict] 计算图的 dense 参数配置, 主要需要
        `name` [string] 指定 dense 参数名字,
        `rown`, `coln`, `dim` [int] 指定参数 shape,
        `dtype [string], 指定模型 dense param 的数据类型，支持 float32 和 float16 ， 默认为 float32

    `model_loader_config`: [dict] 模型加载相关配置，目前完全引用 uni_predict_fused processor 的配置, 在此不重复描述以免出现不一致问题

    `batching_config`: [dict] batching 系统的一系列配置, 目前完全引用 uni_predict_fused processor 的配置

    `executor_config`: [dict] 底层 engine 执行时的一系列配置，目前完全引用 uni_predict_fused processor 的配置

    `graph`: [string] 计算图 uri, 只支持 base64:// 这种 schema (暂不支持)

    `key`: [string] 模型唯一标识符，如果有多个模型需要指定不同的 key

    `embedding_loader`: [dict] sparse feature embedding、item embedding 更新与查询相关配置(TODO: 这配置得放到 uni_predict 外边去)

    `embedding_loader::sparse`: [dict] sparse embedding table 配置信息(HybridEmbedding), sparse fill 要素：
        1. hybrid embedding 加载与更新: from btq, from local sync_server checkpoint (作为 btq 数据的回滚备份), from idt (用于快速补全 btq 的数据, 其实 idt 也能给 checkpoint 补充数据，不过本次迁移没实现)
        2. 查找接口, 封装在 ConcurrentEmbedding = EmbeddingStorage(HybridEmbedding) + FieldEmbeddingStorage(Lookup api)
        3. field -> tensor: 每个 field 和 input tensor 是一一映射，field 在 feature field file 的顺序就是 input tensor 在模型输入中的顺序
        4. 回滚机制：处于回滚状态则启用 from checkpoint 的 embedding，不再回滚状态，则用 from btq 的 embedding

    `embedding_loader::sparse::combine_type`: [int] 默认 0，对应不同的查询处理逻辑 (TODO wuxikun)

    `embedding_loader::sparse::weights_in_tensor`: [bool] 默认 false，GetW/SetW 时是否 copy 数据，true 则不 copy ，false 则 copy

    `embedding_loader::sparse::enable_embedding_lookup_directly`: [bool] true,  FillFieldDataV2, false, FillFieldData

    `embedding_loader::sparse::reordering_feature`: [bool] (TODO wuxikun) 是否对 field feature 在 input tensor 的中填充顺序进行调整，
                                                                 ad 只有 UserSparse/UserDense/ItemSparse/ItemDense 四个输入 tensor、
                                                                 不同的 field 在 tensor 中的存储位置是离线约定好了的，但有时候
                                                                 kai_feature.txt 里 field 的顺序和 tensor 里 field 的顺序不一样，
                                                                 需要做一个 reorder 映射（不要问为什么）

    `embedding_loader::sparse::exponent_per_bucket`: [int] (TODO wuxikun) 看不明白干嘛的，reorder features 时用于重设 field size(why?)

    `embedding_loader::sparse::feature_path`: [string] 模型特征 field 描述文件的路径

    `embedding_loader::sparse::model_meta_path`: [string] 模型特征 field 描述文件的路径

    `embedding_loader::sparse::model_path`: [string] sparse checkpoint embedding 版本文件路径

    `embedding_loader::sparse::hybrid_embed`: [dict] sparse embedding kv table 存取配置(HybridEmbedding)

    `embedding_loader::sparse::hybrid_embed::slot_high_nbits`: [int] 默认 12, 特征 sign(slot_value concat 而成) 的 slot 占 sign 高位的 bit 数 (其实还有个常量 reco_slot_offset_=48)，
        无需 feature map 时，slot 对应 hybrid embedding field 编号，需 feature map 时，slot 需要用下面的 feature_map 进行映射, 对应老实现的 slot_offset
                                             
    `embedding_loader::sparse::hybrid_embed::remove_sign_prefix`: [bool] true 时，特征 sign 的 value 部分作为 hybrid embedding 的查询 key，
        false 时，特征 sign 作为 hybrid embedding 的查询 key

    `embedding_loader::sparse::hybrid_embed::kai_nohash_model`: [bool] deprecated(by liuxin22)

    `embedding_loader::sparse::hybrid_embed::embedding_variable_prefix`: [string] sparse embedding btqueue reader 配置信息(BtqReader),
                                                       根据 embedding name，对 embedding update message 进行白名单过滤

    `embedding_loader::sparse::hybrid_embed::embedding_cache_type`: [string] sparse embedding 存储的实现方式，
                                                             默认 : MemCircularBuffer，直接写内存，重启丢失数据,
                                                             shm : ShmCircularBuffer，在 /dev/shm/... 下，
                                                             file : MmapCircularBuffer，在 root_path/embeddings/ 下

    `embedding_loader::sparse::hybrid_embed::ps_capacity_gb`: [int] 数据存储 capacity

    `embedding_loader::sparse::hybrid_embed::embedding_min_fill_ratio`: [float] sparse embedding table min fill ratio, 小于这个比例时不算可服务状态

    `embedding_loader::sparse::hybrid_embed::zero_for_miss_sign`: [bool] whether use zero embedding for missed sign

    `embedding_loader::sparse::hybrid_embed::update_qps`: [int] sparse embedding kv table 更新 qps 的上限

    `embedding_loader::sparse::hybrid_embed::enable_zero_cache`: [bool] true, 查询失败返回默认的 zero embedding, false，则返回失败状态

    `embedding_loader::sparse::feature_map`: [list] of [dict] 可选，sparse embedding table slot->field mapping config, 
                                             没配置时，直接用 field 映射 hybrid embedding bucket，用 feature sign 做 key
                                             配置时，则要用 sign 的高位部分作 map_slot/remap_slot 来映射 field 再映射 bucket，同时还要修改 sign 做 key

    `embedding_loader::sparse::feautre_map::map_slot`: [int] 特征 sign slot_value 的 slot 部分，为了兼容 kai feature slot

    `embedding_loader::sparse::feautre_map::remap_slot`: [int] map_slot==4095 时的 value 部分，为了兼容 zero cache/默认特征

    `embedding_loader::sparse::feautre_map::field`: [int] ad 特征 field 编号,
                                                    map_slot/remap_slot -> field 关系见 feature_map_for_ps.txt,
                                                    field->slot 关系见原本的 kai_feature.txt

    `embedding_loader::sparse::feautre_map::slot`: [int] hybrid embedding 内部存储的 slot 编号

    `embedding_loader::sparse::feautre_map::category`: [string] 特征分类名称:reco_user/user/photo/combine/dense_user/dense_photo/dense_combine

    `embedding_loader::sparse::emp_service`: [dict] 查询 sparse embedding 时得一些相关配置(ConcurrentEmbedding (FieldEmbeddingStorage))
                                             sparse embedding 比较多时，需要分布式存储，该文件用于描述各 field 存在哪里,
                                             然后 ad 场景的 sparse embedding 分布式存储由 infer 的 local 存储 + emp server 的远程多 shard 存储
                                             组合完成，所以 emp_service 里才有个看似奇怪的 local 部分

    `embedding_loader::sparse::emp_service::ps_capacity_gb`: [int] 本地 shard 数据存储 capacity

    `embedding_loader::sparse::emp_service::emp_capacity_gb`: [int] 远程 shard 数据存储 capacity

    `embedding_loader::sparse::emp_service::emp_capacity_count`: [int] 远程 shard 数

    `embedding_loader::sparse::emp_service::$shard`: [list] of [dict] $shard 是可变的, 可以配置多个 shard （构造 FieldEmbeddingStorage, 每个 shard 代表什么来着？)
                                                     field, 即特征 field id,
                                                     size, 即 field size,
                                                     embedding_len, 即 field 内存储的 embedding 维度,
                                                     capacity, 即该 field 最大可存储的 embedding 数量,
                                                     slot, 即该 field 对应的 storage slot,
                                                     op, 一个field 查询到多个 embedding 后如何处理，支持 SUM(默认)、CONCAT 两种,
                                                     concat_len, op 为 CONCAT 时，约束 embedding 总长度，多了少了都不行,
                                                     remap_slot,
                                                     prefix,
                                                     mio_bucket_size,

    `embedding_loader::sparse::btq_reader`: [dict] sparse embedding btqueue reader 配置信息(BtqReader)

    `embedding_loader::sparse::btq_reader::queue_name`: [string] sparse embedding 更新数据所在的 btqueue topic

    `embedding_loader::sparse::btq_reader::read_time_offset_minutes`: [int64] 默认 0，读取 sparse embedding btq 时的 time offset

    `embedding_loader::sparse::btq_reader::read_thread_num`: [int] 默认 1，读取 sparse embedding btq message 的线程数

    `embedding_loader::sparse::btq_reader::read_qps`: [int] 默认 100，读取 sparse embedding btq 的 qps upperbound 限制

    `embedding_loader::sparse::btq_reader::worker_num`: [int] 默认 1，处理 sparse embedding btq message 的 worker num（具体如何处理由用户设置回调决定）

    `embedding_loader::sparse::btq_reader::queue_memory_limit_size_mb`: [int] 默认 128, queue reader buffer size

    `embedding_loader::sparse::idt(TODO)`: [dict] 服务启动时 sparse embedding idt 加载相关配置，目前配置复用 kconf 的方式，仅通过该配置是否有无决定是否启动 idt 功能

    `embedding_loader::sparse::idt(TODO)::idt_server_kess_name`: [string] idt service kess name (类似 bt shm kv kess service)

    `embedding_loader::sparse::idt(TODO)::rpc_thread_num`: [int] 默认 20，idt 数据交换服务线程数据

    `embedding_loader::sparse::idt(TODO)::rpc_server_port`: [int] 默认 32320

    `embedding_loader::sparse::idt(TODO)::min_fill_ratio`: [float] 默认 0.9, idt 至少得拉去 hybrid embedding 可 serving 数量的比例

    `embedding_loader::sparse::idt(TODO)::use_common_idt_service`: [bool] 默认 false (infer 需要设置 true), true 会启用 common_idt, false 启动 idt

    `embedding_loader::sparse::idt(TODO)::common_idt_config`: [dict], 不配置会直接启用 kconf ad.predict.common_idt_config

    `embedding_loader::sparse::idt(TODO)::common_idt_config::idt_handler_config`: [dict]
        key 在双塔 infer 时只需要 model_data_0 (kconf ad.predict.common_idt_config 因为是很多模块共用，所以 key 还有 item、model_data_1 等等),
        value 也是一个 dict，字段下：
        type_name: [string] AdModelIdtHandler(拉取 model dense weights 和 sparse embedding), AdItemIdtHandler(拉取 item 物料信息),
        data_type: [string] 与 key 保持一致,
        model_data_source: [string] embedding_data_0, embedding_data_1 ... 保持与 data_type 后缀一致,
        embedding_data_source: [string] embedding_data_0, embedding_data_1 ... 保持与 data_type 后缀一致,

    `embedding_loader::sparse::feature_info(TODO)`: [dict] field、slot config (filie_file_info.cc), 先不用这个，直接从 feature field file 加载信息

    `embedding_loader::sparse::feautre_info(TODO)::field`: [string] 特征 field id

    `embedding_loader::sparse::feautre_info(TODO)::prefix`: [int]

    `embedding_loader::sparse::feautre_info(TODO)::slot`: [int]

    `embedding_loader::sparse::feautre_info(TODO)::size`: [int]

    `embedding_loader::sparse::feautre_info(TODO)::capacity`: [int]

    `embedding_loader::sparse::feature_info(TODO)::class`: [string] feature class name, ExtractUserRefreshDirection/ExtractUserId/ ...

    `embedding_loader::sparse::feautre_info(TODO)::category`: [string] 特征分类名称:reco_user/user/photo/combine/dense_user/dense_photo/dense_combine

    `embedding_loader::sparse::feautre_info(TODO)::hash_type`: [string] 特征 hash_type 

    `embedding_loader::sparse::feautre_info(TODO)::emb_size`: [int]

    `embedding_loader::sparse::feautre_info(TODO)::no_default`: [string]


    `embedding_loader::item(TODO)`: [dict] item embedding storage, item 因为是 feature embedding 和 item top embedding concat 一起存储，
                              故有一个单独的存储设计。因为 concat 到一起了，infer 时 item 侧等价于只有一个 feature/field,
                              item input tensor 的构造就很简单了

    `embedding_loader::item(TODO)::consumer`: [dict] embedding message queue consumer, 仿照 MQConsumer & ConsumerConfig 的实现

    `embedding_loader::item(TODO)::consumer::full_emb_topic`: [string] batch embedding btq topic

    `embedding_loader::item(TODO)::consumer::incr_emb_topic`: [string] increament embedding btq topic

    `embedding_loader::item(TODO)::consumer::model_delay_load_seconds`: [int] default 0, embedding 延迟加载的时间，why ？

    `embedding_loader::item(TODO)::consumer::item_embedding_reload_interval`: [int] default 600000, 服务重启时回溯 item embedding btq 数据的时间

    `embedding_loader::item(TODO)::consumer::heartbeat_alert_seconds`: [int] consumer 心跳上报间隔

    `embedding_loader::item(TODO)::consumer::wait_model_reload_threshold_seconds`: [int] 等待模型加载成功的最大等待时间，超时未完成加载视为失败

    `embedding_loader::item(TODO)::consumer::embedding_min_raito`: [int] 最小的 embedding 数据加载比例，加载量大于改值 * 总量可视为有效数据（总量信息由更新协议里携带）

    `embedding_loader::item(TODO)::consumer::check_timestamp`: [bool] 默认 true，检查 embedding 数据的更新时间戳

    `embedding_loader::item(TODO)::consumer::verify_producer_code`: [bool] 默认 true，验证 producer 校验码

    `embedding_loader::item(TODO)::consumer::disable_incr_topic`: [bool] 默认 false， true 视为禁止读取增量更新 topic

    `embedding_loader::item(TODO)::consumer::enable_read_qps_limit`: [bool] 默认 true，是否启用 btq 消费 qps 限制功能

    `embedding_loader::item(TODO)::consumer::read_qps`: [int] 默认 1，消费 btq 数据的 qps 限制

    `embedding_loader::item(TODO)::consumer::read_threads_num`: [int] 默认 1，btq 消费线程数

    `embedding_loader::item(TODO)::consumer::worker_num`: [int] 默认 6，处理 btq message 的 worker 数量

    `embedding_loader::item(TODO)::consumer::check_model_version`: [bool] 验证 model version

    `embedding_loader::item(TODO)::consumer::check_verify_code`: [bool] 验证 model cmd

    `embedding_loader::item(TODO)::storage_type`: [string] EmbeddingStore / PreallocatedEmbeddingGroup,
                                            EmbeddingStore 需要配置下面的 embedding_store 配置项，如果用 gpu_cache 则还需配置配套的 gpu_cache 配置,
                                            PreallocatedEmbeddingGroup, 需要配置下面的embed_group 配置项

    `embedding_loader::item(TODO)::embedding_store`: [dict] 对应着 storage_type=EmbeddingStore (仿照update by WeightManager::InsertEmbeddingStore)

    `embedding_loader::item(TODO)::embedding_store::type`: [string] storege type: ST_EMBEDDING/ST_PROTOBUF/ST_FLATBUFFER/ ... (ad/ad_nn/feature_store/common.h StorageType)

    `embedding_loader::item(TODO)::embedding_store::use_fp16`: [bool] true: float16 embedding data, false: float32 data embedding data 

    `embedding_loader::item(TODO)::embedding_store::pre_allocate_for_callback`: [bool]

    `embedding_loader::item(TODO)::embedding_store::capacity_in_mem`: [int] EmbeddingStore 内存中开辟的最大 kv 存储数量 

    `embedding_loader::item(TODO)::embedding_store::lru_capacity`: [int] lru 更新算法下限定的最大可存储的 kv 数量（涉及存取和更新效率）

    `embedding_loader::item(TODO)::embedding_store::do_not_store_btq_item`: [bool] 一些特殊设计、只消费不存储，先不管

    `embedding_loader::item(TODO)::embed_group`: [dict] 对应着 storage_type=PreallocatedEmbeddingGroup (update by WeightManager::PushEmbAndId),
                                           已集成到 model update 过程中，不用放到 predict processor 里, 相应的 ItemInfo 的更新可以移到后续的 result handler processor 里

    `embedding_loader::item(TODO)::embed_group::cluster_search`: [dict] ClusterSearchConfig::Update

    `embedding_loader::item(TODO)::embed_group::cluster_search::max_bid_list_count`: [int] 默认 10000000

    `embedding_loader::item(TODO)::embed_group::cluster_search::max_cluster_count`: [int] 默认 1500000，物料聚合后 cluster 的最大数量

    `embedding_loader::item(TODO)::embed_group::cluster_search::max_info_cluster_seq_len`: [int] 默认 3000

    `embedding_loader::item(TODO)::embed_group::cluster_search::cluster_topk`: [int] 默认 3000

    `embedding_loader::item(TODO)::embed_group::cluster_search::max_unit_per_combine_key`: [int] 默认-1

    `embedding_loader::item(TODO)::embed_group::cluster_search::expand_bid_op_name`: [string] 默认 expand_bid

    `embedding_loader::item(TODO)::embed_group::cluster_search::expand_index_op_name`: [string] 默认 expand_index

    `embedding_loader::item(TODO)::embed_group::cluster_search::cluster_type`: [int] 默认无效 type

    `embedding_loader::item(TODO)::embed_group::cluster_search::info_cluster_type`: [int] 默认 COMBINE_DUP_PHOTO_PRODUCT_OCPX_BID_TYPE

    `embedding_loader::item(TODO)::embed_group::cluster_search::quality_score_type`: [int] 默认 6

    `embedding_loader::item(TODO)::embed_group::cluster_search::converter_type`: [int] producer 用的配置

    `embedding_loader::item(TODO)::embed_group::cluster_search::cluster_return_type`: [int] 默认 1, photo_ocpc_return_type

    `embedding_loader::item(TODO)::embed_group::embedding`: [list] of [dict] PreallocatedEmbeddingGroup embedding_data_ & cluster_embedding_data_,
                                                       item embedding 存储相关配置, 其实就是 embedding_outputs 的配置

    `embedding_loader::item(TODO)::embed_group::embedding::name`: [int] embedding 列名称

    `embedding_loader::item(TODO)::embed_group::embedding::length`: [int] embedding 维度长度

    `embedding_loader::item(TODO)::embed_group::embedding::max_capacity`: [int] embedding 最大存多少 item

    `embedding_loader::item(TODO)::embed_group::embedding::dtype`: [int] embedding 数据类型，目前只支持 float/half

    `embedding_loader::item(TODO)::embed_group::info:`: [int] ItemInfoListOnArena creative_list_ & cluster_list_, 本质上只是个可顺序遍历的 item_id->ItemInfo 的 map 存储,所以没啥配置

    `embedding_loader::gpu_cache(TODO)`: [dict] 可选配置，item embedding store on gpu 配置信息(DoubleBufferLRUCache),
                                   目前仅 storage_type=EmbeddingStore 需要,(PreallocatedEmbeddingGroup 时 item embedding 全部都 build 到 engine 里了，无需 gpu cache)
                                   gpu_cache_options_ @ teams/ad/ad_nn/kconf_manager/compute_engine_config.cc

    `embedding_loader::gpu_cache(TODO)::monitor_name`: [string] item embedding monitor flag (一般用 btq topic name)

    `embedding_loader::gpu_cache(TODO)::lru_capacity`: [int] lru cache 可有效存取的最大 kv 数量

    `embedding_loader::gpu_cache(TODO)::dtype`: [string] DT_HALF/DT_FLOAT, 支持的 embedding data type

    `embedding_loader::gpu_cache(TODO)::max_query_count`: [int] 一个 cache context 支持的最大 item embedding 数量，涉及 runtime buffer 空间的申请

    `embedding_loader::gpu_cache(TODO)::update_batch_size`: [int] gpu cache batch 更新时支持的最大 item embedding 更新数量 

    `embedding_loader::gpu_cache(TODO)::update_batch_interval_ms`: [int] gpu cache batch 更新的最小间隔

    `embedding_loader::gpu_cache(TODO)::cache_embedding_len`: [int] item embedding length，目前 cache 只支持存储定长的的 item embedding,
                                                        实际由原来 compute_engine const_inputs 决定, 不过这里需要一个长度信息即可

    `embedding_loader::gpu_cache(TODO)::const_inputs`: [dict] item embedding 的组成部分, item spase/dense, item top embedding, ...,
    
    `embedding_loader::gpu_cache(TODO)::const_inputs::name`: [string]

    `embedding_loader::gpu_cache(TODO)::const_inputs::dims`: [list] of [dict]

    `embedding_loader::gpu_cache(TODO)::const_inputs::dtype`: [string]

    `embedding_loader::gpu_cache(TODO)::const_inputs::source`: [string]

    `embedding_loader::gpu_cache(TODO)::const_inputs::transpose`: [bool]
                           
    `embedding_loader::gpu_cache(TODO)::use_context_pool`: [bool] deprecated

    `embedding_loader::gpu_cache(TODO)::context_pool_size`: [int] deprecated

    `is_debug`: [bool] 为 true 时会 dump 一些查询的 sign/embedding 和 tensor 信息

    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(AdUniPredictFusedAttrEnricher(kwargs))
    return self


  def uni_predict_only_ad(self, **kwargs):
    """
    UniPredicOnlyAdAttrEnricher
    把 UniPredictFusedAdAttrEnricher 里 fetch embedding 的部分剥离出去
    ------
    参数配置
    ------
    调用示例
    ------
    ``` python
    ```
    """
    # self._add_processor(UniPredictFusedAdAttrEnricher(kwargs))
    return self


  def fetch_embedding_by_hybrid(self, **kwargs):
    """
    FetchHybridEmbeddingEnricher
    从 hybrid embedding table 中查询 embedding 并构造 input tensor

    参数配置
    ------

    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(AdFetchHybridEmbeddingEnricher(kwargs))
    return self

  def fetch_item_embedding_by_embedding_store(self, **kwargs):
    """
    FetchEmbeddingByEmbeddingStore
    从 embedding store 中查询 item embedding 并构造 input tensor

    ------
    参数配置
    ------
    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(UniPredictFusedAdAttrEnricher(kwargs))
    return self


  def fetch_embedding_by_gpu_cache(self, **kwargs):
    """
    FetchEmbeddingByGPUCache 
    从 gpu cache 中查询 item embedding 并构造 hit_item_tensor、hit_item_index、miss_item_ids、miss_item_index

    ------
    参数配置
    ------
    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(UniPredictFusedAdAttrEnricher(kwargs))
    return self

  def ad_result_cache_retriever(self, **kwargs):
    """
    召回结果读/写缓存

    参数配置
    ------
    `result_cache_ttl`: [int] 缓存过期时间, 单位: 秒

    `result_cache_capacity`: [int] 缓存容量

    `result_cache_keys`: [string] 缓存 key 前缀, 为空则用 user id 作为 key

    `is_read`: [bool] true 为 read, false 为 write

    `hit_cache_attr`: [string] common attr, 如果命中缓存, 写入 1 , 否则写入 0

    `secondary_id_attr`: [string] common attr, 存储 secondary id list

    `request_ptr`: [string] common attr, ad Universe 的 request 指针

    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(AdResultCacheRetriever(kwargs))
    return self

def extract_combine_dense_feature(self, **kwargs):
    """
    抽取combine dense特征.

  
    参数配置
    ------
    `slot_bits`: [int] 输入
    `slot_bits`: [int] 输入
    `uid_attr`: [string] 输入
    `ubits`: [int] 输入
    `item_attr`: [string] 输入
    `ibits`: [int] 输入
    `item_num`: [int] 输入
    `user_action_attr_list`: [vector<string>] 输入
    `output_sign`: [string] 输出
    `output_match_cnt`: [string] 输出
    `....`: [TODO] TODO
    调用示例
    ------
    ``` python
    ```
    """
    self._add_processor(AdCombinieDenseEnricher(kwargs))
    return self