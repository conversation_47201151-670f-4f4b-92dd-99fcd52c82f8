#!/usr/bin/env python3
"""
filename: merchant_retriever.py
description: common_leaf dynamic_json_config DSL intelligent builder, retriever module for merchant
author: <EMAIL>
date: 2021-01-14 13:58:00
"""

import operator
import itertools

from ...common_leaf_util import strict_types, gen_attr_name_with_common_attr_channel, check_arg
from ...common_leaf_processor import LeafR<PERSON>riever, try_add_table_name

class FullRecallForwardFlowParserMessageRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "full_recall_forward_flow_parser_message_retriever"
    
  @property
  @strict_types
  def output_item_attrs(self) -> set:
    ret = set()
    embedding_to_item_attr = self._config.get("embedding_to_item_attr")
    ret.add(embedding_to_item_attr)
    return ret 

class UniRecallResultRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "uni_gpu_full_recall_retrieval"
    
  @strict_types
  def check_config(self) -> None:
    model_key = self._config.get("key")
    assert len(model_key) > 0, "❌ Must set \"model_key\"."
   
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    topk_index_attr_name = self._config.get("topk_index_attr_name")
    topk_score_attr_name = self._config.get("topk_score_attr_name")
    assert len(topk_index_attr_name) > 0, "❌ Must set \"topk_index_attr_name\"."
    assert len(topk_score_attr_name) > 0, "❌ Must set \"topk_score_attr_name\"."
    ret.update([topk_index_attr_name, topk_score_attr_name])
    # 只有海选的模式下才需要这个 attr
    gather_index_tensor_common_attr_name = self._config.get("gather_index_tensor_common_attr_name", "")
    if gather_index_tensor_common_attr_name != "":
        ret.add(gather_index_tensor_common_attr_name)
    return ret

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    ret = set()
    topk_item_id_attr_name = self._config.get("topk_item_id_attr_name")
    ret.add(topk_item_id_attr_name)
    # 只有海选的模式下才需要这个 attr
    gather_index_tensor_common_attr_name = self._config.get("gather_index_tensor_common_attr_name", "")
    if gather_index_tensor_common_attr_name != "":
        save_origin_reason_list_to_common_attr_name_ = self._config.get("save_origin_reason_list_to_common_attr_name", "origin_reason")
        ret.add(save_origin_reason_list_to_common_attr_name_)
    return ret

class MerchantRedisOrderedIndexRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_merchant_redis_ordered_index"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("min_score")))
    attrs.update(self.extract_dynamic_params(self._config.get("max_score")))
    attrs.update(self.extract_dynamic_params(self._config.get("key_list")))
    attrs.update(self.extract_dynamic_params(self._config.get("prefix_name")))
    attrs.update(self.extract_dynamic_params(self._config.get("suffix_name")))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attr = self._config.get("key_save_attr", "")
    if attr:
      attrs.add(attr)
    return attrs

  @strict_types
  def is_async(self) -> bool:
    return True

class MerchantGoodClickColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_good_click_retriever_with_colossus_resp"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
            "save_item_id_to_attr", "save_author_id_to_attr", "save_cate1_to_attr",
            "save_cate2_to_attr", "save_cate3_to_attr", "save_timestamp_to_attr",
        ] if key in self._config
    }

class MerchantGoodClickColossusRespRetrieverV2(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_good_click_retriever_with_colossus_resp_v2"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    ret = set()
    ret.add(self._config["colossus_resp_attr"])
    return ret

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    return {
        self._config[key] for key in [
            "save_item_id_to_attr", "save_click_from_to_attr", "save_cate1_to_attr",
            "save_cate2_to_attr", "save_cate3_to_attr", "save_timestamp_to_attr",
            "save_real_price_to_attr", "save_origin_price_to_attr", "save_label_to_attr",
            "save_seller_id_to_attr", "save_real_seller_id_to_attr", "save_click_flow_type_to_attr",
            "save_detail_page_view_time_to_attr", "save_leaf_cate_to_attr",
        ] if key in self._config
    }

class MerchantRecoLocalKvIndexRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_retrieve_by_local_kv_index"

  @strict_types
  def _check_config(self) -> None:
    check_arg(isinstance(self._config.get("reason"), int) and self._config["reason"] > 0, "reason 需为大于 0 的整数")

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    extra_item_attrs = self._config.get("extra_item_attrs", [])
    for attr in extra_item_attrs:
        attrs.add(attr["as_name"])
    return attrs

class MerchantRecoRedisRegexTriggerRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_uni_recall_redis_regex_trigger"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs
  
  @strict_types
  def _check_config(self) -> None:
    if not self._config.get("redis_triger_kconf_path"):
      check_arg(self._config.get("redis_triger_kconf_gray_path"), "redis_triger_kconf_path 未配的情况下必须配置 redis_triger_kconf_gray_path")

class MerchantRecoLocalKvTriggerRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_uni_recall_local_kv_trigger"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class MerchantRecoCommonAttrRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_uni_recall_direct_trigger"

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

class MerchantCommonDistributedCacheReaderRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_uni_recall_distributed_cache"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    return attrs

  @property
  def output_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("perf_cache_hit_common_attr", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    if "attr_name_types" in self._config:
      attrs = { v.get("as", v["name"])  if isinstance(v, dict) else v \
          for v in self._config.get("attr_name_types", []) }
    return attrs

  @strict_types
  def _check_config(self) -> None:
    check_arg(self._config.get("attr_name_types"), "缺少 attr_name_types")
    check_arg(self._config.get("photo_store_kconf_key"), "缺少 photo_store_kconf_key")
    check_arg(self._config.get("table_kconf_path"), "缺少 table_kconf_path")
    check_arg(self._config.get("table_name"), "缺少 table_name")

class ShopCustomerFaqAnswerTypeRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_answer_type_from_faq_service"
  
  @strict_types
  def is_async(self) -> bool:
    return True
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("kess_service")))
    attrs.update(self.extract_dynamic_params(self._config.get("query", "")))
    attrs.add(self._config.get("from_name", ""))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("answer_type", ""))
    attrs.add(self._config.get("intent", ""))
    attrs.add(self._config.get("query", ""))
    attrs.add(self._config.get("question", ""))
    return attrs

class MerchantElasticSearchRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_elastic_search"
  
  @strict_types
  def is_async(self) -> bool:
    return True
  
  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.update(self.extract_dynamic_params(self._config.get("index")))
    attrs.update(self.extract_dynamic_params(self._config.get("query", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("type", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("cluster", "")))
    attrs.update(self.extract_dynamic_params(self._config.get("normalization_socre_attr_name", "")))
    return attrs

class ShopCustomerSemanticRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrive_by_semantic"

  @strict_types
  def is_async(self) -> bool:
    return True

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("query", ""))
    return attrs

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    attrs = set()
    #attrs.add(self._config.get("output_common_attr", ""))
    return attrs

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("tenant_id", ""))
    attrs.add(self._config.get("recall_num", ""))
    return attrs


  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("intent",""))
    attrs.add(self._config.get("question",""))
    attrs.add(self._config.get("answer",""))
    attrs.add(self._config.get("knowledge_id", ""))
    attrs.add(self._config.get("intent_id", ""))
    attrs.add(self._config.get("question_id", ""))
    attrs.add(self._config.get("answer_id", ""))
    return attrs

class MerchantCommonColossusRespRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_gsu_common_colossus_resp_retriever"

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    return {self._config["colossus_resp_attr"]}

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    if self._config.get("to_common_attr", False):
      return set()
    return set(self._config["item_fields"].values())

  @property
  @strict_types
  def output_common_attrs(self) -> set:
    if self._config.get("to_common_attr", False):
      return set(self._config["item_fields"].values())
    return set()


class MerchantSampleJoinApiRequestRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_retrieve_from_sample_join_api_request"
class MerchantLeafSharedCopyRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "merchant_leaf_shared_copy_retrieve"

  @property
  @strict_types
  def input_item_attrs(self) -> set:
    attrs = set()
    for item in self._config.get("shared_list", []):
      table_name = item.get("from_table")
      item_attr = item.get("target_attr")
      attrs.update(try_add_table_name(table_name, [item_attr]))
    return attrs

  @property
  @strict_types
  def output_item_attrs(self) -> set:
    attrs = set()
    for item in self._config.get("shared_list", []):
      attrs.update(item.get("copied_flat_index_list", []))
      attrs.update(item.get("copied_common_index_list", []))
      attrs.update(item.get("copied_other_attr_list", []))
      # FIXME(huzengyi) 目前依赖检测没有考虑 table ，临时加一些该 table 实际不使用的 attr
      attrs.update(item.get("tricky_attr_list", []))
    return attrs

  @strict_types
  def no_check(self) -> bool:
    # FIXME(huzengyi) flat index 字段只读，临时跳过检测
    return True
  
class MerchantLeafMemoryDataRetriever(LeafRetriever):
  @classmethod
  @strict_types
  def get_type_alias(cls) -> str:
    return "retrieve_by_memory_data"

  @strict_types
  def is_async(self) -> bool:
    return False

  @property
  @strict_types
  def input_common_attrs(self) -> set:
    attrs = set()
    attrs.add(self._config.get("data_ptr_attr"))
    attrs.add(self._config.get("selected_map_key_attr"))
    for name in ["data_key", "retrieve_num"]:
      attrs.update(self.extract_dynamic_params(self._config.get(name)))
    return attrs
