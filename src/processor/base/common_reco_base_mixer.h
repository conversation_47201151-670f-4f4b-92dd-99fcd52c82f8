#pragma once

#include <algorithm>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_processor.h"
#include "folly/String.h"
#include "folly/container/F14Set.h"

namespace ks {
namespace platform {

/**
 * CommonRecoLeaf Mixer 的基类
 * Leaf 开发者应继承该类进行新 Mixer 的实现，针对场景：多表算子
 * 内容更新
 */
class CommonRecoBaseMixer : public CommonRecoBaseProcessor {
 public:
  // 给 Leaf 开发人员实现具体 Mix 逻辑的接口
  virtual void Mix(AddibleRecoContextInterface *context) = 0;

  void Run(AddibleRecoContextInterface *context, std::vector<CommonRecoResult> *results) final {
    Mix(context);
  }

  ProcessorType GetType() const final {
    return ProcessorType::MIXER;
  }

  void OnEnter(ReadableRecoContextInterface *context) override {
    VLOG(100) << "Enter processor: " << GetName();
  }

  void OnPipelineExit(ReadableRecoContextInterface *context) override {}

  void OnExit(ReadableRecoContextInterface *context) override {}

 protected:
  CommonRecoBaseMixer() {}

  // 实现一个追加召回结果的辅助函数
  void AddToRecoResults(AddibleRecoContextInterface *context, AttrTable *table,
                        const std::vector<CommonRecoRetrieveResult> &candidates,
                        std::vector<CommonRecoResult> *results = nullptr) {
    if (candidates.empty()) {
      base::perfutil::PerfUtilWrapper::CountLogStash(kPerfNs, "empty_retrieve",
                                                     GlobalHolder::GetServiceIdentifier(),
                                                     context->GetRequestType(), GetName(), table->GetName());
      return;
    }

    if (results) {
      results->reserve(results->size() + candidates.size());
    }

    // 统计各 reason 数目
    folly::F14FastMap<int, int> reason_count;
    for (const auto &candidate : candidates) {
      auto &result = table->AddCommonRecoResult(candidate.item_key, candidate.reason, candidate.score, 0);
      ++reason_count[candidate.reason];
      if (results) {
        results->push_back(result);
      }
    }
    for (const auto &pr : reason_count) {
      base::perfutil::PerfUtilWrapper::IntervalLogStash(
          pr.second, kPerfNs, "retrieve_item_num", GlobalHolder::GetServiceIdentifier(),
          context->GetRequestType(), base::IntToString(pr.first), table->GetName());
    }
  }

  // 实现一个拷贝 attr 值的辅助函数
  void CopyAttrValues(AddibleRecoContextInterface *context, AttrValue *from_accessor,
                      const std::vector<CommonRecoResult> &from_results, AttrValue *to_accessor,
                      const std::vector<CommonRecoResult> &to_results, CopyMode copy_mode) {
    if (from_results.size() != to_results.size()) {
      CL_LOG_ERROR("mixer_copy_value", "results_size_mismatch");
      return;
    }
    for (int i = 0; i < to_results.size(); i++) {
      const auto &from_result = from_results[i];
      const auto &to_result = to_results[i];
      CopyAttrValue(context, from_accessor, from_result, to_accessor, to_result, copy_mode);
    }
  }

  // 实现一个拷贝 attr 值的辅助函数
  void CopyAttrValue(AddibleRecoContextInterface *context, AttrValue *from_accessor,
                     const CommonRecoResult &from_result, AttrValue *to_accessor,
                     const CommonRecoResult &to_result, CopyMode copy_mode) {
    switch (from_accessor->value_type) {
      case AttrType::INT: {
        if (auto int_value = context->GetIntItemAttr(from_result, from_accessor)) {
          if (copy_mode == CopyMode::OVERWRITE) {
            context->SetIntItemAttr(to_result, to_accessor, int_value.value());
          } else if (copy_mode == CopyMode::MAX) {
            auto origin_value = context->GetIntItemAttr(to_result, to_accessor);
            int64 max_value = int_value.value();
            if (origin_value && origin_value.value() > max_value) {
              max_value = origin_value.value();
            }
            context->SetIntItemAttr(to_result, to_accessor, max_value);
          } else if (copy_mode == CopyMode::CONCAT) {
            context->AppendIntListItemAttr(to_result, to_accessor, int_value.value());
          }
        }
        break;
      }
      case AttrType::FLOAT: {
        if (auto double_value = context->GetDoubleItemAttr(from_result, from_accessor)) {
          if (copy_mode == CopyMode::OVERWRITE) {
            context->SetDoubleItemAttr(to_result, to_accessor, double_value.value());
          } else if (copy_mode == CopyMode::MAX) {
            auto origin_value = context->GetDoubleItemAttr(to_result, to_accessor);
            double max_value = double_value.value();
            if (origin_value && origin_value.value() > max_value) {
              max_value = origin_value.value();
            }
            context->SetDoubleItemAttr(to_result, to_accessor, max_value);
          } else if (copy_mode == CopyMode::CONCAT) {
            context->AppendDoubleListItemAttr(to_result, to_accessor, double_value.value());
          }
        }
        break;
      }
      case AttrType::STRING: {
        if (auto string_view_value = context->GetStringItemAttr(from_result, from_accessor)) {
          std::string string_value(string_view_value->data(), string_view_value->size());
          if (copy_mode == CopyMode::OVERWRITE) {
            context->SetStringItemAttr(to_result, to_accessor, string_value);
          } else if (copy_mode == CopyMode::CONCAT) {
            context->AppendStringListItemAttr(to_result, to_accessor, string_value);
          } else {
            CL_LOG_ERROR_EVERY("table_union_update_mixer", "unsupport_copy_mode", 10000)
                << "Unsupport copy mode:" << int(copy_mode) << " attr name:" << from_accessor->name();
          }
          break;
        }
      }
      case AttrType::INT_LIST: {
        if (auto int_list_value = context->GetIntListItemAttr(from_result, from_accessor)) {
          if (copy_mode == CopyMode::OVERWRITE) {
            std::vector<int64> values(int_list_value->begin(), int_list_value->end());
            context->SetIntListItemAttr(to_result, to_accessor, std::move(values));
          } else if (copy_mode == CopyMode::CONCAT) {
            for (auto int_v : int_list_value.value()) {
              context->AppendIntListItemAttr(to_result, to_accessor, int_v);
            }
          } else {
            CL_LOG_ERROR_EVERY("table_union_update_mixer", "unsupport_copy_mode", 10000)
                << "Unsupport copy mode:" << int(copy_mode) << " attr name:" << from_accessor->name();
          }
        }
        break;
      }
      case AttrType::FLOAT_LIST: {
        if (auto float_list_value = context->GetDoubleListItemAttr(from_result, from_accessor)) {
          if (copy_mode == CopyMode::OVERWRITE) {
            std::vector<double> values(float_list_value->begin(), float_list_value->end());
            context->SetDoubleListItemAttr(to_result, to_accessor, std::move(values));
          } else if (copy_mode == CopyMode::CONCAT) {
            for (auto float_v : float_list_value.value()) {
              context->AppendDoubleListItemAttr(to_result, to_accessor, float_v);
            }
          } else {
            CL_LOG_ERROR_EVERY("table_union_update_mixer", "unsupport_copy_mode", 10000)
                << "Unsupport copy mode:" << int(copy_mode) << " attr name:" << from_accessor->name();
          }
        }
        break;
      }
      case AttrType::STRING_LIST: {
        if (auto string_list_value = context->GetStringListItemAttr(from_result, from_accessor)) {
          if (copy_mode == CopyMode::OVERWRITE) {
            std::vector<std::string> values;
            for (const auto view : string_list_value.value()) {
              values.emplace_back(view.data(), view.size());
            }
            context->SetStringListItemAttr(to_result, to_accessor, std::move(values));
          } else if (copy_mode == CopyMode::CONCAT) {
            for (const auto view : string_list_value.value()) {
              context->AppendStringListItemAttr(to_result, to_accessor, std::string(view));
            }
          } else {
            CL_LOG_ERROR_EVERY("mixer_copy_value", "unsupport_copy_mode", 10000)
                << "Unsupport copy mode:" << int(copy_mode) << " attr name:" << from_accessor->name();
          }
        }
        break;
      }
      case AttrType::EXTRA: {
        if (auto extra_value = context->GetExtraItemAttr(from_result, from_accessor)) {
          if (copy_mode == CopyMode::OVERWRITE) {
            auto val = *extra_value;
            context->SetExtraItemAttr(to_result, to_accessor, std::move(val));
          } else {
            CL_LOG_ERROR_EVERY("mixer_copy_value", "unsupport_copy_mode", 10000)
                << "Unsupport copy mode:" << int(copy_mode) << " attr name:" << from_accessor->name();
          }
        }
        break;
      }
      default:
        break;
    }
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(CommonRecoBaseMixer);
};

/**
 * 含 embedded pipeline 的 Mixer 的基类
 */
class CommonRecoBaseMixerEmbedPipeline : public CommonRecoBaseMixer {
 protected:
  CommonRecoBaseMixerEmbedPipeline() {}

  bool InitEmbeddedPipeline() override;
  bool RunEmbeddedPipeline(
    AddibleRecoContextInterface *context, const std::string &pipeline_name, bool exit_pipeline = true);
  bool ExitPipeline(AddibleRecoContextInterface *context, const std::string &pipeline_name);

 private:
  std::unordered_map<std::string, std::unique_ptr<CommonRecoPipeline>> embedded_pipeline_map_;

  DISALLOW_COPY_AND_ASSIGN(CommonRecoBaseMixerEmbedPipeline);
};

}  // namespace platform
}  // namespace ks
