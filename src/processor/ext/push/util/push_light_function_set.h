#pragma once

#include <algorithm>
#include <ctime>
#include <cstdlib>
#include <limits>
#include <set>
#include <vector>
#include <sstream>
#include <stdexcept>
#include <string>
#include <tuple>
#include <unordered_map>
#include <utility>
#include <regex>
#include <codecvt>
#include <unordered_set>
#include "dragon/src/module/common_reco_light_function.h"
#include "dragon/src/util/logging_util.h"
#include "folly/String.h"
#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"
#include "google/protobuf/util/message_differencer.h"
#include "ks/reco_proto/proto/reco.pb.h"

namespace ks {
namespace platform {

class PushLightFunctionSet : public CommonRecoBaseLightFunctionSet {
 public:
  PushLightFunctionSet() {
    REGISTER_LIGHT_FUNCTION(EnrichSim2);
    REGISTER_LIGHT_FUNCTION(EnrichSimHetu);
    REGISTER_LIGHT_FUNCTION(EnrichSimHetuV4);
    REGISTER_LIGHT_FUNCTION(EnrichRelationOneHot);
    REGISTER_LIGHT_FUNCTION(EnrichPhotoConsumeBucket);
    REGISTER_LIGHT_FUNCTION(EnrichTypeAttr);
    REGISTER_LIGHT_FUNCTION(EnrichDurationAndAgeBucket);
    REGISTER_LIGHT_FUNCTION(EnrichLimitValue);
    REGISTER_LIGHT_FUNCTION(EnrichUserAuthorInfo);
    REGISTER_LIGHT_FUNCTION(EnrichRelationTypeAttr);
    REGISTER_LIGHT_FUNCTION(EnrichProviderInfo);
    REGISTER_LIGHT_FUNCTION(EnrichAuthorGsu);
    REGISTER_LIGHT_FUNCTION(EnrichTagGsu);
    REGISTER_LIGHT_FUNCTION(Int2StringFunc);
    REGISTER_LIGHT_FUNCTION(EnrichMotiItemAttr);
    REGISTER_LIGHT_FUNCTION(EnrichRecallDegradePeriod);
    REGISTER_LIGHT_FUNCTION(EnrichFrDegradePeriod);
    REGISTER_LIGHT_FUNCTION(EnrichContextPhoto);
    REGISTER_LIGHT_FUNCTION(EnrichMcContextPhoto);
    REGISTER_LIGHT_FUNCTION(EnrichMcContextPhotoByEventType);
    REGISTER_LIGHT_FUNCTION(EnrichColossusList);
    REGISTER_LIGHT_FUNCTION(EnrichColossusNegativeList);
    REGISTER_LIGHT_FUNCTION(EnrichColossusConsumeList);
    REGISTER_LIGHT_FUNCTION(EnrichAllianceInfo);
    REGISTER_LIGHT_FUNCTION(EnrichUserInterestPhotosContentTag);
    REGISTER_LIGHT_FUNCTION(EnrichUserInterestPhotosTextInfo);
    REGISTER_LIGHT_FUNCTION(EnrichUserInterestPhotosTruncate);
    REGISTER_LIGHT_FUNCTION(EnrichItemTextInfo);
    REGISTER_LIGHT_FUNCTION(EnrichPushIDList);
    REGISTER_LIGHT_FUNCTION(EnrichPushOpenApiTemplate);
    REGISTER_LIGHT_FUNCTION(EnrichFriendAuthorId);
    REGISTER_LIGHT_FUNCTION(EnrichHighPlayPhoto);
    REGISTER_LIGHT_FUNCTION(EnrichGenerateJudgmentExecutionFlag);
    REGISTER_LIGHT_FUNCTION(EnrichFilterTagPhotoTypeExceed);
    REGISTER_LIGHT_FUNCTION(EnrichFilterTagRecallReasonExceed);
    REGISTER_LIGHT_FUNCTION(EnrichColossusConsumeByHourList);
    REGISTER_LIGHT_FUNCTION(EnrichMcCascadeSampleSign);
    REGISTER_LIGHT_FUNCTION(EnrichMcCascadeSampleSignV2);
    REGISTER_LIGHT_FUNCTION(EnrichMcCascadeSampleSignForMcNegative);
    REGISTER_LIGHT_FUNCTION(EnrichItemCascadeSample);
    REGISTER_LIGHT_FUNCTION(EnrichRankIndex);
    REGISTER_LIGHT_FUNCTION(EnrichDeltaScoreByRecallReason);
    REGISTER_LIGHT_FUNCTION(EnrichCommonSegmentResultConcat);
    REGISTER_LIGHT_FUNCTION(EnrichItemSegmentResultConcat);
    REGISTER_LIGHT_FUNCTION(EnrichMergePush90dClickList);
    REGISTER_LIGHT_FUNCTION(EnrichDeltaScoreByEventType);
    REGISTER_LIGHT_FUNCTION(EnrichFilterEventTypeExceed);
    REGISTER_LIGHT_FUNCTION(EnrichRerankMmrValue);
  }

  static bool EnrichSim2(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                         RecoResultConstIter end) {
    // LOG(INFO) << "EnrichSim2 (const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, "
    // "RecoResultConstIter end)";
    // common input
    auto all_photo_embedding_accessor = context.GetDoubleListCommonAttr("all_photo_embedding");
    if (!all_photo_embedding_accessor) {
      // LOG(INFO) << "all_photo_embedding_accessor is nullopt";
    } else {
      // LOG(INFO) << "all_photo_embedding_accessor size" << all_photo_embedding_accessor->size();
      for (int i = 0; i < all_photo_embedding_accessor->size(); ++i) {
        // LOG(INFO) << "all_photo_embedding_accessor[" << i << "]" << (*all_photo_embedding_accessor)[i];
      }
    }
    // item input
    auto item_emb_accessor = context.GetDoubleListItemAttr("item_emb");
    // item output
    // auto sim2_accessor = context.SetDoubleItemAttr("sim2_test");
    auto sim2_accessor = context.SetDoubleItemAttr("sim2");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_emb_double_list = item_emb_accessor(result);
      if (item_emb_double_list == absl::nullopt) {
        // item_emb_double_list is absl::nullopt
        // LOG(INFO) << "item_emb_double_list is nullopt";
        sim2_accessor(result, 0);
      } else if (all_photo_embedding_accessor) {
        double max_sim2 = std::numeric_limits<double>::lowest();
        for (int i = 0; i < all_photo_embedding_accessor->size() / 32; ++i) {
          // auto item_emb_double_list = item_emb_accessor(result).value_or(std::vector<double>(32, 0.0));
          double tmp_max_sim2 = 0;
          for (int j = 0; j < std::min(size_t(32), item_emb_double_list->size()); ++j) {
            tmp_max_sim2 =
                tmp_max_sim2 + (*all_photo_embedding_accessor)[i * 32 + j] * (*item_emb_double_list)[j];
            // LOG(INFO) << "item_emb_double_list[" << j << "]" << (*item_emb_double_list)[j] << " "
            //<< tmp_max_sim2;
          }
          if (tmp_max_sim2 > max_sim2) {
            max_sim2 = tmp_max_sim2;
          }
        }
        sim2_accessor(result, max_sim2);
      } else {
        // all_photo_embedding_accessor is absl::nullopt
        sim2_accessor(result, 0);
      }
    });
    return true;
  }

  static bool EnrichSimHetu(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                         RecoResultConstIter end) {
    // LOG(INFO) << "EnrichSim2 (const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, "
    // "RecoResultConstIter end)";
    // common input
    auto all_photo_embedding_accessor = context.GetDoubleListCommonAttr("all_photo_embedding");
    if (!all_photo_embedding_accessor) {
      // LOG(INFO) << "all_photo_embedding_accessor is nullopt";
    } else {
      // LOG(INFO) << "all_photo_embedding_accessor size" << all_photo_embedding_accessor->size();
      for (int i = 0; i < all_photo_embedding_accessor->size(); ++i) {
        // LOG(INFO) << "all_photo_embedding_accessor[" << i << "]" << (*all_photo_embedding_accessor)[i];
      }
    }
    // item input
    auto item_emb_accessor = context.GetDoubleListItemAttr("item_hetu_emb");
    // item output
    // auto sim2_accessor = context.SetDoubleItemAttr("sim2_test");
    auto sim2_accessor = context.SetDoubleItemAttr("sim_hetu");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_emb_double_list = item_emb_accessor(result);
      if (item_emb_double_list == absl::nullopt) {
        // item_emb_double_list is absl::nullopt
        // LOG(INFO) << "item_emb_double_list is nullopt";
        sim2_accessor(result, 0);
      } else if (all_photo_embedding_accessor && item_emb_double_list != absl::nullopt) {
        double max_sim2 = std::numeric_limits<double>::lowest();
        for (int i = 0; i < all_photo_embedding_accessor->size() / 64; ++i) {
          // auto item_emb_double_list = item_emb_accessor(result).value_or(std::vector<double>(32, 0.0));
          double tmp_max_sim2 = 0;
          for (int j = 0; j < std::min(size_t(64), item_emb_double_list->size()); ++j) {
            tmp_max_sim2 =
                tmp_max_sim2 + (*all_photo_embedding_accessor)[i * 64 + j] * (*item_emb_double_list)[j];
            // LOG(INFO) << "item_emb_double_list[" << j << "]" << (*item_emb_double_list)[j] << " "
            //<< tmp_max_sim2;
          }
          if (tmp_max_sim2 > max_sim2) {
            max_sim2 = tmp_max_sim2;
          }
        }
        sim2_accessor(result, max_sim2);
      } else {
        // all_photo_embedding_accessor is absl::nullopt
        sim2_accessor(result, 0);
      }
    });
    return true;
  }

  static bool EnrichSimHetuV4(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                         RecoResultConstIter end) {
    // LOG(INFO) << "EnrichSim2 (const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, "
    // "RecoResultConstIter end)";
    // common input
    auto all_photo_embedding_accessor = context.GetDoubleListCommonAttr("all_photo_embedding");
    if (!all_photo_embedding_accessor) {
      // LOG(INFO) << "all_photo_embedding_accessor is nullopt";
    } else {
      // LOG(INFO) << "all_photo_embedding_accessor size" << all_photo_embedding_accessor->size();
      for (int i = 0; i < all_photo_embedding_accessor->size(); ++i) {
        // LOG(INFO) << "all_photo_embedding_accessor[" << i << "]" << (*all_photo_embedding_accessor)[i];
      }
    }
    auto posDecayRate = context.GetDoubleCommonAttr("pos_decay_rate").value_or(0.5);
    // item input
    auto item_emb_accessor = context.GetDoubleListItemAttr("item_hetu_emb");
    // item output
    // auto sim2_accessor = context.SetDoubleItemAttr("sim2_test");
    auto sim2_accessor = context.SetDoubleItemAttr("sim_hetu");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_emb_double_list = item_emb_accessor(result);
      if (item_emb_double_list == absl::nullopt) {
        // item_emb_double_list is absl::nullopt
        // LOG(INFO) << "item_emb_double_list is nullopt";
        sim2_accessor(result, 0);
      } else if (all_photo_embedding_accessor && item_emb_double_list != absl::nullopt) {
        double diversitySim = 0.0;
        double diversityDenom = 0.0;
        double decayRate = 1.0;
        for (int i = 0; i < all_photo_embedding_accessor->size() / 64; ++i) {
          // auto item_emb_double_list = item_emb_accessor(result).value_or(std::vector<double>(32, 0.0));
          double tmp_max_sim2 = 0;
          for (int j = 0; j < std::min(size_t(64), item_emb_double_list->size()); ++j) {
            tmp_max_sim2 =
                tmp_max_sim2 + (*all_photo_embedding_accessor)[i * 64 + j] * (*item_emb_double_list)[j];
            // LOG(INFO) << "item_emb_double_list[" << j << "]" << (*item_emb_double_list)[j] << " "
            //<< tmp_max_sim2;
          }
          diversitySim = diversitySim + tmp_max_sim2 * decayRate;
          diversityDenom = diversityDenom + decayRate;
          decayRate = decayRate * posDecayRate;
        }
        if (diversityDenom > 0) {
          sim2_accessor(result, diversitySim / diversityDenom);
        } else {
          sim2_accessor(result, 0);
        }
      } else {
        // all_photo_embedding_accessor is absl::nullopt
        sim2_accessor(result, 0);
      }
    });
    return true;
  }

  static bool EnrichRelationOneHot(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    // LOG(INFO) << "EnrichRelationOneHot (const CommonRecoLightFunctionContext &context, RecoResultConstIter
    // begin, "
    //             "RecoResultConstIter end)";

    // item input
    auto user_item_relation_list_accessor = context.GetIntListItemAttr("user_item_relation_list");
    // item output
    std::vector<std::function<void(const CommonRecoResult &, const int64)>> relation_accessors(30);
    for (int i = 0; i < 30; ++i) {
      // std::string relation_attr_name = "relation_" + std::to_string(i) + "_test";
      std::string relation_attr_name = "relation_" + std::to_string(i);
      relation_accessors[i] = context.SetIntItemAttr(relation_attr_name.c_str());
    }
    // auto relation_accessor_1000 = context.SetIntItemAttr("relation_1000_test");
    auto relation_accessor_1000 = context.SetIntItemAttr("relation_1000");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto user_item_relation_list = user_item_relation_list_accessor(result);
      if (user_item_relation_list == absl::nullopt) {
        // user_item_relation_list is absl::nullopt
        // LOG(INFO) << "user_item_relation_list is nullopt";
        for (int i = 0; i < 30; ++i) {
          relation_accessors[i](result, 0);
        }
        relation_accessor_1000(result, 0);
      } else {
        folly::F14FastSet<int> relation_table(user_item_relation_list->begin(),
                                              user_item_relation_list->end());
        for (int i = 0; i < 30; ++i) {
          if (relation_table.find(i) != relation_table.end()) {
            relation_accessors[i](result, 1);
          } else {
            relation_accessors[i](result, 0);
          }
        }
        if (relation_table.find(1000) != relation_table.end()) {
          relation_accessor_1000(result, 1);
        } else {
          relation_accessor_1000(result, 0);
        }
      }
    });
    return true;
  }

  static int NumBucket(int x) {
    int n_len = std::to_string((int)std::floor(x)).length() - 1;
    if (x < 0) n_len--;

    int top = std::floor(x / std::pow(10, n_len));
    int bucket = top * std::pow(10, n_len);

    return bucket;
  }
  static bool EnrichPhotoConsumeBucket(const CommonRecoLightFunctionContext &context,
                                       RecoResultConstIter begin, RecoResultConstIter end) {
    // LOG(INFO) << "EnrichPhotoConsumeBucket (const CommonRecoLightFunctionContext &context,
    // RecoResultConstIter begin, "
    //              "RecoResultConstIter end)";
    //  item input
    auto photo_click_count_accessor = context.GetIntItemAttr("photo_click_count");
    auto photo_like_count_accessor = context.GetIntItemAttr("photo_like_count");
    // item output
    // auto photo_click_count_bkt_accessor = context.SetIntItemAttr("photo_click_count_bkt_test");
    // auto photo_like_count_bkt_accessor = context.SetIntItemAttr("photo_like_count_bkt_test");
    auto photo_click_count_bkt_accessor = context.SetIntItemAttr("photo_click_count_bkt");
    auto photo_like_count_bkt_accessor = context.SetIntItemAttr("photo_like_count_bkt");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto photo_click_count = photo_click_count_accessor(result);
      if (photo_click_count == absl::nullopt) {
        photo_click_count_bkt_accessor(result, 0);
      } else {
        photo_click_count_bkt_accessor(result, NumBucket(*photo_click_count));
      }
      auto photo_like_count = photo_like_count_accessor(result);
      if (photo_like_count == absl::nullopt) {
        photo_like_count_bkt_accessor(result, 0);
      } else {
        photo_like_count_bkt_accessor(result, NumBucket(*photo_like_count));
      }
    });
    return true;
  }
  static absl::string_view TypeAttrMapFix(absl::string_view type_attr, absl::string_view event_type,
                                          int userItemRelation) {
    if (event_type == "EVENT_FAN_GROUP_LIVESTREAM") {
      return event_type;
    } else if (type_attr == "people") {
      if (userItemRelation == 40) {
        return "EVENT_PERSON_COMMON_FRIEND_PUSH";
      } else if (userItemRelation == 41) {
        return "EVENT_PERSON_COMMON_FOLLOW_PUSH";
      } else if (userItemRelation == 42) {
        return "EVENT_PERSON_CONTACT_FRIEND_REVERSE_PUSH";
      } else if (userItemRelation == 43) {
        return "EVENT_PERSON_CONTACT_FRIEND_PUSH";
      } else if (userItemRelation == 44) {
        return "EVENT_PERSON_FRIEND_FOLLOW_PUSH";
      } else if (userItemRelation == 45) {
        return "EVENT_PERSON_FOLLOW_FOLLOW_PUSH";
      } else if (userItemRelation == 46) {
        return "EVENT_PERSON_COMMON_CONTACT_PUSH";
      } else if (userItemRelation == 47) {
        return "EVENT_PERSON_SOCIAL_OFFLINE_PUSH";
      } else if (userItemRelation == 48) {
        return "EVENT_PERSON_FANS_PUSH";
      }
      return "EVENT_PERSON_DEFALUT_PUSH";
    } else if (type_attr == "photo") {
      if (userItemRelation == 30) {
        return "EVENT_AUTHOR_COMMON_FRIEND_PUSH";
      } else if (userItemRelation == 32) {
        return "EVENT_AUTHOR_CONTACT_FRIEND_REVERSE_PUSH";
      } else if (userItemRelation == 33) {
        return "EVENT_AUTHOR_CONTACT_FRIEND_PUSH";
      } else if (userItemRelation == 34) {
        return "EVENT_AUTHOR_FRIEND_FOLLOW_PUSH";
      } else if (userItemRelation == 35) {
        return "EVENT_AUTHOR_FOLLOW_FOLLOW_PUSH";
      } else if (userItemRelation == 37) {
        return "EVENT_AUTHOR_SOCIAL_OFFLINE_PUSH";
      }
    }
    return type_attr;
  }

  static bool EnrichTypeAttr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                             RecoResultConstIter end) {
    // LOG(INFO) << "EnrichTypeAttr (const CommonRecoLightFunctionContext &context,
    // RecoResultConstIter begin, "
    //              "RecoResultConstIter end)";
    //  item input
    auto user_item_relation_accessor = context.GetIntItemAttr("userItemRelation");
    auto type_attr_accessor = context.GetStringItemAttr("type_attr");
    auto event_type_accessor = context.GetStringItemAttr("event_type");
    // item output
    // auto export_type_attr_accessor = context.SetStringItemAttr("type_attr_test");
    auto export_type_attr_accessor = context.SetStringItemAttr("type_attr");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto user_item_relation = user_item_relation_accessor(result).value_or(-1);
      auto type_attr = type_attr_accessor(result);
      auto event_type = event_type_accessor(result);
      if (type_attr != absl::nullopt && event_type != absl::nullopt) {
        export_type_attr_accessor(result,
                                  std::string(TypeAttrMapFix(*type_attr, *event_type, user_item_relation)));
      }
    });
    return true;
  }

  static std::tuple<int64_t, int64_t, int64_t> GetDurationAndAgeBucket(int64_t photo_duration_ms,
                                                                       int64_t photo_upload_time,
                                                                       int64_t after_retrival_timestamp) {
    int64_t photo_duration_ms_bkt =
        std::floor(std::min(static_cast<double>(photo_duration_ms) / 10000.0, 30.0));
    int64_t photo_age = after_retrival_timestamp - photo_upload_time;
    int64_t photo_age_bkt = photo_age;
    if (photo_age < 86400000) {
      photo_age_bkt = std::floor(static_cast<double>(photo_age) / 3600000.0);
    } else if (photo_age > 86400000) {
      photo_age_bkt = 24 + std::floor(static_cast<double>(photo_age) / 86400000);
    }
    int64_t photo_age_valid_flag = 0;
    if (photo_upload_time > 0 && photo_age > 0) {
      photo_age_valid_flag = 1;
    }
    return std::make_tuple(photo_duration_ms_bkt, photo_age_bkt, photo_age_valid_flag);
  }
  static bool EnrichDurationAndAgeBucket(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    // LOG(INFO) << "EnrichTypeAttr (const CommonRecoLightFunctionContext &context,
    // RecoResultConstIter begin, "
    //              "RecoResultConstIter end)";
    //  item input
    auto photo_duration_ms_accessor = context.GetIntItemAttr("photo_duration_ms");
    auto photo_upload_time_accessor = context.GetIntItemAttr("photo_upload_time");
    auto after_retrival_timestamp = context.GetIntCommonAttr("after_retrival_timestamp").value_or(0);
    // item output
    // auto export_photo_duration_ms_bkt_accessor = context.SetIntItemAttr("photo_duration_ms_bkt_test");
    // auto export_photo_age_bkt_accessor = context.SetIntItemAttr("photo_age_bkt_test");
    auto export_photo_duration_ms_bkt_accessor = context.SetIntItemAttr("photo_duration_ms_bkt");
    auto export_photo_age_bkt_accessor = context.SetIntItemAttr("photo_age_bkt");
    auto export_photo_age_valid_flag_accessor = context.SetIntItemAttr("photo_age_valid_flag");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto photo_duration_ms = photo_duration_ms_accessor(result).value_or(0);
      auto photo_upload_time = photo_upload_time_accessor(result).value_or(0);

      std::tuple<int64_t, int64_t, int64_t> duration2age =
          GetDurationAndAgeBucket(photo_duration_ms, photo_upload_time, after_retrival_timestamp);
      export_photo_duration_ms_bkt_accessor(result, std::get<0>(duration2age));
      export_photo_age_bkt_accessor(result, std::get<1>(duration2age));
      export_photo_age_valid_flag_accessor(result, std::get<2>(duration2age));
    });
    return true;
  }

  static int SetLimitMax(int v) {
    int res = v;
    if (res < 0) {
      res = 0;
    }
    return std::min(res, 16777215);
  }
  static bool EnrichLimitValue(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    // LOG(INFO) << "EnrichLimitValue CommonRecoLightFunctionContext &context,
    // RecoResultConstIter begin, "
    //              "RecoResultConstIter end)";
    //  item input
    auto photo_click_count_accessor = context.GetIntItemAttr("photo_click_count");
    auto author_fans_count_accessor = context.GetIntItemAttr("author_fans_count");
    // item output
    // auto export_photo_click_count_accessor = context.SetIntItemAttr("photo_click_count_test");
    // auto export_author_fans_count_accessor = context.SetIntItemAttr("author_fans_count_test");
    auto export_photo_click_count_accessor = context.SetIntItemAttr("photo_click_count");
    auto export_author_fans_count_accessor = context.SetIntItemAttr("author_fans_count");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto photo_click_count = photo_click_count_accessor(result).value_or(0);
      export_photo_click_count_accessor(result, SetLimitMax(photo_click_count));
      auto author_fans_count = author_fans_count_accessor(result).value_or(0);
      export_author_fans_count_accessor(result, SetLimitMax(author_fans_count));
    });
    return true;
  }

  static bool EnrichUserAuthorInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    // import common attr
    auto follow_friend_list = context.GetIntListCommonAttr("follow_friend_list");
    auto follow_author_list = context.GetIntListCommonAttr("follow_author_list");
    auto browsed_object_list = context.GetIntListCommonAttr("browsed_object_list");
    auto follow_favorite_list = context.GetIntListCommonAttr("follow_favorite_list");
    auto fan_group_author_list = context.GetIntListCommonAttr("fan_group_author_list");
    auto reason_100_no_reco_browset_filter =
        context.GetIntCommonAttr("reason_100_no_reco_browset_filter").value_or(0);  // ab_param default false

    // import item attr, type:[function<optional<int64>(const CommonRecoResult &)>]
    auto item_id_accessor = context.GetIntItemAttr("item_id");
    auto type_attr_accessor = context.GetStringItemAttr("type_attr");
    auto author_id_accessor = context.GetIntItemAttr("author_id_attr");
    auto photo_id_attr_accessor = context.GetIntItemAttr("photo_id_attr");
    auto recall_reason_accessor = context.GetIntItemAttr("recall_reason");
    auto user_view_type_accessor = context.GetStringItemAttr("user_view_type_attr");

    auto export_type_attr = context.SetStringItemAttr("type_attr");
    auto export_event_type = context.SetStringItemAttr("event_type");
    auto export_photo_id_attr = context.SetIntItemAttr("photo_id_attr");
    auto export_is_follow_attr = context.SetStringItemAttr("is_follow_attr");
    auto export_is_friend_attr = context.SetStringItemAttr("is_friend_attr");
    auto export_user_view_type = context.SetStringItemAttr("user_view_type");
    auto export_is_browsed_object = context.SetIntItemAttr("is_browsed_object");

    // prepare
    folly::F14FastSet<int> follow_set{};
    folly::F14FastSet<int> friend_set{};
    folly::F14FastSet<int> browsed_set{};
    folly::F14FastSet<int> favorite_set{};
    folly::F14FastSet<int> fan_group_set{};

    if (follow_author_list) follow_set.insert(follow_author_list->begin(), follow_author_list->end());
    if (follow_friend_list) friend_set.insert(follow_friend_list->begin(), follow_friend_list->end());
    if (browsed_object_list) browsed_set.insert(browsed_object_list->begin(), browsed_object_list->end());
    if (follow_favorite_list) favorite_set.insert(follow_favorite_list->begin(), follow_favorite_list->end());
    if (fan_group_author_list)
      fan_group_set.insert(fan_group_author_list->begin(), fan_group_author_list->end());

    // calculate
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      // item attrs
      auto item_id = item_id_accessor(result).value_or(0);
      auto author_id = author_id_accessor(result).value_or(0);
      auto recall_reason = recall_reason_accessor(result).value_or(0);
      auto photo_id_attr = photo_id_attr_accessor(result).value_or(0);
      auto type_attr = std::string(type_attr_accessor(result).value_or(""));
      auto user_view_type = std::string(user_view_type_accessor(result).value_or(""));

      // local variable
      int is_browsed_object{};
      std::string is_follow_attr{};
      std::string is_friend_attr{};
      std::string is_favorite_attr{};
      std::string is_fan_group_attr{};

      is_friend_attr = (friend_set.count(author_id)) ? "1" : "0";
      is_favorite_attr = (favorite_set.count(author_id)) ? "1" : "0";
      is_fan_group_attr = (fan_group_set.count(author_id)) ? "1" : "0";
      photo_id_attr = (photo_id_attr == 0) ? item_id : photo_id_attr;

      if (follow_set.count(author_id)) {
        is_follow_attr = "1";
        if (type_attr == "photo" || type_attr == "livestream") {
          user_view_type = "followed_" + type_attr;
        }
      } else {
        is_follow_attr = "0";
        if (type_attr == "photo" || type_attr == "livestream") {
          user_view_type = "unfollowed_" + type_attr;
        }
      }

      if ((recall_reason != 1000 || reason_100_no_reco_browset_filter == 0) &&
          browsed_set.count(photo_id_attr)) {
        is_browsed_object = 1;
      } else {
        is_browsed_object = 0;
      }

      if (user_view_type == "") {
        user_view_type = type_attr;
      }

      auto eventTypeMapperFunc = [&]() -> std::string {
        if (type_attr == "") {
          return "other";
        }
        // photo
        if (type_attr != "photo" && type_attr != "livestream" && type_attr != "people") return type_attr;
        if (type_attr == "photo" && is_favorite_attr == "1") return "EVENT_FAVORITE_AUTHOR_PHOTO_UPLOAD";
        if (type_attr == "photo" && is_friend_attr == "1") return "EVENT_FRIEND_UPLOAD_PHOTO";
        if (type_attr == "photo") {
          // recall reason = 1001
          std::string photo_type_map_1001_reason = "EVENT_FAVORITE_AUTHOR_PHOTO_UPLOAD";
          static std::unordered_map<int, std::string> static_photo_type_map = {
              {1004, "EVENT_SEARCH_AUTHOR_PHOTO_PUSH"},
              {1005, "EVENT_RELATION_AUTHOR_PHOTO_PUSH"},
              {1012, "EVENT_BROWSED_AUTHOR_PHOTO_PUSH"},
              {1035, "EVENT_ENTER_PROFILE_AUTHOR_PHOTO_PUSH"},
              {1038, "EVENT_AUTHOR_RELATION_DEFAULT_PUSH"},
              {1059, "EVENT_RECO_TO_FRIENDS_PHOTO_PUSH"},
              {1069, "EVENT_BROWSED_AUTHOR_PHOTO_PUSH"},
              {1071, "EVENT_AUTHOR_LIKE_PUSH"},
              {1072, "EVENT_AUTHOR_SHARE_PUSH"},
              {1073, "EVENT_AUTHOR_COLLECT_PUSH"},
              {1074, "EVENT_AUTHOR_COMMENT_PUSH"},
              {1075, "EVENT_PEOPLE_NEARBY_PUSH"},
              {1076, "EVENT_AUTHOR_FANS_PUSH"},
              {1077, "EVENT_USER2AUTHOR_PHOTO_PUSH"},
              {1078, "EVENT_AUTHOR_COMMON_FRIEND_PUSH"},
              {1079, "EVENT_AUTHOR_CONTACT_FRIEND_REVERSE_PUSH"},
              {1080, "EVENT_AUTHOR_CONTACT_FRIEND_PUSH"},
              {1081, "EVENT_AUTHOR_FRIEND_FOLLOW_PUSH"},
              {1082, "EVENT_AUTHOR_FOLLOW_FOLLOW_PUSH"},
              {1083, "EVENT_AUTHOR_SOCIAL_OFFLINE_PUSH"},
              {1092, "EVENT_USER_CLICK_SIM_AUTHOR_PHOTO_PUSH"},
              {1093, "EVENT_AUTHOR_RELATION_DEFAULT_PUSH"},
              {1117, "EVENT_PAN_PRIVATE_DOMAIN_AUTHOR_PUSH"}
          };
          photo_type_map_1001_reason =
              (is_favorite_attr == "1")
                  ? "EVENT_FAVORITE_AUTHOR_PHOTO_UPLOAD"
                  : (is_friend_attr == "1" ? "EVENT_FRIEND_UPLOAD_PHOTO"
                                           : (is_follow_attr == "1" ? "EVENT_FOLLOW_UPLOAD_PHOTO" : "other"));
          if (recall_reason == 1001 || recall_reason == 1068 || recall_reason == 1094 ||
              recall_reason == 1095) {
            return photo_type_map_1001_reason;
          }
          if (static_photo_type_map.find(recall_reason) != static_photo_type_map.end()) {
            return static_photo_type_map[recall_reason];
          }
          return "other";
        }
        // 推人
        if (type_attr == "people") {
          static std::unordered_map<int, std::string> static_people_type_map = {
              {1041, "EVENT_PERSON_DEFALUT_PUSH"},
              {1084, "EVENT_PERSON_COMMON_FRIEND_PUSH"},
              {1085, "EVENT_PERSON_CONTACT_FRIEND_REVERSE_PUSH"},
              {1086, "EVENT_PERSON_CONTACT_FRIEND_PUSH"},
              {1087, "EVENT_PERSON_FRIEND_FOLLOW_PUSH"},
              {1088, "EVENT_PERSON_FOLLOW_FOLLOW_PUSH"},
              {1089, "EVENT_PERSON_SOCIAL_OFFLINE_PUSH"},
              {1090, "EVENT_PERSON_FANS_PUSH"},
              {1091, "EVENT_PERSON_DEFALUT_PUSH"}};
          if (static_people_type_map.find(recall_reason) != static_people_type_map.end()) {
            return static_people_type_map[recall_reason];
          }
        }
        // 直播
        if (type_attr == "livestream") {
          if (is_fan_group_attr == "1") return "EVENT_FAN_GROUP_LIVESTREAM";
          if (recall_reason == 1001 || recall_reason == 1068 || recall_reason == 1094 ||
              recall_reason == 1095)
            return "EVENT_OLD_LIVESTREAM_START_NEW_INFRA";
          if (recall_reason == 1103) return "EVENT_UNFOLLOW_LIVE_PUSH";
          if (recall_reason == 1106) return "EVENT_GLOBAL_UNFOLLOW_LIVE_PUSH";
          if (recall_reason == 1107) return "EVENT_PERSONAL_UNFOLLOW_LIVE_PUSH";
          if (recall_reason == 1115) return "EVENT_ONLINE_RETAILERS_LIVE_PUSH";
        }

        return "other";
      };

      auto event_type = eventTypeMapperFunc();
      auto type = (event_type == "EVENT_FAN_GROUP_LIVESTREAM") ? event_type : type_attr;

      export_type_attr(result, type);
      export_event_type(result, event_type);
      export_photo_id_attr(result, photo_id_attr);
      export_is_follow_attr(result, is_follow_attr);
      export_is_friend_attr(result, is_friend_attr);
      export_user_view_type(result, user_view_type);
      export_is_browsed_object(result, is_browsed_object);
    });
    return true;
  }

  static bool EnrichItemCascadeSample(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
    // import common attr
    auto item_cascade_sample_num = context.GetIntCommonAttr("item_cascade_sample_num").value_or(0);
    auto item_cascade_sample_stage = context.GetStringCommonAttr("item_cascade_sample_stage").value_or("");
    if (item_cascade_sample_num <= 0) {
      LOG(INFO) << "item cascade sample num <= 0, item cascade sample num: " << item_cascade_sample_num;
      return false;
    }
    if (item_cascade_sample_stage.size() <= 0) {
      LOG(INFO) << "item cascade sample stage is null or length <= 0, item cascade sample stage: "
                << item_cascade_sample_stage;
      return false;
    }

    if (item_cascade_sample_stage == "recall") {
      return EnrichItemCascadeSampleRecall(context, begin, end, item_cascade_sample_num);
    } else if (item_cascade_sample_stage == "mc") {
      return EnrichItemCascadeSampleMC(context, begin, end, item_cascade_sample_num);
    } else if (item_cascade_sample_stage == "fr") {
      return EnrichItemCascadeSampleFR(context, begin, end, item_cascade_sample_num);
    }
    return true;
  }

  static bool EnrichItemCascadeSampleRecall(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end, int item_cascade_sample_num) {
    int totalCount = std::distance(begin, end);
    if (totalCount < item_cascade_sample_num) {
      return false;
    }

    // enrich
    auto export_item_cascade_sample = context.SetStringItemAttr("item_cascade_sample");
    // 全局负采样
    // 随机采样
    std::vector<typename std::iterator_traits<RecoResultConstIter>::value_type> sampleResult =
      RandomSample(begin, end, item_cascade_sample_num);
    // 标记采样结果
    std::for_each(sampleResult.begin(), sampleResult.end(), [&](const CommonRecoResult &result) {
      export_item_cascade_sample(result, "recall_neg");
    });
    return true;
  }

  static bool EnrichItemCascadeSampleMC(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end, int item_cascade_sample_num) {
    int totalCount = std::distance(begin, end);
    if (totalCount < item_cascade_sample_num) {
      return false;
    }

    // enrich
    auto export_item_cascade_sample = context.SetStringItemAttr("item_cascade_sample");
    // 粗排未进入精排随机采样（召回平均数为 2000，粗排进精排平均数为 400）。
    // 综合样本数量和区分度考虑，所以从 Top -1500 和 召回总数 3/4 取小，进行截断
    int begin_idx = std::min(1500, static_cast<int>(totalCount * 0.75));
    RecoResultConstIter newBegin = end - begin_idx;
    if (std::distance(newBegin, end) >= item_cascade_sample_num) {
      // 随机采样
      std::vector<typename std::iterator_traits<RecoResultConstIter>::value_type> sampleResult =
        RandomSample(newBegin, end, item_cascade_sample_num);
      // 标记采样结果
      std::for_each(sampleResult.begin(), sampleResult.end(), [&](const CommonRecoResult &result) {
        export_item_cascade_sample(result, "mc_neg");
      });
    }
    return true;
  }

  static bool EnrichItemCascadeSampleFR(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end, int item_cascade_sample_num) {
    int totalCount = std::distance(begin, end);
    if (totalCount < item_cascade_sample_num) {
      return false;
    }

    // enrich
    auto export_item_cascade_sample = context.SetStringItemAttr("item_cascade_sample");
    // 精排 Top 作为正样本
    std::for_each(begin, begin + item_cascade_sample_num, [&](const CommonRecoResult &result) {
      export_item_cascade_sample(result, "fr_pos");
    });

    // 精排中间样本采样
    int middle_start = std::min(80, static_cast<int>(totalCount * 0.2));
    int middle_end = std::min(120, static_cast<int>(totalCount * 0.3));
    if (middle_end - middle_start >= item_cascade_sample_num) {
      std::vector<typename std::iterator_traits<RecoResultConstIter>::value_type> sampleResult =
        RandomSample(begin + middle_start, begin + middle_end, item_cascade_sample_num);
      std::for_each(sampleResult.begin(), sampleResult.end(), [&](const CommonRecoResult &result) {
        export_item_cascade_sample(result, "fr_mid");
      });
    }

    // 精排尾部采样
    if (std::distance(begin + middle_end, end) >= item_cascade_sample_num) {
      std::vector<typename std::iterator_traits<RecoResultConstIter>::value_type> sampleResult =
        RandomSample(begin + middle_end, end, item_cascade_sample_num);
      std::for_each(sampleResult.begin(), sampleResult.end(), [&](const CommonRecoResult &result) {
        export_item_cascade_sample(result, "fr_neg");
      });
    }
    return true;
  }

  static std::vector<typename std::iterator_traits<RecoResultConstIter>::value_type> RandomSample(
    RecoResultConstIter begin, RecoResultConstIter end, int64_t item_cascade_sample_num) {
    std::vector<typename std::iterator_traits<RecoResultConstIter>::value_type> sampleResult;

    int totalCount = std::distance(begin, end);
    if (totalCount < item_cascade_sample_num) {
      LOG(INFO) << "random sample total count: "<< totalCount
                << " < item cascade sample num: " << item_cascade_sample_num;
      return sampleResult;
    }

    std::vector<typename std::iterator_traits<RecoResultConstIter>::value_type> targetElements(begin, end);
    // shuffle 实现随机
    std::shuffle(targetElements.begin(), targetElements.end(), std::mt19937 {std::random_device {}()});
    // 存储采样结果
    sampleResult.insert(
      sampleResult.end(), targetElements.begin(), targetElements.begin() + item_cascade_sample_num);
    return sampleResult;
  }

  static bool EnrichRelationTypeAttr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    // import common attr
    auto fixTypeAttrLiveEvents = context.GetStringListCommonAttr("fixTypeAttrLiveEvents");
    auto fixTypeAttrPhotoEvents = context.GetStringListCommonAttr("fixTypeAttrPhotoEvents");
    auto fixTypeAttrInterEvents = context.GetStringListCommonAttr("fixTypeAttrInterEvents");
    auto fixTypeAttrPeopleEvents = context.GetStringListCommonAttr("fixTypeAttrPeopleEvents");
    auto fixTypeAttrOtherrEvents = context.GetStringListCommonAttr("fixTypeAttrOtherrEvents");
    auto fixTypeAttrIncentiveEvents = context.GetStringListCommonAttr("fixTypeAttrIncentiveEvents");
    auto fixTypeAttrMerchantEvents = context.GetStringListCommonAttr("fixTypeAttrMerchantEvents");
    auto relation_author_origin_list = context.GetIntListCommonAttr("relation_author_origin_list");

    // common attrs variable
    folly::F14FastSet<std::string> liveEvents{};
    folly::F14FastSet<std::string> photoEvents{};
    folly::F14FastSet<std::string> interEvents{};
    folly::F14FastSet<std::string> otherEvents{};
    folly::F14FastSet<std::string> peopleEvents{};
    folly::F14FastSet<std::string> incentiveEvents{};
    folly::F14FastSet<std::string> merchantEvents{};
    // folly::F14FastSet<int> origin_relation_list{};
    std::vector<int64> origin_relation_list{};
    if (fixTypeAttrLiveEvents) {
      for (int i = 0; i < fixTypeAttrLiveEvents->size(); i++) {
        liveEvents.insert(std::string(fixTypeAttrLiveEvents->at(i)));
      }
    }
    if (fixTypeAttrPhotoEvents) {
      for (int i = 0; i < fixTypeAttrPhotoEvents->size(); i++) {
        photoEvents.insert(std::string(fixTypeAttrPhotoEvents->at(i)));
      }
    }
    if (fixTypeAttrInterEvents) {
      for (int i = 0; i < fixTypeAttrInterEvents->size(); i++) {
        interEvents.insert(std::string(fixTypeAttrInterEvents->at(i)));
      }
    }
    if (fixTypeAttrPeopleEvents) {
      for (int i = 0; i < fixTypeAttrPeopleEvents->size(); i++) {
        peopleEvents.insert(std::string(fixTypeAttrPeopleEvents->at(i)));
      }
    }
    if (fixTypeAttrOtherrEvents) {
      for (int i = 0; i < fixTypeAttrOtherrEvents->size(); i++) {
        otherEvents.insert(std::string(fixTypeAttrOtherrEvents->at(i)));
      }
    }
    if (fixTypeAttrIncentiveEvents) {
      for (int i = 0; i < fixTypeAttrIncentiveEvents->size(); i++) {
        incentiveEvents.insert(std::string(fixTypeAttrIncentiveEvents->at(i)));
      }
    }
    if (fixTypeAttrMerchantEvents) {
      for (int i = 0; i < fixTypeAttrMerchantEvents->size(); i++) {
        merchantEvents.insert(std::string(fixTypeAttrMerchantEvents->at(i)));
      }
    }
    if (relation_author_origin_list) {
      auto &span_data = relation_author_origin_list.value();
      origin_relation_list.reserve(span_data.size());
      for (auto &value : span_data) {
        origin_relation_list.push_back(static_cast<int64>(value));
      }
    }

    // import item attr accessor, type:[function<optional<int64>(const CommonRecoResult &)>]
    auto type_attr_accessor = context.GetStringItemAttr("type_attr");
    auto event_type_accessor = context.GetStringItemAttr("event_type");
    auto author_id_attr_accessor = context.GetIntItemAttr("author_id_attr");

    // type:[function<void(const CommonRecoResult &, const std::string &)>]
    auto export_relation_type = context.SetIntItemAttr("relation_type");
    auto export_type_attr_parent = context.SetStringItemAttr("type_attr_parent");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      // local variable
      int ind{0};
      int relation_type{0};
      std::string type_attr_parent{""};
      // item attrs variable
      auto type_attr = type_attr_accessor(result).value_or("");
      auto event_type = std::string(event_type_accessor(result).value_or(""));
      auto author_id = author_id_attr_accessor(result).value_or(0);

      // index of Realtion
      if (origin_relation_list.empty() || author_id == 0 || !relation_author_origin_list) {
        ind = -1;
      } else {
        auto iter = std::find(origin_relation_list.begin(), origin_relation_list.end(), author_id);
        ind = (iter == origin_relation_list.end()) ? -1 : std::distance(origin_relation_list.begin(), iter);
      }
      if (ind != -1) {
        ind++;
        if (ind <= 10) {
          relation_type = 1;
        } else if (ind <= 30) {
          relation_type = 2;
        } else if (ind <= 80) {
          relation_type = 3;
        } else {
          relation_type = 4;
        }
      }

      // fill type_attr_parent
      if (photoEvents.count(event_type)) {
        type_attr_parent = "photo";
      } else if (incentiveEvents.count(event_type)) {
        type_attr_parent = "incentive";
      } else if (interEvents.count(event_type)) {
        type_attr_parent = "inter";
      } else if (liveEvents.count(event_type)) {
        type_attr_parent = "live";
      } else if (peopleEvents.count(event_type)) {
        type_attr_parent = "people";
      } else if (otherEvents.count(event_type)) {
        type_attr_parent = "push";
      } else if (merchantEvents.count(event_type)) {
        type_attr_parent = "merchant";
      } else {
        type_attr_parent = std::string(type_attr);
      }

      export_relation_type(result, relation_type);
      export_type_attr_parent(result, type_attr_parent);
    });
    return true;
  }

  static bool EnrichProviderInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    // import common attr: optional<vector<string_view>>
    auto dids = context.GetStringListCommonAttr("push_dids").value_or(std::vector<absl::string_view>{});
    auto terminals =
        context.GetStringListCommonAttr("push_terminals").value_or(std::vector<absl::string_view>{});

    // export item attr
    auto export_infra_did = context.SetStringItemAttr("infra_did");
    auto export_infra_terminal = context.SetStringItemAttr("infra_terminal");
    auto export_default_terminal = context.SetStringItemAttr("default_terminal");

    // local variable
    int default_priority = 7;
    std::string infra_did = "default_did";
    std::string infra_terminal = "OTHER";
    std::string default_terminal = "AABB";
    static std::unordered_map<std::string, int> terminal_priority = {
        {"OPPO", 1}, {"VIVO", 2}, {"XIAOMI", 3}, {"HUAWEI", 4}, {"IPHONE", 5}, {"OTHER", 6}};
    // not used in lua
    // static std::unordered_map<std::string, int> terminal_provider = {
    // {"OPPO", 8}, {"VIVO", 9}, {"XIAOMI", 1}, {"HUAWEI", 2}, {"IPHONE", 4}, {"OTHER", 1}};

    auto startsWith = [&](std::string &terminal, const std::string &name) {
      int m = terminal.size();
      int n = name.size();
      if (m < n) {
        return false;
      }
      return !terminal.compare(0, n, name);
    };

    auto processTerminal = [&](std::string terminal) -> std::string {
      std::transform(terminal.begin(), terminal.end(), terminal.begin(), ::tolower);
      if (startsWith(terminal, "oppo")) return "OPPO";
      if (startsWith(terminal, "vivo") || startsWith(terminal, "iqoo")) return "VIVO";
      if (startsWith(terminal, "xiaomi") || startsWith(terminal, "redmi")) return "XIAOMI";
      if (startsWith(terminal, "huawei") || startsWith(terminal, "honor")) return "HUAWEI";
      if (startsWith(terminal, "iphone") || startsWith(terminal, "ipad")) return "IPHONE";
      return "OTHER";
    };

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      if (!dids.empty() && !terminals.empty()) {
        int n = terminals.size();
        for (int i = 0; i < n; i++) {
          std::string terminal = processTerminal(std::string(terminals[i]));
          int priority = terminal_priority[terminal];
          if (priority < default_priority) {
            infra_terminal = terminal;
            infra_did = std::string(dids[i]);
            default_priority = priority;
          }
        }
      }
      // export item attrs
      export_infra_did(result, infra_did);
      export_infra_terminal(result, infra_terminal);
      export_default_terminal(result, default_terminal);
    });
    return true;
  }

  static bool EnrichTagGsu(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                           RecoResultConstIter end) {
    // item attr : item 的 hetu tag
    auto hetu_tag_accessor = context.GetIntItemAttr("hetu_tag_leaf_node_v1");

    // common attr 包含: leaf 请求时间戳, colossus 序列
    auto timestampMs = context.GetIntCommonAttr("timestampMs").value_or(0);
    double sample_stamp = (double)timestampMs / 1000;
    auto colossus_pid = context.GetIntListCommonAttr("colossus_pid");
    auto colossus_aid = context.GetIntListCommonAttr("colossus_aid");
    auto colossus_duration = context.GetIntListCommonAttr("colossus_duration");
    auto colossus_play = context.GetIntListCommonAttr("colossus_play");
    auto colossus_tag = context.GetIntListCommonAttr("colossus_tag");
    auto colossus_time = context.GetIntListCommonAttr("colossus_time");
    auto colossus_label = context.GetIntListCommonAttr("colossus_label");

    // 判断 common 特征是否为空或长度不一致
    if (!colossus_pid || !colossus_aid || !colossus_duration || !colossus_play || !colossus_tag ||
        !colossus_time || !colossus_label) {
      return false;
    }

    if (colossus_pid->empty() || colossus_aid->empty() || colossus_duration->empty() ||
        colossus_play->empty() || colossus_tag->empty() || colossus_time->empty() ||
        colossus_label->empty()) {
      return false;
    }

    if (colossus_pid->size() != colossus_aid->size() || colossus_pid->size() != colossus_duration->size() ||
        colossus_pid->size() != colossus_play->size() || colossus_pid->size() != colossus_tag->size() ||
        colossus_pid->size() != colossus_time->size() || colossus_pid->size() != colossus_label->size()) {
      return false;
    }

    // type:[function<void(const CommonRecoResult &, const std::string &)>]
    auto export_tag_gsu_pids = context.SetIntListItemAttr("tag_gsu_pids");
    auto export_tag_gsu_aids = context.SetIntListItemAttr("tag_gsu_aids");
    auto export_tag_gsu_tags = context.SetIntListItemAttr("tag_gsu_tags");
    auto export_tag_gsu_play = context.SetIntListItemAttr("tag_gsu_play");
    auto export_tag_gsu_time = context.SetIntListItemAttr("tag_gsu_time");
    auto export_tag_gsu_label = context.SetIntListItemAttr("tag_gsu_label");
    auto export_tag_gsu_len = context.SetIntItemAttr("tag_gsu_len");

    // 遍历 item
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      // 获取 target item 的 hetu tag
      auto hetu_tag = hetu_tag_accessor(result).value_or(-1);
      // 定义存储结果的 vector
      std::vector<int64> pid_list, aid_list, tag_list, play_list, time_list, label_list{};
      // 定义 tag_gsu_len ，记录结果集长度
      int64 tag_gsu_len = 0;
      // 反向遍历 colossus 序列 ( 按时间由近及远进行判断 )
      for (int i = colossus_tag->size() - 1; i >= 0; i--) {
        // 如果 item 的 tag 和当前 colossus 视频的 tag 匹配，将该视频的特征加入结果集
        if (colossus_tag->at(i) == hetu_tag && colossus_time->at(i) < sample_stamp) {
          pid_list.push_back(colossus_pid->at(i));
          aid_list.push_back(colossus_aid->at(i));
          tag_list.push_back(colossus_tag->at(i));
          // 对 play 和 duration 特征进行二次处理
          double double_value = static_cast<double>(colossus_play->at(i)) / 3;
          int64 pre_value = static_cast<int64>(static_cast<int64>(std::floor(double_value)) << 24);
          int64 play = pre_value | colossus_duration->at(i);
          play_list.push_back(play);
          // 对 time 特征进行二次处理，得到该视频据今多少天
          int64 time = std::floor(static_cast<double>(sample_stamp - colossus_time->at(i)) / 86400);
          time_list.push_back(time);
          label_list.push_back(colossus_label->at(i));
          // 如果结果集长度大于等于 15 ，直接结束遍历
          tag_gsu_len = tag_gsu_len + 1;
          if (tag_gsu_len >= 15) {
            break;
          }
        }
      }
      if (tag_gsu_len > 0) {
        export_tag_gsu_pids(result, pid_list);
        export_tag_gsu_aids(result, aid_list);
        export_tag_gsu_tags(result, tag_list);
        export_tag_gsu_play(result, play_list);
        export_tag_gsu_time(result, time_list);
        export_tag_gsu_label(result, label_list);
        export_tag_gsu_len(result, tag_gsu_len);
      }
    });
    return true;
  }

  static bool EnrichAuthorGsu(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                              RecoResultConstIter end) {
    // import common attr
    auto timestampMs = context.GetIntCommonAttr("timestampMs").value_or(0);
    double sample_stamp = (double)timestampMs / 1000;

    // import item attr, type:[function<optional<int64>(const CommonRecoResult &)>]
    auto author_id_attr_accessor = context.GetIntItemAttr("author_id_attr");
    auto author_gsu_duration_accessor = context.GetIntListItemAttr("author_gsu_duration");
    auto author_gsu_play_time_accessor = context.GetIntListItemAttr("author_gsu_play_time");
    auto author_gsu_timestamp_accessor = context.GetIntListItemAttr("author_gsu_timestamp");

    // type:[function<void(const CommonRecoResult &, const std::string &)>]
    auto export_author_gsu_len = context.SetIntItemAttr("author_gsu_len");
    auto export_author_gsu_play = context.SetIntListItemAttr("author_gsu_play");
    auto export_author_gsu_time = context.SetIntListItemAttr("author_gsu_time");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      // item attrs
      auto author_id_attr = author_id_attr_accessor(result).value_or(0);
      auto author_gsu_duration = author_gsu_duration_accessor(result).value_or(absl::Span<const int64>{});
      auto author_gsu_timestamp = author_gsu_timestamp_accessor(result).value_or(absl::Span<const int64>{});
      auto author_gsu_play_time = author_gsu_play_time_accessor(result).value_or(absl::Span<const int64>{});

      std::vector<int64> play_list, time_list{};
      if (author_gsu_duration.empty() || author_id_attr <= 0) {
        // export_author_gsu_play(result, play_list);
        // export_author_gsu_time(result, time_list);
        export_author_gsu_len(result, 0);
        return;
      }
      // local variable
      int author_gsu_len = author_gsu_duration.size();
      play_list.reserve(author_gsu_len);
      time_list.reserve(author_gsu_len);
      for (int i = 0; i < author_gsu_len; i++) {
        double double_value = static_cast<double>(author_gsu_play_time[i]) / 3;
        int64 pre_value = static_cast<int64>(static_cast<int64>(std::floor(double_value)) << 24);
        int64 play = pre_value | author_gsu_duration[i];

        int64 time = std::floor(static_cast<double>(sample_stamp - author_gsu_timestamp[i]) / 86400);
        play_list.push_back(play);
        time_list.push_back(time);
      }
      export_author_gsu_play(result, play_list);
      export_author_gsu_time(result, time_list);
      export_author_gsu_len(result, author_gsu_len);
    });
    return true;
  }

  static std::vector<std::string> Split(const std::string &input_str, char sep = ' ') {
    std::vector<std::string> result;
    std::istringstream iss(input_str.data());
    std::string token;
    while (std::getline(iss, token, sep)) {
      result.push_back(std::move(token));
    }
    return result;
  }

  static bool EnrichMotiItemAttr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    // import common attrs
    auto watched_author_detail_list = context.GetStringListCommonAttr("watched_author_detail_list");
    // .value_or(std::vector<absl::string_view>{});
    auto origin_browsed_author_list =
        context.GetIntListCommonAttr("origin_browsed_author_list").value_or(absl::Span<const int64>{});

    // import item attrs
    auto author_id_attr_accessor = context.GetIntItemAttr("author_id_attr");
    auto click_motivation_type_accessor = context.GetIntItemAttr("click_motivation_type");

    // type:[function<void(const CommonRecoResult &, const std::string &)>]
    auto export_is_like_author = context.SetIntItemAttr("is_like_author");
    auto export_motivation_type = context.SetStringItemAttr("motivation_type");
    auto export_browsed_author_type = context.SetIntItemAttr("browsed_author_type");
    auto export_is_share_author = context.SetIntItemAttr("is_share_author");
    auto export_is_collect_author = context.SetIntItemAttr("is_collect_author");
    auto export_is_comment_author = context.SetIntItemAttr("is_comment_author");
    auto export_browsed_author_type_fixed = context.SetIntItemAttr("browsed_author_type_fixed");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      // item attrs
      auto author_id = author_id_attr_accessor(result).value_or(0);
      auto click_motivation_type = click_motivation_type_accessor(result).value_or(0);
      // is_collect_author, is_comment_author, is_share_author, is_like_author
      int is_collect_author{0};
      int is_comment_author{0};
      int is_share_author{0};
      int is_like_author{0};
      int64 browsed_author_type{0};
      int64 browsed_author_type_fixed{0};
      int64 ind{-1};  // ind 如果被标记为-1，就代表 indexOf 函数返回的值是 false, 默认设置为 -1

      // ----------------------------------------------------------------
      // -------------计算看过作者 item attr --------------------------------
      // ----------------------------------------------------------------
      auto indexOf = [&]() -> void {
        if (!watched_author_detail_list.has_value() || watched_author_detail_list->empty() ||
            origin_browsed_author_list.empty() || author_id == 0) {
          return;
        }
        int n = origin_browsed_author_list.size();
        auto &watched_author_detail_list_ref = watched_author_detail_list.value();
        for (int i = 0; i < n; i++) {
          if (origin_browsed_author_list[i] == author_id) {
            std::string watched_author_detail_list_i = std::string(watched_author_detail_list_ref[i]);
            auto segs = Split(watched_author_detail_list_i, ';');
            // int64 aid = std::stoll(Split(segs[0], ':')[0]);
            std::string aid = Split(segs[0], ':')[0];
            std::string author_id_for_cmp = std::to_string(author_id);
            if (aid != author_id_for_cmp) {
              ind = i + 1;
              return;
            }
            if (!absl::SimpleAtoi(segs[1], &is_collect_author) ||
                !absl::SimpleAtoi(segs[2], &is_comment_author) ||
                !absl::SimpleAtoi(segs[3], &is_share_author) || !absl::SimpleAtoi(segs[4], &is_like_author)) {
              CL_LOG(WARNING) << "SimpleAtoi Error occurred!" << std::endl;
            }
            ind = i + 1;
            return;
          }
        }
      };
      indexOf();
      if (ind != -1) {
        if (ind <= 10) {
          browsed_author_type = 3;
          browsed_author_type_fixed = 3;
        } else if (ind <= 30) {
          browsed_author_type = 2;
          browsed_author_type_fixed = 2;
        } else if (ind <= 70) {
          browsed_author_type = 1;
          browsed_author_type_fixed = 1;
        } else {
          browsed_author_type = 2;
          browsed_author_type_fixed = 4;
        }
      }
      //-----------------------------------------------------------------
      //-------------计算 motivation type ----------------------------------
      //-----------------------------------------------------------------
      std::string motivation_type = "content";
      if (click_motivation_type == 2 || click_motivation_type == 3) {
        motivation_type = "author";
      }

      export_is_like_author(result, is_like_author);
      export_motivation_type(result, motivation_type);
      export_is_share_author(result, is_share_author);
      export_is_collect_author(result, is_collect_author);
      export_is_comment_author(result, is_comment_author);
      export_browsed_author_type(result, browsed_author_type);
      export_browsed_author_type_fixed(result, browsed_author_type_fixed);
    });
    return true;
  }

  static bool Int2StringFunc(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                             RecoResultConstIter end) {
    // import item attr
    auto userItemRelation_accessor = context.GetIntItemAttr("userItemRelation");
    // export item attr
    auto export_user_item_relation_str = context.SetStringItemAttr("user_item_relation_str");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      // item attrs
      auto userItemRelation = userItemRelation_accessor(result);
      if (userItemRelation) {
        std::string str(std::to_string(userItemRelation.value()));
        export_user_item_relation_str(result, str);
      }
    });
    return true;
  }
  static bool EnrichRecallDegradePeriod(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    // import common attr
    auto recall_degrade_period_list = context.GetStringListCommonAttr("recall_degrade_period");
    if (recall_degrade_period_list != absl::nullopt && recall_degrade_period_list.has_value()) {
      int in_recall_degrade_period = IsDegradePeriod(*recall_degrade_period_list);
      context.SetIntCommonAttr("in_recall_degrade_period", in_recall_degrade_period);
    } else {
      context.SetIntCommonAttr("in_recall_degrade_period", 0);
    }
    return true;
  }

  static bool EnrichFrDegradePeriod(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    // import common attr
    auto fr_degrade_period_list = context.GetStringListCommonAttr("fr_degrade_period");
    if (fr_degrade_period_list != absl::nullopt && fr_degrade_period_list.has_value()) {
      int in_fr_degrade_period = IsDegradePeriod(*fr_degrade_period_list);
      context.SetIntCommonAttr("in_fr_degrade_period", in_fr_degrade_period);
    } else {
      context.SetIntCommonAttr("in_fr_degrade_period", 0);
    }
    return true;
  }

  static int IsDegradePeriod(const std::vector<absl::string_view> &degrade_period_list) {
    // export common attr
    if (degrade_period_list.size() <= 0) {
      return 0;
    }
    int in_fr_degrade_period = 0;
    char local_time_str[100];
    std::string prefix_date, degrade_begin_time_str, degrade_end_time_str;
    std::vector<std::string> local_time_str_list;

    const auto time_now = time(nullptr);  // 获取当前秒时间
    struct tm local_time;
    localtime_r(&time_now, &local_time);  // 转为本地时间

    // 本地时间转换为字符串
    strftime(local_time_str, sizeof(local_time_str), "%Y-%m-%d %H:%M:%S", &local_time);
    folly::split(' ', local_time_str, local_time_str_list);
    if (local_time_str_list.size() > 0) {
      prefix_date = local_time_str_list[0];  // 获取当前日期
      // LOG(INFO) << "the current time is: " << local_time_str << "date is: " << prefix_date << "end";
    } else {
      LOG(ERROR) << "get error current time!";
      return false;
    }

    for (int i = 0; i < degrade_period_list.size(); i++) {
      std::vector<std::string> fr_degrade_period_single_list;
      folly::split('-', std::string(degrade_period_list.at(i)).data(), fr_degrade_period_single_list);

      if (fr_degrade_period_single_list.size() == 2) {
        tm tm_begin, tm_end;

        degrade_begin_time_str = prefix_date + " " + fr_degrade_period_single_list[0];
        degrade_end_time_str = prefix_date + " " + fr_degrade_period_single_list[1];
        // LOG(INFO) << "the degrade time is: " << degrade_begin_time_str
        // << " to " << degrade_end_time_str << "end";

        strptime(degrade_begin_time_str.c_str(), "%Y-%m-%d %H:%M:%S", &tm_begin);  // 将字符串转换为 tm 时间
        strptime(degrade_end_time_str.c_str(), "%Y-%m-%d %H:%M:%S", &tm_end);

        tm_begin.tm_isdst = -1;
        tm_end.tm_isdst = -1;

        auto t_begin = mktime(&tm_begin);  // 将 tm 时间转换为秒时间
        auto t_end = mktime(&tm_end);
        if (t_begin <= time_now && time_now <= t_end) {
          in_fr_degrade_period = 1;
          break;
        }
      } else {
        LOG(ERROR) << "fr degrade period format error";
      }
    }
    return in_fr_degrade_period;
  }

  static std::vector<int64> GetContextPhotoIndexList(
      absl::optional<absl::Span<const int64>> context_photo_id_list_all, int context_photo_sample_num) {
    std::vector<int64> context_photo_index_list;
    if (!context_photo_id_list_all || context_photo_id_list_all->empty()) return context_photo_index_list;
    int item_num = context_photo_id_list_all->size();
    int sample_num = context_photo_sample_num;

    if (item_num <= sample_num) {
      for (int i = 0; i < item_num; ++i) {
        context_photo_index_list.push_back(i);
      }
      return context_photo_index_list;
    }

    double step = static_cast<double>(item_num) / sample_num;
    for (int i = 0; i < item_num; i += step) {
      if (i <= (item_num - 1)) {
        context_photo_index_list.push_back(std::floor(i));
      }
    }

    return context_photo_index_list;
  }

  static bool EnrichContextPhoto(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    // import common attr
    auto context_photo_id_list_all = context.GetIntListCommonAttr("context_photo_id_list_all");
    auto context_author_id_list_all = context.GetIntListCommonAttr("context_author_id_list_all");
    auto context_photo_hetu_tag_list_all = context.GetIntListCommonAttr("context_photo_hetu_tag_list_all");
    auto context_event_type_list_all = context.GetStringListCommonAttr("context_event_type_list_all");
    auto context_photo_sample_num = context.GetIntCommonAttr("context_photo_sample_num").value_or(0);

    if (!context_photo_id_list_all || !context_author_id_list_all || !context_photo_hetu_tag_list_all ||
        !context_event_type_list_all) {
      return false;
    }

    if (context_photo_id_list_all->empty() || context_author_id_list_all->empty() ||
        context_photo_hetu_tag_list_all->empty() || context_event_type_list_all->empty()) {
      return false;
    }

    if (context_photo_id_list_all->size() != context_author_id_list_all->size() ||
        context_photo_id_list_all->size() != context_photo_hetu_tag_list_all->size() ||
        context_photo_id_list_all->size() != context_event_type_list_all->size()) {
      return false;
    }

    std::vector<int64> context_photo_index_list =
        GetContextPhotoIndexList(context_photo_id_list_all, context_photo_sample_num);
    if (context_photo_index_list.empty()) {
      return false;
    }

    if (context_photo_index_list.size() > context_photo_id_list_all->size() ||
        context_photo_index_list.back() > (context_photo_id_list_all->size() - 1)) {
      return false;
    }

    std::vector<int64> context_photo_id_list, context_author_id_list, context_photo_hetu_tag_list;
    std::vector<std::string> context_event_type_list;

    for (int i = 0; i < context_photo_index_list.size(); ++i) {
      context_photo_id_list.push_back(
          static_cast<int64>(context_photo_id_list_all->at(context_photo_index_list[i])));
      context_author_id_list.push_back(
          static_cast<int64>(context_author_id_list_all->at(context_photo_index_list[i])));
      context_photo_hetu_tag_list.push_back(
          static_cast<int64>(context_photo_hetu_tag_list_all->at(context_photo_index_list[i])));
      context_event_type_list.push_back(
          std::string(context_event_type_list_all->at(context_photo_index_list[i])));
    }

    if (context_photo_id_list.empty() || context_photo_id_list.size() == 0 ||
        context_author_id_list.empty() || context_author_id_list.size() == 0 ||
        context_photo_hetu_tag_list.empty() || context_photo_hetu_tag_list.size() == 0 ||
        context_event_type_list.empty() || context_event_type_list.size() == 0) {
      return false;
    }
    context.SetIntListCommonAttr("context_photo_id_list", std::move(context_photo_id_list));
    context.SetIntListCommonAttr("context_author_id_list", std::move(context_author_id_list));
    context.SetIntListCommonAttr("context_photo_hetu_tag_list", std::move(context_photo_hetu_tag_list));
    context.SetStringListCommonAttr("context_event_type_list", std::move(context_event_type_list));

    return true;
  }

  static int IsInList(int64 target_value, absl::optional<absl::Span<const int64>> value_list) {
    // 判断 target_value 是否在 value_list 中
    if (!value_list || value_list->empty()) {
      return 0;
    }

    for (int i = 0; i < value_list->size(); i++) {
      if (value_list->at(i) == target_value && target_value > 0) {
        return 1;
      }
    }

    return 0;
  }

  static int IsInteract(int64 current_label, int64 current_aid,
                        absl::optional<absl::Span<const int64>> push_click_aid_list) {
    // 判断用户对当前视频是否有点赞、关注、评论等互动行为
    int like = current_label & 0x01;
    int follow = current_label & (1 << 1);
    int forward = current_label & (1 << 2);
    int comment = current_label & (1 << 4);
    int has_entered_profile = current_label & (1 << 6);
    // 判断当前视频的作者是否在用户点过的 push 作者中
    int has_clicked_author = IsInList(current_aid, push_click_aid_list);
    int interact = like + follow + forward + comment + has_entered_profile + has_clicked_author;

    if (interact > 0) {
      return 1;
    }

    return 0;
  }

  static int IsLongPlay(int64 duration, int64 play) {
    // 判断用户是否长播当前视频
    if (duration == 0 || play == 0 || duration < 3) {
      return 0;
    }

    if (duration >= 3 && duration < 18 && play >= duration) {
      return 1;
    }

    if (duration >= 18 && play >= 18) {
      return 1;
    }

    return 0;
  }

  static int IsValidPlay(int64 duration, int64 play) {
    // 判断用户是否有效播放当前视频
    if (duration == 0 || play == 0 || duration < 3) {
      return 0;
    }

    if (duration >= 3 && duration < 7 && play >= duration) {
      return 1;
    }

    if (duration >= 7 && play >= 7) {
      return 1;
    }

    return 0;
  }

  static bool GetColossusResult(absl::optional<absl::Span<const int64>> colossus_pid,
                                absl::optional<absl::Span<const int64>> colossus_aid,
                                absl::optional<absl::Span<const int64>> colossus_tag,
                                absl::optional<absl::Span<const int64>> colossus_play,
                                absl::optional<absl::Span<const int64>> colossus_duration,
                                absl::optional<absl::Span<const int64>> colossus_time,
                                absl::optional<absl::Span<const int64>> colossus_label,
                                std::vector<int64> *pid_list, std::vector<int64> *aid_list,
                                std::vector<int64> *tag_list, std::vector<int64> *play_list,
                                std::vector<int64> *time_list, std::vector<int64> *label_list,
                                std::vector<int64> *reason_list, std::vector<int64> index_list,
                                int64 list_max_len, int recent_len, double sample_stamp, int reason) {
    if (!colossus_pid || !colossus_aid || !colossus_duration || !colossus_play || !colossus_tag ||
        !colossus_time || !colossus_label) {
      return false;
    }

    int len = recent_len;

    for (int i = 0; i < index_list.size(); i++) {
      // 判断下标是否在输入特征的长度范围
      if (index_list[i] >= 0 && index_list[i] < colossus_pid->size()) {
        pid_list->push_back(colossus_pid->at(index_list[i]));
        aid_list->push_back(colossus_aid->at(index_list[i]));
        tag_list->push_back(colossus_tag->at(index_list[i]));
        double double_value = static_cast<double>(colossus_play->at(index_list[i])) / 3;
        int64 pre_value = static_cast<int64>(static_cast<int64>(std::floor(double_value)) << 24);
        int64 play = pre_value | colossus_duration->at(index_list[i]);
        play_list->push_back(play);
        int64 time = std::floor(static_cast<double>(sample_stamp - colossus_time->at(index_list[i])) / 86400);
        time_list->push_back(time);
        label_list->push_back(colossus_label->at(index_list[i]));
        reason_list->push_back(reason);
        len = len + 1;
      } else {
        return false;
      }
      if (len >= list_max_len) {
        break;
      }
    }

    return true;
  }

  static bool EnrichColossusList(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    // 获取 timestampMs 特征
    auto timestampMs = context.GetIntCommonAttr("timestampMs").value_or(0);
    // timestampMs 毫秒转换为秒
    double sample_stamp = (double)timestampMs / 1000;
    // 获取相关 common 特征
    auto colossus_inter_range = context.GetIntCommonAttr("colossus_inter_range_new").value_or(-1);
    auto colossus_lp_range = context.GetIntCommonAttr("colossus_lp_range_new").value_or(-1);
    auto colossus_vp_range = context.GetIntCommonAttr("colossus_vp_range_new").value_or(-1);
    auto colossus_list_max_len = context.GetIntCommonAttr("colossus_list_max_len_new").value_or(-1);
    auto colossus_list_min_len = context.GetIntCommonAttr("colossus_list_min_len_new").value_or(-1);
    auto colossus_pid = context.GetIntListCommonAttr("colossus_pid");
    auto colossus_aid = context.GetIntListCommonAttr("colossus_aid");
    auto colossus_duration = context.GetIntListCommonAttr("colossus_duration");
    auto colossus_play = context.GetIntListCommonAttr("colossus_play");
    auto colossus_tag = context.GetIntListCommonAttr("colossus_tag");
    auto colossus_time = context.GetIntListCommonAttr("colossus_time");
    auto colossus_label = context.GetIntListCommonAttr("colossus_label");
    auto push_click_author_90d = context.GetIntListCommonAttr("push_click_author_90d");

    // 判断获取的特征是否异常
    if (colossus_inter_range == -1 || colossus_lp_range == -1 || colossus_vp_range == -1 ||
        colossus_list_max_len == -1 || colossus_list_min_len == -1) {
      return false;
    }

    if (!colossus_pid || !colossus_aid || !colossus_duration || !colossus_play || !colossus_tag ||
        !colossus_time || !colossus_label) {
      return false;
    }

    if (colossus_pid->empty() || colossus_aid->empty() || colossus_duration->empty() ||
        colossus_play->empty() || colossus_tag->empty() || colossus_time->empty() ||
        colossus_label->empty()) {
      return false;
    }

    if (colossus_pid->size() != colossus_aid->size() || colossus_pid->size() != colossus_duration->size() ||
        colossus_pid->size() != colossus_play->size() || colossus_pid->size() != colossus_tag->size() ||
        colossus_pid->size() != colossus_time->size() || colossus_pid->size() != colossus_label->size()) {
      return false;
    }

    // 声明各个变量，用于存储返回序列
    std::vector<int64> pid_list, aid_list, tag_list, play_list, time_list, label_list, reason_list;
    // 存储满足不同条件的视频的下标
    std::vector<int64> interact_index_list, lp_index_list, vp_index_list;

    // 输入的 common 特征是按时间由远及近正向排列的，反向遍历序列
    for (int i = colossus_aid->size() - 1; i >= 0; i--) {
      // 如果出现特征穿越，跳过
      if (colossus_time->at(i) >= sample_stamp) {
        continue;
      }
      // 判断此次请求前 colossus_inter_range 天的视频
      // 是否满足 IsInteract (即有用户和视频有互动) 条件
      if ((sample_stamp - colossus_time->at(i)) < (colossus_inter_range * 86400)) {
        if (IsInteract(colossus_label->at(i), colossus_aid->at(i), push_click_author_90d) == 1) {
          // 若有交互，加入 interact_index_list
          interact_index_list.push_back(i);
          // 交互优先级 > 长播 > 有效播，因此如果当前视频为交互视频
          // 则不会再作为长播或者有效播视频，直接遍历下一个
          continue;
        }
      }
      // 判断此次请求前 colossus_lp_range 天的视频
      // 是否满足 IsLongPlay (即用户长播当前视频) 条件
      if ((sample_stamp - colossus_time->at(i)) < (colossus_lp_range * 86400)) {
        if (IsLongPlay(colossus_duration->at(i), colossus_play->at(i)) == 1) {
          lp_index_list.push_back(i);
          // 交互优先级 > 长播 > 有效播，因此如果当前视频为长播视频
          // 则不会再作为有效播视频，直接遍历下一个
          continue;
        }
      }
      // 判断此次请求前 colossus_lp_range 天的视频
      // 是否满足 IsLongPlay (即用户有效播当前视频) 条件
      if ((sample_stamp - colossus_time->at(i)) < (colossus_vp_range * 86400)) {
        if (IsValidPlay(colossus_duration->at(i), colossus_play->at(i)) == 1) {
          vp_index_list.push_back(i);
          continue;
        }
      }
      // 输入的 common 特征是按时间由远及近正向排列的
      // 在反向遍历时，如果当前元素时间戳之差大于阈值
      // 再继续反向遍历的元素还是都大于阈值，因此可以直接跳出循环
      if ((sample_stamp - colossus_time->at(i)) >= (colossus_inter_range * 86400) &&
          (sample_stamp - colossus_time->at(i)) >= (colossus_lp_range * 86400) &&
          (sample_stamp - colossus_time->at(i)) >= (colossus_vp_range * 86400)) {
        break;
      }
    }

    bool error_flag = true;
    // 首先遍历互动视频下标序列
    if (!interact_index_list.empty() && interact_index_list.size() > 0) {
      error_flag = GetColossusResult(
          colossus_pid, colossus_aid, colossus_tag, colossus_play, colossus_duration, colossus_time,
          colossus_label, &pid_list, &aid_list, &tag_list, &play_list, &time_list, &label_list, &reason_list,
          interact_index_list, colossus_list_max_len, pid_list.size(), sample_stamp, 1);
      if (error_flag == false) {
        return false;
      }
    }

    // 如果互动视频的长度小于 colossus_list_min_len
    // 遍历满足长播条件的视频下标序列
    if (pid_list.size() < colossus_list_min_len) {
      if (!lp_index_list.empty() && lp_index_list.size() > 0) {
        error_flag = GetColossusResult(
            colossus_pid, colossus_aid, colossus_tag, colossus_play, colossus_duration, colossus_time,
            colossus_label, &pid_list, &aid_list, &tag_list, &play_list, &time_list, &label_list,
            &reason_list, lp_index_list, colossus_list_min_len, pid_list.size(), sample_stamp, 2);
        if (error_flag == false) {
          return false;
        }
      }
    }

    // 如果互动 + 长播视频的长度还是小于 colossus_list_min_len
    // 遍历满足有效播条件的视频下标序列
    if (pid_list.size() < colossus_list_min_len) {
      if (!vp_index_list.empty() && vp_index_list.size() > 0) {
        error_flag = GetColossusResult(
            colossus_pid, colossus_aid, colossus_tag, colossus_play, colossus_duration, colossus_time,
            colossus_label, &pid_list, &aid_list, &tag_list, &play_list, &time_list, &label_list,
            &reason_list, vp_index_list, colossus_list_min_len, pid_list.size(), sample_stamp, 3);
        if (error_flag == false) {
          return false;
        }
      }
    }

    if (pid_list.empty() || pid_list.size() == 0) {
      return false;
    }

    context.SetIntListCommonAttr("colossus_recent_pids_new", std::move(pid_list));
    context.SetIntListCommonAttr("colossus_recent_aids_new", std::move(aid_list));
    context.SetIntListCommonAttr("colossus_recent_tags_new", std::move(tag_list));
    context.SetIntListCommonAttr("colossus_recent_play_new", std::move(play_list));
    context.SetIntListCommonAttr("colossus_recent_time_new", std::move(time_list));
    context.SetIntListCommonAttr("colossus_recent_label_new", std::move(label_list));
    context.SetIntListCommonAttr("colossus_recent_reason_new", std::move(reason_list));

    return true;
  }

  static bool EnrichColossusNegativeList(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    // 获取 timestampMs 特征
    auto timestampMs = context.GetIntCommonAttr("timestampMs").value_or(0);
    // timestampMs 毫秒转换为秒
    double sample_stamp = (double)timestampMs / 1000;
    // 获取相关 common 特征
    auto colossus_negative_list_time_ragne =
        context.GetIntCommonAttr("colossus_negative_list_time_ragne").value_or(-1);
    auto colossus_negative_list_max_len =
        context.GetIntCommonAttr("colossus_negative_list_max_len").value_or(-1);
    auto colossus_pid = context.GetIntListCommonAttr("colossus_pid");
    auto colossus_aid = context.GetIntListCommonAttr("colossus_aid");
    auto colossus_duration = context.GetIntListCommonAttr("colossus_duration");
    auto colossus_play = context.GetIntListCommonAttr("colossus_play");
    auto colossus_tag = context.GetIntListCommonAttr("colossus_tag");
    auto colossus_time = context.GetIntListCommonAttr("colossus_time");
    auto colossus_label = context.GetIntListCommonAttr("colossus_label");

    // 判断获取的特征是否异常
    if (colossus_negative_list_time_ragne == -1 || colossus_negative_list_max_len == -1) {
      return false;
    }

    if (!colossus_pid || !colossus_aid || !colossus_duration || !colossus_play || !colossus_tag ||
        !colossus_time || !colossus_label) {
      return false;
    }

    if (colossus_pid->empty() || colossus_aid->empty() || colossus_duration->empty() ||
        colossus_play->empty() || colossus_tag->empty() || colossus_time->empty() ||
        colossus_label->empty()) {
      return false;
    }

    if (colossus_pid->size() != colossus_aid->size() || colossus_pid->size() != colossus_duration->size() ||
        colossus_pid->size() != colossus_play->size() || colossus_pid->size() != colossus_tag->size() ||
        colossus_pid->size() != colossus_time->size() || colossus_pid->size() != colossus_label->size()) {
      return false;
    }

    // 声明各个变量，用于存储返回序列
    std::vector<int64> pid_list, aid_list, tag_list, play_list, time_list, label_list;
    int list_len = 0;

    // 输入的 common 特征是按时间由远及近正向排列的，反向遍历序列
    for (int i = colossus_pid->size() - 1; i >= 0; i--) {
      // 如果出现特征穿越，跳过
      if (colossus_time->at(i) >= sample_stamp) {
        continue;
      }

      // 在反向遍历时，如果当前元素时间戳之差大于阈值
      // 再继续反向遍历的元素还是都大于阈值，因此可以直接跳出循环
      if ((sample_stamp - colossus_time->at(i)) >= (colossus_negative_list_time_ragne * 86400)) {
        break;
      }

      if (colossus_play->at(i) < 3) {
        pid_list.push_back(colossus_pid->at(i));
        aid_list.push_back(colossus_aid->at(i));
        tag_list.push_back(colossus_tag->at(i));
        double double_value = static_cast<double>(colossus_play->at(i)) / 3;
        int64 pre_value = static_cast<int64>(static_cast<int64>(std::floor(double_value)) << 24);
        int64 play = pre_value | colossus_duration->at(i);
        play_list.push_back(play);
        int64 time = std::floor(static_cast<double>(sample_stamp - colossus_time->at(i)) / 86400);
        time_list.push_back(time);
        label_list.push_back(colossus_label->at(i));
        list_len += 1;
      }

      if (list_len >= colossus_negative_list_max_len) {
        break;
      }
    }

    if (pid_list.empty() || pid_list.size() == 0) {
      return false;
    }

    context.SetIntListCommonAttr("colossus_negative_pids", std::move(pid_list));
    context.SetIntListCommonAttr("colossus_negative_aids", std::move(aid_list));
    context.SetIntListCommonAttr("colossus_negative_tags", std::move(tag_list));
    context.SetIntListCommonAttr("colossus_negative_play", std::move(play_list));
    context.SetIntListCommonAttr("colossus_negative_time", std::move(time_list));
    context.SetIntListCommonAttr("colossus_negative_label", std::move(label_list));

    return true;
  }

  static bool EnrichColossusConsumeByHourList(const CommonRecoLightFunctionContext &context,
                    RecoResultConstIter begin, RecoResultConstIter end) {
    // 获取 timestampMs 特征
    auto timestamp_ms = context.GetIntCommonAttr("timestampMs").value_or(0);
    // timestampMs 毫秒转换为秒
    double sample_stamp = (double)timestamp_ms / 1000;
    int current_hour = GetHourFromTimestamp(sample_stamp);
    if (current_hour == -1) {
      return false;
    }

    auto colossus_inter_range = context.GetIntCommonAttr("colossus_hour_inter_range_new").value_or(-1);
    auto colossus_lp_range = context.GetIntCommonAttr("colossus_hour_lp_range_new").value_or(-1);
    auto colossus_vp_range = context.GetIntCommonAttr("colossus_hour_vp_range_new").value_or(-1);
    auto colossus_consume_by_hour_max_len =
        context.GetIntCommonAttr("colossus_consume_by_hour_list_max_len").value_or(-1);
    auto colossus_pid = context.GetIntListCommonAttr("colossus_pid");
    auto colossus_aid = context.GetIntListCommonAttr("colossus_aid");
    auto colossus_duration = context.GetIntListCommonAttr("colossus_duration");
    auto colossus_play = context.GetIntListCommonAttr("colossus_play");
    auto colossus_tag = context.GetIntListCommonAttr("colossus_tag");
    auto colossus_time = context.GetIntListCommonAttr("colossus_time");
    auto colossus_label = context.GetIntListCommonAttr("colossus_label");
    auto friend_list = context.GetIntListCommonAttr("follow_friend_list");

    if (colossus_consume_by_hour_max_len == -1 || colossus_inter_range == -1 || colossus_lp_range == -1 ||
        colossus_vp_range == -1) {
      return false;
    }

    if (!colossus_pid || !colossus_aid || !colossus_duration || !colossus_play || !colossus_tag ||
        !colossus_time || !colossus_label) {
      return false;
    }

    if (colossus_pid->empty() || colossus_aid->empty() || colossus_duration->empty() ||
        colossus_play->empty() || colossus_tag->empty() || colossus_time->empty() ||
        colossus_label->empty()) {
      return true;
    }

    if (colossus_pid->size() != colossus_aid->size() || colossus_pid->size() != colossus_duration->size() ||
        colossus_pid->size() != colossus_play->size() || colossus_pid->size() != colossus_tag->size() ||
        colossus_pid->size() != colossus_time->size() || colossus_pid->size() != colossus_label->size()) {
      return false;
    }

    // 声明各个变量，用于存储返回序列
    std::vector<int64> pid_list, aid_list, tag_list, play_list, time_list, label_list, reason_list;
    // 存储满足不同条件的视频的下标
    std::vector<int64> interact_index_list, lp_index_list, vp_index_list;

    // 输入的 common 特征是按时间由远及近正向排列的，反向遍历序列
    for (int i = colossus_pid->size() - 1; i >= 0; i--) {
      // 如果出现特征穿越，跳过
      if (colossus_time->at(i) >= sample_stamp) {
        continue;
      }
      auto colossus_hour = GetHourFromTimestamp(colossus_time->at(i));
      // 不等于当前 hour，直接跳过
      if (colossus_hour != current_hour) {
        continue;
      }
      // 如果当前作品的作者是互关好友, 跳过
      if (IsFriend(colossus_aid->at(i), friend_list) == 1) {
        continue;
      }

      if ((sample_stamp - colossus_time->at(i)) < (colossus_inter_range * 86400)) {
        if (IsInteractStrict(colossus_label->at(i)) == 1) {
          // 若有交互，加入 interact_index_list
          interact_index_list.push_back(i);
          // 交互优先级 > 长播 > 有效播，因此如果当前视频为交互视频
          // 则不会再作为长播或者有效播视频，直接遍历下一个
          continue;
        }
      }

      if ((sample_stamp - colossus_time->at(i)) < (colossus_lp_range * 86400)) {
        if (IsLongPlay(colossus_duration->at(i), colossus_play->at(i)) == 1) {
          lp_index_list.push_back(i);
          // 交互优先级 > 长播 > 有效播，因此如果当前视频为长播视频
          // 则不会再作为有效播视频，直接遍历下一个
          continue;
        }
      }

      if ((sample_stamp - colossus_time->at(i)) < (colossus_vp_range * 86400)) {
        if (IsValidPlay(colossus_duration->at(i), colossus_play->at(i)) == 1) {
          vp_index_list.push_back(i);
          continue;
        }
      }

      if ((sample_stamp - colossus_time->at(i)) >= (colossus_inter_range * 86400) &&
          (sample_stamp - colossus_time->at(i)) >= (colossus_lp_range * 86400) &&
          (sample_stamp - colossus_time->at(i)) >= (colossus_vp_range * 86400)) {
        break;
      }
    }

    bool error_flag = true;
    // 按照交互 长播 有效播的顺序取
    if (!interact_index_list.empty() && interact_index_list.size() > 0) {
      error_flag = GetColossusResult(
        colossus_pid, colossus_aid, colossus_tag, colossus_play, colossus_duration, colossus_time,
        colossus_label, &pid_list, &aid_list, &tag_list, &play_list, &time_list, &label_list, &reason_list,
        interact_index_list, colossus_consume_by_hour_max_len, pid_list.size(), sample_stamp, 1);
      if (error_flag == false) {
        return false;
      }
    }

    if (pid_list.size() < colossus_consume_by_hour_max_len) {
      if (!lp_index_list.empty() && lp_index_list.size() > 0) {
        error_flag = GetColossusResult(
            colossus_pid, colossus_aid, colossus_tag, colossus_play, colossus_duration, colossus_time,
            colossus_label, &pid_list, &aid_list, &tag_list, &play_list, &time_list, &label_list,
            &reason_list, lp_index_list, colossus_consume_by_hour_max_len, pid_list.size(), sample_stamp, 2);
        if (error_flag == false) {
          return false;
        }
      }
    }

    if (pid_list.size() < colossus_consume_by_hour_max_len) {
      if (!vp_index_list.empty() && vp_index_list.size() > 0) {
        error_flag = GetColossusResult(
            colossus_pid, colossus_aid, colossus_tag, colossus_play, colossus_duration, colossus_time,
            colossus_label, &pid_list, &aid_list, &tag_list, &play_list, &time_list, &label_list,
            &reason_list, vp_index_list, colossus_consume_by_hour_max_len, pid_list.size(), sample_stamp, 3);
        if (error_flag == false) {
          return false;
        }
      }
    }
    if (pid_list.empty() || pid_list.size() == 0) {
      return false;
    }

    context.SetIntListCommonAttr("colossus_consume_by_hour_pids", std::move(pid_list));
    context.SetIntListCommonAttr("colossus_consume_by_hour_aids", std::move(aid_list));
    context.SetIntListCommonAttr("colossus_consume_by_hour_tags", std::move(tag_list));
    context.SetIntListCommonAttr("colossus_consume_by_hour_play", std::move(play_list));
    context.SetIntListCommonAttr("colossus_consume_by_hour_time", std::move(time_list));
    context.SetIntListCommonAttr("colossus_consume_by_hour_label", std::move(label_list));
    context.SetIntListCommonAttr("colossus_consume_by_hour_reason", std::move(reason_list));

    return true;
  }

  static int GetHourFromTimestamp(int64 timestamp) {
    if (timestamp > 0) {
      // 计算总小时数（UTC 时间）
      int64_t total_hour = timestamp / 3600;

      // 将总小时数调整为中国时区（UTC+8）
      int current_hour_in_china = (total_hour + 8) % 24;

      return current_hour_in_china;
    }
    return -1;
  }

  static int IsInteractStrict(int64 current_label) {
    uint16_t label = current_label;

    uint16_t is_dislike = label & (1 << 3);
    uint16_t is_uninterested = label & (1 << 13);
    uint16_t useless_interact = is_dislike + is_uninterested;
    if (useless_interact > 0) {
      return 0;
    }

    // 判断用户对当前视频是否有点赞、关注、评论等互动行为
    uint16_t like = label & 0x01;
    uint16_t follow = label & (1 << 1);
    uint16_t forward = label & (1 << 2);
    uint16_t comment = label & (1 << 4);
    uint16_t has_entered_profile = label & (1 << 6);
    uint16_t interact = like + follow + forward + comment + has_entered_profile;

    if (interact > 0) {
      return 1;
    }

    return 0;
  }

  static int IsFriend(int64 current_aid, absl::optional<absl::Span<const int64>> friend_list) {
    // 判断当前作品的作者是否是互关好友
    if (!friend_list || friend_list->empty() || friend_list->size() == 0) {
      return 0;
    }
    for (int i = 0; i < friend_list->size(); i++) {
      if (friend_list->at(i) == current_aid) {
        return 1;
      }
    }
    return 0;
  }

  static bool GetColossusResultWithTimestamp(absl::optional<absl::Span<const int64>> colossus_pid,
                                absl::optional<absl::Span<const int64>> colossus_aid,
                                absl::optional<absl::Span<const int64>> colossus_tag,
                                absl::optional<absl::Span<const int64>> colossus_play,
                                absl::optional<absl::Span<const int64>> colossus_duration,
                                absl::optional<absl::Span<const int64>> colossus_time,
                                absl::optional<absl::Span<const int64>> colossus_label,
                                std::vector<int64> *pid_list, std::vector<int64> *aid_list,
                                std::vector<int64> *tag_list, std::vector<int64> *play_list,
                                std::vector<int64> *time_list, std::vector<int64> *label_list,
                                std::vector<int64> *reason_list, std::vector<int64> *timestamp_list,
                                std::vector<int64> index_list, int64 list_max_len, int recent_len,
                                double sample_stamp, int reason) {
    if (!colossus_pid || !colossus_aid || !colossus_duration || !colossus_play || !colossus_tag ||
        !colossus_time || !colossus_label) {
      return false;
    }

    int len = recent_len;

    for (int i = 0; i < index_list.size(); i++) {
      // 判断下标是否在输入特征的长度范围
      if (index_list[i] >= 0 && index_list[i] < colossus_pid->size()) {
        pid_list->push_back(colossus_pid->at(index_list[i]));
        aid_list->push_back(colossus_aid->at(index_list[i]));
        tag_list->push_back(colossus_tag->at(index_list[i]));
        double double_value = static_cast<double>(colossus_play->at(index_list[i])) / 3;
        int64 pre_value = static_cast<int64>(static_cast<int64>(std::floor(double_value)) << 24);
        int64 play = pre_value | colossus_duration->at(index_list[i]);
        play_list->push_back(play);
        int64 time = std::floor(static_cast<double>(sample_stamp - colossus_time->at(index_list[i])) / 86400);
        time_list->push_back(time);
        timestamp_list->push_back(colossus_time->at(index_list[i]));
        label_list->push_back(colossus_label->at(index_list[i]));
        reason_list->push_back(reason);
        len = len + 1;
      } else {
        return false;
      }
      if (len >= list_max_len) {
        break;
      }
    }

    return true;
  }

  // static bool EnrichColossusConsumeList(const CommonRecoLightFunctionContext &context,
  //                   RecoResultConstIter begin, RecoResultConstIter end) {
  //   // 获取 timestampMs 特征
  //   auto timestampMs = context.GetIntCommonAttr("timestampMs").value_or(0);
  //   // timestampMs 毫秒转换为秒
  //   double sample_stamp = (double)timestampMs / 1000;
  //   // 获取相关 common 特征
  //   auto colossus_inter_range = context.GetIntCommonAttr("colossus_inter_range").value_or(-1);
  //   auto colossus_lp_range = context.GetIntCommonAttr("colossus_lp_range").value_or(-1);
  //   auto colossus_vp_range = context.GetIntCommonAttr("colossus_vp_range").value_or(-1);
  //   auto colossus_list_max_len = context.GetIntCommonAttr("colossus_list_max_len").value_or(-1);
  //   auto colossus_list_min_len = context.GetIntCommonAttr("colossus_list_min_len").value_or(-1);
  //   auto colossus_pid = context.GetIntListCommonAttr("colossus_pid");
  //   auto colossus_aid = context.GetIntListCommonAttr("colossus_aid");
  //   auto colossus_duration = context.GetIntListCommonAttr("colossus_duration");
  //   auto colossus_play = context.GetIntListCommonAttr("colossus_play");
  //   auto colossus_tag = context.GetIntListCommonAttr("colossus_tag");
  //   auto colossus_time = context.GetIntListCommonAttr("colossus_time");
  //   auto colossus_label = context.GetIntListCommonAttr("colossus_label");
  //   auto friend_list = context.GetIntListCommonAttr("follow_friend_list");
  //   auto user_id = context.GetIntCommonAttr("user_id").value_or(0);

  //   // 判断获取的特征是否异常
  //   if (colossus_inter_range == -1 || colossus_lp_range == -1 || colossus_vp_range == -1 ||
  //       colossus_list_max_len == -1 || colossus_list_min_len == -1) {
  //     return false;
  //   }

  //   if (!colossus_pid || !colossus_aid || !colossus_duration || !colossus_play || !colossus_tag ||
  //       !colossus_time || !colossus_label) {
  //     return false;
  //   }

  //   if (colossus_pid->empty() || colossus_aid->empty() || colossus_duration->empty() ||
  //       colossus_play->empty() || colossus_tag->empty() || colossus_time->empty() ||
  //       colossus_label->empty()) {
  //     return false;
  //   }

  //   if (colossus_pid->size() != colossus_aid->size() ||
  //       colossus_pid->size() != colossus_duration->size() ||
  //       colossus_pid->size() != colossus_play->size() || colossus_pid->size() != colossus_tag->size() ||
  //       colossus_pid->size() != colossus_time->size() || colossus_pid->size() != colossus_label->size()) {
  //     return false;
  //   }

  //   // 声明各个变量，用于存储返回序列
  //   std::vector<int64> pid_list, aid_list, tag_list, play_list, time_list,
  //     label_list, reason_list, timestamp_list;
  //   // 存储满足不同条件的视频的下标
  //   std::vector<int64> interact_index_list, lp_index_list, vp_index_list;

  //   // 输入的 common 特征是按时间由远及近正向排列的，反向遍历序列
  //   for (int i = colossus_aid->size() - 1; i >= 0; i--) {
  //     // 如果出现特征穿越，跳过
  //     if (colossus_time->at(i) >= sample_stamp) {
  //       continue;
  //     }
  //     // 如果当前视频的作者是用户自己, 跳过
  //     if (colossus_aid->at(i) == user_id) {
  //       continue;
  //     }
  //     // 如果当前作品的作者是互关好友, 跳过
  //     if (IsFriend(colossus_aid->at(i), friend_list) == 1) {
  //       continue;
  //     }
  //     // 判断此次请求前 colossus_inter_range 天的视频
  //     // 是否满足 IsInteract (即有用户和视频有互动) 及有效播放条件
  //     if ((sample_stamp - colossus_time->at(i)) < (colossus_inter_range * 86400)) {
  //       if ((IsInteractStrict(colossus_label->at(i)) == 1) &&
  //           (IsValidPlay(colossus_duration->at(i), colossus_play->at(i)) == 1)) {
  //         // 若满足，加入 interact_index_list
  //         interact_index_list.push_back(i);
  //         // 交互优先级 > 长播 > 有效播，因此如果当前视频为交互视频
  //         // 则不会再作为长播或者有效播视频，直接遍历下一个
  //         continue;
  //       }
  //     }
  //     // 判断此次请求前 colossus_lp_range 天的视频
  //     // 是否满足 IsLongPlay (即用户长播当前视频) 条件
  //     if ((sample_stamp - colossus_time->at(i)) < (colossus_lp_range * 86400)) {
  //       if (IsLongPlay(colossus_duration->at(i), colossus_play->at(i)) == 1) {
  //         lp_index_list.push_back(i);
  //         // 交互优先级 > 长播 > 有效播，因此如果当前视频为长播视频
  //         // 则不会再作为有效播视频，直接遍历下一个
  //         continue;
  //       }
  //     }
  //     // 判断此次请求前 colossus_lp_range 天的视频
  //     // 是否满足 IsLongPlay (即用户有效播当前视频) 条件
  //     if ((sample_stamp - colossus_time->at(i)) < (colossus_vp_range * 86400)) {
  //       if (IsValidPlay(colossus_duration->at(i), colossus_play->at(i)) == 1) {
  //         vp_index_list.push_back(i);
  //         continue;
  //       }
  //     }
  //     // 输入的 common 特征是按时间由远及近正向排列的
  //     // 在反向遍历时，如果当前元素时间戳之差大于阈值
  //     // 再继续反向遍历的元素还是都大于阈值，因此可以直接跳出循环
  //     if ((sample_stamp - colossus_time->at(i)) >= (colossus_inter_range * 86400) &&
  //         (sample_stamp - colossus_time->at(i)) >= (colossus_lp_range * 86400) &&
  //         (sample_stamp - colossus_time->at(i)) >= (colossus_vp_range * 86400)) {
  //       break;
  //     }
  //   }

  //   bool error_flag = true;
  //   // 首先遍历互动视频下标序列
  //   if (!interact_index_list.empty() && interact_index_list.size() > 0) {
  //     error_flag = GetColossusResultWithTimestamp(
  //         colossus_pid, colossus_aid, colossus_tag, colossus_play, colossus_duration, colossus_time,
  //         colossus_label, &pid_list, &aid_list, &tag_list, &play_list, &time_list, &label_list,
  //         &reason_list, &timestamp_list, interact_index_list, colossus_list_max_len,
  //         pid_list.size(), sample_stamp, 1);
  //     if (error_flag == false) {
  //       return false;
  //     }
  //   }

  //   // 如果互动视频的长度小于 colossus_list_min_len
  //   // 遍历满足长播条件的视频下标序列
  //   if (pid_list.size() < colossus_list_min_len) {
  //     if (!lp_index_list.empty() && lp_index_list.size() > 0) {
  //       error_flag = GetColossusResultWithTimestamp(
  //           colossus_pid, colossus_aid, colossus_tag, colossus_play, colossus_duration, colossus_time,
  //           colossus_label, &pid_list, &aid_list, &tag_list, &play_list, &time_list, &label_list,
  //           &reason_list, &timestamp_list, lp_index_list, colossus_list_min_len,
  //           pid_list.size(), sample_stamp, 2);
  //       if (error_flag == false) {
  //         return false;
  //       }
  //     }
  //   }

  //   // 如果互动 + 长播视频的长度还是小于 colossus_list_min_len
  //   // 遍历满足有效播条件的视频下标序列
  //   if (pid_list.size() < colossus_list_min_len) {
  //     if (!vp_index_list.empty() && vp_index_list.size() > 0) {
  //       error_flag = GetColossusResultWithTimestamp(
  //           colossus_pid, colossus_aid, colossus_tag, colossus_play, colossus_duration, colossus_time,
  //           colossus_label, &pid_list, &aid_list, &tag_list, &play_list, &time_list, &label_list,
  //           &reason_list, &timestamp_list, vp_index_list, colossus_list_min_len,
  //           pid_list.size(), sample_stamp, 3);
  //       if (error_flag == false) {
  //         return false;
  //       }
  //     }
  //   }

  //   if (pid_list.empty() || pid_list.size() == 0) {
  //     return false;
  //   }

  //   context.SetIntListCommonAttr("colossus_recent_pids", std::move(pid_list));
  //   context.SetIntListCommonAttr("colossus_recent_aids", std::move(aid_list));
  //   context.SetIntListCommonAttr("colossus_recent_tags", std::move(tag_list));
  //   context.SetIntListCommonAttr("colossus_recent_play", std::move(play_list));
  //   context.SetIntListCommonAttr("colossus_recent_time", std::move(time_list));
  //   context.SetIntListCommonAttr("colossus_recent_label", std::move(label_list));
  //   context.SetIntListCommonAttr("colossus_recent_reason", std::move(reason_list));
  //   context.SetIntListCommonAttr("colossus_recent_timestamp", std::move(timestamp_list));
  //   return true;
  // }

  static bool EnrichColossusConsumeList(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
    // 获取 timestampMs 特征
    auto timestampMs = context.GetIntCommonAttr("timestampMs").value_or(0);
    // timestampMs 毫秒转换为秒
    double sample_stamp = (double)timestampMs / 1000;
    // 获取相关 common 特征
    auto colossus_inter_lp_range = context.GetIntCommonAttr("colossus_inter_range").value_or(-1);
    // auto colossus_lp_range = context.GetIntCommonAttr("colossus_lp_range").value_or(-1);
    // auto colossus_vp_range = context.GetIntCommonAttr("colossus_vp_range").value_or(-1);
    auto colossus_list_max_len = context.GetIntCommonAttr("colossus_list_max_len").value_or(-1);
    // auto colossus_list_min_len = context.GetIntCommonAttr("colossus_list_min_len").value_or(-1);
    auto colossus_pid = context.GetIntListCommonAttr("colossus_pid");
    auto colossus_aid = context.GetIntListCommonAttr("colossus_aid");
    auto colossus_duration = context.GetIntListCommonAttr("colossus_duration");
    auto colossus_play = context.GetIntListCommonAttr("colossus_play");
    auto colossus_tag = context.GetIntListCommonAttr("colossus_tag");
    auto colossus_time = context.GetIntListCommonAttr("colossus_time");
    auto colossus_label = context.GetIntListCommonAttr("colossus_label");
    auto push_click_author_90d = context.GetIntListCommonAttr("push_click_author_90d");

    // 判断获取的特征是否异常
    if (colossus_inter_lp_range == -1 ||
        colossus_list_max_len == -1) {
      return false;
    }

    if (!colossus_pid || !colossus_aid || !colossus_duration || !colossus_play || !colossus_tag ||
        !colossus_time || !colossus_label) {
      return false;
    }

    if (colossus_pid->empty() || colossus_aid->empty() || colossus_duration->empty() ||
        colossus_play->empty() || colossus_tag->empty() || colossus_time->empty() ||
        colossus_label->empty()) {
      return false;
    }

    if (colossus_pid->size() != colossus_aid->size() || colossus_pid->size() != colossus_duration->size() ||
        colossus_pid->size() != colossus_play->size() || colossus_pid->size() != colossus_tag->size() ||
        colossus_pid->size() != colossus_time->size() || colossus_pid->size() != colossus_label->size()) {
      return false;
    }

    // 声明各个变量，用于存储返回序列
    std::vector<int64> pid_list, aid_list, tag_list, play_list, time_list, label_list, reason_list;
    // 存储满足不同条件的视频的下标
    int len = 0;

    // 输入的 common 特征是按时间由远及近正向排列的，反向遍历序列
    for (int i = colossus_aid->size() - 1; i >= 0; i--) {
      // 如果出现特征穿越，跳过
      if (colossus_time->at(i) >= sample_stamp) {
        continue;
      }
      // 判断此次请求前 colossus_inter_lp_range 天的视频
      // 是否满足 IsInteract (即有用户和视频有互动) 条件
      double time_diff = sample_stamp - colossus_time->at(i);
      if (time_diff < (colossus_inter_lp_range * 86400)) {
        if (IsInteract(colossus_label->at(i), colossus_aid->at(i), push_click_author_90d) == 1 ||
            IsLongPlay(colossus_duration->at(i), colossus_play->at(i)) == 1) {
          pid_list.push_back(colossus_pid->at(i));
          aid_list.push_back(colossus_aid->at(i));
          tag_list.push_back(colossus_tag->at(i));
          double double_value = static_cast<double>(colossus_play->at(i)) / 3;
          int64 pre_value = static_cast<int64>(static_cast<int64>(std::floor(double_value)) << 24);
          int64 play = pre_value | colossus_duration->at(i);
          play_list.push_back(play);

          int64 time = static_cast<int64>(std::floor(time_diff / 86400));
          time_list.push_back(time);
          label_list.push_back(colossus_label->at(i));
          reason_list.push_back(1);  // reason 固定为 1
          len++;

          if (len >= colossus_list_max_len) {
            break;
          }
        }
      }
    }
    if (pid_list.empty() || pid_list.size() == 0) {
      return false;
    }

    context.SetIntListCommonAttr("colossus_recent_pids", std::move(pid_list));
    context.SetIntListCommonAttr("colossus_recent_aids", std::move(aid_list));
    context.SetIntListCommonAttr("colossus_recent_tags", std::move(tag_list));
    context.SetIntListCommonAttr("colossus_recent_play", std::move(play_list));
    context.SetIntListCommonAttr("colossus_recent_time", std::move(time_list));
    context.SetIntListCommonAttr("colossus_recent_label", std::move(label_list));
    context.SetIntListCommonAttr("colossus_recent_reason", std::move(reason_list));

    return true;
  }

  static std::vector<int64> RandomSample(const std::vector<int64> &item_index_list, int sample_num) {
    if (item_index_list.empty() || sample_num >= item_index_list.size()) {
      return item_index_list;
    }

    // 根据 sample_num 在 item_index_list 中随机采样
    std::vector<int64> result(sample_num);
    std::random_device rd;                           // 用于获取随机数种子
    std::mt19937 gen(rd());                          // 用于产生随机数
    std::vector<int64> temp_list = item_index_list;  // 新建一个副本以保护原始数据
    std::uniform_int_distribution<int64> distribution(0, temp_list.size() - 1);  // 用于定义随机采样范围

    for (int i = 0; i < sample_num && !temp_list.empty(); i++) {
      int64 random_index = distribution(gen);  // 产生随机索引
      // 判断 random_index 是否在 temp_list 下标范围内
      if (random_index >= 0 && random_index < temp_list.size()) {
        result[i] = temp_list[random_index];                // 将值放入结果向量中
        temp_list.erase(temp_list.begin() + random_index);  // 删除已选择的随机元素
        // 重新生成分布以适应变化后的列表长度
        distribution = std::uniform_int_distribution<int64>(0, temp_list.size() - 1);
      } else {
        // 如果 random_index 超出了 temp_list 下标范围，返回空 vector
        return std::vector<int64>();
      }
    }

    return result;
  }

  static bool EnrichMcContextPhoto(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    // 召回结果集的 pid aid hetu_tag event_type recall_reason 序列
    auto context_photo_id_list_all = context.GetIntListCommonAttr("mc_context_photo_id_list_all");
    auto context_author_id_list_all = context.GetIntListCommonAttr("mc_context_author_id_list_all");
    auto context_photo_hetu_tag_list_all = context.GetIntListCommonAttr("mc_context_photo_hetu_tag_list_all");
    auto context_event_type_list_all = context.GetStringListCommonAttr("mc_context_event_type_list_all");
    auto context_recall_reason_list_all = context.GetIntListCommonAttr("mc_context_recall_reason_list_all");
    // 采样数量
    auto context_photo_sample_num = context.GetIntCommonAttr("mc_context_photo_sample_num").value_or(0);
    // 召回结果集的 item 总数
    auto mc_recall_item_num = context.GetIntCommonAttr("mc_recall_item_num").value_or(0);
    // 召回结果集包含的所有 recall_reason
    auto mc_recall_reason_list = context.GetIntListCommonAttr("mc_recall_reason_list");
    // 每种 recall_reason 的 item 数量
    auto mc_recall_reason_count_list = context.GetIntListCommonAttr("mc_recall_reason_count_list");
    // 判断接收参数有无异常值或为空
    if (mc_recall_item_num == 0 || context_photo_sample_num == 0 || !mc_recall_reason_list ||
        !mc_recall_reason_count_list || mc_recall_reason_list->empty() ||
        mc_recall_reason_count_list->empty() ||
        mc_recall_reason_list->size() != mc_recall_reason_count_list->size()) {
      return false;
    }

    if (!context_photo_id_list_all || !context_author_id_list_all || !context_photo_hetu_tag_list_all ||
        !context_event_type_list_all || !context_recall_reason_list_all) {
      return false;
    }

    if (context_photo_id_list_all->empty() || context_author_id_list_all->empty() ||
        context_photo_hetu_tag_list_all->empty() || context_event_type_list_all->empty() ||
        context_recall_reason_list_all->empty()) {
      return false;
    }

    if (context_photo_id_list_all->size() != context_author_id_list_all->size() ||
        context_photo_id_list_all->size() != context_photo_hetu_tag_list_all->size() ||
        context_photo_id_list_all->size() != context_event_type_list_all->size() ||
        context_photo_id_list_all->size() != context_recall_reason_list_all->size()) {
      return false;
    }

    // 定义存储采样结果的 vector
    std::vector<int64> context_photo_id_list, context_author_id_list, context_photo_hetu_tag_list;
    std::vector<std::string> context_event_type_list;
    // 定义存储采样下标的 vector
    std::vector<int64> context_photo_index_list;

    // 如果采样数量大于等于召回结果集的数量，直接返回召回的所有结果
    if (context_photo_sample_num >= mc_recall_item_num) {
      context_photo_id_list.assign(context_photo_id_list_all->begin(), context_photo_id_list_all->end());
      context_author_id_list.assign(context_author_id_list_all->begin(), context_author_id_list_all->end());
      context_photo_hetu_tag_list.assign(context_photo_hetu_tag_list_all->begin(),
                                         context_photo_hetu_tag_list_all->end());
      std::vector<std::string> context_event_type_list_all_vec(context_event_type_list_all->begin(),
                                                               context_event_type_list_all->end());
      context_event_type_list.assign(context_event_type_list_all_vec.begin(),
                                     context_event_type_list_all_vec.end());
    } else {
      // 定义存储每种 recall_reason 采样数量的 vector
      std::vector<int64> mc_recall_reason_sample_num_list(mc_recall_reason_count_list->size());

      // 召回数量转换为 double 类型
      double mc_recall_item_num_double = static_cast<double>(mc_recall_item_num);
      if (mc_recall_item_num_double == 0.0) return false;

      // 遍历 mc_recall_reason_count_list，计算每种 reason 采样数量
      // 每种 reason 的采样数量 = (该 reason 的 item 数量 / 总的 item 数量) * 采样数量
      for (int i = 0; i < mc_recall_reason_count_list->size(); i++) {
        mc_recall_reason_sample_num_list[i] = std::floor(
            (mc_recall_reason_count_list->at(i) / mc_recall_item_num_double) * context_photo_sample_num);
        // 如果向下取整结果为 0, 将采样数量设置为 1, 保证该 reason 有采样结果
        if (mc_recall_reason_sample_num_list[i] == 0) mc_recall_reason_sample_num_list[i] = 1;
      }

      // 定义存储每个 recall_reason item 下标的 map
      folly::F14FastMap<int64, std::vector<int64>> mc_recall_reason_index_map;
      // 将每个 item 的下标存至 map 中相应 reason 对应的 vector
      for (int i = 0; i < context_recall_reason_list_all->size(); i++) {
        mc_recall_reason_index_map[context_recall_reason_list_all->at(i)].push_back(i);
      }

      std::vector<int64> tmp_res;
      // 遍历 mc_recall_reason_sample_num_list
      // 对每种 recall reason 进行采样
      for (int i = 0; i < mc_recall_reason_sample_num_list.size(); i++) {
        // 判断 map 中当前 recall reason 的 vector 是否为空
        // 若有，则存在异常，返回 false
        if (!mc_recall_reason_index_map[mc_recall_reason_list->at(i)].empty()) {
          // 如果当前 recall_reason 的 item 数量小于等于采样数量, 全部采样
          // context_photo_index_list 存储的是被采样的 item 在全部召回结果集中的下标
          if (mc_recall_reason_index_map[mc_recall_reason_list->at(i)].size() <=
              mc_recall_reason_sample_num_list[i]) {
            context_photo_index_list.insert(context_photo_index_list.end(),
                                            mc_recall_reason_index_map[mc_recall_reason_list->at(i)].begin(),
                                            mc_recall_reason_index_map[mc_recall_reason_list->at(i)].end());
          } else {
            // 否则根据当前 reason 的采样数量在该 reason 的所有 item 中随机采样
            tmp_res = RandomSample(mc_recall_reason_index_map[mc_recall_reason_list->at(i)],
                                   mc_recall_reason_sample_num_list[i]);
            if (tmp_res.empty()) {
              // 前面限制了每种 recall reason 至少采样 1 个
              // 如果返回了空说明有异常，返回 false
              return false;
            }
            context_photo_index_list.insert(context_photo_index_list.end(), tmp_res.begin(), tmp_res.end());
          }
        } else {
          return false;
        }
      }

      // 判断采样结果的大小是否为空或大于召回结果集大小
      if (context_photo_index_list.empty()) {
        return false;
      }
      if (context_photo_index_list.size() > context_photo_id_list_all->size()) {
        return false;
      }
      // 将被采样 item 的 attr 存至相应的 vector
      for (int i = 0; i < context_photo_index_list.size(); ++i) {
        // 下标在原始 attr list 的下标范围内才 push back 特征
        if (context_photo_index_list[i] >= 0 &&
            context_photo_index_list[i] < context_photo_id_list_all->size()) {
          context_photo_id_list.push_back(
              static_cast<int64>(context_photo_id_list_all->at(context_photo_index_list[i])));
          context_author_id_list.push_back(
              static_cast<int64>(context_author_id_list_all->at(context_photo_index_list[i])));
          context_photo_hetu_tag_list.push_back(
              static_cast<int64>(context_photo_hetu_tag_list_all->at(context_photo_index_list[i])));
          context_event_type_list.push_back(
              std::string(context_event_type_list_all->at(context_photo_index_list[i])));
        } else {
          // 如果下标不在原始 attr list 的下标范围内
          // 说明存在异常，返回 false
          return false;
        }
      }
    }

    if (context_photo_id_list.empty() || context_author_id_list.empty() ||
        context_photo_hetu_tag_list.empty() || context_event_type_list.empty()) {
      return false;
    }

    context.SetIntListCommonAttr("mc_context_photo_id_list", std::move(context_photo_id_list));
    context.SetIntListCommonAttr("mc_context_author_id_list", std::move(context_author_id_list));
    context.SetIntListCommonAttr("mc_context_photo_hetu_tag_list", std::move(context_photo_hetu_tag_list));
    context.SetStringListCommonAttr("mc_context_event_type_list", std::move(context_event_type_list));

    return true;
  }

  static bool EnrichMcContextPhotoByEventType(const CommonRecoLightFunctionContext &context,
                                              RecoResultConstIter begin, RecoResultConstIter end) {
    // 召回结果集的 pid aid hetu_tag event_type 序列
    auto context_photo_id_list_all = context.GetIntListCommonAttr("mc_context_photo_id_list_all");
    auto context_author_id_list_all = context.GetIntListCommonAttr("mc_context_author_id_list_all");
    auto context_photo_hetu_tag_list_all = context.GetIntListCommonAttr("mc_context_photo_hetu_tag_list_all");
    auto context_event_type_list_all = context.GetStringListCommonAttr("mc_context_event_type_list_all");
    // 待采样的 event_type 类型
    auto push_show_type_one_hundred = context.GetStringListCommonAttr("push_show_type_one_hundred");
    // 召回结果集包含的所有 event_type
    auto mc_recall_event_type_list = context.GetStringListCommonAttr("mc_recall_event_type_list");
    // 召回结果集中每种 event_type 的 item 数量
    auto mc_recall_event_type_count_list = context.GetIntListCommonAttr("mc_recall_event_type_count_list");
    // 采样数量
    auto context_photo_sample_num = context.GetIntCommonAttr("mc_context_photo_sample_num").value_or(0);

    // 判断接收参数有无异常值或为空
    if (context_photo_sample_num == 0 || !mc_recall_event_type_list || !mc_recall_event_type_count_list ||
        mc_recall_event_type_list->empty() || mc_recall_event_type_count_list->empty() ||
        mc_recall_event_type_list->size() != mc_recall_event_type_count_list->size() ||
        !push_show_type_one_hundred || push_show_type_one_hundred->empty()) {
      return false;
    }

    if (!context_photo_id_list_all || !context_author_id_list_all || !context_photo_hetu_tag_list_all ||
        !context_event_type_list_all) {
      return false;
    }

    if (context_photo_id_list_all->empty() || context_author_id_list_all->empty() ||
        context_photo_hetu_tag_list_all->empty() || context_event_type_list_all->empty()) {
      return false;
    }

    if (context_photo_id_list_all->size() != context_author_id_list_all->size() ||
        context_photo_id_list_all->size() != context_photo_hetu_tag_list_all->size() ||
        context_photo_id_list_all->size() != context_event_type_list_all->size()) {
      return false;
    }
    // 获取目标 event_type set
    std::set<std::string> target_event_type_list;
    for (int i = 0; i < push_show_type_one_hundred->size(); i++) {
      target_event_type_list.insert(std::string(push_show_type_one_hundred->at(i)));
    }

    // 定义存储采样结果的 vector
    std::vector<int64> context_photo_id_list, context_author_id_list, context_photo_hetu_tag_list;
    std::vector<std::string> context_event_type_list;
    // 定义存储采样下标的 vector
    std::vector<int64> context_photo_index_list;

    // 计算目标 type 的总召回数
    double target_event_type_item_num = 0.0;
    for (int i = 0; i < mc_recall_event_type_list->size(); i++) {
      if (target_event_type_list.find(std::string(mc_recall_event_type_list->at(i))) !=
          target_event_type_list.end()) {
        target_event_type_item_num =
            target_event_type_item_num + static_cast<double>(mc_recall_event_type_count_list->at(i));
      }
    }

    // 定义存储每个 type item 下标的 map
    folly::F14FastMap<std::string, std::vector<int64>> mc_recall_event_type_index_map;
    // 将每个 item 的下标存至 map 中相应 type 对应的 vector
    for (int i = 0; i < context_event_type_list_all->size(); i++) {
      mc_recall_event_type_index_map[std::string(context_event_type_list_all->at(i))].push_back(i);
    }

    if (context_photo_sample_num >= target_event_type_item_num) {
      for (int i = 0; i < mc_recall_event_type_list->size(); i++) {
        if (target_event_type_list.find(std::string(mc_recall_event_type_list->at(i))) !=
            target_event_type_list.end()) {
          context_photo_index_list.insert(
              context_photo_index_list.end(),
              mc_recall_event_type_index_map[std::string(mc_recall_event_type_list->at(i))].begin(),
              mc_recall_event_type_index_map[std::string(mc_recall_event_type_list->at(i))].end());
        }
      }
    } else {
      // 定义存储每种 event_type 采样数量的 vector
      std::vector<int64> mc_event_type_sample_num_list(mc_recall_event_type_list->size());
      // 遍历 mc_recall_event_type_list，计算每种 event_type 采样数量
      // 每种 event_type 的采样数量 =
      // (该 event_type 的 item 数量 / 总的 target event_type 数量) * 采样数量
      for (int i = 0; i < mc_recall_event_type_list->size(); i++) {
        if (target_event_type_list.find(std::string(mc_recall_event_type_list->at(i))) !=
            target_event_type_list.end()) {
          mc_event_type_sample_num_list[i] =
              std::floor((mc_recall_event_type_count_list->at(i) / target_event_type_item_num) *
                         context_photo_sample_num);
          // 如果向下取整结果为 0, 将采样数量设置为 1, 保证该 event_type 有采样结果
          if (mc_event_type_sample_num_list[i] == 0) mc_event_type_sample_num_list[i] = 1;
        } else {
          mc_event_type_sample_num_list[i] = 0;
        }
      }
      std::vector<int64> tmp_res;
      // 遍历 mc_event_type_sample_num_list
      // 对每种 event_type 进行采样
      for (int i = 0; i < mc_event_type_sample_num_list.size(); i++) {
        if (mc_event_type_sample_num_list[i] != 0) {
          // 判断 map 中当前 event_type 的 vector 是否为空
          // 若有，则存在异常，返回 false
          if (!mc_recall_event_type_index_map[std::string(mc_recall_event_type_list->at(i))].empty()) {
            // 如果当前 event_type 的 item 数量小于等于采样数量, 全部采样
            // context_photo_index_list 存储的是被采样的 item 在全部召回结果集中的下标
            if (mc_recall_event_type_index_map[std::string(mc_recall_event_type_list->at(i))].size() <=
                mc_event_type_sample_num_list[i]) {
              context_photo_index_list.insert(
                  context_photo_index_list.end(),
                  mc_recall_event_type_index_map[std::string(mc_recall_event_type_list->at(i))].begin(),
                  mc_recall_event_type_index_map[std::string(mc_recall_event_type_list->at(i))].end());
            } else {
              // 否则根据当前 event_type 的采样数量在该 event_type 的所有 item 中随机采样
              tmp_res =
                  RandomSample(mc_recall_event_type_index_map[std::string(mc_recall_event_type_list->at(i))],
                               mc_event_type_sample_num_list[i]);
              if (tmp_res.empty()) {
                // 前面限制了每种 event_type 至少采样 1 个
                // 如果返回了空说明有异常，返回 false
                return false;
              }
              context_photo_index_list.insert(context_photo_index_list.end(), tmp_res.begin(), tmp_res.end());
            }
          } else {
            return false;
          }
        }
      }
    }
    // 判断采样结果的大小是否为空或大于召回结果集大小
    if (context_photo_index_list.empty()) {
      return false;
    }
    if (context_photo_index_list.size() > context_photo_id_list_all->size()) {
      return false;
    }
    // 将被采样 item 的 attr 存至相应的 vector
    for (int i = 0; i < context_photo_index_list.size(); ++i) {
      // 下标在原始 attr list 的下标范围内才 push back 特征
      if (context_photo_index_list[i] >= 0 &&
          context_photo_index_list[i] < context_photo_id_list_all->size()) {
        context_photo_id_list.push_back(
            static_cast<int64>(context_photo_id_list_all->at(context_photo_index_list[i])));
        context_author_id_list.push_back(
            static_cast<int64>(context_author_id_list_all->at(context_photo_index_list[i])));
        context_photo_hetu_tag_list.push_back(
            static_cast<int64>(context_photo_hetu_tag_list_all->at(context_photo_index_list[i])));
        context_event_type_list.push_back(
            std::string(context_event_type_list_all->at(context_photo_index_list[i])));
      } else {
        // 如果下标不在原始 attr list 的下标范围内
        // 说明存在异常，返回 false
        return false;
      }
    }

    if (context_photo_id_list.empty() || context_author_id_list.empty() ||
        context_photo_hetu_tag_list.empty() || context_event_type_list.empty()) {
      return false;
    }

    context.SetIntListCommonAttr("mc_context_photo_id_list", std::move(context_photo_id_list));
    context.SetIntListCommonAttr("mc_context_author_id_list", std::move(context_author_id_list));
    context.SetIntListCommonAttr("mc_context_photo_hetu_tag_list", std::move(context_photo_hetu_tag_list));
    context.SetStringListCommonAttr("mc_context_event_type_list", std::move(context_event_type_list));

    return true;
  }

  static bool EnrichUserInterestPhotosContentTag(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    // user_interest_photos_tag_info_list, 每个 pid 可能对应多个 tag_info
    // [147945851409|3046454:1.001;145777218525|633871:1.04,3423767:1.009,633638:1.004]
    auto user_interest_photos_tag_info_list =
        context.GetStringListCommonAttr("uUserInterestPhotosTagIdAndScoreList");
    auto photo_tag_max_num = context.GetIntCommonAttr("photo_tag_max_num").value_or(0);
    auto interest_photo_max_num = context.GetIntCommonAttr("interest_photo_max_num").value_or(0);

    if (!user_interest_photos_tag_info_list ||
        user_interest_photos_tag_info_list->empty() ||
        user_interest_photos_tag_info_list->size() == 0 ||
        photo_tag_max_num == 0 ||
        interest_photo_max_num == 0) return false;

    std::vector<int64> user_interest_photos_pid_list;
    std::vector<int64> user_interest_photos_tag_id_list;
    std::vector<double> user_interest_photos_tag_score_list;

    std::vector<std::string> current_photo_tag_info_list;
    current_photo_tag_info_list.clear();
    std::vector<std::string> current_tag_info_split_result;
    current_tag_info_split_result.clear();
    int current_photo_tag_num;
    std::string pid_str;
    int64 pid;
    std::string tag_id_str;
    int64 tag_id;
    std::string tag_score_str;
    double tag_score;

    for (int i = 0; i < user_interest_photos_tag_info_list->size(); i++) {
      // 记录每个 photo 的 tag 数量
      current_photo_tag_num = 0;
      // 通过 | 符号将 pid 与其对应的 tag_info 分隔开
      size_t sep_pos = user_interest_photos_tag_info_list->at(i).find("|");
      pid_str = std::string(user_interest_photos_tag_info_list->at(i)).substr(0, sep_pos);
      if (absl::SimpleAtoi(pid_str, &pid)) {
        user_interest_photos_pid_list.push_back(pid);
      } else {
        continue;
      }
      // 通过 , 符号将 tag_info_list 的多个 tag 信息分隔开
      folly::split(
        ',',
        std::string(user_interest_photos_tag_info_list->at(i)).substr(sep_pos+1),
        current_photo_tag_info_list);
      // 遍历每个 tag 信息, 得到相应的 tag_id 和 tag_score
      for (int j = 0; j < current_photo_tag_info_list.size(); j++) {
        // 每个 tag 信息的格式为 tag_id:tag_score
        folly::split(':', current_photo_tag_info_list[j], current_tag_info_split_result);
        if (current_tag_info_split_result.size() == 2) {
          tag_id_str = current_tag_info_split_result[0];
          tag_score_str = current_tag_info_split_result[1];
          // 类型转换
          if (absl::SimpleAtoi(tag_id_str, &tag_id) &&
                absl::SimpleAtod(tag_score_str, &tag_score)) {
            user_interest_photos_tag_id_list.push_back(tag_id);
            user_interest_photos_tag_score_list.push_back(tag_score);
            current_photo_tag_num++;
          }
        }
        current_tag_info_split_result.clear();
        // 如果当前视频的 tag 数量达到阈值, 不再继续遍历
        if (current_photo_tag_num >= photo_tag_max_num) break;
      }

      // 如果当前视频的 tag 数量未达到阈值, 执行 padding 操作
      if (current_photo_tag_num < photo_tag_max_num) {
        for (int k = 0; k < (photo_tag_max_num - current_photo_tag_num); k++) {
          user_interest_photos_tag_id_list.push_back(0);
          user_interest_photos_tag_score_list.push_back(0.0);
        }
      }
      current_photo_tag_info_list.clear();
      // 如果用户感兴趣视频的数量达到阈值, 不再继续遍历
      if (user_interest_photos_pid_list.size() >= interest_photo_max_num) break;
    }

    // 如果用户感兴趣视频的数量小于阈值, 执行 padding 操作
    int current_interest_photos_size = user_interest_photos_pid_list.size();
    if (current_interest_photos_size < interest_photo_max_num) {
      for (int m = 0; m < (interest_photo_max_num - current_interest_photos_size); m++) {
        user_interest_photos_pid_list.push_back(0);
        for (int n = 0; n < photo_tag_max_num; n++) {
          user_interest_photos_tag_id_list.push_back(0);
          user_interest_photos_tag_score_list.push_back(0.0);
        }
      }
    }

    if (user_interest_photos_pid_list.empty() ||
        user_interest_photos_pid_list.size() == 0 ||
        user_interest_photos_tag_id_list.empty() ||
        user_interest_photos_tag_id_list.size() == 0 ||
        user_interest_photos_tag_score_list.empty() ||
        user_interest_photos_tag_score_list.size() == 0) {
      return false;
    }

    // 所有视频的 tag 数量都会 padding 到 photo_tag_max_num, 如果未能整除说明有异常
    if (user_interest_photos_tag_id_list.size() % photo_tag_max_num != 0) {
      return false;
    }

    if (user_interest_photos_tag_id_list.size() !=
        (photo_tag_max_num * user_interest_photos_pid_list.size())) {
      return false;
    }

    context.SetIntCommonAttr("debug_flag",
                                    std::move(1));
    context.SetIntListCommonAttr("user_interest_photos_pid_list",
                                    std::move(user_interest_photos_pid_list));
    context.SetIntListCommonAttr("user_interest_photos_content_tag_id_list",
                                    std::move(user_interest_photos_tag_id_list));
    context.SetDoubleListCommonAttr("user_interest_photos_content_tag_score_list",
                                 std::move(user_interest_photos_tag_score_list));
    return true;
  }

  static bool EnrichAllianceInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    // import common attr
    auto alliance_app_active_top20_str_list =
        context.GetStringListCommonAttr("alliance_app_active_top20_str_list");

    if (!alliance_app_active_top20_str_list || alliance_app_active_top20_str_list->empty()) return false;

    std::vector<std::string> alliance_app_active_top20_list;
    std::vector<int64> alliance_app_active_top20_duration_bkt;
    char sep = ':';
    int64 duration_int;

    for (int i = 0; i < alliance_app_active_top20_str_list->size(); i++) {
      std::vector<std::string> split_result;
      folly::split(sep, std::string(alliance_app_active_top20_str_list->at(i)).data(), split_result);
      if (split_result.size() == 2) {
        alliance_app_active_top20_list.push_back(split_result[0]);
        if (absl::SimpleAtoi(split_result[1], &duration_int)) {
          alliance_app_active_top20_duration_bkt.push_back(std::floor(NumBucket(duration_int)));
        } else {
          alliance_app_active_top20_duration_bkt.push_back(0);
        }
      }
    }
    if (alliance_app_active_top20_list.empty() || alliance_app_active_top20_list.size() == 0 ||
        alliance_app_active_top20_duration_bkt.empty() ||
        alliance_app_active_top20_duration_bkt.size() == 0) {
      return false;
    }
    context.SetStringListCommonAttr("alliance_app_active_top20_list",
                                    std::move(alliance_app_active_top20_list));
    context.SetIntListCommonAttr("alliance_app_active_top20_duration_bkt",
                                 std::move(alliance_app_active_top20_duration_bkt));
    return true;
  }

  static bool EnrichPushIDList(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                               RecoResultConstIter end) {
    auto push_id_accessor = context.GetStringItemAttr("push_id");

    std::vector<int64> push_id_list;
    int size = std::distance(begin, end);
    push_id_list.reserve(size);
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto push_id = push_id_accessor(result).value_or("");
      if (push_id.size() <= 0) {
        // LOG(WARNING) << "push_id is nullopt";
      } else {
        int push_id_int = 0;
        if (absl::SimpleAtoi(push_id, &push_id_int)) {
          push_id_list.push_back(push_id_int);
        } else {
          LOG(WARNING) << "push_id is SimpleAtoi error. push_id:" << push_id;
        }
        // push_id_list.push_back(std::stoll(push_id));
      }
    });
    // export
    context.SetIntListCommonAttr("push_id_list", std::move(push_id_list));
    return true;
  }

  static bool is_emoji(wchar_t ch) {
    return (ch >= 0x1F600 && ch <= 0x1F64F) ||  // 表情符号
          (ch >= 0x1F300 && ch <= 0x1F5FF) ||  // 杂项符号和象形文字
          (ch >= 0x1F680 && ch <= 0x1F6FF) ||  // 交通和地图符号
          (ch >= 0x2600 && ch <= 0x26FF) ||  // 杂项符号
          (ch >= 0x2700 && ch <= 0x27BF) ||  // 装饰符号
          (ch >= 0xFE00 && ch <= 0xFE0F) ||  // 变体选择器
          (ch >= 0x1F900 && ch <= 0x1F9FF) ||  // 补充符号和图形
          (ch >= 0x1F1E6 && ch <= 0x1F1FF);  // 地区指示符号
  }

  static std::wstring caption_filter(const std::wstring &caption_input) {
    std::wstring caption = caption_input;
    // 如果是空直接返回
    if (caption.empty() || caption == L"") return L"";
    // 去除 photo caption 的 @ 信息
    std::wregex atPattern(L"@+\\S*");
    caption = std::regex_replace(caption, atPattern, L"");
    // 将连续的 # 替换成一个 #
    std::wregex hashPattern(L"#+");
    caption = std::regex_replace(caption, hashPattern, L"#");
    // 将 \n, \t, \r 替换为空格
    std::wregex controlPattern(L"[\n\t\r]");
    caption = std::regex_replace(caption, controlPattern, L" ");
    // 将多个连续空格替换为单个空格
    std::wregex spacePattern(L" +");
    caption = std::regex_replace(caption, spacePattern, L" ");

    return caption;
  }

  static bool EnrichUserInterestPhotosTextInfo(const CommonRecoLightFunctionContext &context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
    // 用户高消费率视频的 id 特征和行为特征
    auto u_interest_photos_pid_list = context.GetIntListCommonAttr("uUserInterestPhotosPidList");
    auto u_interest_photos_aid_list = context.GetIntListCommonAttr("uUserInterestPhotosAidList");
    auto u_interest_photos_time_list = context.GetIntListCommonAttr("uUserInterestPhotosTimeList");
    auto u_interest_photos_play_list = context.GetIntListCommonAttr("uUserInterestPhotosPlayList");
    auto u_interest_photos_label_list = context.GetIntListCommonAttr("uUserInterestPhotosLabelList");
    auto u_interest_photos_reason_list = context.GetIntListCommonAttr("uUserInterestPhotosReasonList");
    // 用户高消费率视频的文本特征
    auto u_interest_photos_author_name_list =
        context.GetStringListCommonAttr("uUserInterestPhotosAuthorNameList");
    auto u_interest_photos_caption_list =
        context.GetStringListCommonAttr("uUserInterestPhotosCaptionList");
    auto u_interest_photos_keywords_id_info_list =
        context.GetStringListCommonAttr("uUserInterestPhotosKeywordsIdInfoList");
    // 用户高消费率视频的序列长度限制
    auto u_interest_photos_max_num = context.GetIntCommonAttr("u_interest_photos_max_num").value_or(0);
    // 每个视频的 keywords 长度限制
    auto photo_keywords_max_num = context.GetIntCommonAttr("u_interest_photos_keywords_max_num").value_or(0);

    if (photo_keywords_max_num == 0 || u_interest_photos_max_num == 0) return false;

    // 判断用户高消费率视频序列是否为空
    if (!u_interest_photos_pid_list || u_interest_photos_pid_list->empty() ||
        !u_interest_photos_aid_list || u_interest_photos_aid_list->empty() ||
        !u_interest_photos_time_list || u_interest_photos_time_list->empty() ||
        !u_interest_photos_play_list || u_interest_photos_play_list->empty() ||
        !u_interest_photos_label_list || u_interest_photos_label_list->empty() ||
        !u_interest_photos_reason_list || u_interest_photos_reason_list->empty() ||
        !u_interest_photos_author_name_list || u_interest_photos_author_name_list->empty() ||
        !u_interest_photos_caption_list || u_interest_photos_caption_list->empty() ||
        !u_interest_photos_keywords_id_info_list || u_interest_photos_keywords_id_info_list->empty()
    ) return false;

    // 特征应该是一一对应的, 如果序列长度不一致说明数据异常
    int list_size = u_interest_photos_pid_list->size();
    if (list_size != u_interest_photos_aid_list->size() ||
        list_size != u_interest_photos_time_list->size() ||
        list_size != u_interest_photos_play_list->size() ||
        list_size != u_interest_photos_label_list->size() ||
        list_size != u_interest_photos_reason_list->size() ||
        list_size != u_interest_photos_author_name_list->size() ||
        list_size != u_interest_photos_caption_list->size() ||
        list_size != u_interest_photos_keywords_id_info_list->size()
    ) return false;

    std::vector<int64> u_interest_photos_photo_id_list, u_interest_photos_author_id_list,
        u_interest_photos_interact_time_list, u_interest_photos_play_time_list,
        u_interest_photos_interact_label_list, u_interest_photos_interact_reason_list,
        u_interest_photos_keywords_id_list;

    std::vector<std::string> u_interest_photos_aid_name_list, u_interest_photos_pid_caption_list;
    std::vector<double> u_interest_photos_keywords_score_list, u_interest_photos_mask_list;

    std::vector<std::string> current_photo_keywords_info_list;
    current_photo_keywords_info_list.clear();
    std::vector<std::string> current_keyword_split_result;
    current_keyword_split_result.clear();
    int current_photo_keywords_num;
    int64 keyword_id;
    double keyword_score;
    // 如果限制的长度小于序列长度, 取最新日期的数据(序列是按时间升序的)
    int begin_idx = 0;
    if (list_size > u_interest_photos_max_num) begin_idx = list_size - u_interest_photos_max_num;
    for (int i = begin_idx; i < list_size; i++) {
      u_interest_photos_mask_list.push_back(1.0);
      u_interest_photos_photo_id_list.push_back(u_interest_photos_pid_list->at(i));
      u_interest_photos_author_id_list.push_back(u_interest_photos_aid_list->at(i));
      u_interest_photos_interact_time_list.push_back(u_interest_photos_time_list->at(i));
      u_interest_photos_play_time_list.push_back(u_interest_photos_play_list->at(i));
      u_interest_photos_interact_label_list.push_back(u_interest_photos_label_list->at(i));
      u_interest_photos_interact_reason_list.push_back(u_interest_photos_reason_list->at(i));
      std::string author_name(u_interest_photos_author_name_list->at(i));
      u_interest_photos_aid_name_list.push_back(author_name);
      // 处理 photo caption
      std::string photo_caption(u_interest_photos_caption_list->at(i));
      std::wstring_convert<std::codecvt_utf8<wchar_t>> caption_converter;
      std::wstring photo_caption_wstring = caption_converter.from_bytes(photo_caption);
      std::wstring photo_caption_result = caption_filter(photo_caption_wstring);
      u_interest_photos_pid_caption_list.push_back(caption_converter.to_bytes(photo_caption_result));
      // 处理视频的 keywords
      std::string keywords_info(u_interest_photos_keywords_id_info_list->at(i));
      // 记录视频的 keywords 数量
      current_photo_keywords_num = 0;
      // 通过逗号将多个 keywords 信息分隔开
      folly::split(',', keywords_info, current_photo_keywords_info_list);
      // 遍历 keywords, 得到相应的 id 和 score
      for (int j = 0; j < current_photo_keywords_info_list.size(); j++) {
        // 每个 keyword 信息的格式为 id:score
        folly::split(':', current_photo_keywords_info_list[j], current_keyword_split_result);
        if (current_keyword_split_result.size() == 2) {
          std::string keyword_id_str = current_keyword_split_result[0];
          std::string keyword_score_str = current_keyword_split_result[1];
          // 类型转换
          if (absl::SimpleAtoi(keyword_id_str, &keyword_id) &&
              absl::SimpleAtod(keyword_score_str, &keyword_score)) {
            u_interest_photos_keywords_id_list.push_back(keyword_id);
            u_interest_photos_keywords_score_list.push_back(keyword_score);
            current_photo_keywords_num++;
          }
        }
        current_keyword_split_result.clear();
        // 如果当前视频的 keywords 数量达到阈值, 不再继续遍历
        if (current_photo_keywords_num >= photo_keywords_max_num) break;
      }

      // 如果当前视频的 keywords 数量未达到阈值, 执行 padding 操作
      if (current_photo_keywords_num < photo_keywords_max_num) {
        for (int k = 0; k < (photo_keywords_max_num - current_photo_keywords_num); k++) {
          u_interest_photos_keywords_id_list.push_back(0);
          u_interest_photos_keywords_score_list.push_back(0.0);
        }
      }
      current_photo_keywords_info_list.clear();
    }

    // 如果用户感兴趣视频的数量小于阈值, 记录 msk 部分
    int current_interest_photos_size = u_interest_photos_photo_id_list.size();
    if (current_interest_photos_size < u_interest_photos_max_num) {
      for (int m = 0; m < (u_interest_photos_max_num - current_interest_photos_size); m++) {
        u_interest_photos_mask_list.push_back(0.0);
      }
    }

    context.SetIntListCommonAttr("u_interest_photos_photo_id_list",
                                    std::move(u_interest_photos_photo_id_list));
    context.SetIntListCommonAttr("u_interest_photos_author_id_list",
                                    std::move(u_interest_photos_author_id_list));
    context.SetIntListCommonAttr("u_interest_photos_interact_time_list",
                                    std::move(u_interest_photos_interact_time_list));
    context.SetIntListCommonAttr("u_interest_photos_play_time_list",
                                    std::move(u_interest_photos_play_time_list));
    context.SetIntListCommonAttr("u_interest_photos_interact_label_list",
                                    std::move(u_interest_photos_interact_label_list));
    context.SetIntListCommonAttr("u_interest_photos_interact_reason_list",
                                    std::move(u_interest_photos_interact_reason_list));
    context.SetStringListCommonAttr("u_interest_photos_aid_name_list",
                                    std::move(u_interest_photos_aid_name_list));
    context.SetStringListCommonAttr("u_interest_photos_pid_caption_list",
                                    std::move(u_interest_photos_pid_caption_list));
    context.SetIntListCommonAttr("u_interest_photos_keywords_id_list",
                                    std::move(u_interest_photos_keywords_id_list));
    context.SetDoubleListCommonAttr("u_interest_photos_keywords_score_list",
                                    std::move(u_interest_photos_keywords_score_list));
    context.SetDoubleListCommonAttr("u_interest_photos_mask_list",
                                    std::move(u_interest_photos_mask_list));
    return true;
  }

  static bool EnrichUserInterestPhotosTruncate(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
    // 用户兴趣视频的 id 特征
    auto u_interest_photos_pid_list = context.GetIntListCommonAttr("uUserInterestPhotosPidList");
    auto u_interest_photos_aid_list = context.GetIntListCommonAttr("uUserInterestPhotosAidList");
    // 用户兴趣视频的文本特征
    auto u_interest_photos_author_name_list =
      context.GetStringListCommonAttr("uUserInterestPhotosAuthorNameList");
    auto u_interest_photos_caption_list =
      context.GetStringListCommonAttr("uUserInterestPhotosCaptionList");
    // 用户兴趣视频的序列长度限制
    auto u_interest_photos_max_num = context.GetIntCommonAttr("u_interest_photos_max_num").value_or(0);
    // 判断用户兴趣视频序列是否为空或者长度限制是否为 0
    if (!u_interest_photos_pid_list || u_interest_photos_pid_list->empty() ||
      !u_interest_photos_aid_list || u_interest_photos_aid_list->empty() ||
      !u_interest_photos_author_name_list || u_interest_photos_author_name_list->empty() ||
      !u_interest_photos_caption_list || u_interest_photos_caption_list->empty() ||
      u_interest_photos_max_num <= 0
    ) return false;

    // 特征应该是一一对应的, 如果序列长度不一致说明数据异常
    int list_size = u_interest_photos_pid_list->size();
    if (list_size != u_interest_photos_aid_list->size() ||
      list_size != u_interest_photos_author_name_list->size() ||
      list_size != u_interest_photos_caption_list->size()
    ) return false;

    std::vector<int64> u_interest_photos_photo_id_list, u_interest_photos_author_id_list;
    std::vector<std::string> u_interest_photos_aid_name_list, u_interest_photos_pid_caption_list;

    // 如果限制的长度小于序列长度, 取最新日期的数据(序列是按时间升序的)
    int begin_idx = 0;
    if (list_size > u_interest_photos_max_num) begin_idx = list_size - u_interest_photos_max_num;
    for (int i = begin_idx; i < list_size; i++) {
      u_interest_photos_photo_id_list.push_back(u_interest_photos_pid_list->at(i));
      u_interest_photos_author_id_list.push_back(u_interest_photos_aid_list->at(i));
      std::string author_name(u_interest_photos_author_name_list->at(i));
      u_interest_photos_aid_name_list.push_back(author_name);
      std::string photo_caption(u_interest_photos_caption_list->at(i));
      u_interest_photos_pid_caption_list.push_back(photo_caption);
    }

    context.SetIntListCommonAttr("u_interest_photos_photo_id_list",
            std::move(u_interest_photos_photo_id_list));
    context.SetIntListCommonAttr("u_interest_photos_author_id_list",
            std::move(u_interest_photos_author_id_list));
    context.SetStringListCommonAttr("u_interest_photos_aid_name_list",
            std::move(u_interest_photos_aid_name_list));
    context.SetStringListCommonAttr("u_interest_photos_pid_caption_list",
            std::move(u_interest_photos_pid_caption_list));
    return true;
  }

  static bool EnrichCommonSegmentResultConcat(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
    // 输入特征举例:
    // 文字: [ 我, 爱, 中国, [TEXT_SEP], 央视网, [NAME_SEP], 奥运会, [TEXT_SEP], 新闻, 频道, [NAME_SEP] ]
    // token_id: [ 101, 102, 103, 5, 104 6, 105, 5, 106, 107, 6 ]
    // tokens_len: [ 4, 2, 2, 3 ]

    // 用户所有兴趣视频的 [ 标题分词结果 , 作者名分词结果 ] 拼接在一起
    auto token_ids = context.GetIntListCommonAttr("u_text_and_name_ids_list");
    // 用户所有兴趣视频的 [ 标题分词长度 , 作者名分词长度 ] 拼接在一起
    auto tokens_len = context.GetIntListCommonAttr("u_text_and_name_ids_len_list");

    int tokens_len_sum = 0;
    if (tokens_len && tokens_len->size() > 0) {
      tokens_len_sum = std::accumulate(tokens_len->begin(), tokens_len->end(), 0);
    }

    // 异常处理
    if (!token_ids || token_ids->empty() || !tokens_len || tokens_len->empty() ||
      tokens_len->size() % 2 != 0 || tokens_len_sum == 0 || token_ids->size() != tokens_len_sum) {
      // 返回默认初始化值
      std::vector<int64> token_ids_res = {1, 5, 6};
      std::vector<int64> token_type_segment_res = {0, 0, 1};
      std::vector<int64> token_pos_segment_res = {0, 1, 2};
      std::vector<double> tokens_real_len_res = {3.0};
      std::vector<int64> event_type_token_type_segment_res = {2, 2};
      context.SetIntListCommonAttr("u_interest_photos_token_ids", std::move(token_ids_res));
      context.SetIntListCommonAttr("u_interest_photos_token_type_segment", std::move(token_type_segment_res));
      context.SetIntListCommonAttr("u_interest_photos_token_pos_segment", std::move(token_pos_segment_res));
      context.SetDoubleListCommonAttr("u_interest_photos_tokens_real_len_double_list",
        std::move(tokens_real_len_res));
      context.SetIntCommonAttr("item_token_begin_pos", std::move(3));
      context.SetIntCommonAttr("event_type_sep_token_id", std::move(7));
      context.SetIntListCommonAttr("event_type_token_type_segment",
        std::move(event_type_token_type_segment_res));
      return true;
    }

    // 在整个序列首部加上 [CLS] 对应的 token_id
    std::vector<int64> token_ids_res(token_ids->begin(), token_ids->end());
    token_ids_res.emplace(token_ids_res.begin(), 1);

    // token_type_segment 代表当前 token 属于标题还是作者名还是 event_type, 分别为 0 1 2
    std::vector<int64> token_type_segment_res;
    token_type_segment_res.reserve(tokens_len_sum+1);
    token_type_segment_res.emplace_back(0);
    tokens_len_sum += 1;

    // token_pos_segment 代表当前 token 在整个序列的位置
    std::vector<int64> token_pos_segment_res(tokens_len_sum);
    std::iota(token_pos_segment_res.begin(), token_pos_segment_res.end(), 0);

    // 遍历每个视频的标题和作者名长度
    for (int i = 0; i < tokens_len->size(); i++) {
      int current_len = tokens_len->at(i);
      // 确定当前 tokens 的类型
      int segment_type = i % 2;
      std::vector<int64> current_token_type(current_len, segment_type);
      token_type_segment_res.insert(token_type_segment_res.end(),
        current_token_type.begin(), current_token_type.end());
    }

    // 获取当前序列的实际长度
    std::vector<double> tokens_real_len_res = {static_cast<double>(tokens_len_sum)};

    // 生成 event_type 的 token_type
    std::vector<int64> event_type_token_type_segment_res = {2, 2};

    // 填充特征
    context.SetIntListCommonAttr("u_interest_photos_token_ids",
            std::move(token_ids_res));
    context.SetIntListCommonAttr("u_interest_photos_token_type_segment",
            std::move(token_type_segment_res));
    context.SetIntListCommonAttr("u_interest_photos_token_pos_segment",
            std::move(token_pos_segment_res));
    context.SetDoubleListCommonAttr("u_interest_photos_tokens_real_len_double_list",
            std::move(tokens_real_len_res));
    context.SetIntCommonAttr("item_token_begin_pos", std::move(tokens_len_sum));
    context.SetIntCommonAttr("event_type_sep_token_id", std::move(7));
    context.SetIntListCommonAttr("event_type_token_type_segment",
            std::move(event_type_token_type_segment_res));
    return true;
  }

  static bool EnrichItemSegmentResultConcat(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
    // item [ 标题分词结果 , 作者名分词结果 ] 拼接在一起
    auto token_ids_accessor = context.GetIntListItemAttr("item_text_and_name_ids");
    // item [ 标题分词长度 , 作者名分词长度 ] 拼接在一起
    auto tokens_len_accessor = context.GetIntListItemAttr("item_text_and_name_ids_len");
    // 候选视频的起始位置
    auto item_token_begin_pos = context.GetIntCommonAttr("item_token_begin_pos").value_or(-1);
    // 输出结果
    auto item_token_ids_export = context.SetIntListItemAttr("item_token_ids");
    auto item_token_type_segment_export = context.SetIntListItemAttr("item_token_type_segment");
    auto item_token_pos_segment_export = context.SetIntListItemAttr("item_token_pos_segment");
    auto item_tokens_real_len_export = context.SetDoubleListItemAttr("item_tokens_real_len_double_list");
    auto event_type_token_pos_segment_export = context.SetIntListItemAttr("event_type_token_pos_segment");

    // 遍历每个候选
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto token_ids = token_ids_accessor(result).value_or(std::vector<int64>{});
      auto tokens_len = tokens_len_accessor(result).value_or(std::vector<int64>{});

      // 输入的 token 序列的长度
      int tokens_len_sum = 0;
      if (!tokens_len.empty()) {
        tokens_len_sum = std::accumulate(tokens_len.begin(), tokens_len.end(), 0);
      }

      // 异常处理
      if (token_ids.empty() || tokens_len.empty() ||
        tokens_len.size() != 2 || tokens_len_sum == 0 || token_ids.size() != tokens_len_sum) {
        // 填充特征
        std::vector<int64> token_ids_res = {5, 6};
        std::vector<int64> token_type_segment_res = {0, 1};
        std::vector<int64> token_pos_segment_res = {item_token_begin_pos, item_token_begin_pos+1};
        std::vector<double> tokens_real_len_res = {2.0};
        std::vector<int64> event_type_token_pos_segment_res =
          {item_token_begin_pos+2, item_token_begin_pos+3};
        item_token_ids_export(result, token_ids_res);
        item_token_type_segment_export(result, token_type_segment_res);
        item_token_pos_segment_export(result, token_pos_segment_res);
        item_tokens_real_len_export(result, tokens_real_len_res);
        event_type_token_pos_segment_export(result, event_type_token_pos_segment_res);
        return;
      }

      // 输出结果类型转换
      std::vector<int64> token_ids_res(token_ids.begin(), token_ids.end());

      // token_pos_segment 代表当前 token 在整个序列的位置
      std::vector<int64> token_pos_segment_res(tokens_len_sum);
      std::iota(token_pos_segment_res.begin(), token_pos_segment_res.end(), item_token_begin_pos);

      // token_type_segment 代表当前 token 属于标题还是作者名还是 event_type, 分别为 0 1 2
      std::vector<int64> token_type_segment_res;
      token_type_segment_res.reserve(tokens_len_sum);

      // 遍历每个视频标题和作者名的长度
      for (int i = 0; i < tokens_len.size(); i++) {
        int current_len = tokens_len[i];
        // 确定当前 tokens 的类型
        int segment_type = i % 2;
        std::vector<int64> current_token_type(current_len, segment_type);
        token_type_segment_res.insert(token_type_segment_res.end(),
          current_token_type.begin(), current_token_type.end());
      }

      // 获取当前序列的实际长度
      std::vector<double> tokens_real_len_res = {static_cast<double>(tokens_len_sum)};

      // 生成 event_type 的 token_pos
      int event_type_token_begin_pos = item_token_begin_pos + tokens_len_sum;
      std::vector<int64> event_type_token_pos_segment_res =
        {event_type_token_begin_pos, event_type_token_begin_pos+1};

      // 填充特征
      item_token_ids_export(result, token_ids_res);
      item_token_type_segment_export(result, token_type_segment_res);
      item_token_pos_segment_export(result, token_pos_segment_res);
      item_tokens_real_len_export(result, tokens_real_len_res);
      event_type_token_pos_segment_export(result, event_type_token_pos_segment_res);
    });
    return true;
  }

  static bool EnrichItemTextInfo(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                              RecoResultConstIter end) {
    auto push_text_accessor = context.GetStringItemAttr("push_text_tmp");
    auto export_push_text = context.SetStringItemAttr("push_text_filter");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      std::string push_text(
          push_text_accessor(result).value_or(""));
      // 处理 push text
      try {
        std::wstring_convert<std::codecvt_utf8<wchar_t>> text_converter;
        std::wstring push_text_wstring = text_converter.from_bytes(push_text);
        std::wstring push_text_result = caption_filter(push_text_wstring);
        export_push_text(result, text_converter.to_bytes(push_text_result));
      } catch (const std::range_error& e) {
        export_push_text(result, "");
        LOG(ERROR) << "EnrichItemTextInfo encoding error: " << e.what() << std::endl;
      } catch (const std::exception& e) {
        export_push_text(result, "");
        LOG(ERROR) << "EnrichItemTextInfo standard exception: " << e.what() << std::endl;
      } catch (...) {
        export_push_text(result, "");
        LOG(ERROR) << "EnrichItemTextInfo unknown exception occurred!" << std::endl;
      }
    });
    return true;
  }

  static bool EnrichPushOpenApiTemplate(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    auto push_id_accessor = context.GetStringItemAttr("push_id");
    auto need_callback_accessor = context.GetStringItemAttr("need_callback");
    auto template_title_accessor = context.GetStringItemAttr("template_title");
    auto template_body_accessor = context.GetStringItemAttr("template_body");
    auto template_url_accessor = context.GetStringItemAttr("template_url");
    auto template_switch_type_accessor = context.GetIntItemAttr("template_switch_type");
    auto platform_extra_data_accessor = context.GetStringItemAttr("platform_extra_data");
    auto open_api_template_info_accessor = context.GetStringItemAttr("open_api_template_info");
    auto version_accessor = context.GetIntItemAttr("push_version");
    folly::F14FastMap<std::string, std::pair<int64_t, std::string>> pushid2teplate;
    folly::F14FastMap<std::string, std::pair<std::string, std::string>> pushid2extra_data;
    folly::F14FastMap<std::string, std::string> pushid2need_callback;
    // 遍历所有 item ，找到所有模板，将模板存储到 pushid2teplate
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      if (result.reason == 1061) {
        Json::Value root;
        auto push_id = push_id_accessor(result).value_or("");
        auto need_callback = need_callback_accessor(result).value_or("");
        auto template_title = template_title_accessor(result).value_or("");
        auto template_body = template_body_accessor(result).value_or("");
        auto template_url = template_url_accessor(result).value_or("");
        auto platform_extra_data = platform_extra_data_accessor(result).value_or("");
        auto open_api_template_info = open_api_template_info_accessor(result).value_or("");
        auto template_switch_type = template_switch_type_accessor(result).value_or(0);
        auto version = version_accessor(result).value_or(0);
        if (push_id.size() <= 0) {
          // LOG(WARNING) << "push_id is nullopt";
        } else {
          root["pushid"] = std::string(push_id);
          root["title"] = std::string(template_title);
          root["body"] = std::string(template_body);
          root["url"] = std::string(template_url);
          root["template_switch_type"] = Json::Int64(template_switch_type);
          root["version"] = Json::Int64(version);
          std::string template_json = Json::FastWriter().write(root);
          // LOG(WARNING) <<  "push_id:" << push_id << " "<< template_json;
          // 将模板转为 json，存储到 pushid2teplate，并且保留 version
          pushid2teplate.insert(
              std::make_pair(push_id, std::pair<int64_t, std::string>(version, template_json)));
          pushid2extra_data.insert(std::make_pair(
              push_id, std::pair<std::string, std::string>(platform_extra_data, open_api_template_info)));
          pushid2need_callback.insert(std::make_pair(push_id, need_callback));
        }
      }
    });
    // export
    // 到处模版
    auto template_accessor = context.SetStringItemAttr("openapi_template");
    auto export_platform_extra_data_accessor = context.SetStringItemAttr("platform_extra_data");
    auto export_open_api_template_info_accessor = context.SetStringItemAttr("open_api_template_info");
    auto export_need_callback = context.SetStringItemAttr("need_callback");
    // 模板是否匹配
    auto template_error_accessor = context.SetIntItemAttr("template_error");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      if (result.reason == 1034) {
        auto push_id = push_id_accessor(result).value_or("");
        auto version = version_accessor(result).value_or(-1);
        // 判断 push_id 是否存在
        if (version_accessor(result) == absl::nullopt || push_id.size() <= 0) {
          // LOG(WARNING) << "push_id or version is nullopt";
          template_error_accessor(result, 1);
        } else {
          // 找到当前 item 对应的模板
          auto it = pushid2teplate.find(std::string(push_id));
          if (it != pushid2teplate.end()) {
            // 对比下消息和模板的 version 是否一致
            if (it->second.first != version) {
              template_error_accessor(result, 1);
              LOG(WARNING) << "version is not equal. template_version:" << it->second.first
                           << " version:" << version << " push_id:" << push_id
                           << " template_json:" << it->second.second;
            } else {
              template_accessor(result, it->second.second);
              export_platform_extra_data_accessor(result, pushid2extra_data[std::string(push_id)].first);
              export_open_api_template_info_accessor(result, pushid2extra_data[std::string(push_id)].second);
              export_need_callback(result, pushid2need_callback[std::string(push_id)]);
              template_error_accessor(result, 0);
            }
          } else {
            template_error_accessor(result, 1);
          }
        }
      }
    });
    return true;
  }

  static bool EnrichFriendAuthorId(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    // import common attr
    auto reco_friends_id_list_accessor = context.GetIntListCommonAttr("reco_friends_id_list");
    auto reco_friend_id_accessor = context.GetIntListItemAttr("reco_friend_id");
    // export intem attr
    auto export_author_id_accessor = context.SetIntItemAttr("author_id_attr");

    folly::F14FastSet<int> reco_friend_set{};
    if (reco_friends_id_list_accessor) {
      reco_friend_set.insert(reco_friends_id_list_accessor->begin(), reco_friends_id_list_accessor->end());
    }
    if (reco_friend_set.size() <= 0) {
      return true;
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto reco_friend_id_list = reco_friend_id_accessor(result).value_or(std::vector<int64_t>{});
      if (result.reason == 1059) {
        for (int i = 0; i < reco_friend_id_list.size(); ++i) {
          if (reco_friend_set.find(reco_friend_id_list[i]) != reco_friend_set.end()) {
            export_author_id_accessor(result, reco_friend_id_list[i]);
            break;
          }
        }
      }
    });
    return true;
  }

  static bool EnrichHighPlayPhoto(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    // 获取 timestampMs 特征
    auto timestampMs = context.GetIntCommonAttr("timestampMs").value_or(0);
    // timestampMs 毫秒转换为秒
    double sample_stamp = (double)timestampMs / 1000;
    // 获取 colossus common 特征
    auto colossus_pid = context.GetIntListCommonAttr("colossus_pid");
    auto colossus_aid = context.GetIntListCommonAttr("colossus_aid");
    auto colossus_duration = context.GetIntListCommonAttr("colossus_duration");
    auto colossus_play = context.GetIntListCommonAttr("colossus_play");
    auto colossus_tag = context.GetIntListCommonAttr("colossus_tag");
    auto colossus_time = context.GetIntListCommonAttr("colossus_time");
    auto colossus_label = context.GetIntListCommonAttr("colossus_label");
    // 超参
    auto colossus_high_play_photo_sample_num =
        context.GetIntCommonAttr("colossus_high_play_photo_sample_num").value_or(-1);
    auto colossus_high_play_photo_truncate_num =
        context.GetIntCommonAttr("colossus_high_play_photo_truncate_num").value_or(-1);

    // 判断获取的特征是否异常
    if (colossus_high_play_photo_sample_num == -1 || colossus_high_play_photo_truncate_num == -1) {
      return false;
    }

    if (!colossus_pid || !colossus_aid || !colossus_duration || !colossus_play || !colossus_tag ||
        !colossus_time || !colossus_label) {
      return false;
    }

    if (colossus_pid->empty() || colossus_aid->empty() || colossus_duration->empty() ||
        colossus_play->empty() || colossus_tag->empty() || colossus_time->empty() ||
        colossus_label->empty()) {
      return false;
    }

    if (colossus_pid->size() != colossus_aid->size() || colossus_pid->size() != colossus_duration->size() ||
        colossus_pid->size() != colossus_play->size() || colossus_pid->size() != colossus_tag->size() ||
        colossus_pid->size() != colossus_time->size() || colossus_pid->size() != colossus_label->size()) {
      return false;
    }

    // key 为 photo 下标, value 为当前 photo 与其他 photo 相比满意度更高的次数
    folly::F14FastMap<int, int> photo_pos_cnt_map;
    int cur_photo_num_i = 1;

    // 遍历 colossus 序列
    for (int i = colossus_pid->size() - 1; i >= 0; i--) {
      if (colossus_time->at(i) >= sample_stamp) {
        continue;
      }

      if (cur_photo_num_i > colossus_high_play_photo_truncate_num) break;

      int cur_photo_num_j = 1;
      for (int j = colossus_pid->size() - 1; j >= 0; j--) {
        if (colossus_time->at(j) >= sample_stamp) {
          continue;
        }
        if (cur_photo_num_j > colossus_high_play_photo_truncate_num) break;
        // 判断视频本身时长是否大于 0, 避免分母为 0
        if (colossus_duration->at(i) > 0 && colossus_duration->at(j) > 0) {
          // 如果 duraton_i > duration_j 且 play_ratio_i > play_ratio_j
          // 则定义用户对视频 i 的满意度大于视频 j
          double play_ratio_i = (double)colossus_play->at(i) / colossus_duration->at(i);
          double play_ratio_j = (double)colossus_play->at(j) / colossus_duration->at(j);
          if (colossus_duration->at(i) > colossus_duration->at(j) && play_ratio_i > play_ratio_j) {
            photo_pos_cnt_map[i] += 1;
          }
        }
        cur_photo_num_j += 1;
      }
      cur_photo_num_i += 1;
    }

    // 先将 map 中的元素复制到 vector 中
    std::vector<std::pair<int, int>> vec(photo_pos_cnt_map.begin(), photo_pos_cnt_map.end());
    // 最后返回满意度 top_n 的视频
    int64 top_n = std::min(colossus_high_play_photo_sample_num, static_cast<int64>(vec.size()));
    // 使用 partial_sort 对 vector 进行部分排序，从而获取前 top_n 个最大的元素
    std::partial_sort(
        vec.begin(), vec.begin() + top_n, vec.end(),
        [](const std::pair<int, int> &a, const std::pair<int, int> &b) { return a.second > b.second; });
    // 获取 top_n 视频的特征
    std::vector<int64> pid_list, aid_list, tag_list, play_list, time_list, label_list;
    for (int i = 0; i < top_n; i++) {
      pid_list.push_back(colossus_pid->at(vec[i].first));
      aid_list.push_back(colossus_aid->at(vec[i].first));
      tag_list.push_back(colossus_tag->at(vec[i].first));
      // 对 play 和 duration 特征进行二次处理
      double double_value = static_cast<double>(colossus_play->at(vec[i].first)) / 3;
      int64 pre_value = static_cast<int64>(static_cast<int64>(std::floor(double_value)) << 24);
      int64 play = pre_value | colossus_duration->at(vec[i].first);
      play_list.push_back(play);
      // 对 time 特征进行二次处理，得到该视频据今多少天
      int64 time = std::floor(static_cast<double>(sample_stamp - colossus_time->at(vec[i].first)) / 86400);
      time_list.push_back(time);
      label_list.push_back(colossus_label->at(vec[i].first));
    }

    if (pid_list.empty() || pid_list.size() == 0) {
      return false;
    }

    context.SetIntListCommonAttr("colossus_high_play_pids", std::move(pid_list));
    context.SetIntListCommonAttr("colossus_high_play_aids", std::move(aid_list));
    context.SetIntListCommonAttr("colossus_high_play_tags", std::move(tag_list));
    context.SetIntListCommonAttr("colossus_high_play_play", std::move(play_list));
    context.SetIntListCommonAttr("colossus_high_play_time", std::move(time_list));
    context.SetIntListCommonAttr("colossus_high_play_label", std::move(label_list));

    return true;
  }

  static bool EnrichGenerateJudgmentExecutionFlag(const CommonRecoLightFunctionContext &context,
                                                  RecoResultConstIter begin, RecoResultConstIter end) {
    // 解析 字符串 形如 1,1 前者是登录标志，后者是未登录标志
    // 返回的是执行标志，用于判断该块逻辑是否执行
    auto ab_param_config_str = context.GetStringCommonAttr("AB_PARAM_CONFIG_STR").value_or("");
    auto user_id = context.GetIntCommonAttr("user_id").value_or(0);
    if (ab_param_config_str.size() != 3) {
      LOG(ERROR) << "ab param config string is null or length is not 3, string is " << ab_param_config_str;
      return false;
    }
    std::vector<absl::string_view> config_str_vec = absl::StrSplit(ab_param_config_str, ',');

    if (config_str_vec.size() != 2) {
      LOG(ERROR) << "ab param config string split error, string is " << ab_param_config_str;
      return false;
    }
    size_t flag_idx = user_id == 0 ? 1 : 0;  // 登录取第 0 个下标，未登录取第 1 个下标
    int value = 0;
    if (!absl::SimpleAtoi(config_str_vec[flag_idx], &value)) {
      LOG(ERROR) << "push judge ab param config string to int error, string is " << config_str_vec[flag_idx];
    }
    // commont output
    context.SetIntCommonAttr("executionFlag", value);
    return true;
  }
  static bool EnrichFilterTagPhotoTypeExceed(const CommonRecoLightFunctionContext &context,
                                             RecoResultConstIter begin, RecoResultConstIter end) {
    // input
    auto single_push_type_limit = context.GetIntCommonAttr("single_push_type_limit").value_or(0);
    auto push_type_accessor = context.GetStringItemAttr("push_type");
    auto item_type_accessor = context.GetIntItemAttr("item_type");
    // output
    auto photo_type_exceed_tag_accessor = context.SetIntItemAttr("photo_type_exceed_tag");
    // local val
    std::unordered_map<std::string, int> photo_type_num_map;
    // num check
    if (single_push_type_limit <= 0) {
      LOG(ERROR) << "EnrichFilterTagPhotoTypeExceed::single_push_type_limit <= 0! single_push_type_limit = "
                 << single_push_type_limit;
      return false;
    }
    int filter_num = 1;
    int not_filter_num = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto push_type = std::string(push_type_accessor(result).value_or(""));
      auto item_type = item_type_accessor(result).value_or(-1);
      if (item_type == 0 || item_type == -1 || item_type == 2 || item_type == 3) {  // 直播和推人进行过滤豁免
        photo_type_exceed_tag_accessor(result, not_filter_num);
        return;
      }
      if (push_type == "") {  // push type 为空 默认过滤
        photo_type_exceed_tag_accessor(result, filter_num);
        return;
      }
      if (photo_type_num_map.count(push_type) && photo_type_num_map[push_type] >= single_push_type_limit) {
        photo_type_exceed_tag_accessor(result, filter_num);
        return;
      }
      if (!photo_type_num_map.count(push_type) || photo_type_num_map[push_type] < single_push_type_limit) {
        photo_type_num_map[push_type]++;
        photo_type_exceed_tag_accessor(result, not_filter_num);
        return;
      }
    });
    return true;
  }
  static bool EnrichFilterTagRecallReasonExceed(const CommonRecoLightFunctionContext &context,
                                             RecoResultConstIter begin, RecoResultConstIter end) {
    // input
    auto single_recall_reason_limit = context.GetIntCommonAttr("single_recall_reason_limit").value_or(0);
    auto total_limit = context.GetIntCommonAttr("total_limit").value_or(0);
    auto recall_reason_accessor = context.GetIntItemAttr("recall_reason");

    // output
    auto recall_reason_exceed_tag_accessor = context.SetIntItemAttr("recall_reason_exceed_tag");
    // local val
    std::unordered_map<int, int> recall_reason_num_map;
    // num check
    if (single_recall_reason_limit <= 0) {
      LOG(ERROR) << "EnrichFilterTagRecallReasonExceed::single_recall_reason_limit <= 0!"
                <<  " single_recall_reason_limit = " << single_recall_reason_limit;
    }
    int filter_num = 1;
    int remain_cnt = 0;
    int not_filter_num = 0;
    std::vector<int> recall_reason_exceed_tag_list{};
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto recall_reason = recall_reason_accessor(result).value_or(0);
      if (recall_reason <= 0) {  // recall_reason 为空 默认过滤
        recall_reason_exceed_tag_accessor(result, filter_num);
        recall_reason_exceed_tag_list.push_back(filter_num);
        LOG(ERROR) << "EnrichFilterTagRecallReasonExceed::recall_reason <= 0!"
                <<  " recall_reason = " << recall_reason;
        return;
      }
      if (recall_reason_num_map.count(recall_reason)
          && recall_reason_num_map[recall_reason] >= single_recall_reason_limit) {
        recall_reason_exceed_tag_accessor(result, filter_num);
        recall_reason_exceed_tag_list.push_back(filter_num);

        return;
      }
      if (!recall_reason_num_map.count(recall_reason)
          || recall_reason_num_map[recall_reason] < single_recall_reason_limit) {
        recall_reason_num_map[recall_reason]++;
        recall_reason_exceed_tag_accessor(result, not_filter_num);
        recall_reason_exceed_tag_list.push_back(not_filter_num);
        remain_cnt = remain_cnt + 1;
        return;
      }
    });

    if (remain_cnt > total_limit) {
      LOG(ERROR) << "EnrichFilterTagRecallReasonExceed::remain_cnt > total_limit remain_cnt "
                << remain_cnt << " total_limit " << total_limit;
      return false;
    }

    int i = -1;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      i = i+1;
      int cur_filter_status = not_filter_num;
      if (i < recall_reason_exceed_tag_list.size()) {
        cur_filter_status = recall_reason_exceed_tag_list[i];
      }
      if (cur_filter_status == filter_num) {
        if (remain_cnt < total_limit) {
          recall_reason_exceed_tag_accessor(result, not_filter_num);
          remain_cnt = remain_cnt + 1;
        }
        return;
      }
    });

    return true;
  }

  static bool EnrichFilterEventTypeExceed(const CommonRecoLightFunctionContext &context,
                                             RecoResultConstIter begin, RecoResultConstIter end) {
    // input
    auto single_recall_reason_limit = context.GetIntCommonAttr("single_recall_reason_limit").value_or(0);
    auto total_limit = context.GetIntCommonAttr("total_limit").value_or(0);
    auto event_type_accessor = context.GetStringItemAttr("event_type");
    // output
    auto event_type_exceed_tag_accessor = context.SetIntItemAttr("event_type_exceed_tag");
    // local val
    std::unordered_map<std::string, int> event_type_num_map;
    // num check
    if (single_recall_reason_limit <= 0) {
      LOG(ERROR) << "EnrichFilterEventTypeExceed::single_event_type_limit <= 0!"
                <<  " single_event_type_limit = " << single_recall_reason_limit;
    }
    int filter_num = 1;
    int remain_cnt = 0;
    int not_filter_num = 0;
    std::vector<int> event_type_exceed_tag_list{};
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto event_type = std::string(event_type_accessor(result).value_or("unknown"));
      if (event_type.empty() || event_type == "unknown") {
        event_type_exceed_tag_accessor(result, filter_num);
        event_type_exceed_tag_list.push_back(filter_num);
        LOG(ERROR) << "EnrichFilterEventTypeExceed::event_type exception!"
                <<  " event_type = " << event_type;
        return;
      }

      if (event_type_num_map.count(event_type)
        && event_type_num_map[event_type] >= single_recall_reason_limit) {
        event_type_exceed_tag_accessor(result, filter_num);
        event_type_exceed_tag_list.push_back(filter_num);
        return;
      }
      if (!event_type_num_map.count(event_type)
        || event_type_num_map[event_type] < single_recall_reason_limit) {
        event_type_num_map[event_type]++;
        event_type_exceed_tag_accessor(result, not_filter_num);
        event_type_exceed_tag_list.push_back(not_filter_num);
        remain_cnt = remain_cnt + 1;
        return;
      }
    });

    int i = -1;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      i = i+1;
      int cur_filter_status = not_filter_num;
      auto event_type = std::string(event_type_accessor(result).value_or("unknown"));
      if (i < event_type_exceed_tag_list.size()) {
        cur_filter_status = event_type_exceed_tag_list[i];
      }
      if (cur_filter_status == filter_num
        && !(event_type.empty() || event_type == "unknown")) {
        if (remain_cnt < total_limit) {
          event_type_exceed_tag_accessor(result, not_filter_num);
          remain_cnt = remain_cnt + 1;
        }
        return;
      }
    });

    return true;
  }

  static bool EnrichMcCascadeSampleSignV2(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
    // input common
    auto positive_top_n = context.GetIntCommonAttr("mc_cascade_positive_top_n").value_or(0);
    auto positive_rand_n = context.GetIntCommonAttr("mc_cascade_positive_rand_n").value_or(0);
    auto negative_bottom_n = context.GetIntCommonAttr("mc_cascade_negative_bottom_n").value_or(0);
    auto negative_rand_n = context.GetIntCommonAttr("mc_cascade_negative_rand_n").value_or(0);
    // input item
    auto item_cascade_sample = context.GetStringItemAttr("item_cascade_sample");
    auto not_ug_photo = context.GetIntItemAttr("not_ug_photo");

    // output
    auto item_cascade_sample_accessor = context.SetStringItemAttr("item_cascade_sample");
    auto item_cascade_mc_label_accessor = context.SetStringItemAttr("item_cascade_mc_label");

    auto cascade_sample_sign = "mc_cascade_sample";
    auto cascade_label_positive_sign = "mc_positive";
    auto cascade_label_negative_sign = "mc_negative";
    const int result_size = std::distance(begin, end);

    if ((positive_top_n + positive_rand_n == 0)) {
      LOG(WARNING) << "EnrichMcCascadeSampleSignV2::no postive sample!";
      return false;
    }
    if ((negative_bottom_n + negative_rand_n == 0)) {
      LOG(WARNING) << "EnrichMcCascadeSampleSignV2::no negative sample!";
      return false;
    }
    if (positive_top_n + positive_rand_n +
        negative_bottom_n + negative_rand_n > result_size) {
      // LOG(WARNING) << "EnrichMcCascadeSampleSignV2::no enough result size to make sample! "
      //              << "positive_top_n: " << positive_top_n
      //              << ", positive_rand_n: " << positive_rand_n
      //              << ", negative_bottom_n: " << negative_bottom_n
      //              << ", negative_rand_n: " << negative_rand_n
      //              << ", result_size: " << result_size;
      return false;
    }

    // 级联样本的构造思路
    // 正样本：精排 top n，11-50 随机 n 个
    // 负样本：精排最后 n 个， 51-result_size 随机 n 个

    // 正样本， positive_top_n-50 随机 n 个 index
    int positive_begin = positive_top_n;
    int positive_end = std::min(49, static_cast<int>(result_size-negative_bottom_n-1));
    auto positive_rand_indexs = GetRandList(positive_begin, positive_end, positive_rand_n);

    // 负样本，positive_end+1 - result_size 随机 n 个 index
    int negative_begin = positive_end + 1;
    int negative_end = result_size - negative_bottom_n - 1;
    auto negative_rand_indexs = GetRandList(negative_begin, negative_end, negative_rand_n);

    int not_ug_photo_count = 0;
    int index = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      // 正样本
      // 精排 top n || 11-50 随机 n 个
      auto item_cascade_sample_str = item_cascade_sample(result);
      if (index < positive_top_n
          || (positive_rand_indexs.find(index) != positive_rand_indexs.end())) {
            auto not_ug_photo_int = not_ug_photo(result);
            if (not_ug_photo_int > 0) {
              not_ug_photo_count = not_ug_photo_count + 1;
            }
      }
      index = index + 1;
    });

    if (not_ug_photo_count > 0) {
      return false;
    }

    int count = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      // 正样本
      // 精排 top n || 11-50 随机 n 个
      auto item_cascade_sample_str = item_cascade_sample(result);
      if (count < positive_top_n
          || (positive_rand_indexs.find(count) != positive_rand_indexs.end())) {
        if (item_cascade_sample_str == absl::nullopt) {
          item_cascade_sample_accessor(result, cascade_sample_sign);
        } else {
          if (!item_cascade_sample_str.has_value()) {
            item_cascade_sample_accessor(result, cascade_sample_sign);
          } else {
            if (*item_cascade_sample_str == "") {
              item_cascade_sample_accessor(result, cascade_sample_sign);
            }
          }
        }
        item_cascade_mc_label_accessor(result, cascade_label_positive_sign);
      }

      // 负样本
      // 精排最后 n 个
      if ((count >= result_size - negative_bottom_n)
          || (negative_rand_indexs.find(count) != negative_rand_indexs.end())) {
        if (item_cascade_sample_str == absl::nullopt) {
          item_cascade_sample_accessor(result, cascade_sample_sign);
        } else {
          if (!item_cascade_sample_str.has_value()) {
            item_cascade_sample_accessor(result, cascade_sample_sign);
          } else {
            if (*item_cascade_sample_str == "") {
              item_cascade_sample_accessor(result, cascade_sample_sign);
            }
          }
        }
        item_cascade_mc_label_accessor(result, cascade_label_negative_sign);
      }
      count++;
    });
    return true;
  }

  static bool EnrichMcCascadeSampleSign(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
    // input common
    auto positive_top_n = context.GetIntCommonAttr("mc_cascade_positive_top_n").value_or(0);
    auto positive_rand_n = context.GetIntCommonAttr("mc_cascade_positive_rand_n").value_or(0);
    auto negative_bottom_n = context.GetIntCommonAttr("mc_cascade_negative_bottom_n").value_or(0);
    auto negative_rand_n = context.GetIntCommonAttr("mc_cascade_negative_rand_n").value_or(0);
    // input item
    auto item_cascade_sample = context.GetStringItemAttr("item_cascade_sample");

    // output
    auto item_cascade_sample_accessor = context.SetStringItemAttr("item_cascade_sample");
    auto item_cascade_mc_label_accessor = context.SetStringItemAttr("item_cascade_mc_label");

    auto cascade_sample_sign = "mc_cascade_sample";
    auto cascade_label_positive_sign = "mc_positive";
    auto cascade_label_negative_sign = "mc_negative";
    const int result_size = std::distance(begin, end);

    if ((positive_top_n + positive_rand_n == 0)) {
      LOG(WARNING) << "EnrichMcCascadeSampleSign::no postive sample!";
      return false;
    }
    if ((negative_bottom_n + negative_rand_n == 0)) {
      LOG(WARNING) << "EnrichMcCascadeSampleSign::no negative sample!";
      return false;
    }
    if (positive_top_n + positive_rand_n +
        negative_bottom_n + negative_rand_n > result_size) {
      // LOG(WARNING) << "EnrichMcCascadeSampleSign::no enough result size to make sample! "
      //              << "positive_top_n: " << positive_top_n
      //              << ", positive_rand_n: " << positive_rand_n
      //              << ", negative_bottom_n: " << negative_bottom_n
      //              << ", negative_rand_n: " << negative_rand_n
      //              << ", result_size: " << result_size;
      return false;
    }

    // 级联样本的构造思路
    // 正样本：精排 top n，11-50 随机 n 个
    // 负样本：精排最后 n 个， 51-result_size 随机 n 个

    // 正样本， positive_top_n-50 随机 n 个 index
    int positive_begin = positive_top_n;
    int positive_end = std::min(49, static_cast<int>(result_size-negative_bottom_n-1));
    auto positive_rand_indexs = GetRandList(positive_begin, positive_end, positive_rand_n);

    // 负样本，positive_end+1 - result_size 随机 n 个 index
    int negative_begin = positive_end + 1;
    int negative_end = result_size - negative_bottom_n - 1;
    auto negative_rand_indexs = GetRandList(negative_begin, negative_end, negative_rand_n);

    int count = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      // 正样本
      // 精排 top n || 11-50 随机 n 个
      auto item_cascade_sample_str = item_cascade_sample(result);
      if (count < positive_top_n
          || (positive_rand_indexs.find(count) != positive_rand_indexs.end())) {
        if (item_cascade_sample_str == absl::nullopt) {
          item_cascade_sample_accessor(result, cascade_sample_sign);
        } else {
          if (!item_cascade_sample_str.has_value()) {
            item_cascade_sample_accessor(result, cascade_sample_sign);
          } else {
            if (*item_cascade_sample_str == "") {
              item_cascade_sample_accessor(result, cascade_sample_sign);
            }
          }
        }
        item_cascade_mc_label_accessor(result, cascade_label_positive_sign);
      }

      // 负样本
      // 精排最后 n 个
      if ((count >= result_size - negative_bottom_n)
          || (negative_rand_indexs.find(count) != negative_rand_indexs.end())) {
        if (item_cascade_sample_str == absl::nullopt) {
          item_cascade_sample_accessor(result, cascade_sample_sign);
        } else {
          if (!item_cascade_sample_str.has_value()) {
            item_cascade_sample_accessor(result, cascade_sample_sign);
          } else {
            if (*item_cascade_sample_str == "") {
              item_cascade_sample_accessor(result, cascade_sample_sign);
            }
          }
        }
        item_cascade_mc_label_accessor(result, cascade_label_negative_sign);
      }
      count++;
    });
    return true;
  }

  static bool EnrichMcCascadeSampleSignForMcNegative(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
    // input common
    auto negative_rand_n = context.GetIntCommonAttr("mc_cascade_negative_rand_n_for_mc_negative").value_or(0);
    auto mc_model_trunc_size = context.GetIntCommonAttr("mc_model_trunc_size").value_or(0);
    // input item
    auto item_cascade_sample = context.GetStringItemAttr("item_cascade_sample");

    // output
    auto item_cascade_sample_accessor = context.SetStringItemAttr("item_cascade_sample");
    auto item_cascade_mc_label_accessor = context.SetStringItemAttr("item_cascade_mc_label");

    auto cascade_sample_sign = "mc_cascade_sample";
    auto cascade_label_negative_sign = "mc_negative_v2";
    const int result_size = std::distance(begin, end);
    // 级联样本的构造思路
    // 负样本，positive_end+1, result_size-1 随机 n 个 index
    int negative_begin = mc_model_trunc_size + 1;
    int negative_end = result_size - 1;
    if (negative_rand_n == 0 || mc_model_trunc_size == 0
        || (negative_end - negative_begin) < negative_rand_n) {
      // LOG(WARNING) << "EnrichMcCascadeSampleSignForMcNegative::no negative sample!"
      //            << "mc_model_trunc_size: " << mc_model_trunc_size
      //            << ", negative_rand_n: " << negative_rand_n
      //            << ", (negative_end - negative_begin): " << (negative_end - negative_begin);
      return false;
    }
    auto negative_rand_indexs = GetRandList(negative_begin, negative_end, negative_rand_n);

    int index = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
    auto item_cascade_sample_str = item_cascade_sample(result);
    // 负样本
    // 粗排没有进精排样本随机 n 个
    if (negative_rand_indexs.find(index) != negative_rand_indexs.end()) {
        if (item_cascade_sample_str == absl::nullopt) {
          item_cascade_sample_accessor(result, cascade_sample_sign);
        } else {
          if (!item_cascade_sample_str.has_value()) {
            item_cascade_sample_accessor(result, cascade_sample_sign);
          } else {
            if (*item_cascade_sample_str == "") {
              item_cascade_sample_accessor(result, cascade_sample_sign);
            }
          }
        }
        item_cascade_mc_label_accessor(result, cascade_label_negative_sign);
      }
      index++;
    });
    return true;
  }

  // 对每个 reason 的 top1 根据 top2 的情况给一个 boost
  static bool EnrichDeltaScoreByRecallReason(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
    // input
    auto one_reason_default_weight = context.GetDoubleCommonAttr("one_reason_default_weight").value_or(0.0);
    auto delta_ctr_score_clip_is_on = context.GetIntCommonAttr("delta_ctr_score_clip_is_on").value_or(0);
    auto delta_ctr_score_max_boost_weight
     = context.GetDoubleCommonAttr("delta_ctr_score_max_boost_weight").value_or(100.0);
    auto recall_reason_accessor = context.GetIntItemAttr("recall_reason");
    auto pctr_accessor = context.GetDoubleItemAttr("mix_ctr");
    // output
    auto delta_score_by_reason_accessor = context.SetDoubleItemAttr("delta_score_by_reason");

    std::unordered_map<int64, std::vector<double>> reasonScores;
    std::unordered_map<int64, std::vector<int>> reasonIndex;
    int index = 0;
    // 将分数按 recall reason 分类
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto recall_reason = recall_reason_accessor(result).value_or(0);
      auto pctr = pctr_accessor(result).value_or(0);
      reasonScores[recall_reason].push_back(pctr);
      reasonIndex[recall_reason].push_back(index);
      index++;
    });
    index = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto recall_reason = recall_reason_accessor(result).value_or(0);
      auto reason_score_list = reasonScores[recall_reason];
      auto reason_index_list = reasonIndex[recall_reason];
      if (reason_score_list.size() == 1) {
        if (index == reason_index_list[0]) {
          delta_score_by_reason_accessor(result, one_reason_default_weight);
        }
      } else {
        if (index == reason_index_list[0]) {
          if (reason_score_list[1] != 0) {
            double delta_ctr = (reason_score_list[0]-reason_score_list[1]) / (reason_score_list[1]);
            if (delta_ctr_score_clip_is_on == 1) {
              // 如果算出来小于 0，说明越界了，还是置为最大 boost weight
              if (delta_ctr < 0) {
                delta_ctr = delta_ctr_score_max_boost_weight;
              }
              delta_ctr = std::min(delta_ctr, delta_ctr_score_max_boost_weight);
            }
            delta_score_by_reason_accessor(result, delta_ctr);
          }
        }
      }
      index++;
    });
    return true;
  }

  // 对每个 event type 的 top1 根据 top2 的情况给一个 boost
  static bool EnrichDeltaScoreByEventType(const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end) {
      // input
      auto one_reason_default_weight = context.GetDoubleCommonAttr("one_reason_default_weight").value_or(0.0);
      auto delta_ctr_score_max_boost_weight
      = context.GetDoubleCommonAttr("delta_ctr_score_max_boost_weight").value_or(100.0);
      auto pctr_accessor = context.GetDoubleItemAttr("mix_ctr");
      auto event_type_accessor = context.GetStringItemAttr("event_type");

      // output
      auto delta_score_by_event_accessor = context.SetDoubleItemAttr("delta_score_by_event_type");

      std::unordered_map<std::string, std::vector<double>> eventTypeScores;
      std::unordered_map<std::string, std::vector<int>> eventTypeIndex;
      int index = 0;
      // 将分数按 event type 分类
      std::for_each(begin, end, [&] (const CommonRecoResult &result) {
        auto event_type = std::string(event_type_accessor(result).value_or("unknown"));
        auto pctr = pctr_accessor(result).value_or(0);
        eventTypeScores[event_type].push_back(pctr);
        eventTypeIndex[event_type].push_back(index);
        index++;
      });
      index = 0;
      std::for_each(begin, end, [&](const CommonRecoResult &result) {
        auto event_type = std::string(event_type_accessor(result).value_or("unknown"));
        auto event_score_list = eventTypeScores[event_type];
        auto event_index_list = eventTypeIndex[event_type];
        if (event_score_list.size() == 1) {
          if (index == event_index_list[0]) {
            delta_score_by_event_accessor(result, one_reason_default_weight);
          }
        } else {
          if (index == event_index_list[0]) {
            if (event_score_list[1] != 0) {
              double delta_ctr = (event_score_list[0]-event_score_list[1]) / (event_score_list[1] + 1e-6);
              // 如果算出来小于 0，说明越界了，还是置为最大 boost weight
              if (delta_ctr < 0) {
                delta_ctr = delta_ctr_score_max_boost_weight;
              }
              delta_ctr = std::min(delta_ctr, delta_ctr_score_max_boost_weight);
              delta_score_by_event_accessor(result, delta_ctr);
            }
          }
        }
        index++;
      });
      return true;
  }

  static bool EnrichRankIndex(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto rank_log_type = context.GetStringCommonAttr("rank_log_type").value_or("none");
    // output
    auto mc_rank_index_accessor = context.SetIntItemAttr("mc_rank_index");
    auto fr_rank_index_accessor = context.SetIntItemAttr("fr_rank_index");
    int count = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      if (rank_log_type == "fr") {
        fr_rank_index_accessor(result, count);
      }
      if (rank_log_type == "mc") {
        mc_rank_index_accessor(result, count);
      }
      count++;
    });
    return true;
  }

  static std::set<int> GetRandList(int begin, int end, int randNum) {
    std::set<int> unique_numbers;
    if (begin >= end || randNum <= 0 || randNum > (end-begin)) {
      return unique_numbers;
    }
    // 设置随机数种子
    std::srand(static_cast<unsigned int>(std::time(nullptr)));

    // 生成 randNum 个不同的随机数字
    for (int i = 0; i < randNum; ++i) {
      int random_number = std::rand() % (end - begin) + begin;  // 生成范围内的随机数
      unique_numbers.insert(random_number);  // 插入到集合中，确保唯一性
    }
    return unique_numbers;
  }

  static std::string GenerateClickListKey(int64 pid, int64 aid, const std::string& event_type, int64 ts) {
    return std::to_string(pid) + "_" + std::to_string(aid) + "_" + event_type + "_" + std::to_string(ts);
  }

  static std::vector<int64> MergeHetuTagLists(const std::vector<int64>& tag_list,
                                        const std::vector<int64>& colossus_tag_list) {
      std::vector<int64> result;
      int i = 0, j = 0;

      while (i < tag_list.size() && j < colossus_tag_list.size()) {
          // 找到 tag_list 中与 colossus_tag_list[j] 相同的元素位置
          int match_in_click = i;
          while (match_in_click < tag_list.size() && tag_list[match_in_click] != colossus_tag_list[j]) {
            match_in_click++;
          }
          if (match_in_click < tag_list.size()) {
            // 先把 colossus_tag_list 中 i 到 match_in_click 的新元素加入结果
            while (j < colossus_tag_list.size() && colossus_tag_list[j] != tag_list[i]) {
              result.push_back(colossus_tag_list[j]);
              j++;
            }
            // 现在 colossus_tag_list[j] == tag_list[i]
            // 处理重叠部分
            while (i < tag_list.size() && j < colossus_tag_list.size() &&
                  tag_list[i] == colossus_tag_list[j]) {
              result.push_back(tag_list[i]);
              i++;
              j++;
            }
          } else {
            // colossus_tag_list[j] 不在 tag_list 剩余部分中, 直接加入
            result.push_back(colossus_tag_list[j]);
            j++;
          }
      }
      // 添加剩余元素
      while (i < tag_list.size()) {
        result.push_back(tag_list[i++]);
      }
      while (j < colossus_tag_list.size()) {
        result.push_back(colossus_tag_list[j++]);
      }
      return result;
  }

  static bool EnrichMergePush90dClickList(const CommonRecoLightFunctionContext &context,
                                    RecoResultConstIter begin, RecoResultConstIter end) {
    // 目标: 将 push_colossus_click_list 合并到 push_click_list, 去重且按时间降序排列

    // 获取 push_click_list
    auto push_click_pid_90d = context.GetIntListCommonAttr("push_click_pid_90d");
    auto push_click_author_90d = context.GetIntListCommonAttr("push_click_author_90d");
    auto push_click_event_type_90d = context.GetStringListCommonAttr("push_click_event_type_90d");
    auto push_click_hetu_level_one_90d = context.GetIntListCommonAttr("push_click_hetu_level_one_90d");
    auto push_click_hetu_level_two_90d = context.GetIntListCommonAttr("push_click_hetu_level_two_90d");
    auto push_click_ts_90d = context.GetIntListCommonAttr("push_click_ts_90d");

    // 获取 push_colossus_click_list
    auto push_colossus_click_pid_90d = context.GetIntListCommonAttr("push_colossus_click_pid_90d");
    auto push_colossus_click_aid_90d = context.GetIntListCommonAttr("push_colossus_click_aid_90d");
    auto push_colossus_click_event_type_90d =
      context.GetStringListCommonAttr("push_colossus_click_event_type_90d");
    auto push_colossus_click_hetu_level_one_90d =
      context.GetIntListCommonAttr("push_colossus_click_hetu_level_one_90d");
    auto push_colossus_click_hetu_level_two_90d =
      context.GetIntListCommonAttr("push_colossus_click_hetu_level_two_90d");
    auto push_colossus_click_ts_90d = context.GetIntListCommonAttr("push_colossus_click_ts_90d");

    // 创建结果集
    std::vector<int64> pid_list, aid_list, hetu_level_one_list, hetu_level_two_list, ts_ms_list;
    std::vector<std::string> event_type_list;
    // 创建一个集合存储 push_click_list 已有的元素
    folly::F14FastSet<std::string> seen;

    bool colossus_list_status = true;
    bool click_list_status = true;

    // 判断 push_colossus_click_list 是否异常
    if (!push_colossus_click_pid_90d || !push_colossus_click_aid_90d ||
        !push_colossus_click_event_type_90d || !push_colossus_click_hetu_level_one_90d ||
        !push_colossus_click_hetu_level_two_90d || !push_colossus_click_ts_90d) {
      colossus_list_status = false;
    }
    int colossus_size = push_colossus_click_pid_90d->size();
    if (colossus_size == 0 || colossus_size != push_colossus_click_aid_90d->size() ||
        colossus_size != push_colossus_click_event_type_90d->size() ||
        colossus_size != push_colossus_click_hetu_level_one_90d->size() ||
        colossus_size != push_colossus_click_hetu_level_two_90d->size() ||
        colossus_size != push_colossus_click_ts_90d->size()) {
      colossus_list_status = false;
    }

    // 判断 push_click_list 是否异常
    if (!push_click_pid_90d || !push_click_author_90d ||
        !push_click_event_type_90d || !push_click_ts_90d) {
      click_list_status = false;
    }
    int click_size = push_click_pid_90d->size();
    if (click_size == 0 || click_size != push_click_author_90d->size() ||
        click_size != push_click_event_type_90d->size() ||
        click_size != push_click_ts_90d->size()) {
      click_list_status = false;
    }

    if (colossus_list_status == false && click_list_status == false) {
      return false;
    }

    // 如果 push_colossus_click_list 为空, 保持 push_click_list
    if (colossus_list_status == false) {
      pid_list.assign(push_click_pid_90d->begin(), push_click_pid_90d->end());
      aid_list.assign(push_click_author_90d->begin(), push_click_author_90d->end());
      for (int i = 0; i < push_click_event_type_90d->size(); i++) {
        event_type_list.push_back(std::string(push_click_event_type_90d->at(i)));
      }
      if (push_click_hetu_level_one_90d && push_click_hetu_level_one_90d->size() > 0) {
        hetu_level_one_list.assign(
          push_click_hetu_level_one_90d->begin(), push_click_hetu_level_one_90d->end());
      }
      if (push_click_hetu_level_two_90d && push_click_hetu_level_two_90d->size() > 0) {
        hetu_level_two_list.assign(
          push_click_hetu_level_two_90d->begin(), push_click_hetu_level_two_90d->end());
      }
      ts_ms_list.assign(push_click_ts_90d->begin(), push_click_ts_90d->end());
    }

    // 如果 push_click_list 为空, 返回 push_colossus_click_list
    if (click_list_status == false) {
      // 由于 push_colossus_click_list 是时间升序, 需要反向遍历改为降序
      if (push_colossus_click_pid_90d && push_colossus_click_pid_90d->size() > 0) {
        for (int i = push_colossus_click_pid_90d->size() - 1; i >= 0; i--) {
          pid_list.push_back(push_colossus_click_pid_90d->at(i));
          aid_list.push_back(push_colossus_click_aid_90d->at(i));
          event_type_list.push_back(std::string(push_colossus_click_event_type_90d->at(i)));
          // push_click_list 中, hetu 特征不保存 0 值, 这里需要处理下
          int64 hetu_level_one = push_colossus_click_hetu_level_one_90d->at(i);
          int64 hetu_level_two = push_colossus_click_hetu_level_two_90d->at(i);
          if (hetu_level_one != 0) {
            hetu_level_one_list.push_back(hetu_level_one);
          }
          if (hetu_level_two != 0) {
            hetu_level_two_list.push_back(hetu_level_two);
          }
          ts_ms_list.push_back(push_colossus_click_ts_90d->at(i));
        }
      }
    }

    // 如果二者都不为空, 合并序列
    if (colossus_list_status && click_list_status) {
      // 第一个序列 序列本身是按时间降序的
      for (int i = 0; i < push_click_pid_90d->size(); i++) {
        int64 pid = push_click_pid_90d->at(i);
        int64 aid = push_click_author_90d->at(i);
        std::string event_type = std::string(push_click_event_type_90d->at(i));
        int64 ts_ms = push_click_ts_90d->at(i);

        // 原始时间戳是毫秒级, 转为秒级
        int64 ts_s = ts_ms / 1000;
        std::string key = GenerateClickListKey(pid, aid, event_type, ts_s);
        if (seen.find(key) == seen.end()) {
          seen.insert(key);
        }

        pid_list.push_back(pid);
        aid_list.push_back(aid);
        event_type_list.push_back(event_type);
        ts_ms_list.push_back(ts_ms);
      }
      if (push_click_hetu_level_one_90d && push_click_hetu_level_one_90d->size() > 0) {
        hetu_level_one_list.assign(
          push_click_hetu_level_one_90d->begin(), push_click_hetu_level_one_90d->end());
      }
      if (push_click_hetu_level_two_90d && push_click_hetu_level_two_90d->size() > 0) {
        hetu_level_two_list.assign(
          push_click_hetu_level_two_90d->begin(), push_click_hetu_level_two_90d->end());
      }
      // 第二个序列本身是按时间升序的
      std::vector<int64> colossus_hetu_one_list, colossus_hetu_two_list;
      if (push_colossus_click_pid_90d && push_colossus_click_pid_90d->size() > 0) {
        for (int i = push_colossus_click_pid_90d->size() - 1; i >= 0; i--) {
          int64 pid = push_colossus_click_pid_90d->at(i);
          int64 aid = push_colossus_click_aid_90d->at(i);
          std::string event_type = std::string(push_colossus_click_event_type_90d->at(i));
          int64 hetu_level_one = push_colossus_click_hetu_level_one_90d->at(i);
          int64 hetu_level_two = push_colossus_click_hetu_level_two_90d->at(i);
          int64 ts_ms = push_colossus_click_ts_90d->at(i);

          // 原始时间戳是毫秒级, 转为秒级
          int64 ts_s = ts_ms / 1000;
          std::string key = GenerateClickListKey(pid, aid, event_type, ts_s);

          if (seen.find(key) == seen.end()) {
            pid_list.push_back(pid);
            aid_list.push_back(aid);
            event_type_list.push_back(event_type);
            ts_ms_list.push_back(ts_ms);
            seen.insert(key);
          }

          if (hetu_level_one != 0) {
            colossus_hetu_one_list.push_back(hetu_level_one);
          }
          if (hetu_level_two != 0) {
            colossus_hetu_two_list.push_back(hetu_level_two);
          }
        }
      }

      // 创建索引数组用于排序
      std::vector<int64> indices(ts_ms_list.size());
      for (int i = 0; i < indices.size(); ++i) {
        indices[i] = i;
      }

      // 按时间戳降序排序索引
      std::stable_sort(indices.begin(), indices.end(),
        [&ts_ms_list](int64 i1, int64 i2) {
        return ts_ms_list[i1] > ts_ms_list[i2];  // 大于号实现降序
      });

      // 定义重新排序的 lambda 函数
      auto reorder = [&indices](auto& vec) {
        auto tmp = vec;
        for (int i = 0; i < indices.size(); ++i) {
          vec[i] = tmp[indices[i]];
        }
      };

      // 重新排序
      reorder(pid_list);
      reorder(aid_list);
      reorder(event_type_list);
      reorder(ts_ms_list);

      if (!hetu_level_one_list.empty() && !colossus_hetu_one_list.empty()) {
        hetu_level_one_list = MergeHetuTagLists(hetu_level_one_list, colossus_hetu_one_list);
      } else if (hetu_level_one_list.empty()) {
        hetu_level_one_list = colossus_hetu_one_list;
      }

      if (!hetu_level_two_list.empty() && !colossus_hetu_two_list.empty()) {
        hetu_level_two_list = MergeHetuTagLists(hetu_level_two_list, colossus_hetu_two_list);
      } else if (hetu_level_two_list.empty()) {
        hetu_level_two_list = colossus_hetu_two_list;
      }
    }

    context.SetIntListCommonAttr("push_click_pid_90d", std::move(pid_list));
    context.SetIntListCommonAttr("push_click_author_90d", std::move(aid_list));
    context.SetStringListCommonAttr("push_click_event_type_90d", std::move(event_type_list));
    context.SetIntListCommonAttr("push_click_hetu_level_one_90d", std::move(hetu_level_one_list));
    context.SetIntListCommonAttr("push_click_hetu_level_two_90d", std::move(hetu_level_two_list));
    context.SetIntListCommonAttr("push_click_ts_90d", std::move(ts_ms_list));

    return true;
  }

  static bool EnrichRerankMmrValue(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                   RecoResultConstIter end) {
    auto mmr_for_active_degree = context.GetIntCommonAttr("mmr_for_active_degree").value_or(0);
    auto user_id = context.GetIntCommonAttr("user_id").value_or(0);
    auto use_l30_absence = context.GetIntCommonAttr("use_l30_absence").value_or(0);
    auto user_l30 = context.GetIntCommonAttr("user_l30").value_or(-1);
    auto fuse_type = std::string(context.GetStringCommonAttr("fuse_type").value_or("add"));
    std::vector<std::string> defult_keys = {"mmr_lambda_hetu",
                                            "mmr_lambda_authorid",
                                            "mmr_lambda_event_type",
                                            "mmr_lambda_event_type_for_add",
                                            "mmr_lambda_event_type_for_multiply",
                                            "mmr_lambda_event_type_for_cluster632"};
    for (auto defult_key : defult_keys) {
      auto defult_value = context.GetDoubleCommonAttr(defult_key).value_or(0.0);
      if (mmr_for_active_degree && user_id > 0) {
        std::string key = "";
        if (use_l30_absence) {
          user_l30 = context.GetIntCommonAttr("uid_l30_absence").value_or(-1);
        }
        if (user_l30 < 10) {
          key = "low_active_" + defult_key + fuse_type;
        } else if (user_l30 >= 10 && user_l30 < 20) {
          key = "middle_active_" + defult_key + fuse_type;
        } else {
          key = "high_active_" + defult_key + fuse_type;
        }
        auto temp_value = context.GetDoubleCommonAttr("temp_" + key).value_or(defult_value);
        if (temp_value == -1.0) {
          // -1.0 代表 kconf 没取到值，用 defult_value 设置默认值
          temp_value = defult_value;
        }
        auto result = context.GetDoubleCommonAttr(key).value_or(temp_value);
        if (result == -1.0) {
          // -1.0 代表 ab 没取到值，用 temp_value 设置默认值
          result = temp_value;
        }
        context.SetDoubleCommonAttr(defult_key, result);
      }
    }
    return true;
  }
};
}  // namespace platform
}  // namespace ks
