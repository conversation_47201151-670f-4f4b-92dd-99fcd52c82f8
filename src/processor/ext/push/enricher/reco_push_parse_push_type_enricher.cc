#include "dragon/src/processor/ext/push/enricher/reco_push_parse_push_type_enricher.h"
#include <chrono>
#include <ctime>
#include <filesystem>
#include <utility>
#include "base/time/timestamp.h"
#include "ks/base/perfutil/perfutil_wrapper.h"

namespace ks {
namespace platform {

void PushParsePushTypeEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                       RecoResultConstIter end) {
  if (debug_log_info_flag_) {
    DebugSingleReasonCast();
  }
  follow_friend_list_ = context->GetIntListCommonAttr("follow_friend_list");
  follow_favorite_list_ = context->GetIntListCommonAttr("follow_favorite_list");
  auto user_id_op = context->GetIntCommonAttr("user_id");
  auto user_id = user_id_op.has_value() ? user_id_op.value() : 0;

  // auto *item_type_accessor = context->GetItemAttrAccessor("item_type");
  // auto *type_attr_accessor = context->GetItemAttrAccessor("type_attr");
  // auto *recall_reason_accessor = context->GetItemAttrAccessor("recall_reason");
  // auto *user_item_relation_accessor = context->GetItemAttrAccessor("userItemRelation");
  // auto *author_id_accessor = context->GetItemAttrAccessor("author_id_attr");
  if (user_id > 0) {
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_type_op = context->GetIntItemAttr(result.item_key, "item_type");
      auto type_attr_op = context->GetStringItemAttr(result.item_key, "type_attr");
      auto recall_reason_op = context->GetIntItemAttr(result.item_key, "recall_reason");
      auto user_item_relation_op = context->GetIntItemAttr(result.item_key, "userItemRelation");
      auto author_id_op = context->GetIntItemAttr(result.item_key, "author_id_attr");

      auto item_type = item_type_op.has_value() ? item_type_op.value() : -1;
      auto type_attr = type_attr_op.has_value() ? std::string(type_attr_op.value()) : "";
      auto recall_reason = recall_reason_op.has_value() ? recall_reason_op.value() : -1;
      auto user_item_relation = user_item_relation_op.has_value() ? user_item_relation_op.value() : -1;
      auto author_id = author_id_op.has_value() ? author_id_op.value() : -1;

      std::string push_type = "";

      if (black_mix_event_type_->Get()->count(type_attr)) {
        push_type = "unknown";
        context->SetStringItemAttr(result.item_key, push_type_attr_name_, push_type);
        SetAttrByPushType(push_type, context, result.item_key);
        return;
      }
      if (social_person_push_bug_fix_switch_->Get() && type_attr == "author") {
        type_attr = "people";
        // perf
      }

      if (type_attr == "photo" || item_type == 1) {
        GetPhotoPushTypeByReason(recall_reason, author_id, user_item_relation, push_type);
      } else if (type_attr == "livestream" || item_type == 2) {
        GetLivePushTypeByReason(recall_reason, push_type);
      } else if (unfollow_people_recall_switch_->Get() && item_type == 3) {
        GetPeoplePushTypeByReason(recall_reason, user_item_relation, type_attr, push_type);
      } else if (type_attr == "moment") {
        push_type = "followAuthorMoment";
      } else {
        if (parser_array_[parser_idx_].leaf_type_to_push_type_.count(type_attr)) {
          push_type = parser_array_[parser_idx_].leaf_type_to_push_type_[type_attr];
        }
      }
      if (!push_type.empty()) {
        context->SetStringItemAttr(result.item_key, push_type_attr_name_, push_type);
        SetAttrByPushType(push_type, context, result.item_key);
      } else {
        base::perfutil::PerfUtilWrapper::CountLogStash("reco.push", "generate.push.type", "type_attr.error",
                                                       type_attr);
        if (type_attr_op.has_value()) {
          CL_LOG_ERROR_EVERY("generate.push.type", "type_attr.not.found: " + type_attr, 5000);
        } else {
          CL_LOG_ERROR_EVERY("generate.push.type", "type_attr.null: ", 5000);
        }
      }
    });
  } else {
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_type_op = context->GetIntItemAttr(result.item_key, "item_type");
      auto recall_reason_op = context->GetIntItemAttr(result.item_key, "recall_reason");

      auto item_type = item_type_op.has_value() ? item_type_op.value() : -1;
      auto recall_reason = recall_reason_op.has_value() ? recall_reason_op.value() : -1;

      std::string push_type = "";
      UnLoginGetPushTypeByReason(recall_reason, item_type, push_type);
      if (!push_type.empty()) {
        context->SetStringItemAttr(result.item_key, push_type_attr_name_, push_type);
      }
      int is_photo_item = parser_array_[parser_idx_].IsPhotoItem(push_type);
      SetAttrByPushType(push_type, context, result.item_key);
    });
  }
  // 处理 push record 记录
  auto push_record_push_types = context->GetStringListCommonAttr("push_record_push_types");
  int len_push_record = push_record_push_types.has_value() ? push_record_push_types->size() : 0;
  std::vector<std::string> push_record_event_types;
  std::vector<int64> push_record_is_live_items;
  push_record_event_types.reserve(len_push_record);
  push_record_is_live_items.reserve(len_push_record);
  for (int i = 0; i < len_push_record; ++i) {
    std::string push_type(push_record_push_types->at(i));
    if (parser_array_[parser_idx_].node_map_.count(push_type)) {
      push_record_event_types.emplace_back(parser_array_[parser_idx_].node_map_[push_type]->leaf_type_attr);
    }
    int is_live_item = parser_array_[parser_idx_].IsDescendant(push_type, "live");
    push_record_is_live_items.emplace_back(is_live_item);
  }
  context->SetIntListCommonAttr("push_record_is_live_items", std::move(push_record_is_live_items));
  context->SetStringListCommonAttr("push_record_event_types", std::move(push_record_event_types));
  return;
}

void PushParsePushTypeEnricher::GetPhotoPushTypeByReason(int64 recall_reason, int64 author_id,
                                                         int64 user_item_relation, std::string &push_type) {
  int len_favorite_list = follow_favorite_list_.has_value() ? follow_favorite_list_->size() : 0;
  // 特别关注优先
  for (int i = 0; i < len_favorite_list; ++i) {
    if (author_id == follow_favorite_list_->at(i)) {
      push_type = "favoriteAuthorPhoto";
      return;
    }
  }
  int len_friend_list = follow_friend_list_.has_value() ? follow_friend_list_->size() : 0;
  for (int i = 0; i < len_friend_list; ++i) {
    if (author_id == follow_friend_list_->at(i)) {
      push_type = "friendUploadPhoto";
      return;
    }
  }
  int64 un_follow_author_reason = parser_array_[parser_idx_].reason_name_to_code_["UN_FOLLOW_AUTHOR_REASON"];
  if (recall_reason == un_follow_author_reason) {
    // UN_FOLLOW_AUTHOR_REASON
    UnFollowAuthorPushType(user_item_relation, push_type);
  } else if (parser_array_[parser_idx_].reason_to_photo_push_type_.count(recall_reason)) {
    push_type = parser_array_[parser_idx_].reason_to_photo_push_type_[recall_reason];
    return;
  } else {
    // 兜底
    push_type = "followedUploadPhoto";
  }
}

void PushParsePushTypeEnricher::UnFollowAuthorPushType(int64 user_item_relation, std::string &push_type) {
  if (parser_array_[parser_idx_].reason_author_relation_to_push_type_.count(user_item_relation)) {
    push_type = parser_array_[parser_idx_].reason_author_relation_to_push_type_[user_item_relation];
  } else {
    // 缺省用可能感兴趣
    push_type = "userAuthorDefaultRelationPhoto";
  }
}

void PushParsePushTypeEnricher::GetLivePushTypeByReason(int64 recall_reason, std::string &push_type) {
  if (parser_array_[parser_idx_].reason_to_live_push_type_.count(recall_reason)) {
    push_type = parser_array_[parser_idx_].reason_to_live_push_type_[recall_reason];
  } else {
    push_type = "liveStart";
  }
}

void PushParsePushTypeEnricher::GetPeoplePushTypeByReason(int64 recall_reason, int64 user_item_relation,
                                                          const std::string &type_attr,
                                                          std::string &push_type) {
  int64 un_follow_people_reason = parser_array_[parser_idx_].reason_name_to_code_["UN_FOLLOW_PEOPLE_REASON"];
  if (recall_reason == un_follow_people_reason) {
    UnFollowPeoplePushType(user_item_relation, push_type);
  } else if (parser_array_[parser_idx_].reason_to_people_push_type_.count(recall_reason)) {
    push_type = parser_array_[parser_idx_].reason_to_people_push_type_[recall_reason];
  } else {
    // 兜底直接使用 leaf_type_attr to push_type
    if (parser_array_[parser_idx_].leaf_type_to_push_type_.count(type_attr)) {
      push_type = parser_array_[parser_idx_].leaf_type_to_push_type_[type_attr];
    }
  }
}

void PushParsePushTypeEnricher::UnFollowPeoplePushType(int64 user_item_relation, std::string &push_type) {
  if (parser_array_[parser_idx_].reason_people_relation_to_push_type_.count(user_item_relation)) {
    push_type = parser_array_[parser_idx_].reason_people_relation_to_push_type_[user_item_relation];
  } else {
    push_type = "relationPeopleDefault";
  }
}

void PushParsePushTypeEnricher::UnLoginGetPushTypeByReason(int64 recall_reason, int64 item_type,
                                                           std::string &push_type) {
  if (parser_array_[parser_idx_].unlogin_reason_to_push_type_.count(recall_reason)) {
    push_type = parser_array_[parser_idx_].unlogin_reason_to_push_type_[recall_reason];
  } else {
    UnLoginGetPushTypeByItemType(item_type, push_type);
  }
}

void PushParsePushTypeEnricher::UnLoginGetPushTypeByItemType(int64 item_type, std::string &push_type) {
  if (parser_array_[parser_idx_].unlogin_item_type_to_push_type_.count(item_type)) {
    push_type = parser_array_[parser_idx_].unlogin_item_type_to_push_type_[item_type];
  } else {
    push_type = "unknown";
  }
}

void PushParsePushTypeEnricher::SetAttrByPushType(const std::string &push_type,
                                                  MutableRecoContextInterface *context, uint64 item_key) {
  int is_photo_item = parser_array_[parser_idx_].IsPhotoItem(push_type);
  context->SetIntItemAttr(item_key, "is_photo_item", is_photo_item);
  if (parser_array_[parser_idx_].node_map_.count(push_type)) {
    std::string leaf_type_attr = parser_array_[parser_idx_].node_map_[push_type]->leaf_type_attr;
    context->SetStringItemAttr(item_key, rerank_leaf_type_attr_name_, leaf_type_attr);
  }

  int is_live_item = parser_array_[parser_idx_].IsDescendant(push_type, "live");
  context->SetIntItemAttr(item_key, "is_live_item", is_live_item);
}

void PushParsePushTypeEnricher::DebugSingleReasonCast() {
  if (!parser_array_[parser_idx_].node_map_.count(debug_read_push_type_)) {
    LOG(ERROR) << "debug_read_push_type not find in config";
    return;
  }
  LOG(INFO) << "debug_read_push_type name: "
            << parser_array_[parser_idx_].node_map_[debug_read_push_type_]->name;
  LOG(INFO) << "debug_read_push_type code: "
            << parser_array_[parser_idx_].node_map_[debug_read_push_type_]->code;
  LOG(INFO) << "debug_read_push_type parent: "
            << parser_array_[parser_idx_].node_map_[debug_read_push_type_]->parent_name;
  LOG(INFO) << "debug_read_push_type ancestor: "
            << parser_array_[parser_idx_].node_map_[debug_read_push_type_]->ancestor_name;
  LOG(INFO) << "debug_read_push_type primary_attribution: "
            << parser_array_[parser_idx_].node_map_[debug_read_push_type_]->primary_attribution;
  LOG(INFO) << "debug_read_push_type secondary_attribution: "
            << parser_array_[parser_idx_].node_map_[debug_read_push_type_]->secondary_attribution;
  LOG(INFO) << "debug_read_push_type path: "
            << parser_array_[parser_idx_].node_map_[debug_read_push_type_]->path;
  LOG(INFO) << "debug_read_push_type level: "
            << parser_array_[parser_idx_].node_map_[debug_read_push_type_]->level;
  if (!debug_reason_to_photo_push_type_.empty()) {
    int code = parser_array_[parser_idx_].reason_name_to_code_[debug_reason_to_photo_push_type_];
    if (parser_array_[parser_idx_].reason_to_photo_push_type_.count(code)) {
      LOG(INFO) << "debug_reason_to_photo_push_type_ get push type: "
                << parser_array_[parser_idx_].reason_to_photo_push_type_[code] << " reason code: " << code;
    } else {
      LOG(INFO) << "debug_reason_to_photo_push_type_ get push type: "
                << "not contain reason code: " << code;
    }
  }
  if (!debug_reason_to_live_push_type_.empty()) {
    int code = parser_array_[parser_idx_].reason_name_to_code_[debug_reason_to_live_push_type_];
    if (parser_array_[parser_idx_].reason_to_live_push_type_.count(code)) {
      LOG(INFO) << "debug_reason_to_live_push_type_ get push type: "
                << parser_array_[parser_idx_].reason_to_live_push_type_[code] << " reason code: " << code;
    } else {
      LOG(INFO) << "debug_reason_to_live_push_type_ get push type: "
                << "not contain reason code: " << code;
    }
  }
  if (!debug_reason_to_people_push_type_.empty()) {
    int code = parser_array_[parser_idx_].reason_name_to_code_[debug_reason_to_people_push_type_];
    if (parser_array_[parser_idx_].reason_to_people_push_type_.count(code)) {
      LOG(INFO) << "debug_reason_to_people_push_type_ get push type: "
                << parser_array_[parser_idx_].reason_to_people_push_type_[code] << " reason code: " << code;
    } else {
      LOG(INFO) << "debug_reason_to_people_push_type_ get push type: "
                << "not contain reason code: " << code;
    }
  }
  if (!debug_reason_author_relation_to_push_type_.empty()) {
    int code = parser_array_[parser_idx_].reason_name_to_code_[debug_reason_author_relation_to_push_type_];
    if (parser_array_[parser_idx_].reason_author_relation_to_push_type_.count(code)) {
      LOG(INFO) << "debug_reason_author_relation_to_push_type_ get push type: "
                << parser_array_[parser_idx_].reason_author_relation_to_push_type_[code]
                << " reason code: " << code;
    } else {
      LOG(INFO) << "debug_reason_author_relation_to_push_type_ get push type: "
                << "not contain reason code: " << code;
    }
  }
  if (!debug_reason_people_relation_to_push_type_.empty()) {
    int code = parser_array_[parser_idx_].reason_name_to_code_[debug_reason_people_relation_to_push_type_];
    if (parser_array_[parser_idx_].reason_people_relation_to_push_type_.count(code)) {
      LOG(INFO) << "debug_reason_people_relation_to_push_type_ get push type: "
                << parser_array_[parser_idx_].reason_people_relation_to_push_type_[code]
                << " reason code: " << code;
    } else {
      LOG(INFO) << "debug_reason_people_relation_to_push_type_ get push type: "
                << "not contain reason code: " << code;
    }
  }
  if (!debug_unlogin_reason_to_push_type_.empty()) {
    int code = parser_array_[parser_idx_].reason_name_to_code_[debug_unlogin_reason_to_push_type_];
    if (parser_array_[parser_idx_].unlogin_reason_to_push_type_.count(code)) {
      LOG(INFO) << "debug_unlogin_reason_to_push_type_ get push type: "
                << parser_array_[parser_idx_].unlogin_reason_to_push_type_[code] << " reason code: " << code;
    } else {
      LOG(INFO) << "debug_unlogin_reason_to_push_type_ get push type: "
                << "not contain reason code: " << code;
    }
  }
  if (!debug_unlogin_item_type_to_push_type_.empty()) {
    int code = parser_array_[parser_idx_].item_type_name_to_code_[debug_unlogin_item_type_to_push_type_];
    if (parser_array_[parser_idx_].unlogin_item_type_to_push_type_.count(code)) {
      LOG(INFO) << "debug_unlogin_item_type_to_push_type_ get push type: "
                << parser_array_[parser_idx_].unlogin_item_type_to_push_type_[code]
                << " reason code: " << code;
    } else {
      LOG(INFO) << "debug_unlogin_item_type_to_push_type_ get push type: "
                << "not contain reason code: " << code;
    }
  }
  return;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, PushParsePushTypeEnricher, PushParsePushTypeEnricher)

}  // namespace platform
}  // namespace ks
