#pragma once

#include <math.h>
#include <algorithm>
#include <ctime>
#include <functional>
#include <limits>
#include <random>
#include <set>
#include <string>
#include <tuple>
#include <unordered_map>
#include <utility>
#include <vector>
#include <memory>
#include <unordered_set>
#include "Eigen/Dense"
#include "base/strings/string_number_conversions.h"
#include "base/strings/string_split.h"
#include "base/time/timestamp.h"
#include "dragon/src/module/common_reco_light_function.h"
#include "folly/String.h"
#include "folly/container/F14Map.h"
#include "ks/reco_pub/reco/util/util.h"
#include "ks/reco/memory_data_map/util.h"

namespace ks {
namespace platform {

class InterestExploreLightFunctionSet : public CommonRecoBaseLightFunctionSet {
 public:
  InterestExploreLightFunctionSet() {
    REGISTER_LIGHT_FUNCTION(FetchCluster2ClusterResult);
    REGISTER_LIGHT_FUNCTION(ConvertHetuClusterList);
    REGISTER_LIGHT_FUNCTION(MultiGetFromIntBytesIntMap);
    REGISTER_LIGHT_FUNCTION(MultiGetFromIntBytesIntStringMap);
    REGISTER_LIGHT_FUNCTION(MultiGetFromIStringBytesMap);
    REGISTER_LIGHT_FUNCTION(GetItemValueFromMemoryMap);
    REGISTER_LIGHT_FUNCTION(CalHistClusterWeights);
    REGISTER_LIGHT_FUNCTION(CalHistConfBuckets);
    REGISTER_LIGHT_FUNCTION(CalHistConfAggr);
    REGISTER_LIGHT_FUNCTION(CheckAuthorEmbdMiss);
    REGISTER_LIGHT_FUNCTION(GenerateSeq);
    REGISTER_LIGHT_FUNCTION(SampleFromList);
    REGISTER_LIGHT_FUNCTION(GetCocoonPids);
    REGISTER_LIGHT_FUNCTION(GetCocoonPids2);
    REGISTER_LIGHT_FUNCTION(MultiEmbSimilarity);
    REGISTER_LIGHT_FUNCTION(GetAuthorCircleId);
    REGISTER_LIGHT_FUNCTION(GetTriggerSimEmb);
    REGISTER_LIGHT_FUNCTION(CalcEmbL2Distance);
    REGISTER_LIGHT_FUNCTION(PCAEmbSimScore);
    REGISTER_LIGHT_FUNCTION(QueryEmbSimFlag);
    REGISTER_LIGHT_FUNCTION(GetValidPid);
    REGISTER_LIGHT_FUNCTION(GetRecentInterestPids);
  }

  struct ConfItem {
    ConfItem(int64 play_time, int64 label, int64 conf) : play_time(play_time), label(label), conf(conf) {}
    int64 play_time;
    int64 label;
    int64 conf;
  };
  struct BucketItem {
    BucketItem(float pvtr, float pltr, float pftr, float pwtr, float pcmtr, float var, float start_conf,
               float end_conf)
        : pvtr_score(pvtr)
        , pltr_score(pltr)
        , pftr_score(pftr)
        , pwtr_score(pwtr)
        , pcmtr_score(pcmtr)
        , dist_var(var)
        , start_conf(start_conf)
        , end_conf(end_conf) {}
    float pvtr_score = 0., pltr_score = 0., pftr_score = 0., pwtr_score = 0.;
    float pcmtr_score = 0., dist_var = 0.;
    int64 start_conf = 0, end_conf = 0;
  };

  static bool FetchCluster2ClusterResult(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto memory_data_ptr =
        context.GetPtrCommonAttr<folly::F14FastMap<std::string, folly::F14FastMap<uint64, double>>>(
            "c2c_hetu_cluster_list__memory_data");
    if (!memory_data_ptr) {
      CL_LOG_EVERY_N(WARNING, 1000)
          << "InterestExploreLightFunctionSet. FetchCluster2ClusterResult failed to get memory data";
      return false;
    }
    // 读取 cluster 1000 -> cluster 862 的映射
    auto cluster_mapping_list_ptr = context.GetIntListCommonAttr("remap_cluster_id");
    if (!cluster_mapping_list_ptr) {
      CL_LOG_EVERY_N(WARNING, 1000)
          << "InterestExploreLightFunctionSet. FetchCluster2ClusterResult cluster mapping list is null";
      return false;
    }
    // 处理得到 cluster 862 -> cluster 1000 的映射
    folly::F14FastMap<int64, std::vector<int64>> cluster_remapping_map;
    for (int idx = 0; idx < cluster_mapping_list_ptr->size(); ++idx) {
      int64 new_cluster_id = cluster_mapping_list_ptr->at(idx);
      if (cluster_remapping_map.find(new_cluster_id) != cluster_remapping_map.end()) {
        cluster_remapping_map[new_cluster_id].push_back(idx);
      } else {
        cluster_remapping_map.insert(std::make_pair(new_cluster_id, std::vector<int64>(1, idx)));
      }
    }

    std::set<uint64> all_target_cluster_set;
    std::vector<int64> all_target_cluster_list;
    auto positive_cluster_list = context.GetIntListCommonAttr("c2c_recent_positive_hetu_cluster_id");
    if (!positive_cluster_list) return true;
    std::set<uint64> positive_cluster_set(positive_cluster_list->begin(), positive_cluster_list->end());
    for (auto &cluster_id : *positive_cluster_list) {
      std::string memory_data_key = "c2c_" + std::to_string(cluster_id);
      auto memory_data_iter = memory_data_ptr->find(memory_data_key);
      if (memory_data_iter != memory_data_ptr->end()) {
        auto &target_cluster_map = memory_data_iter->second;
        for (auto cluster_iter = target_cluster_map.begin(); cluster_iter != target_cluster_map.end();
             ++cluster_iter) {
          // 已经在正向历史行为中的，不进行探索
          if (positive_cluster_set.find(cluster_iter->first) == positive_cluster_set.end()) {
            // 需要将 cluster 映射回 1000 的原始版本进行倒排召回
            auto remap_iter = cluster_remapping_map.find(cluster_iter->first);
            if (remap_iter != cluster_remapping_map.end()) {
              all_target_cluster_set.insert(remap_iter->second.begin(), remap_iter->second.end());
            }
          }
        }
      }
    }
    all_target_cluster_list.assign(all_target_cluster_set.begin(), all_target_cluster_set.end());
    context.SetIntListCommonAttr("c2c_cluster_result", std::move(all_target_cluster_list));
    return true;
  }

  static bool ConvertHetuClusterList(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                     RecoResultConstIter end) {
    auto origin_cluster_list_ptr = context.GetIntListCommonAttr("origin_cluster_list");
    auto cluster_mapping_list_ptr = context.GetIntListCommonAttr("remap_cluster_id");
    if (!cluster_mapping_list_ptr) {
      CL_LOG_EVERY_N(WARNING, 1000)
          << "InterestExploreLightFunctionSet. ConvertHetuClusterList mapping list is null";
      return false;
    }
    if (!origin_cluster_list_ptr) return true;
    std::vector<int64> new_cluster_list;
    for (auto &cluster_id : *origin_cluster_list_ptr) {
      if (cluster_id >= 0 && cluster_id < cluster_mapping_list_ptr->size()) {
        int64 mapping_cluster_id = cluster_mapping_list_ptr->at(cluster_id);
        new_cluster_list.push_back(mapping_cluster_id);
      }
    }
    context.SetIntListCommonAttr("new_cluster_list", std::move(new_cluster_list));
    return true;
  }


  static bool GetItemValueFromMemoryMap(const CommonRecoLightFunctionContext &context,
                                        RecoResultConstIter begin, RecoResultConstIter end) {
    LOG_EVERY_N(INFO, 1000) << "GetItemValueFromMemory At Begging";
    auto attr_key_accessor = context.GetIntItemAttr("attr_key");
    auto attr_value_accessor = context.SetIntItemAttr("attr_value");
    LOG_EVERY_N(INFO, 1000) << "GetAttrValueAndAttrKey";
    auto map_ptr = context.GetPtrCommonAttr<const folly::F14FastMap<uint64, uint64>>("map_ptr");
    LOG_EVERY_N(INFO, 1000) << "GetItemValueFromMemoryMa map ptr" << map_ptr->size();


    for (auto iter = begin; iter != end; ++iter) {
      auto attr_key_tmp = attr_key_accessor(*iter).value_or(0);
      LOG_EVERY_N(INFO, 1000)   << "GetItemValueFromMemoryMa attr key tmp" << attr_key_tmp;

      if (map_ptr && map_ptr->count(attr_key_tmp) > 0) {
        auto value = map_ptr->at(attr_key_tmp);
        LOG_EVERY_N(INFO, 1000)   << "GetItemValueFromMemoryMa attr value set" << value;
        attr_value_accessor(*iter, value);
      }
    }

    return true;
  }

  static bool GetValidPid(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    const auto photo_id_list = context.GetIntListCommonAttr("photo_id_list");
    const auto label_list = context.GetIntListCommonAttr("label_list");
    const auto duration_list = context.GetIntListCommonAttr("duration_list");
    const auto play_time_list = context.GetIntListCommonAttr("play_time_list");
    const auto timestamp_list = context.GetIntListCommonAttr("timestamp_list");
    std::vector<int64> valid_cocoon_pid_vec;
    valid_cocoon_pid_vec.clear();
    const int history_days_count = 7;
    if (!photo_id_list || photo_id_list->empty()) {
      CL_LOG_EVERY_N(WARNING, 10000) << "GetValidPid failed to get data";
      return true;
    }
    const int64_t item_num = photo_id_list->size();
    auto limit_his_pid_length = context.GetIntCommonAttr(
      "retrieval_interest_explore_pid_chase_trigger_num").value_or(0);
    if (!photo_id_list || !label_list || !duration_list || !play_time_list || !timestamp_list ||
        item_num <= 0 ||
        timestamp_list->size() != photo_id_list->size() ||
        play_time_list->size() != photo_id_list->size() ||
        duration_list->size() != photo_id_list->size() ||
        label_list->size() != photo_id_list->size() ||
        limit_his_pid_length <= 0) {
      CL_LOG_EVERY_N(WARNING, 10000) << "GetValidPid input common_attr check failed";
      return true;
    }
    int64 current_time_s = base::GetTimestamp() / 1e6;
    int64 limit_timestamp = current_time_s - history_days_count * 24 * 3600;
    folly::F14FastMap<int64, double> cocoon_pid_weight;
    cocoon_pid_weight.clear();
    for (int i = item_num -1; i >=0; --i) {
      const auto &photo_id = (*photo_id_list)[i];
      const auto &label = (*label_list)[i];
      const auto &duration = (*duration_list)[i];
      const auto &play_time = (*play_time_list)[i];
      if ((*timestamp_list)[i] < limit_timestamp) break;
      if (play_time <= 0) continue;
      bool like = label & 0x01;
      bool follow = label & (1 << 1);
      bool forward = label & (1 << 2);
      bool comment = label & (1 << 4);
      bool profile = label & (1 << 6);
      double base = (*duration_list)[i];
      if ((*duration_list)[i] < 14.0) {
          base = 0.881369 * (*duration_list)[i] + 5.184729;
      } else if ((*duration_list)[i] >= 14 && (*duration_list)[i] < 53) {
          base = 0.918007 * (*duration_list)[i] + 3.847164;
      } else if ((*duration_list)[i] >= 53 && (*duration_list)[i] < 95) {
          base = 0.732063 * (*duration_list)[i] + 13.416789;
      } else if ((*duration_list)[i] >= 95 && (*duration_list)[i] < 170) {
          base = 0.226594 * (*duration_list)[i] + 62.817905;
      } else {
          base = std::max(-0.180531 * (*duration_list)[i] + 131.062293, 36.0);
      }
      bool long_view = (*play_time_list)[i] >= base;
      bool pos_act = like || follow || forward || comment || profile || long_view;
      std::string active_w_str = std::string(context.GetStringCommonAttr(
        "retrieval_interest_explore_pid_trigger_act_weight_params").value_or(""));
      std::vector<double> action_w_vec;
      action_w_vec.clear();
      ks::reco::SimpleStringToDoubleVec(active_w_str, &action_w_vec);
      double action_value = 0.0;
      if (action_w_vec.size() >= 6) {
        action_value = action_w_vec[0] * long_view
                          + action_w_vec[1] * profile
                          + action_w_vec[2] * like
                          + action_w_vec[3] * follow
                          + action_w_vec[4] * forward
                          + action_w_vec[5] * comment;
      }
      if (pos_act) {
          cocoon_pid_weight[photo_id] += action_value;
      }
    }
    std::vector<std::pair<int64, double>> cocoon_pid_list;
    if (cocoon_pid_weight.size() > 0) {
      for (const auto &elem : cocoon_pid_weight) {
        cocoon_pid_list.push_back(std::pair<int64, double>(elem.first, elem.second));
      }
      std::sort(cocoon_pid_list.begin(), cocoon_pid_list.end(),
                            [](const std::pair<int64, double> &x, const std::pair<int32, double> &y) -> bool {
                            return x.second > y.second;
                            });
    }
    for (auto it = cocoon_pid_list.begin(); it != cocoon_pid_list.end()
          && valid_cocoon_pid_vec.size() < limit_his_pid_length; ++it) {
        valid_cocoon_pid_vec.push_back(it->first);
    }

    LOG_EVERY_N(INFO, 10000) << "GetValidPids:"
                        << ", valid_cocoon_pids=" << valid_cocoon_pid_vec.size();
    context.SetIntListCommonAttr("valid_cocoon_pids", std::move(valid_cocoon_pid_vec));
    return true;
  }

  static bool MultiGetFromIStringBytesMap(const CommonRecoLightFunctionContext &context,
                                          RecoResultConstIter begin, RecoResultConstIter end) {
    auto key_list = context.GetIntListCommonAttr("key_list");
    auto map_ptr = context.GetPtrCommonAttr<std::unordered_map<uint64_t, std::string>>("map_ptr");
    std::vector<std::string> value_list;
    if (key_list) {
      for (int i = 0; i < key_list->size(); i++) {
        const auto &key = key_list->at(i);
        if (map_ptr && map_ptr->count(key) > 0) {
          auto &source_bytes = map_ptr->at(key);
          if (source_bytes.size() > 0) {
            // size_t length = sizeof(source_bytes) / sizeof(source_bytes[0]);
            // std::string value(reinterpret_cast<const char *>(source_bytes.data()), length);
            value_list.emplace_back(source_bytes);
          } else {
            value_list.emplace_back("<empty>");
          }
        } else {
          value_list.emplace_back("<null>");
        }
      }
    }
    context.SetStringListCommonAttr("value_list", std::move(value_list));
    return true;
  }

  static bool MultiGetFromIntBytesIntMap(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto key_list = context.GetIntListCommonAttr("key_list");
    auto default_value = context.GetIntCommonAttr("default_value").value_or(0);
    auto bytes_size = context.GetIntCommonAttr("bytes_size").value_or(8);
    auto attr_num = context.GetIntCommonAttr("attr_num").value_or(9);
    auto map_ptr = context.GetPtrCommonAttr<std::unordered_map<uint64_t, std::string>>("map_ptr");
    std::vector<std::vector<int64>> values_list(attr_num);
    if (key_list) {
      for (int i = 0; i < key_list->size(); i++) {
        const auto &key = key_list->at(i);
        std::vector<int64> values(attr_num, default_value);
        if (map_ptr && map_ptr->count(key) > 0) {
          auto &source_bytes = map_ptr->at(key);
          // sim_cluster_id,realshow,author_circle_id,musicId,musicTagUsedTimes,mmuFingerprintId,hetu_tag_num,hetu_tag_list,face_id_num,face_id_list,user_hash_tag_id_list,hetu_sim_cluster_id_v4,author_circle_lv1
          size_t array_length = source_bytes.size() / bytes_size;
          for (int j = 0; j < array_length; j++) {
            int64 tmp;
            memcpy(&tmp, source_bytes.data() + j * bytes_size, bytes_size);
            if (j < values.size()) {
              values[j] = tmp;
            }
          }
        }
        for (int j = 0; j < attr_num; j++) {
          values_list[j].emplace_back(values[j]);
        }
      }
    }
    for (int j = 0; j < attr_num; j++) {
      context.SetIntListCommonAttr("value_list" + std::to_string(j), std::move(values_list[j]));
    }
    return true;
  }

  static bool MultiGetFromIntBytesIntStringMap(const CommonRecoLightFunctionContext &context,
                                         RecoResultConstIter begin, RecoResultConstIter end) {
    auto key_list = context.GetIntListCommonAttr("key_list");
    auto default_value = context.GetIntCommonAttr("default_value").value_or(0);
    auto bytes_size = context.GetIntCommonAttr("bytes_size").value_or(8);
    auto attr_num = context.GetIntCommonAttr("attr_num").value_or(4);
    auto map_ptr = context.GetPtrCommonAttr<std::unordered_map<uint64_t, std::string>>("map_ptr");
    // std::vector<std::vector<int64>> values_list(attr_num);
    std::vector<std::vector<int64_t>> int_values_list(8);
    std::vector<std::vector<std::string>> string_values_list(3);
    if (key_list) {
      for (int i = 0; i < key_list->size(); i++) {
        const auto &key = key_list->at(i);
        std::vector<int64_t> fixed_values(8, default_value);
        std::string hetu_str, face_str, hash_str;
        if (map_ptr && map_ptr->count(key) > 0) {
          auto &source_bytes = map_ptr->at(key);
          size_t offset = 0;
          // sim_cluster_id,realshow,author_circle_id,musicId,musicTagUsedTimes,mmuFingerprintId,hetu_sim_cluster_id_v4,author_circle_lv1
          for (int j = 0; j < 8; ++j) {
            if (offset + bytes_size <= source_bytes.size()) {
              int64_t val;
              memcpy(&val, source_bytes.data() + offset, bytes_size);
              fixed_values[j] = val;
              offset += bytes_size;
            }
          }
          // 读取 hetu_tag_num 和 hetu_tags list
          int64_t hetu_num = 0;
          if (offset + bytes_size <= source_bytes.size()) {
            memcpy(&hetu_num, source_bytes.data() + offset, bytes_size);
            offset += bytes_size;
          }
          for (int j = 0; j < hetu_num && offset + bytes_size <= source_bytes.size(); ++j) {
            int64_t tag;
            memcpy(&tag, source_bytes.data() + offset, bytes_size);
            offset += bytes_size;
            hetu_str += std::to_string(tag) + "\t";
          }
          if (!hetu_str.empty()) hetu_str.pop_back();

          // 读取 face_id_num 和 face_ids list
          int64_t face_num = 0;
          if (offset + bytes_size <= source_bytes.size()) {
            memcpy(&face_num, source_bytes.data() + offset, bytes_size);
            offset += bytes_size;
          }
          for (int j = 0; j < face_num && offset + bytes_size <= source_bytes.size(); ++j) {
            int64_t id;
            memcpy(&id, source_bytes.data() + offset, bytes_size);
            offset += bytes_size;
            face_str += std::to_string(id) + "\t";
          }
          if (!face_str.empty()) face_str.pop_back();

          // 读取 hash_tag_count 和 hash_tag list
          int64_t hash_num = 0;
          if (offset + bytes_size <= source_bytes.size()) {
            memcpy(&hash_num, source_bytes.data() + offset, bytes_size);
            offset += bytes_size;
          }
          for (int j = 0; j < hash_num && offset + bytes_size <= source_bytes.size(); ++j) {
            int64_t id;
            memcpy(&id, source_bytes.data() + offset, bytes_size);
            offset += bytes_size;
            hash_str += std::to_string(id) + "\t";
          }
          if (!hash_str.empty()) hash_str.pop_back();
        }
        for (int j = 0; j < 8; ++j) {
          int_values_list[j].emplace_back(fixed_values[j]);
          }
        string_values_list[0].emplace_back(hetu_str);
        string_values_list[1].emplace_back(face_str);
        string_values_list[2].emplace_back(hash_str);
      }
    }
    for (int j = 0; j < 8; j++) {
      context.SetIntListCommonAttr("value_list" + std::to_string(j), std::move(int_values_list[j]));
    }
    for (int j = 0; j < 3; ++j) {
      context.SetStringListCommonAttr("string_value_list" + std::to_string(j),
                                      std::move(string_values_list[j]));
    }
    return true;
  }

  static bool CheckLongView(uint32_t duration, uint32_t play_time) {
    if (duration < 3)
      return play_time >= 18;
    else if (duration >= 36)
      return play_time >= 36;
    else
      return play_time >= (duration * 28 + 180) / 33.0;
  }

  static bool CalHistClusterWeights(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                    RecoResultConstIter end) {
    auto pid_list = context.GetIntListCommonAttr("hist_pid_list");
    auto cluster_list = context.GetIntListCommonAttr("cluster_list");
    auto author_circle_list = context.GetIntListCommonAttr("author_circle_list");
    auto realshow_list = context.GetIntListCommonAttr("realshow_list");
    auto music_id_list = context.GetIntListCommonAttr("music_id_list");
    auto music_used_times_list = context.GetIntListCommonAttr("music_used_times_list");
    auto mmu_finger_id_list = context.GetIntListCommonAttr("mmu_finger_id_list");
    auto hetu_sim_cluster_id_v4_list = context.GetIntListCommonAttr("hetu_sim_cluster_id_v4_list");
    auto author_circle_lv1_list = context.GetIntListCommonAttr("author_circle_lv1_list");
    auto hetu_tag_list = context.GetStringListCommonAttr("hetu_tag_list");
    auto face_id_list = context.GetStringListCommonAttr("face_id_list");
    auto user_hash_tag_id_list = context.GetStringListCommonAttr("user_hash_tag_id_list");
    auto play_time_list = context.GetIntListCommonAttr("play_time_list");
    auto timestamp_list = context.GetIntListCommonAttr("timestamp_list");
    auto duration_list = context.GetIntListCommonAttr("duration_list");
    auto label_list = context.GetIntListCommonAttr("label_list");
    auto time_win = context.GetIntCommonAttr("time_win").value_or(0);            // day
    auto ev_thres = context.GetIntCommonAttr("ev_thres").value_or(7);            // s
    auto max_tag_size = context.GetIntCommonAttr("max_tag_size").value_or(1000);
    auto cluster_tags_map_ptr =
        context.GetPtrCommonAttr<folly::F14FastMap<uint64, std::vector<double>>>("cluster_tags_map_ptr");
    auto user_tag_weights_str =
        context.GetStringCommonAttr("user_tag_weights").value_or("");
    std::vector<double> user_tag_weights;
    ks::reco::StringToDoubleVec(static_cast<std::string>(user_tag_weights_str), &user_tag_weights);

    // like,follow,forward,comment,profile,hate,decay_max,stop,weight
    auto reward_weights_config =
        context.GetStringCommonAttr("reward_weights").value_or("1.0,1.0,1.0,1.0,1.0,-1.0,1.0,0.0,1.0");
    std::vector<double> reward_weights;
    ks::reco::StringToDoubleVec(static_cast<std::string>(reward_weights_config), &reward_weights);

    if (!pid_list || !cluster_list || !play_time_list || !timestamp_list ||
      !duration_list ||!label_list || !author_circle_list ) {
      LOG_EVERY_N(ERROR, 10000) << "list is null.";
      return true;
    }
    if (!realshow_list) {
        LOG_EVERY_N(ERROR, 10000) << "realshow_list is null.";
        return true;
    }
    if (!hetu_sim_cluster_id_v4_list) {
        LOG_EVERY_N(ERROR, 10000) << "hetu_sim_cluster_id_v4_list is null.";
        return true;
    }
    if (!author_circle_lv1_list) {
        LOG_EVERY_N(ERROR, 10000) << "author_circle_lv1_list is null.";
        return true;
    }
    if (!music_id_list) {
        LOG_EVERY_N(ERROR, 10000) << "music_id_list is null.";
        return true;
    }
    if (!music_used_times_list) {
        LOG_EVERY_N(ERROR, 10000) << "music_used_times_list is null.";
        return true;
    }
    if (!mmu_finger_id_list) {
        LOG_EVERY_N(ERROR, 10000) << "mmu_finger_id_list is null.";
        return true;
    }
    if (pid_list->size() != cluster_list->size() || cluster_list->size() != play_time_list->size() ||
        play_time_list->size() != timestamp_list->size() || timestamp_list->size() != duration_list->size() ||
        duration_list->size() != label_list->size() || label_list->size() != author_circle_list->size() ||
        author_circle_list->size() != music_id_list->size() ||
        music_id_list->size() != music_used_times_list->size() ||
        music_used_times_list->size() != mmu_finger_id_list->size() ||
        mmu_finger_id_list->size() != realshow_list->size()||
        realshow_list->size() != hetu_sim_cluster_id_v4_list->size() ||
        hetu_sim_cluster_id_v4_list->size() != author_circle_lv1_list->size()
        ) {
      LOG_EVERY_N(ERROR, 10000) << "list size not match.";
      return true;
    }
    if (reward_weights.size() < 10) {
      LOG_EVERY_N(ERROR, 10000) << "#reward_weights less than 9, size=" << reward_weights.size();
      return false;
    }

    int64 cur_time_s = base::GetTimestamp() / 1e6;
    int hist_len =
        std::min(std::min(std::min(cluster_list->size(), play_time_list->size()), timestamp_list->size()),
                 label_list->size());
    folly::F14FastMap<int, double> clsuter_calibration_map, cluster_ratio_map;
    std::vector<int64> hist_author_circle_list;
    std::vector<int64> hist_realshow_list;
    std::vector<int64> hist_music_id_list;
    std::vector<int64> hist_music_used_times_list;
    std::vector<int64> hist_mmu_finger_id_list;
    std::vector<int64> hist_hetu_sim_cluster_id_v4_list;
    std::vector<int64> hist_author_circle_lv1_list;
    std::vector<std::string> hist_hetu_tag_list;
    std::vector<std::string> hist_face_id_list;
    std::vector<std::string> hist_user_hash_tag_id_list;

    for (int i = 0; i < hist_len; i++) {
      auto &pid = pid_list->at(i);
      auto &cluster_id = cluster_list->at(i);
      auto &author_circle_id = author_circle_list->at(i);
      auto &realshow = realshow_list->at(i);
      auto &music_id = music_id_list->at(i);
      auto &music_tag_used_times = music_used_times_list->at(i);
      auto &mmu_fingerprint_id = mmu_finger_id_list->at(i);
      auto &hetu_sim_cluster_id_v4 = hetu_sim_cluster_id_v4_list->at(i);
      auto &author_circle_lv1_id = author_circle_lv1_list->at(i);
      std::string hetu_tag;
      if (hetu_tag_list && i < hetu_tag_list->size()) {
          hetu_tag = std::string(hetu_tag_list->at(i));
          // GetStringListCommonAttr 返回的是 vector<absl::string_view>??
      }
      hist_hetu_tag_list.emplace_back(hetu_tag);

      std::string face_id;
      if (face_id_list && i < face_id_list->size()) {
          face_id = std::string(face_id_list->at(i));
      }
      hist_face_id_list.emplace_back(face_id);

      std::string user_hash_tag_id;
      if (user_hash_tag_id_list && i < user_hash_tag_id_list->size()) {
          user_hash_tag_id = std::string(user_hash_tag_id_list->at(i));
      }
      hist_user_hash_tag_id_list.emplace_back(user_hash_tag_id);
      auto &play_time = play_time_list->at(i);
      auto &duration = duration_list->at(i);
      auto &timestamp = timestamp_list->at(i);
      auto &label = label_list->at(i);
      bool like = label & 0x01;
      bool follow = label & (1 << 1);
      bool forward = label & (1 << 2);
      bool hate = label & (1 << 3);
      bool comment = label & (1 << 4);
      bool profile = label & (1 << 6);
      float day_gap = (cur_time_s - timestamp) / 3600.0 / 24.0;
      if (day_gap > time_win) {
        break;
      }
      if (play_time >= ev_thres) {
        double action_weight = reward_weights[0] * like + reward_weights[1] * follow +
                        reward_weights[2] * forward + reward_weights[3] * comment +
                        reward_weights[4] * profile + reward_weights[5] * hate +
                        reward_weights[6] * play_time / 60.0;
        clsuter_calibration_map[cluster_id] += action_weight;
        // clsuter_calibration_sum += action_weight;
        cluster_ratio_map[cluster_id] += 1;
        // cluster_count_sum += 1;
      } else {
        clsuter_calibration_map[cluster_id] += 0;
        cluster_ratio_map[cluster_id] += 0;
      }
      hist_author_circle_list.emplace_back(author_circle_id);
      hist_realshow_list.emplace_back(realshow);
      hist_music_id_list.emplace_back(music_id);
      hist_music_used_times_list.emplace_back(music_tag_used_times);
      hist_mmu_finger_id_list.emplace_back(mmu_fingerprint_id);
      hist_hetu_sim_cluster_id_v4_list.emplace_back(hetu_sim_cluster_id_v4);
      hist_author_circle_lv1_list.emplace_back(author_circle_lv1_id);
    }
    folly::F14FastMap<int, double> tag_score_map;
    // cluster-tag
    if (cluster_tags_map_ptr) {
      for (auto it = clsuter_calibration_map.begin(); it != clsuter_calibration_map.end(); it++) {
        const auto &cluster = it->first;
        const auto &cluster_score = it->second;
        if (cluster_tags_map_ptr->count(cluster) == 0) continue;
        auto &tags_weights = cluster_tags_map_ptr->at(cluster);
        for (int t = 0; t + 1 < tags_weights.size(); t += 2) {
          int tag_id = tags_weights[t];
          double tag_w = tags_weights[t+1];
          tag_score_map[tag_id] += tag_w * cluster_score;
        }
      }
    }
    // user-tag
    if (user_tag_weights.size() > 0) {
      for (int t = 0; t + 1 < user_tag_weights.size(); t += 2) {
        int tag_id = user_tag_weights[t];
        double tag_w = user_tag_weights[t+1];
        // double tag_vv = user_tag_weights[t+2];
        tag_score_map[tag_id] += tag_w;
      }
    }
    // fill
    std::vector<int64> hist_cluster_list, content_tag_list;
    std::vector<double> clsuter_calibration_weights, cluster_ratios, content_tag_weights;
    for (auto it = clsuter_calibration_map.begin(); it != clsuter_calibration_map.end(); it++) {
      const auto &cluster = it->first;
      hist_cluster_list.emplace_back(cluster);
      double weight = it->second;  // clsuter_calibration_sum > 0 ? it->second / clsuter_calibration_sum : 0.;
      clsuter_calibration_weights.emplace_back(std::max(weight, 1e-6));
      cluster_ratios.emplace_back(std::max(cluster_ratio_map[cluster], 1e-6));
    }
    double min_weight = 99999.0, max_weight = -99999.0;
    for (auto tag_it = tag_score_map.begin(); tag_it != tag_score_map.end(); tag_it++) {
      min_weight = std::min(min_weight, tag_it->second);
      max_weight = std::max(max_weight, tag_it->second);
    }
    for (auto tag_it = tag_score_map.begin(); tag_it != tag_score_map.end(); tag_it++) {
      content_tag_list.emplace_back(tag_it->first);
      double diff = max_weight - min_weight;
      double w = diff > 0. ? (tag_it->second - min_weight) / diff : tag_it->second;
      content_tag_weights.emplace_back(w);
    }
    context.SetIntListCommonAttr("hist_cluster_list", std::move(hist_cluster_list));
    context.SetIntListCommonAttr("hist_author_circle_list", std::move(hist_author_circle_list));
    context.SetIntListCommonAttr("hist_realshow_list", std::move(hist_realshow_list));
    context.SetIntListCommonAttr("hist_music_id_list", std::move(hist_music_id_list));
    context.SetIntListCommonAttr("hist_music_used_times_list", std::move(hist_music_used_times_list));
    context.SetIntListCommonAttr("hist_mmu_finger_id_list", std::move(hist_mmu_finger_id_list));
    context.SetIntListCommonAttr("hist_hetu_sim_cluster_id_v4_list",
                                 std::move(hist_hetu_sim_cluster_id_v4_list));
    context.SetIntListCommonAttr("hist_author_circle_lv1_list", std::move(hist_author_circle_lv1_list));
    context.SetStringListCommonAttr("hist_hetu_tag_list", std::move(hist_hetu_tag_list));
    context.SetStringListCommonAttr("hist_face_id_list", std::move(hist_face_id_list));
    context.SetStringListCommonAttr("hist_user_hash_tag_id_list", std::move(hist_user_hash_tag_id_list));
    context.SetDoubleListCommonAttr("clsuter_calibration_weights", std::move(clsuter_calibration_weights));
    context.SetDoubleListCommonAttr("cluster_ratios", std::move(cluster_ratios));
    context.SetIntListCommonAttr("content_tag_list", std::move(content_tag_list));
    context.SetDoubleListCommonAttr("content_tag_weights", std::move(content_tag_weights));
    return true;
  }

  static bool CalHistConfBuckets(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto play_time_list = context.GetIntListCommonAttr("play_time_list");
    // auto timestamp_list = context.GetIntListCommonAttr("timestamp_list");
    auto label_list = context.GetIntListCommonAttr("label_list");
    auto conf_list = context.GetIntListCommonAttr("conf_list");
    auto bucket_num = context.GetIntCommonAttr("bucket_num").value_or(0);
    // auto time_win = context.GetIntCommonAttr("time_win").value_or(0);  // day
    auto ev_thres = context.GetIntCommonAttr("ev_thres").value_or(7);  // s

    if (!play_time_list || !label_list || !conf_list) {
      LOG_EVERY_N(ERROR, 10000) << "list is null.";
      return false;
    }
    // fill positive items
    int hist_len = std::min(std::min(conf_list->size(), play_time_list->size()), label_list->size());
    // int64 cur_time_s = base::GetTimestamp() / 1e6;
    std::vector<ConfItem> hist_conf_items;
    for (int i = 0; i < hist_len; i++) {
      auto &play_time = play_time_list->at(i);
      /*
      auto &timestamp = timestamp_list->at(i);
      float day_gap = (cur_time_s - timestamp) / 3600.0 / 24.0;
      if (day_gap > time_win) break;
      bool like = label & 0x01;
      bool follow = label & (1 << 1);
      bool forward = label & (1 << 2);
      bool comment = label & (1 << 4);
      bool profile = label & (1 << 6);
      bool effective_play = play_time >= ev_thres;
      bool pos_interaction = like || follow || forward || comment || profile;
      bool is_positive = effective_play || pos_interaction;
      */
      auto &conf = conf_list->at(i);
      auto &label = label_list->at(i);
      hist_conf_items.emplace_back(play_time, label, conf);
    }
    // sort by conf
    hist_len = hist_conf_items.size();
    bucket_num = bucket_num > hist_len ? 1 : bucket_num;
    int bucket_len = hist_len / bucket_num;
    if (bucket_len <= 0 || hist_len < 1) {
      LOG_EVERY_N(ERROR, 10000) << "Size error. bucket_len=" << bucket_len << ",hist_len=" << hist_len;
      return true;
    }
    std::sort(hist_conf_items.begin(), hist_conf_items.end(),
              [](const ConfItem &a, const ConfItem &b) { return a.conf < b.conf; });
    // bucket
    std::vector<BucketItem> bucket_items;
    for (int b = 0; b < bucket_num; b++) {
      int start_i = b * bucket_len, end_i = (b + 1) * bucket_len;
      float bucket_conf_mean = 0;
      for (int i = start_i; i < end_i; i++) {
        bucket_conf_mean += hist_conf_items[i].conf;
      }
      bucket_conf_mean /= bucket_len;
      float dist_all = 0, dist_min = std::abs(hist_conf_items[0].conf - bucket_conf_mean), dist_max = -1;
      float pvtr_score = 0., pltr_score = 0., pftr_score = 0., pwtr_score = 0., pcmtr_score = 0.;
      for (int i = start_i; i < end_i; i++) {
        float cur_dist = std::abs(hist_conf_items[i].conf - bucket_conf_mean);
        dist_all += cur_dist;
        dist_min = std::min(dist_min, cur_dist);
        dist_max = std::max(dist_max, cur_dist);
        auto &play_time = hist_conf_items[i].play_time;
        auto &label = hist_conf_items[i].label;
        pvtr_score += play_time;
        pltr_score += (label & 0x01);
        pftr_score += (label & (1 << 2));
        pwtr_score += (label & (1 << 1));
        pcmtr_score += (label & (1 << 4));
      }
      float dist_var = (dist_all / bucket_len - dist_min) / std::max(dist_max - dist_min, 1.f);
      pvtr_score /= bucket_len;
      pltr_score /= bucket_len;
      pftr_score /= bucket_len;
      pwtr_score /= bucket_len;
      pcmtr_score /= bucket_len;
      bucket_items.emplace_back(pvtr_score, pltr_score, pftr_score, pwtr_score, pcmtr_score, dist_var,
                                hist_conf_items[start_i].conf, hist_conf_items[end_i - 1].conf);
    }
    // item
    auto item_confs = context.GetIntItemAttr("item_confs");
    auto set_conf_dist_var = context.SetDoubleItemAttr("conf_dist_var");
    auto set_conf_pvtr = context.SetDoubleItemAttr("conf_pvtr");
    auto set_conf_pltr = context.SetDoubleItemAttr("conf_pltr");
    auto set_conf_pftr = context.SetDoubleItemAttr("conf_pftr");
    auto set_conf_pwtr = context.SetDoubleItemAttr("conf_pwtr");
    auto set_conf_pcmtr = context.SetDoubleItemAttr("conf_pcmtr");
    std::for_each(begin, end, [=, &bucket_items](const CommonRecoResult &result) {
      int64 item_conf = item_confs(result).value_or(0);
      auto hit_bucket_it = std::lower_bound(bucket_items.begin(), bucket_items.end(), item_conf,
                                            [](const BucketItem &a, int64 b) { return a.start_conf < b; });
      hit_bucket_it = (hit_bucket_it == bucket_items.end()) ? (bucket_items.end() - 1) : hit_bucket_it;
      set_conf_dist_var(result, hit_bucket_it->dist_var);
      set_conf_pvtr(result, hit_bucket_it->pvtr_score);
      set_conf_pltr(result, hit_bucket_it->pltr_score);
      set_conf_pftr(result, hit_bucket_it->pftr_score);
      set_conf_pwtr(result, hit_bucket_it->pwtr_score);
      set_conf_pcmtr(result, hit_bucket_it->pcmtr_score);
    });
    // context.SetPtrCommonAttr("bucket_items", std::move(&bucket_items));
    return true;
  }

  static bool CalHistConfAggr(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                              RecoResultConstIter end) {
    auto conf_list = context.GetIntListCommonAttr("conf_list");
    auto bucket_num = context.GetIntCommonAttr("bucket_num").value_or(0);

    if (!conf_list || conf_list->size() <= 0) {
      LOG_EVERY_N(ERROR, 10000) << "list is null.";
      return false;
    }
    // fill
    std::vector<int64> conf_items(conf_list->begin(), conf_list->end());
    int hist_len = conf_items.size();
    bucket_num = bucket_num > hist_len ? 1 : bucket_num;
    int bucket_len = hist_len / bucket_num;
    if (bucket_len <= 0 || hist_len < 1) {
      LOG_EVERY_N(ERROR, 10000) << "Size error. bucket_len=" << bucket_len << ",hist_len=" << hist_len;
      return true;
    }
    // sort
    std::sort(conf_items.begin(), conf_items.end());
    // bucket
    std::vector<BucketItem> bucket_items;
    for (int b = 0; b < bucket_num; b++) {
      int start_i = b * bucket_len, end_i = (b + 1) * bucket_len;
      float bucket_conf_mean = 0;
      for (int i = start_i; i < end_i; i++) {
        bucket_conf_mean += conf_items[i];
      }
      bucket_conf_mean /= bucket_len;
      float dist_all = 0, dist_min = std::abs(conf_items[0] - bucket_conf_mean), dist_max = -1;
      for (int i = start_i; i < end_i; i++) {
        float cur_dist = std::abs(conf_items[i] - bucket_conf_mean);
        dist_all += cur_dist;
        dist_min = std::min(dist_min, cur_dist);
        dist_max = std::max(dist_max, cur_dist);
      }
      float dist_var = (dist_all / bucket_len - dist_min) / std::max(dist_max - dist_min, 1.f);
      bucket_items.emplace_back(0.f, 0.f, 0.f, 0.f, 0.f, dist_var, conf_items[start_i],
                                conf_items[end_i - 1]);
    }
    // item
    auto item_confs = context.GetIntItemAttr("item_confs");
    auto set_conf_dist_var = context.SetDoubleItemAttr("conf_dist_var");
    std::for_each(begin, end, [=, &bucket_items](const CommonRecoResult &result) {
      int64 item_conf = item_confs(result).value_or(0);
      auto hit_bucket_it = std::lower_bound(bucket_items.begin(), bucket_items.end(), item_conf,
                                            [](const BucketItem &a, int64 b) { return a.start_conf < b; });
      hit_bucket_it = (hit_bucket_it == bucket_items.end()) ? (bucket_items.end() - 1) : hit_bucket_it;
      set_conf_dist_var(result, hit_bucket_it->dist_var);
    });
    // context.SetPtrCommonAttr("bucket_items", std::move(&bucket_items));
    return true;
  }

  static bool CheckAuthorEmbdMiss(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto mmu_author_emb_list = context.GetDoubleListItemAttr("mmu_author_emb");
    auto set_is_miss_embedding = context.SetIntItemAttr("is_miss_embedding");
    int emb_dim_size_128 = 128;
    int emb_dim_size_64 = 64;
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      // item mmu embd
      auto mmu_author_emb = mmu_author_emb_list(result);
      int is_miss_embedding_flag = 1;
      if (!mmu_author_emb) {
        is_miss_embedding_flag = 1;
      } else if (mmu_author_emb->size() == emb_dim_size_64 || mmu_author_emb->size() == emb_dim_size_128) {
        is_miss_embedding_flag = 0;
      }
      set_is_miss_embedding(result, is_miss_embedding_flag);
    });
    return true;
  }

  static bool GenerateSeq(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto photo_id_attr = context.GetIntItemAttr("photo_id");
    auto like_attr = context.GetIntItemAttr("like");
    auto comment_attr = context.GetIntItemAttr("comment");
    auto forward_attr = context.GetIntItemAttr("forward");
    auto follow_attr = context.GetIntItemAttr("follow");
    auto long_view_attr = context.GetIntItemAttr("long_view");

    auto photo_id_list_attr = context.SetIntListItemAttr("photo_id_list");
    auto like_list_attr = context.SetIntListItemAttr("like_list");
    auto comment_list_attr = context.SetIntListItemAttr("comment_list");
    auto forward_list_attr = context.SetIntListItemAttr("forward_list");
    auto follow_list_attr = context.SetIntListItemAttr("follow_list");
    auto long_view_list_attr = context.SetIntListItemAttr("long_view_list");

    std::vector<int64> photo_id_list_all;
    std::vector<int64> like_list_all;
    std::vector<int64> comment_list_all;
    std::vector<int64> forward_list_all;
    std::vector<int64> follow_list_all;
    std::vector<int64> long_view_list_all;
    std::vector<int64> idx_list_all;
    int64 idx = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 photo_id = photo_id_attr(result).value_or(0);
      int64 like = like_attr(result).value_or(0);
      int64 comment = comment_attr(result).value_or(0);
      int64 forward = forward_attr(result).value_or(0);
      int64 follow = follow_attr(result).value_or(0);
      int64 long_view = long_view_attr(result).value_or(0);
      photo_id_list_all.push_back(photo_id);
      like_list_all.push_back(like);
      comment_list_all.push_back(comment);
      forward_list_all.push_back(forward);
      follow_list_all.push_back(follow);
      long_view_list_all.push_back(long_view);
      idx_list_all.push_back(idx);
      ++idx;
    });
    int64 idx2 = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      std::vector<int64> photo_id_list;
      std::vector<int64> like_list;
      std::vector<int64> comment_list;
      std::vector<int64> forward_list;
      std::vector<int64> follow_list;
      std::vector<int64> long_view_list;
      photo_id_list.assign(photo_id_list_all.begin(), photo_id_list_all.begin() + idx2);
      like_list.assign(like_list_all.begin(), like_list_all.begin() + idx2);
      comment_list.assign(comment_list_all.begin(), comment_list_all.begin() + idx2);
      forward_list.assign(forward_list_all.begin(), forward_list_all.begin() + idx2);
      follow_list.assign(follow_list_all.begin(), follow_list_all.begin() + idx2);
      long_view_list.assign(long_view_list_all.begin(), long_view_list_all.begin() + idx2);
      photo_id_list_attr(result, photo_id_list);
      like_list_attr(result, like_list);
      comment_list_attr(result, comment_list);
      forward_list_attr(result, forward_list);
      long_view_list_attr(result, follow_list);
      long_view_list_attr(result, long_view_list);
      ++idx2;
    });
    return true;
  }
  static bool SampleFromList(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto model_sid_first_prob = context.GetDoubleListCommonAttr("model_sid_first_prob");
    auto model_sid_second_prob = context.GetDoubleListCommonAttr("model_sid_second_prob");
    auto model_sid_third_prob = context.GetDoubleListCommonAttr("model_sid_third_prob");
    auto sample_num_per_layer = context.GetIntCommonAttr("sample_num_per_layer");
    if (!model_sid_first_prob || model_sid_first_prob->empty() ||
        !model_sid_second_prob || model_sid_second_prob->empty() ||
        !model_sid_third_prob || model_sid_third_prob->empty() ||
        !sample_num_per_layer) {
      CL_LOG_EVERY_N(WARNING, 1000)
          << "InterestExploreLightFunctionSet. SampleFromList failed to get data";
      return true;
    }

    double sum_1 = 0.0;
    double sum_2 = 0.0;
    double sum_3 = 0.0;
    for (std::size_t i=0; i < model_sid_first_prob->size(); i++) {
      sum_1 += model_sid_first_prob->at(i);
      if (i < model_sid_second_prob->size()) sum_2 += model_sid_second_prob->at(i);
      if (i < model_sid_third_prob->size()) sum_3 += model_sid_third_prob->at(i);
    }
    std::vector<double> random_doubles1;
    std::vector<double> random_doubles2;
    std::vector<double> random_doubles3;
    std::srand(std::time(nullptr));
    for (int i = 0; i < sample_num_per_layer ; i++) {
      random_doubles1.push_back(static_cast<double>(std::rand())/RAND_MAX);
      random_doubles2.push_back(static_cast<double>(std::rand())/RAND_MAX);
      random_doubles3.push_back(static_cast<double>(std::rand())/RAND_MAX);
    }
    std::sort(random_doubles1.begin(), random_doubles1.end());
    std::sort(random_doubles2.begin(), random_doubles2.end());
    std::sort(random_doubles3.begin(), random_doubles3.end());

    double cum_1 = 0.0;
    double cum_2 = 0.0;
    double cum_3 = 0.0;
    int used_num1 = 0;
    int used_num2 = 0;
    int used_num3 = 0;
    std::vector<int64> sid1_list;
    std::vector<int64> sid2_list;
    std::vector<int64> sid3_list;
    int64 i = 0;
    while (i < model_sid_first_prob->size()) {
      cum_1 += model_sid_first_prob->at(i) / (0.01 + sum_1);
      if (used_num1 < sample_num_per_layer && used_num1 < random_doubles1.size() &&
          cum_1 > random_doubles1[used_num1]) {
        sid1_list.push_back(i);
        while (used_num1 < sample_num_per_layer && used_num1 < random_doubles1.size() &&
              cum_1 > random_doubles1[used_num1]) {
          used_num1++;
        }
      }
      i++;
    }
    i = 0;
    while (i < model_sid_second_prob->size()) {
      cum_2 += model_sid_second_prob->at(i) / (0.01 + sum_2);
      if (used_num2 < sample_num_per_layer && used_num2 < random_doubles2.size() &&
          cum_2 > random_doubles2[used_num2]) {
        sid2_list.push_back(i);
        while (used_num2 < sample_num_per_layer && used_num2 < random_doubles2.size() &&
          cum_2 > random_doubles2[used_num2]) {
          used_num2++;
        }
      }
      i++;
    }
    i = 0;
    while (i < model_sid_third_prob->size()) {
      cum_3 += model_sid_third_prob->at(i) / (0.01 + sum_3);
      if (used_num3 < sample_num_per_layer && used_num3 < random_doubles3.size() &&
          cum_3 > random_doubles3[used_num3]) {
        sid3_list.push_back(i);
        while (used_num3 < sample_num_per_layer && used_num3 < random_doubles3.size() &&
          cum_3 > random_doubles3[used_num3]) {
          used_num3++;
        }
      }
      i++;
    }
    context.SetIntListCommonAttr("sid1_list", std::move(sid1_list));
    context.SetIntListCommonAttr("sid2_list", std::move(sid2_list));
    context.SetIntListCommonAttr("sid3_list", std::move(sid3_list));
    return true;
  }

  static bool GetCocoonPids(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto photo_id_list = context.GetIntListCommonAttr("photo_id_list");
    auto cluster_id_list = context.GetIntListCommonAttr("cluster_id_list");
    auto long_cluster_id_list = context.GetIntListCommonAttr("long_cluster_id_list");
    std::vector<int64> in_cocoon_pids;
    std::vector<int64> out_cocoon_pids;
    in_cocoon_pids.clear();
    out_cocoon_pids.clear();
    if (!long_cluster_id_list || long_cluster_id_list->empty() ||
        !cluster_id_list || cluster_id_list->empty() ||
        !photo_id_list || photo_id_list->empty()) {
      CL_LOG_EVERY_N(WARNING, 1000)
          << "InterestExploreLightFunctionSet. GetCocoonPids failed to get data";
      return true;
    }
    std::unordered_set<int> within_cocoon_ids(long_cluster_id_list->begin(), long_cluster_id_list->end());
    for (std::size_t i=0; i < photo_id_list->size() && i < cluster_id_list->size(); i++) {
      if (within_cocoon_ids.count(cluster_id_list->at(i)) > 0) {
        in_cocoon_pids.emplace_back(photo_id_list->at(i));
      } else {
        out_cocoon_pids.emplace_back(photo_id_list->at(i));
      }
    }
    context.SetIntListCommonAttr("in_cocoon_pids", std::move(in_cocoon_pids));
    context.SetIntListCommonAttr("out_cocoon_pids", std::move(out_cocoon_pids));
    return true;
  }

  static bool MultiEmbSimilarity(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto item_emb_accessor = context.GetDoubleListItemAttr("item_emb");
    auto item_type_accessor = context.GetIntItemAttr("item_type");
    auto emb_dim = context.GetIntCommonAttr("emb_dim").value_or(128);
    auto method = context.GetIntCommonAttr("method").value_or(0);
    auto sim_score_setter = context.SetDoubleItemAttr("sim_score");
    auto item_norm_setter = context.SetDoubleItemAttr("item_norm");

    if (!item_emb_accessor) {
      LOG_EVERY_N(ERROR, 10000) << "item_emb_accessor is null.";
      return false;
    }

    std::vector<double> aggregated_emb(emb_dim, 0.0);
    double aggregated_emb_mean_norm = 0.0;
    int item_num = std::distance(begin, end);
    int agg_type = std::abs(method % 10);
    int agg_method = std::abs(method / 10);

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_emb = item_emb_accessor(result);
      int64 item_type = item_type_accessor(result).value_or(0);
      if (!item_emb || item_type != agg_type) return;
      for (int i = 0; i < item_emb->size() && i < emb_dim; i++) {
        double v = item_emb->at(i);
        if (agg_method == 0) {
          aggregated_emb[i] += v / (item_num + 1e-6);
        } else if (agg_method == 1) {
          aggregated_emb[i] = std::max(aggregated_emb[i], v);
        }
      }
    });
    for (const auto &v : aggregated_emb) {
      aggregated_emb_mean_norm += v * v;
    }
    aggregated_emb_mean_norm = std::sqrt(aggregated_emb_mean_norm);

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_emb = item_emb_accessor(result);
      int64 item_type = item_type_accessor(result).value_or(0);
      if (item_type != 0) return;
      double sim_score = 0.0, emb_mean_norm = 0.0;
      if (item_emb) {
        int emb_size = std::min((int)item_emb->size(), (int)emb_dim);
        for (int i = 0; i < emb_size; i++) {
          emb_mean_norm += item_emb->at(i) * item_emb->at(i);
        }
        emb_mean_norm = std::sqrt(emb_mean_norm);
        for (int i = 0; i < item_emb->size() && i < emb_dim; i++) {
          double mod_len = emb_mean_norm * aggregated_emb_mean_norm;
          sim_score += item_emb->at(i) * aggregated_emb[i] / (mod_len + 1e-6);
        }
        sim_score = std::max(-1.0, std::min(1.0, sim_score));
        sim_score_setter(result, sim_score);
      } else {
        sim_score_setter(result, -1.0);
      }
      item_norm_setter(result, emb_mean_norm);
    });
    context.SetDoubleListCommonAttr("agg_emb", std::move(aggregated_emb));
    context.SetDoubleCommonAttr("agg_norm", aggregated_emb_mean_norm);
    return true;
  }
  static bool GetCocoonPids2(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto photo_id_list = context.GetIntListCommonAttr("photo_id_list");
    auto cluster_id_list = context.GetIntListCommonAttr("cluster_id_list");
    auto long_cluster_id_list = context.GetIntListCommonAttr("long_cluster_id_list");
    std::vector<int64> in_cocoon_pids;
    std::vector<int64> out_cocoon_pids;
    in_cocoon_pids.clear();
    out_cocoon_pids.clear();
    if (!long_cluster_id_list || long_cluster_id_list->empty() ||
        !cluster_id_list || cluster_id_list->empty() ||
        !photo_id_list || photo_id_list->empty()) {
      LOG_EVERY_N(WARNING, 1000)
          << "InterestExploreLightFunctionSet. GetCocoonPids failed to get data";
      return true;
    }
    // 初始化参数
    std::string user_type = "ukn";
    if (context.GetIntCommonAttr("is_nebula_user").value_or(0) > 0) {
      user_type = "nebula";
    } else if (context.GetIntCommonAttr("is_gamora_user").value_or(0) > 0) {
      user_type = "gamora";
    }
    std::unordered_set<int> within_cocoon_ids(long_cluster_id_list->begin(), long_cluster_id_list->end());
    // kconf 读取 hetu cluster 映射信息
    auto hetu_cluster_map_ptr =
        ks::infra::KConf()
            .GetList("reco.interestExplore.remapClusterId632", std::make_shared<std::vector<int>>())
            ->Get();
    if (hetu_cluster_map_ptr == nullptr) {
      LOG_EVERY_N(WARNING, 1000)
          << "InterestExploreLightFunctionSet. GetCocoonPids failed to get cluster_632 kconf";
      return true;
    }
    for (std::size_t i=0; i < photo_id_list->size() && i < cluster_id_list->size(); i++) {
      if (hetu_cluster_map_ptr && cluster_id_list->at(i) >= 0 &&
          cluster_id_list->at(i) < hetu_cluster_map_ptr->size()) {
        int mapping_cluster_id = hetu_cluster_map_ptr->at(cluster_id_list->at(i));
        if (within_cocoon_ids.count(mapping_cluster_id)) {
          in_cocoon_pids.emplace_back(photo_id_list->at(i));
        } else {
          out_cocoon_pids.emplace_back(photo_id_list->at(i));
        }
      } else {
        out_cocoon_pids.emplace_back(photo_id_list->at(i));
      }
    }
    auto enable_mc_non_slide_jingxi_interest_boost
               = context.GetIntCommonAttr("enable_mc_non_slide_jingxi_interest_boost").value_or(0);
    auto cascading_interest_explore_jingxi_pid_list_type
               = context.GetIntCommonAttr("cascading_interest_explore_jingxi_pid_list_type").value_or(0);
    LOG_EVERY_N(INFO, 10000)
          << "InterestExploreLightFunctionSet GetCocoonPids:"
          << ",enable_mc_non_slide_jingxi_interest_boost:" << enable_mc_non_slide_jingxi_interest_boost
          << ",cascading_interest_explore_jingxi_pid_list_type:"
          << cascading_interest_explore_jingxi_pid_list_type
          << ",in_cocoon_pids_size:" << in_cocoon_pids.size()
          << ",outcocoon_size:" << out_cocoon_pids.size();
    if (cascading_interest_explore_jingxi_pid_list_type >= 4 &&
        enable_mc_non_slide_jingxi_interest_boost > 0) {
      static const std::string ns = "reco.cascading.trigger";
      ks::perfutil::PerfUtilWrapper::IntervalLogStashProduct(in_cocoon_pids.size(),
                                                              ns,
                                                              user_type,
                                                              "incocoon.pids");
      ks::perfutil::PerfUtilWrapper::IntervalLogStashProduct(out_cocoon_pids.size(),
                                                              ns,
                                                              user_type,
                                                              "outcocoon.pids");
    }
    context.SetIntListCommonAttr("in_cocoon_pids", std::move(in_cocoon_pids));
    context.SetIntListCommonAttr("out_cocoon_pids", std::move(out_cocoon_pids));
    return true;
  }

  static bool GetRecentInterestPids(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    auto photo_id_list = context.GetIntListCommonAttr("photo_id_list");
    auto cluster_id_list = context.GetIntListCommonAttr("cluster_id_list");
    auto recent_interest_cluster_list = context.GetIntListCommonAttr("recent_interest_cluster_list");
    std::vector<int64> in_cocoon_pids;
    std::vector<int64> out_cocoon_pids;
    in_cocoon_pids.clear();
    out_cocoon_pids.clear();
    if (!recent_interest_cluster_list || recent_interest_cluster_list->empty() ||
        !cluster_id_list || cluster_id_list->empty() ||
        !photo_id_list || photo_id_list->empty()) {
      LOG_EVERY_N(WARNING, 1000)
          << "InterestExploreLightFunctionSet. GetRecentInterestPids failed to get data";
      return true;
    }
    std::unordered_set<int> within_cocoon_ids(
      recent_interest_cluster_list->begin(), recent_interest_cluster_list->end());
    // kconf 读取 hetu cluster 映射信息
    auto hetu_cluster_map_ptr =
        ks::infra::KConf()
            .GetList("reco.interestExplore.remapClusterId632", std::make_shared<std::vector<int>>())
            ->Get();
    if (hetu_cluster_map_ptr == nullptr) {
      LOG_EVERY_N(WARNING, 1000)
          << "InterestExploreLightFunctionSet. GetRecentInterestPids failed to get cluster_632 kconf";
      return true;
    }
    for (std::size_t i=0; i < photo_id_list->size() && i < cluster_id_list->size(); i++) {
      if (hetu_cluster_map_ptr && cluster_id_list->at(i) >= 0 &&
          cluster_id_list->at(i) < hetu_cluster_map_ptr->size()) {
        int mapping_cluster_id = hetu_cluster_map_ptr->at(cluster_id_list->at(i));
        if (within_cocoon_ids.count(mapping_cluster_id)) {
          in_cocoon_pids.emplace_back(photo_id_list->at(i));
        } else {
          out_cocoon_pids.emplace_back(photo_id_list->at(i));
        }
      } else {
        out_cocoon_pids.emplace_back(photo_id_list->at(i));
      }
    }
    LOG_EVERY_N(INFO, 10000)
          << "InterestExploreLightFunctionSet GetRecentInterestPids:"
          << ",in_cocoon_pids_size:" << in_cocoon_pids.size()
          << ",outcocoon_size:" << out_cocoon_pids.size();
    context.SetIntListCommonAttr("in_cocoon_pids", std::move(in_cocoon_pids));
    context.SetIntListCommonAttr("out_cocoon_pids", std::move(out_cocoon_pids));
    return true;
  }


  static bool PCAEmbSimScore(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin, RecoResultConstIter end) {
    auto item_emb_accessor = context.GetDoubleListItemAttr("item_emb");
    auto item_emb_photo_accessor = context.GetDoubleListItemAttr("item_emb_photo");
    auto item_type_accessor = context.GetIntItemAttr("item_type");
    auto emb_dim = context.GetIntCommonAttr("emb_dim").value_or(128);
    auto var_threshold = context.GetDoubleCommonAttr("var_threshold").value_or(0.85);
    auto max_components_num = context.GetIntCommonAttr("max_components_num").value_or(5);
    auto sim_score_setter = context.SetDoubleItemAttr("pca_sim_score");

    if (!item_emb_accessor || !item_emb_photo_accessor) {
      LOG_EVERY_N(ERROR, 10000) << "PCAEmbSimScore: null item_emb_accessor || item_emb_photo_accessor";
      return false;
    }
    // preprocess
    std::vector<double> pospid_emb_vec;
    std::vector<double> candidate_emb_vec;
    double history_pospid_hit_ratio = 0.0, history_pospid_hit_num = 0.0;
    double candidate_hit_flag = 0.0, candidate_hit_num = 0.0;
    double pos_pid_cnt = 0.0, candidate_cnt = 0.0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_emb = item_emb_accessor(result);
      auto item_emb_photo = item_emb_photo_accessor(result);
      int64 item_type = item_type_accessor(result).value_or(-1);
      if (item_type == 2) {  // pospid
        pos_pid_cnt += 1.0;
        if (!item_emb || item_emb->size() % emb_dim != 0) {
          if (!item_emb_photo || item_emb_photo->size() != emb_dim) {
            return;
          } else {
            for (int i = 0; i < item_emb_photo->size(); ++i) pospid_emb_vec.push_back(item_emb_photo->at(i));
          }
        } else {
          for (int i = 0; i < item_emb->size(); ++i) pospid_emb_vec.push_back(item_emb->at(i));
          history_pospid_hit_ratio += 1.0;
          history_pospid_hit_num += (item_emb->size() / emb_dim);
        }
      } else if (item_type == 0) {  // candidate
        candidate_cnt += 1.0;
        std::vector<double> tmp_emb_default(emb_dim, 0.0);
        if (item_emb && item_emb->size() > 1e-6 && item_emb->size() % emb_dim == 0) {
          candidate_hit_flag += 1.0;
          candidate_hit_num += (item_emb->size() / emb_dim);
          int tag_num_tmp = item_emb->size() / emb_dim;
          for (int i = 0; i < item_emb->size(); ++i) {
            tmp_emb_default[i%emb_dim] += item_emb->at(i) / std::max(tag_num_tmp, 1);
          }
        } else if (item_emb_photo && item_emb_photo->size() > 1e-6 && item_emb_photo->size() == emb_dim) {
          for (int i = 0; i < emb_dim; ++i) tmp_emb_default[i] = item_emb_photo->at(i);
        }
        candidate_emb_vec.insert(candidate_emb_vec.end(), tmp_emb_default.begin(), tmp_emb_default.end());
      }
    });
    context.SetDoubleCommonAttr("history_pospid_hit_ratio",
      history_pospid_hit_ratio / std::max(pos_pid_cnt * 1.0, 1.0));
    context.SetDoubleCommonAttr("history_pospid_hit_num",
      history_pospid_hit_num / std::max(history_pospid_hit_ratio * 1.0, 1.0));
    context.SetDoubleCommonAttr("candidate_hit_flag",
      candidate_hit_flag / std::max(candidate_cnt * 1.0, 1.0));
    context.SetDoubleCommonAttr("candidate_hit_num",
      candidate_hit_num / std::max(candidate_hit_flag * 1.0, 1.0));

    // trans to matrix
    if (pospid_emb_vec.size() % emb_dim != 0 || candidate_emb_vec.size() % emb_dim != 0) return false;
    if (pospid_emb_vec.size() < 1e-6 || candidate_emb_vec.size() < 1e-6) return true;
    int pospid_tag_num = pospid_emb_vec.size() / emb_dim;
    int candidate_item_num = candidate_emb_vec.size() / emb_dim;
    Eigen::Map<Eigen::MatrixXd> pospid_emb_mat(pospid_emb_vec.data(), pospid_tag_num, emb_dim);
    Eigen::Map<Eigen::MatrixXd> candidate_emb_mat(candidate_emb_vec.data(), candidate_item_num, emb_dim);

    // sim score - m * dim, n * dim -> m * n -> max pooling -> m * 1
    Eigen::MatrixXd sim_score_mat = Eigen::MatrixXd::Zero(candidate_emb_mat.rows(), 1);
    if (pospid_emb_mat.rows() > max_components_num && pospid_emb_mat.rows() > 1) {
      // dimension reduction - PCA - m * dim ==> n * dim, n < m
      Eigen::MatrixXd centered =
        pospid_emb_mat.rowwise() - pospid_emb_mat.colwise().mean();    // m * dim
      Eigen::MatrixXd cov_matrix =
        (centered * centered.transpose()) / (pospid_emb_mat.rows() - 1);  // m * m
      // eigen
      Eigen::SelfAdjointEigenSolver<Eigen::MatrixXd> solver(cov_matrix);
      // sorted desc
      Eigen::VectorXd eigen_values = solver.eigenvalues().reverse();
      Eigen::MatrixXd eigen_vectors = solver.eigenvectors().rowwise().reverse();
      // accumulated var -> components_num
      double var_threshold_val = eigen_values.sum() * var_threshold;
      double variance_accumulated = 0.0;
      int components_num = 0;
      for (int i = 0; i < eigen_values.size(); ++i) {
          variance_accumulated += eigen_values[i];
          ++components_num;
          if ((variance_accumulated >= var_threshold_val) || (components_num >= max_components_num)) break;
      }
      Eigen::MatrixXd pospid_emb_mat_pca =
        eigen_vectors.leftCols(components_num).transpose() * centered;  // (n*m) x (m*dim)
      if (candidate_emb_mat.cols() == emb_dim && pospid_emb_mat_pca.cols() == emb_dim) {
        sim_score_mat = (candidate_emb_mat * pospid_emb_mat_pca.transpose()).array().rowwise().maxCoeff();
      }
    } else {
      if (candidate_emb_mat.cols() == emb_dim && pospid_emb_mat.cols() == emb_dim) {
        sim_score_mat = (candidate_emb_mat * pospid_emb_mat.transpose()).array().rowwise().maxCoeff();
      }
    }

    // check & return
    std::vector<double> sim_score_vec(candidate_item_num, 0.0);
    if (sim_score_mat.rows() == candidate_item_num) {
      for (int i = 0; i < candidate_item_num; ++i) {
        sim_score_vec[i] = sim_score_mat(i, 0);
      }
    }
    int sim_score_idx = 0;
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int64 item_type = item_type_accessor(result).value_or(-1);
      if (item_type != 0) return;
      if (sim_score_idx < sim_score_vec.size()) {
        sim_score_setter(result, sim_score_vec[sim_score_idx]);
        sim_score_idx++;
      } else {
        sim_score_setter(result, 0.0);
      }
    });
    return true;
  }

  static bool QueryEmbSimFlag(const CommonRecoLightFunctionContext &context,
                                  RecoResultConstIter begin, RecoResultConstIter end) {
    auto keyword_emb_accessor = context.GetDoubleListItemAttr("keyword_li_emb");
    auto emb_dim = context.GetIntCommonAttr("keyword_emb_dim").value_or(256);
    auto keyword_sim_score_setter = context.SetDoubleListItemAttr("keyword_sim_score");
    if (!keyword_emb_accessor) {
      LOG_EVERY_N(ERROR, 10000) << "QueryEmbSimFlag: null keyword_emb_accessor";
      return false;
    }
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      std::vector<double> keyword_sim_score = {1.0};
      auto keyword_emb = keyword_emb_accessor(result);
      if (keyword_emb && keyword_emb->size() % emb_dim == 0 && keyword_emb->size() > emb_dim) {
        // load
        std::vector<double> keyword_trigger_emb_vec;
        std::vector<double> keyword_after_emb_vec;
        for (int i = 0; i < emb_dim; ++i) keyword_trigger_emb_vec.push_back(keyword_emb->at(i));
        for (int i = emb_dim; i < keyword_emb->size(); ++i) {
          keyword_after_emb_vec.push_back(keyword_emb->at(i));
        }
        // to matrix
        int future_num = keyword_after_emb_vec.size() / emb_dim;
        Eigen::Map<Eigen::MatrixXd> trigger_emb_mat(keyword_trigger_emb_vec.data(), 1, emb_dim);
        Eigen::Map<Eigen::MatrixXd> after_emb_mat(keyword_after_emb_vec.data(), emb_dim, future_num);
        // norm
        double tmp_norm = 0.0;
        tmp_norm = trigger_emb_mat.row(0).norm();
        trigger_emb_mat.row(0) /= (tmp_norm > 1e-6 ? tmp_norm : 1.0);
        for (int i = 0; i < after_emb_mat.cols(); ++i) {
          tmp_norm = after_emb_mat.col(i).norm();
          after_emb_mat.col(i) /= (tmp_norm > 1e-6 ? tmp_norm : 1.0);
        }
        // calc
        Eigen::MatrixXd sim_score_mat = Eigen::MatrixXd::Zero(after_emb_mat.rows(), 1);
        sim_score_mat = (trigger_emb_mat * after_emb_mat).array().colwise().maxCoeff();
        for (int i = 0; i < sim_score_mat.cols(); ++i) {
          keyword_sim_score.push_back(sim_score_mat(0, i));
        }
      }
      // set score & return
      keyword_sim_score_setter(result, keyword_sim_score);
    });
    return true;
  }

  static bool CalcEmbL2Distance(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto item_emb1_accessor = context.GetDoubleListItemAttr("trigger_sim_emb");
    auto item_emb2_accessor = context.GetDoubleListItemAttr("circle_emb");
    auto emb_dim = context.GetIntCommonAttr("emb_dim").value_or(128);
    auto max_distance = context.GetDoubleCommonAttr("max_distance").value_or(2.0);
    auto sim_score_setter = context.SetDoubleItemAttr("sim_score");
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto item_emb1 = item_emb1_accessor(result);
      auto item_emb2 = item_emb2_accessor(result);
      double sim_score = (double)max_distance;
      double sum_diff = 0.0, sum_of_squares = 0.0;
      if (item_emb1 && item_emb2) {
        int emb_size = std::min(std::min((int)item_emb1->size(), (int)item_emb2->size()), (int)emb_dim);
        for (int i = 0; i < emb_size; i++) {
          double diff = item_emb1->at(i) * item_emb2->at(i);
          sum_diff += diff;
          double sub_diff = item_emb1->at(i) - item_emb2->at(i);
          sum_of_squares += sub_diff * sub_diff;
        }
        if (sum_diff == 0.0) {
          sim_score = (double)max_distance;
        } else {
          sim_score = std::sqrt(sum_of_squares);
        }
        sim_score_setter(result, sim_score);
      } else {
        sim_score_setter(result, (double)max_distance);
      }
    });
    return true;
  }

  static bool GetTriggerSimEmb(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
    auto emb_dim = context.GetIntCommonAttr("emb_dim").value_or(128);
    auto emb_list = context.GetStringListCommonAttr("emb_list");
    auto circle_list = context.GetIntListCommonAttr("circle_list");
    auto circle_id_accessor = context.GetIntItemAttr("circle_id");
    auto item_attr_list = context.SetDoubleListItemAttr("trigger_sim_emb");
    if (!emb_list || !circle_list) return true;
    // init result
    std::vector<double> aggregated_emb(emb_dim, 0.0);
    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      int trigger_cnt = 0;
      int64 circle_id = circle_id_accessor(result).value_or(0);
      aggregated_emb.clear();
      // for trigger circle
      for (int i = 0; i < circle_list->size() && circle_id > 0; i++) {
        int64 circle_val = circle_list->at(i);
        // if trigger circle == item circle
        if (circle_val == circle_id) {
          // trigger emb_list_i
          std::vector<absl::string_view> str_vec =
            absl::StrSplit(std::string(emb_list->at(i)), ",", absl::SkipEmpty());
          std::vector<double> tmp_vec;
          // 将每个切分后的字符串转换为 double
          for (const auto& part : str_vec) {
            double value;
            if (absl::SimpleAtod(part, &value)) {
                tmp_vec.push_back(value);
            } else {
                std::cerr << "GetTriggerSimEmb 转换错误: " << part << std::endl;
            }
          }
          // std::string emb_list_i = std::string(emb_list->at(i));
          // std::vector<double> tmp_vec;
          // ks::reco::StringToDoubleVec(emb_list_i, &tmp_vec);
          // for emb
          for (int j=0; j < aggregated_emb.size() && j < emb_dim; j++) {
            aggregated_emb[j] += tmp_vec[j];
          }
          trigger_cnt++;
        }
      }  // for
      if (trigger_cnt > 0) {
        // avg emb
        for (int j=0; j < aggregated_emb.size() && j < emb_dim; j++) {
          aggregated_emb[j] = aggregated_emb[j] / trigger_cnt;
        }
      }
      // write item res
      item_attr_list(result, aggregated_emb);
    });
    return true;
  }

  static bool GetAuthorCircleId(const CommonRecoLightFunctionContext &context, RecoResultConstIter begin,
                                  RecoResultConstIter end) {
    std::shared_ptr<const folly::F14FastMap<uint64, uint64>> mc_author_circle_interest_memory_data_key_map;
    auto author_id_accessor = context.GetIntItemAttr("author_id");
    auto author_circle_id_accessor = context.SetIntItemAttr("author_circle_id");
    std::string mc_author_circle_interest_memory_data_key =
        std::string(context.GetStringCommonAttr("mc_author_circle_interest_memory_data_key").value_or(""));
    ks::reco::MemoryDataMapSingleton::Singleton()->Init();
    bool ret = ks::reco::MemoryDataMapSingleton::Singleton()->GetMemoryDataMap().GetData(
        mc_author_circle_interest_memory_data_key, mc_author_circle_interest_memory_data_key_map);
    if (!ret || !mc_author_circle_interest_memory_data_key_map) {
      return true;
    }
    std::for_each(begin, end, [=](const CommonRecoResult &result) {
      uint64 author_circle_id = 0;
      auto author_id = author_id_accessor(result).value_or(0);
      if (mc_author_circle_interest_memory_data_key_map != nullptr) {
        auto it = mc_author_circle_interest_memory_data_key_map->find(author_id);
        if (it != mc_author_circle_interest_memory_data_key_map->end()) {
          author_circle_id = it->second;
        }
      }  // if map
      author_circle_id_accessor(result, author_circle_id);
    });
    return true;
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(InterestExploreLightFunctionSet);
};

}  // namespace platform
}  // namespace ks
