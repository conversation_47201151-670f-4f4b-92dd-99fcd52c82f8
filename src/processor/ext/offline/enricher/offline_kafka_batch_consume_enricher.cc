#include "dragon/src/processor/ext/offline/enricher/offline_kafka_batch_consume_enricher.h"

#include <algorithm>
#include <mutex>
#include <utility>
#include <vector>

#include "folly/Format.h"
#include "serving_base/util/scope_exit.h"

namespace ks {
namespace platform {

std::shared_ptr<::ks::infra::kfk::SimpleConsumer> OfflineKafkaBatchConsumeEnricher::consumer_ = nullptr;
std::once_flag OfflineKafkaBatchConsumeEnricher::init_once_;
std::atomic<int> OfflineKafkaBatchConsumeEnricher::member_id_allocator_(0);

bool OfflineKafkaBatchConsumeEnricher::InitProcessor() {
  topic_id_ = config()->GetString("topic_id");
  if (topic_id_.empty()) {
    LOG(ERROR) << "OfflineKafkaBatchConsumeEnricher init failed! topic_id is required.";
    return false;
  }

  group_id_ = config()->GetString("group_id");
  if (group_id_.empty()) {
    LOG(ERROR) << "OfflineKafkaBatchConsumeEnricher init failed! group_id is required.";
    return false;
  }

  timeout_ms_ = config()->GetInt("timeout_ms", 100);
  msg_num_ = config()->GetInt("msg_num", 100);

  output_attr_ = config()->GetString("output_attr");
  if (output_attr_.empty()) {
    LOG(ERROR) << "OfflineKafkaBatchConsumeEnricher init failed! output_attr is required.";
    return false;
  }

  std::string extra_params = "fetch.message.max.bytes=5242800";
  int member_number = std::max(::ks::platform::GlobalHolder::GetWorkerThreadNum(), 1);
  extra_params += folly::sformat(";kuaishou.simple.consumer.num.members={}", member_number);
  user_extra_params_ = config()->GetString("user_extra_params");
  if (!user_extra_params_.empty()) {
    extra_params += folly::sformat(";{}", user_extra_params_);
  }

  std::call_once(init_once_, [this, &extra_params]() {
    consumer_ = ::ks::infra::kfk::DynamicKafkaClient::GetSimpleConsumerByLogicalTopicId(
        topic_id_, group_id_, kKafkaConsumeModeAllRegion, extra_params);
  });

  if (!consumer_) {
    LOG(ERROR) << "Failed to init kafka simple consumer with topic_id: " << topic_id_
               << " group_id: " << group_id_ << " consume_mode: " << kKafkaConsumeModeAllRegion
               << " extra_params: " << extra_params;
    return false;
  }

  member_id_ = member_id_allocator_.fetch_add(1) % member_number;

  LOG(INFO) << "Successfully build kafka simple consumer with topic_id: " << topic_id_
            << " group_id: " << group_id_ << " consume_mode: " << kKafkaConsumeModeAllRegion
            << " extra_params: " << extra_params << " member_id: " << member_id_;

  return true;
}

void OfflineKafkaBatchConsumeEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                              RecoResultConstIter end) {
  std::vector<::ks::infra::kfk::SimpleMessage *> msgs =
      consumer_->PollBatchForMember(timeout_ms_, msg_num_, member_id_);

  if (msgs.size() == 0) {
    CL_LOG(ERROR) << "PollBatchForMember failed, topic_id: " << topic_id_ << " group_id: " << group_id_
                  << " timeout_ms: " << timeout_ms_ << " msg_num: " << msg_num_
                  << " member_id: " << member_id_;
    return;
  }

  base::ScopeExit on_scope_exit([&msgs]() {
    for (auto msg : msgs) {
      if (msg) delete msg;
    }
  });

  std::vector<std::string> payloads;
  payloads.reserve(msgs.size());

  for (auto msg : msgs) {
    if (msg && msg->err() == RdKafka::ErrorCode::ERR_NO_ERROR) {
      VLOG(10) << "consume success payload len: " << msg->len();
      payloads.emplace_back(static_cast<const char *>(msg->payload()), msg->len());
      consumer_->CommitOffset(msg->cluster_id(), msg->topic_name(), msg->partition(), msg->offset());
    } else {
      if (msg) {
        CL_LOG(ERROR) << "consume failed: " << RdKafka::err2str(msg->err());
      } else {
        CL_LOG(ERROR) << "consume failed: null msg";
      }
      payloads.push_back("");
    }
  }

  context->SetStringListCommonAttr(output_attr_, std::move(payloads));
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, OfflineKafkaBatchConsumeEnricher, OfflineKafkaBatchConsumeEnricher)
}  // namespace platform
}  // namespace ks
