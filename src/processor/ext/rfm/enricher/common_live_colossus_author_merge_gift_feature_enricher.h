#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <unordered_map>
#include <unordered_set>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/core/common_reco_base.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "kess/rpc/grpc/grpc_client_builder.h"
#include "ks/reco_proto/proto/predict_kess_service.kess.grpc.pb.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "serving_base/utility/timer.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"
#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"


namespace ks {
namespace platform {

class CommonLiveColossusAuthorGiftMergeFeatureEnricher : public CommonRecoBaseEnricher {
 public:
  CommonLiveColossusAuthorGiftMergeFeatureEnricher() {}
  ~CommonLiveColossusAuthorGiftMergeFeatureEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;

 private:
  std::string live_colossus_attr_;

  std::string u_recent_live_author_gift_list_attr_;
  std::string u_recent_live_reward_gift_list_attr_;
  std::string u_recent_live_cluster_gift_list_attr_;
  std::string u_recent_live_playtime_gift_list_attr_;
  std::string u_recent_live_position_gift_list_attr_;
  std::string u_recent_live_consume_reward_gift_list_attr_;
  std::string u_recent_live_mix_reward_gift_list_attr_;
  std::string u_recent_live_recent_clk_ts_gift_list_attr_;

  std::vector<int> interval_days_ = {0, 1, 3, 7, 14, 30, 60, 90, 120, 180, 360, 720};

  serving_base::Timer timer_;

  DISALLOW_COPY_AND_ASSIGN(CommonLiveColossusAuthorGiftMergeFeatureEnricher);
};

}  // namespace platform
}  // namespace ks

