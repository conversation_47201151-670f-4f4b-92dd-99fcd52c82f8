#include "dragon/src/processor/ext/rfm/enricher/common_live_colossus_author_merge_gift_feature_enricher.h"

#include <algorithm>
#include <iostream>
#include <map>

#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "base/thread/thread_pool.h"
#include "kconf/kconf.h"
#include "learning/kuiba/parameter/parameter.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"


namespace ks {
namespace platform {

bool CommonLiveColossusAuthorGiftMergeFeatureEnricher::InitProcessor() {
  live_colossus_attr_ = config()->GetString("live_colossus_attr", "");
  if (live_colossus_attr_.empty()) {
    CL_LOG(ERROR) << "miss colossus attr " << live_colossus_attr_;
    return false;
  }

  u_recent_live_author_gift_list_attr_ =
      config()->GetString("u_recent_live_author_gift_list_attr",
                          "u_recent_live_author_gift_list_attr");
  u_recent_live_reward_gift_list_attr_ =
      config()->GetString("u_recent_live_reward_gift_list_attr",
                          "u_recent_live_reward_gift_list_attr");
  u_recent_live_cluster_gift_list_attr_ =
      config()->GetString("u_recent_live_cluster_gift_list_attr",
                          "u_recent_live_cluster_gift_list_attr");
  u_recent_live_playtime_gift_list_attr_ =
      config()->GetString("u_recent_live_playtime_gift_list_attr",
                          "u_recent_live_playtime_gift_list_attr");
  u_recent_live_position_gift_list_attr_ =
      config()->GetString("u_recent_live_position_gift_list_attr",
                          "u_recent_live_position_gift_list_attr");
  u_recent_live_consume_reward_gift_list_attr_ =
      config()->GetString("u_recent_live_consume_reward_gift_list_attr",
                          "u_recent_live_consume_reward_gift_list_attr");
  u_recent_live_mix_reward_gift_list_attr_ =
      config()->GetString("u_recent_live_mix_reward_gift_list_attr",
                          "u_recent_live_mix_reward_gift_list_attr");
  u_recent_live_recent_clk_ts_gift_list_attr_ =
      config()->GetString("u_recent_live_recent_clk_ts_gift_list_attr",
                          "u_recent_live_recent_clk_ts_gift_list_attr");

  return true;
}

void CommonLiveColossusAuthorGiftMergeFeatureEnricher::Enrich(MutableRecoContextInterface *context,
                                                          RecoResultConstIter begin,
                                                          RecoResultConstIter end) {
  timer_.Start();

  int filter_time_len = config()->GetInt("filter_time_len", 0);
  bool enable_filter_e_shop = config()->GetBoolean("enable_filter_e_shop", true);
  bool enable_filter_outside_item = config()->GetBoolean("enable_filter_outside_item", true);
  bool enable_filter_long_view_item = config()->GetBoolean("enable_filter_long_view_item", true);
  int long_view_threshold = config()->GetInt("long_view_threshold", 60);
  // int latest_n_live = config()->GetInt("latest_n_live", 50);
  // int latest_valid_n_live = config()->GetInt("latest_valid_n_live", 50);
  int latest_n_live = GetIntProcessorParameter(context, "latest_n_live", 10);
  int latest_valid_n_live = GetIntProcessorParameter(context, "latest_valid_n_live", 10);

  bool enable_time_seq = config()->GetBoolean("enable_time_seq", false);

  const auto* live_items = context->GetPtrCommonAttr<std::pair<const void*, int>>(live_colossus_attr_);
  if (!live_items || !live_items->first || live_items->second == 0) {
    CL_LOG(WARNING) << "colossus response " << live_colossus_attr_ << " is null";
    return;
  }
  CL_LOG(INFO) << "colossus response " << live_colossus_attr_ << " size is " << live_items->second;
  timer_.AppendCostMs("get_live_colossus_resp");

  std::unordered_map <int64, int64> author_reward;
  std::unordered_map <int64, int64> author_playtime;
  std::unordered_map <int64, int64> author_mix_reward;
  std::unordered_map <int64, int64> author_cluster;
  std::unordered_map <int64, int64> author_consume_reward;
  std::unordered_map <int64, int64> author_showcount;
  std::unordered_map <int64, int64> author_recent_click_ts;

  int64 filter_time = context->GetRequestTime() / 1000 - 60;
  int64 cur_time_ms = (int64)(context->GetRequestTime() / 1000);

  int cnt = 0;
  for (int i = live_items->second - 1; i >= std::max(0, (int)live_items->second - latest_n_live); i--) {
    auto *item = ::google::protobuf::down_cast<const colossus::LiveItemV4T *>(live_items->first) + i;
    if (!item) {
      LOG_EVERY_N(WARNING, 1000000) << "colossus " << live_colossus_attr_ << "[" << i << "] is null";
      continue;
    }
    // 时间戳过滤条件
    if (item->timestamp > (filter_time - filter_time_len)) continue;
    // 电商直播间过滤
    if (enable_filter_e_shop && (item->label >> 8) == 1) continue;
    // 间外直播间过滤
    if (enable_filter_outside_item && item->play_time == 0) continue;

    int64 total_play_time = item->auto_play_time + item->play_time;
    if (enable_filter_long_view_item && (total_play_time < long_view_threshold)) continue;

    // channel filter
    // int64 channel = item->hetu_tag_channel & channel_mask_;

    // int64 cur_live_id = item->live_id;
    int64 author_id = item->author_id;
    // int64 play_time = item->play_time;
    // int64 timestamp = item->timestamp;
    //  int64 timestamp_trans = timestamp + begin_timestamp;
    // int64 time_lag_min = (filter_time - timestamp) / 60;
    // int64 time_lag_hour = (filter_time - timestamp) / 3600;
    // int64 time_lag_day = (filter_time - timestamp) / 3600 / 24;

    int64 reward_gvalue = item->reward;
    if (reward_gvalue <= 0) continue;
    if (reward_gvalue < 0) {
      reward_gvalue = 0;
    }
    // int64 reward_count = item->reward_count;
    int64 cluster_id = item->cluster_id;

    uint16_t label = item->label;
    int64 like = (label & (1 << 0)) > 0;
    int64 follow = (label & (1 << 7)) > 0;
    int64 forward = (label & (1 << 5)) > 0;
    int64 comment = (label & (1 << 2)) > 0;
    int64 has_entered_profile = (label & (1 << 4)) > 0;

    int64 play_reward = (int64)(total_play_time / 60.0);
    if (play_reward < 0) {
      play_reward = 0;
    }
    int64 interact = has_entered_profile + forward * 6 + like + comment * 6 + follow * 12;
    int64 reward_consume = play_reward + interact;
    int64 mix_reward = (reward_gvalue * 1800) + reward_consume;

    int64 interval_days_bucket = 999;
    if (enable_time_seq && (author_reward.find(author_id) == author_reward.end())) {
      // NOTE 获取时间相关信息
      time_t ts = item->timestamp;
      struct tm tm_info;
      localtime_r(&ts, &tm_info);
      int64 interval_days = (int64)((cur_time_ms - item->timestamp) / 86400);
      // int hour_of_day = tm_info.tm_hour;
      // int is_weekend = (tm_info.tm_wday == 0 || tm_info.tm_wday == 6) ? 1 : 0;

      for (int i = 0; i < interval_days_.size(); i++) {
        if (interval_days <= interval_days_[i]) {
          interval_days_bucket = interval_days_[i];
          break;
        }
      }
    }

    if (author_reward.find(author_id) == author_reward.end()) {
      author_reward.emplace(author_id, reward_gvalue);
      author_playtime.emplace(author_id, total_play_time);
      author_mix_reward.emplace(author_id, mix_reward);
      author_cluster.emplace(author_id, cluster_id);
      author_consume_reward.emplace(author_id, reward_consume);
      author_showcount.emplace(author_id, 1);
      author_recent_click_ts.emplace(author_id, interval_days_bucket);
    } else {
      author_reward[author_id] += reward_gvalue;
      author_playtime[author_id] += total_play_time;
      author_mix_reward[author_id] += mix_reward;
      author_consume_reward[author_id] += reward_consume;
      author_showcount[author_id] += 1;
    }

    if (++cnt >= latest_valid_n_live) {
        break;
    }

    LOG_EVERY_N(INFO, 1000000) << "live_colossus item info: " << author_id << " " << reward_gvalue
        << " " << cluster_id << " " << total_play_time << " " << reward_consume;
  }
  timer_.AppendCostMs("traverse_live_colossus_and_merge_authors");

  // 平均每次长播的 mix_reward, reward 分桶(todo)
  for (auto iter = author_mix_reward.begin(); iter != author_mix_reward.end(); iter++) {
    if (author_showcount[iter->first] == 0) {
      continue;
    }
    iter->second = (int64)(10 * std::log((iter->second + 1.0) / author_showcount[iter->first]));
  }

  // copy to vector
  std::vector<std::pair<int64, int64> > vec(author_mix_reward.begin(), author_mix_reward.end());

  // 按 mix reward 降序
  sort(vec.begin(), vec.end(), [](auto p1, auto p2) { return p1.second > p2.second; });
  timer_.AppendCostMs("sort_by_reward");

  std::vector<int64> u_recent_live_author_gift_list;
  std::vector<int64> u_recent_live_reward_gift_list;
  std::vector<int64> u_recent_live_cluster_gift_list;
  std::vector<int64> u_recent_live_playtime_gift_list;
  std::vector<int64> u_recent_live_position_gift_list;
  std::vector<int64> u_recent_live_consume_gift_list;
  std::vector<int64> u_recent_live_mix_reward_gift_list;
  std::vector<int64> u_recent_live_recent_clk_ts_gift_list;

  for (int i = 0; i < vec.size(); i ++) {
    int64 author_id = vec[i].first;
    int64 reward = vec[i].second;
    int64 cluster = author_cluster[author_id];
    int64 playtime = author_playtime[author_id];
    int64 reward_consume = author_consume_reward[author_id];
    int64 mix_reward = author_mix_reward[author_id];
    int64 recent_clk_ts = author_recent_click_ts[author_id];

    u_recent_live_author_gift_list.push_back(author_id);
    u_recent_live_reward_gift_list.push_back(reward);
    u_recent_live_cluster_gift_list.push_back(cluster);
    u_recent_live_playtime_gift_list.push_back(playtime);
    u_recent_live_position_gift_list.push_back(i + 1);
    u_recent_live_consume_gift_list.push_back(reward_consume);
    u_recent_live_mix_reward_gift_list.push_back(mix_reward);
    u_recent_live_recent_clk_ts_gift_list.push_back(recent_clk_ts);
  }

  context->SetIntListCommonAttr(u_recent_live_author_gift_list_attr_,
                                std::move(u_recent_live_author_gift_list));
  context->SetIntListCommonAttr(u_recent_live_reward_gift_list_attr_,
                                std::move(u_recent_live_reward_gift_list));
  context->SetIntListCommonAttr(u_recent_live_cluster_gift_list_attr_,
                                std::move(u_recent_live_cluster_gift_list));
  context->SetIntListCommonAttr(u_recent_live_playtime_gift_list_attr_,
                                std::move(u_recent_live_playtime_gift_list));
  context->SetIntListCommonAttr(u_recent_live_position_gift_list_attr_,
                                std::move(u_recent_live_position_gift_list));
  context->SetIntListCommonAttr(u_recent_live_consume_reward_gift_list_attr_,
                                std::move(u_recent_live_consume_gift_list));
  context->SetIntListCommonAttr(u_recent_live_mix_reward_gift_list_attr_,
                                std::move(u_recent_live_mix_reward_gift_list));
  context->SetIntListCommonAttr(u_recent_live_recent_clk_ts_gift_list_attr_,
                                std::move(u_recent_live_recent_clk_ts_gift_list));
  timer_.AppendCostMs("fill_attr");
  LOG_EVERY_N(INFO, 1000000) << "cost display:" << timer_.display();
}


typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, CommonLiveColossusAuthorGiftMergeFeatureEnricher,
                 CommonLiveColossusAuthorGiftMergeFeatureEnricher);

}  // namespace platform
}  // namespace ks

