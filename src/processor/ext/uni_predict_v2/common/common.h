#pragma once
#include <string>
#include <vector>

#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"

#include "ks/reco_pub/embedding_util/emb_compressor.h"
#include "teams/reco-arch/uni-predict-v2/src/utils/fp16.h"
#include "teams/reco-arch/uni-predict-v2/src/utils/bf16.h"

namespace ks {
namespace platform {

template <typename Key, typename Value>
using Map = folly::F14FastMap<Key, Value>;

template <typename Value>
using Set = folly::F14FastSet<Value>;
inline namespace uni_predict_common {
using SlotSigns = Map<uint64_t, std::vector<uint64_t>>;    // slot->signs
using SlotSignPos = Map<uint64_t, std::vector<uint32_t>>;  // slot->sign_positions

// 仅用于模版函数
// 在一开始的位置保存了 float 类型 scale, 内部数据类型为 int8
class ScaleInt8 {
 public:
  typedef int8_t DataType;
  static const int data_offset = 4;
};

// 在一开始的位置保存了 float 类型 scale, 内部数据类型为 int8
class ScaleInt16 {
 public:
  typedef int16_t DataType;
  static const int data_offset = 4;
};

// 固定 scale, 内部数据类型为 int16
class MioInt16 {
 public:
  typedef int16_t DataType;
  static const int data_offset = 0;
};

class Float32 {
 public:
  typedef float DataType;
  static const int data_offset = 0;
};

class Float16 {
 public:
  typedef reco_arch::uni_predict::float16 DataType;
  static const int data_offset = 0;
};

class Bfloat16 {
 public:
  typedef reco_arch::uni_predict::bfloat16 DataType;
  static const int data_offset = 0;
};

using EmbCompressType = ks::emb_util::EmbCompressor::CompressType;

template <EmbCompressType compress_type>
struct EmbCompressTypeToClass {};

template <>
struct EmbCompressTypeToClass<EmbCompressType::kMioInt16> {
typedef MioInt16 EmbCompressClass;
};

template <>
struct EmbCompressTypeToClass<EmbCompressType::kFP32> {
typedef Float32 EmbCompressClass;
};

template <>
struct EmbCompressTypeToClass<EmbCompressType::kFP16> {
typedef Float16 EmbCompressClass;
};

template <>
struct EmbCompressTypeToClass<EmbCompressType::kScaleInt16> {
typedef ScaleInt16 EmbCompressClass;
};

template <>
struct EmbCompressTypeToClass<EmbCompressType::kScaleInt8> {
typedef ScaleInt8 EmbCompressClass;
};

template <typename CompressType>
size_t CalcEmbeddingDim(size_t data_len) {
  return (data_len - CompressType::data_offset) / sizeof(typename CompressType::DataType);
}

// TODO(liujiaqiang): 等 JSON 配置基本完成切换后, 支持 float 和 fp16
inline EmbCompressType StringToDataType(const std::string &dtype) {
  if (dtype == "mio_int16") {
    return EmbCompressType::kMioInt16;
  } else if (dtype == "scale_int8") {
    return EmbCompressType::kScaleInt8;
  } else if (dtype == "scale_int16") {
    return EmbCompressType::kScaleInt16;
  } else if (dtype == "float16") {
    return EmbCompressType::kFP16;
  } else if (dtype == "float32") {
    return EmbCompressType::kFP32;
  } else {
    return EmbCompressType::kMioInt16;
  }
}

// TODO(liujiaqiang): 等 JSON 配置基本完成切换后，支持 float 和 fp16
inline std::string DataTypeToString(EmbCompressType dtype) {
  switch (dtype) {
    case EmbCompressType::kMioInt16:
      return "mio_int16";
    case EmbCompressType::kScaleInt8:
      return "scale_int8";
    case EmbCompressType::kScaleInt16:
      return "scale_int16";
    default:
      return "unknown";
  }
}

struct SlotConfig {
  std::string input_name;  // attr_name
  std::vector<int> slots;
  std::vector<std::string> weight_attrs;
  int dim;
  int expand;
  bool common;
  bool sized = false;
  EmbCompressType dtype = EmbCompressType::kMioInt16;
  // 对于 expand = 1 的 slot ，需要把多少个 sign 的 embedding 进行 reduce sum
  std::vector<int> real_expand;
  int real_expand_sum;  // slots_reduce_sum_sign_cnt 加起来
};

struct InputConfig {
  std::string attr_name;
  std::string tensor_name;
  int64_t dim;
  bool common;
};

enum OutputType { OT_REAL = 0, OT_DUMP, OT_LIMIT };

struct OutputConfig {
  bool common = false;
  OutputType type = OT_REAL;
  std::string tensor_name;
  std::vector<std::string> attr_names;
};

struct EmbeddingConfig {
  int32_t fetcher_index = 0;
  std::vector<std::string> common_slots_inputs;
  std::vector<std::string> common_parameters_inputs;
  std::vector<std::string> slots_inputs;
  std::vector<std::string> parameters_inputs;
  Map<uint64_t, uint32_t> all_signs;
  std::vector<uint64_t> seq_signs;
  Map<std::string, SlotConfig> slot_configs;  // attr_name => SlotConfig

  bool slot_as_attr_name_ = false;
  std::vector<int64_t> common_slots_id_inputs;
  std::vector<int64_t> slots_id_inputs;
  Map<uint64_t, int> slot_to_expand;
  std::string kess_service = "";
  bool use_kconf_client = true;
  std::string colossusdb_embd_model_name;
  std::string colossusdb_embd_table_name;
  int shards = 0;
  std::string fetcher_type;
  int max_signs_per_request;
  int timeout_ms;
  //  for rdma: 默认 0 代表不启用 rdma, 如要使用 rdma 须显式设置
  int rdma_max_async_num = 0;
};

constexpr float LOAD_FACTOR_4_FAST_MAP = 14.0f / 12;
constexpr float kPerfBase = 1000.0;
constexpr char kUniPredictSetDevice[] = "uni_predict_set_device";  // uni_predict_processor set device attr
constexpr char kUniPredictTensorCache[] =
    "uni_predict_tensor_cache";  // uni_predict_processor tensor cache attr
// 提供 FAQ 文档的引导
#define FAQ_GUIDE(faq_label)                                                                   \
  "please visit https://docs.corp.kuaishou.com/k/home/<USER>/fcACWwTHOqtDzvo4hMTRfFH7G " \
  "and search " #faq_label " for details."
}  // namespace uni_predict_common
}  // namespace platform
}  // namespace ks
