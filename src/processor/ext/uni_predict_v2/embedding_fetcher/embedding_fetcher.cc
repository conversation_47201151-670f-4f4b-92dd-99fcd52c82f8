#include "dragon/src/processor/ext/uni_predict_v2/embedding_fetcher/embedding_fetcher.h"

#include <memory>
#include <string>
#include <utility>

#include "base/strings/string_split.h"
#include "dragon/src/processor/ext/uni_predict_v2/common/common.h"
#include "dragon/src/processor/ext/uni_predict_v2/embedding_fetcher/embedding_local_fetcher.h"
#include "dragon/src/processor/ext/uni_predict_v2/embedding_fetcher/embedding_server_fetcher.h"
#include "dragon/src/processor/ext/uni_predict_v2/embedding_fetcher/embedding_server_rdma_fetcher.h"
#include "serving_base/jansson/json.h"

namespace ks {
namespace platform {
namespace embedding_fetcher {

#define CONFIG_CHECK(cond, ret, msg) \
  if (!(cond)) {                     \
    *err_msg = (msg);                \
    return (ret);                    \
  }

bool ExtractEmbedingConfig(const base::Json *config, EmbeddingConfig *embedding_config,
                           std::string *err_msg) {
  auto colossusdb_embd_model_name = config->GetString("colossusdb_embd_model_name", "");
  if (colossusdb_embd_model_name.empty()) {
    colossusdb_embd_model_name = config->GetString("colossusdb_embd_service_name", "unknown");
  }
  embedding_config->fetcher_type = config->GetString("fetcher_type");
  embedding_config->kess_service = config->GetString("kess_service", "unknown");
  embedding_config->use_kconf_client = config->GetBoolean("use_kconf_client", true);
  embedding_config->colossusdb_embd_model_name = colossusdb_embd_model_name;
  embedding_config->colossusdb_embd_table_name = config->GetString("colossusdb_embd_table_name", "unknown");
  // for rdma
  embedding_config->rdma_max_async_num = config->GetInt("rdma_max_async_num", 128);
  embedding_config->shards = config->GetInt("shards", 0);
  embedding_config->slot_as_attr_name_ = config->GetBoolean("slot_as_attr_name", false);
  embedding_config->max_signs_per_request = config->GetInt("max_signs_per_request", 500);
  embedding_config->timeout_ms = config->GetInt("timeout_ms", 50);
  auto *common_slots_inputs_config = config->Get("common_slots_inputs");
  if (common_slots_inputs_config) {  // 没有的话就不填 common slot
    CONFIG_CHECK(common_slots_inputs_config->IsArray(), false, "common_slots_inputs must be an array");
    for (auto *c : common_slots_inputs_config->array()) {
      if (!embedding_config->slot_as_attr_name_) {
        auto common_slots_inputs_attr_name = c->StringValue();
        CONFIG_CHECK(!common_slots_inputs_attr_name.empty(), false,
                     "common_slots_inputs must be an array of non-empty strings.");
        embedding_config->common_slots_inputs.push_back(common_slots_inputs_attr_name);
      } else {
        auto slot_id = c->IntValue(-1);
        CONFIG_CHECK(slot_id > 0, false, "common_slots_inputs must be an array of non-empty ints.");
        embedding_config->common_slots_id_inputs.push_back(slot_id);
        embedding_config->common_slots_inputs.push_back(std::to_string(slot_id));
      }
    }
  }

  auto *common_parameters_inputs_config = config->Get("common_parameters_inputs");
  if (common_parameters_inputs_config) {  // 没有的话就不填 common signs
    CONFIG_CHECK(common_parameters_inputs_config->IsArray(), false,
                 "common_parameters_inputs must be an array.");
    for (auto *c : common_parameters_inputs_config->array()) {
      auto common_parameters_inputs_attr_name = c->StringValue();
      CONFIG_CHECK(!common_parameters_inputs_attr_name.empty(), false,
                   "common_parameters_inputs must be an array of non-empty strings");
      embedding_config->common_parameters_inputs.push_back(common_parameters_inputs_attr_name);
    }
  }

  // init item slots and signs
  auto *slots_inputs_config = config->Get("slots_inputs");
  if (slots_inputs_config) {  // 没有的话就不填 item slot
    CONFIG_CHECK(slots_inputs_config->IsArray(), false, "slots_inputs must be an array.");
    for (auto *c : slots_inputs_config->array()) {
      if (!embedding_config->slot_as_attr_name_) {
        auto slots_inputs_attr_name = c->StringValue();
        CONFIG_CHECK(!slots_inputs_attr_name.empty(), false,
                     "slots_inputs must be an array of non-empty strings.");
        embedding_config->slots_inputs.push_back(slots_inputs_attr_name);
      } else {
        auto slot_id = c->IntValue(-1);
        CONFIG_CHECK(slot_id > 0, false, "common_slots_inputs must be an array of non-empty ints.");
        embedding_config->slots_id_inputs.push_back(slot_id);
        embedding_config->slots_inputs.push_back(std::to_string(slot_id));
      }
    }
  }

  auto *parameters_inputs_config = config->Get("parameters_inputs");
  if (parameters_inputs_config) {  // 没有的话就不填 item signs
    CONFIG_CHECK(parameters_inputs_config->IsArray(), false, "parameters_inputs must be an array.");

    for (auto *c : parameters_inputs_config->array()) {
      auto parameters_inputs_attr_name = c->StringValue();
      CONFIG_CHECK(!parameters_inputs_attr_name.empty(), false,
                   "parameters_inputs must be an array of non-empty strings.");
      embedding_config->parameters_inputs.push_back(parameters_inputs_attr_name);
    }
  }

  // check common, item slot and signs
  CONFIG_CHECK(embedding_config->slots_inputs.size() + embedding_config->common_slots_inputs.size() != 0,
               false, "either slots_inputs or common_slots_inputs must be filled.");
  CONFIG_CHECK(embedding_config->slots_inputs.size() == embedding_config->parameters_inputs.size(), false,
               "the sizes of slots_inputs and parameters_inputs must be equal.");
  CONFIG_CHECK(
      embedding_config->common_slots_inputs.size() == embedding_config->common_parameters_inputs.size(),
      false, "the sizes of common_slots_inputs and common_parameters_inputs must be equal.");

  // slots config of attr
  auto *slots_config = config->Get("slots_config");
  CONFIG_CHECK(slots_config && slots_config->IsArray(), false, "slots_config must exist and be an array.");
  for (auto *c : slots_config->array()) {
    const auto &attr_name = c->GetString("input_name");
    CONFIG_CHECK(!attr_name.empty(), false, "input_name must exist.");
    auto &slot_config = embedding_config->slot_configs[attr_name];
    slot_config.input_name = attr_name;
    slot_config.common = c->GetBoolean("common", false);
    slot_config.dim = c->GetInt("dim", -1);
    CONFIG_CHECK(slot_config.dim > 0, false, attr_name + " dim must exist and larger than 0.");
    slot_config.expand = c->GetInt("expand", 1);
    // sized: 每个 item/user 的 tensor 最后加一位, 表示这个 item tensor 的所有 slot 抽取到 sign 的个数
    slot_config.sized = c->GetBoolean("sized", false);
    CONFIG_CHECK(!c->GetBoolean("sentry", false), false, attr_name + " get sentry = true, not support.");
    if (c->Get("compress_group")) {
      // compress_group 意思是该 tensor 可以根据 compress_group 来复用.
      // 例如 common 就是 compress_group 是根据 "USER" 来复用
      // 也就是请求里所有 item 都是可以复用这个 tensor.
      // 目前只实现 compress_group = "USER" 这个规则, 转换到 common 上去.
      // 举例未来可能实现的 compress_group: 一个请求中某些 item 可能属于同一个 cluster id, 共享 cluster
      // tensor. 那这些 item attr 中可能应该有一个 "compress_group_cluster" = 0, 相同 cluster id 的 item
      // 属性相同, 从 0 开始. 这类 tensor 应该有独立的 sum pooling 和 batching 逻辑.
      auto compress_group = c->GetString("compress_group");
      CONFIG_CHECK(compress_group == "USER", false,
                   attr_name + " slot config get compress_group: " + c->GetString("compress_group") +
                       ", only support USER now");
      if (compress_group == "USER") {
        slot_config.common = true;
      }
    }
    auto *slots_config_slots = c->Get("slots");
    if (slots_config_slots->IsInteger()) {
      slot_config.slots.push_back(slots_config_slots->IntValue(-1));
    } else if (slots_config_slots->IsString()) {
      std::vector<std::string> slot_strings;
      base::SplitString(slots_config_slots->StringValue(), " ", &slot_strings);
      for (const auto &slot_str : slot_strings) {
        int slot_id = std::stoi(slot_str);
        CONFIG_CHECK(slot_id > 0, false, "the value of slot must int larger than 0.");
        slot_config.slots.push_back(slot_id);
      }
    } else if (slots_config_slots->IsArray()) {
      for (auto *slot : slots_config_slots->array()) {
        int slot_id = slot->IntValue(-1);
        CONFIG_CHECK(slot_id > 0, false, "the value of slot must int larger than 0.");
        slot_config.slots.push_back(slot_id);
      }
    } else {
      *err_msg = "slots must be a string or an array.";
      return false;
    }

    auto *real_expand_config = c->Get("real_expand");
    if (real_expand_config && slot_config.expand == 1) {
      if (real_expand_config->IsInteger()) {
        slot_config.real_expand.push_back(real_expand_config->IntValue(1));
      } else if (real_expand_config->IsString()) {
        std::vector<std::string> real_exapnd_strings;
        base::SplitString(real_expand_config->StringValue(), " ", &real_exapnd_strings);
        for (const auto &real_expand_str : real_exapnd_strings) {
          int real_expand_id = std::stoi(real_expand_str);
          CONFIG_CHECK(real_expand_id > 0, false, "the value of real_expand must int larger than 0.");
          slot_config.real_expand.push_back(real_expand_id);
        }
      } else if (real_expand_config->IsArray()) {
        for (auto *real_expand_str : real_expand_config->array()) {
          int real_expand_id = real_expand_str->IntValue(1);
          CONFIG_CHECK(real_expand_id > 0, false, "the value of real_expand must int larger than 0.");
          slot_config.real_expand.push_back(real_expand_id);
        }
      }
      if (slot_config.real_expand.size() > 0 && slot_config.real_expand.size() != slot_config.slots.size()) {
        *err_msg = "real_expand size must equal slots size.";
        return false;
      }
    }
    for (int j = 0; j < slot_config.slots.size(); ++j) {
      if (slot_config.real_expand.size() != slot_config.slots.size()) {
        slot_config.real_expand.emplace_back(slot_config.expand);
      }
      slot_config.real_expand_sum += slot_config.real_expand[j];
    }

    auto *weights_config = c->Get("weights");
    if (weights_config) {
      if (weights_config->IsString()) {
        base::SplitStringWithOptions(weights_config->StringValue(), " ", true, true,
                                     &slot_config.weight_attrs);
      } else if (weights_config->IsArray()) {
        for (const auto *weight_config : weights_config->array()) {
          slot_config.weight_attrs.emplace_back(weight_config->StringValue());
        }
      } else {
        *err_msg = "weights must be a string or an array.";
        return false;
      }
    }
    if (!slot_config.weight_attrs.empty()) {
      CONFIG_CHECK(slot_config.weight_attrs.size() == slot_config.slots.size(), false,
                   "weights must have the same size with slots.");
    }

    auto embedding_dtype = c->GetString("dtype");
    if (!embedding_dtype.empty()) {
      slot_config.dtype = StringToDataType(embedding_dtype);
      if (embedding_dtype == "float32") {
        LOG(WARNING) << "Make sure using embedding dtype=float32 | " << static_cast<int>(slot_config.dtype)
                     << " for slots_config[" << slot_config.input_name << "]";
      }
    }
    for (int loop = 0; loop < slot_config.slots.size(); ++loop) {
      embedding_config->slot_to_expand[slot_config.slots[loop]] = slot_config.expand;
    }
  }
  return true;
}

bool BuildEmbeddingFetcherDeprecated(const base::Json *config, EmbeddingConfig *embedding_config,
                                     std::shared_ptr<BaseEmbeddingFetcher> *embedding_fetcher,
                                     std::string *err_msg) {
  // 没有 embedding_fetchers 的配置配置写法, 默认是从 BtEmbeddingServer 拉, 必须有 kess_service, shards,
  // slots_configs. 没有的话跳过拉 embedding 阶段.
  std::string kess_service = config->GetString("kess_service");
  CONFIG_CHECK(!kess_service.empty(), true,
               "kess_service of embedding server not specified. skip fetching embedding");
  auto shards = config->GetInt("shards", -1);
  CONFIG_CHECK(shards > 0, true,
               "embedding server shards not valid: " + std::to_string(shards) + ". skip fetching embedding");
  auto slots_config = config->Get("slots_config");
  CONFIG_CHECK(slots_config && slots_config->IsArray(), true,
               "slots_config not valid. skip fetching embedding");

  // 如果 common_slots 和 item slots 都没有, 也跳过拉 embedding 阶段.
  auto *common_slots_inputs_config = config->Get("common_slots_inputs");
  auto *slots_inputs_config = config->Get("slots_inputs");
  CONFIG_CHECK((common_slots_inputs_config && common_slots_inputs_config->IsArray()) ||
                   (slots_inputs_config && slots_inputs_config->IsArray()),
               true, "common and item slots not specified. skip fetching embedding");

  // 假如都有, 那应该还是要拉 embedding 的.
  // 先填充 embedding config
  std::string embedding_config_msg;
  if (!ExtractEmbedingConfig(config, embedding_config, &embedding_config_msg)) {
    *err_msg = embedding_config_msg;
    return false;
  }
  // 再构造 embedding fetcher
  std::string fetcher_msg;
  auto fetcher = BuildBtEmbeddingServerFetcherFromConfig(config, &fetcher_msg);
  if (!fetcher) {
    *err_msg = fetcher_msg;
    return false;
  }
  *embedding_fetcher = fetcher;
  return true;
}

bool BuildEmbeddingFetcherFromConfig(const base::Json *config,
                                     std::vector<EmbeddingConfig> *embedding_configs,
                                     std::vector<std::shared_ptr<BaseEmbeddingFetcher>> *embedding_fetcher,
                                     std::string *err_msg) {
  // 1. 假如没有 embedding_fetchers 这个配置, 就走原来的路子.
  auto embedding_fetcher_configs = config->Get("embedding_fetchers");
  if (!embedding_fetcher_configs) {
    std::string msg;
    std::shared_ptr<BaseEmbeddingFetcher> fetcher = nullptr;
    EmbeddingConfig embedding_config;
    if (!BuildEmbeddingFetcherDeprecated(config, &embedding_config, &fetcher, &msg)) {
      // 失败
      *err_msg = msg;
      return false;
    } else if (fetcher) {  // 上面函数返回 success 并不表示真的创建了 fetcher
      // 这个子函数有可能成功, 但是报了一些 log, 打出来.
      embedding_config.fetcher_index = embedding_configs->size();
      embedding_configs->emplace_back(std::move(embedding_config));
      fetcher->SetFetcherType("BtEmbeddingServerFetcher");
      embedding_fetcher->emplace_back(fetcher);
      LOG(INFO) << msg;
    } else {
      LOG(INFO) << msg;
    }
  } else {
    // 2. 否则就逐个读 embedding_fetcher
    // 逐个构造 fetcher
    CONFIG_CHECK(embedding_fetcher_configs->IsArray(), false, "embedding_fetchers must be an array");
    for (auto *c : embedding_fetcher_configs->array()) {
      std::string msg;
      EmbeddingConfig embedding_config;
      if (!ExtractEmbedingConfig(c, &embedding_config, &msg)) {
        *err_msg = msg;
        return false;
      }
      embedding_config.fetcher_index = embedding_configs->size();
      embedding_configs->emplace_back(std::move(embedding_config));

      // 根据 type 来构造 fetcher
      msg.clear();
      auto fetcher_type = c->Get("fetcher_type");
      c->set("fetcher_index", (int)embedding_fetcher->size());
      CONFIG_CHECK(fetcher_type != nullptr, false, "fetcher_type must be specified in embedding_fetchers");
      std::shared_ptr<BaseEmbeddingFetcher> fetcher;
      if (fetcher_type->StringValue() == "BtEmbeddingServerFetcher") {
        fetcher = BuildBtEmbeddingServerFetcherFromConfig(c, &msg);
      } else if (fetcher_type->StringValue() == "ColossusdbEmbeddingServerFetcher") {
        fetcher = BuildColossusdbEmbeddingServerFetcherFromConfig(c, &msg);
      } else if (fetcher_type->StringValue() == "RdmaColossusdbEmbeddingServerFetcher") {
        fetcher = BuildRdmaColossusdbEmbeddingServerFetcherFromConfig(c, &msg);
      } else if (fetcher_type->StringValue() == "LocalEmbeddingFetcher") {
        fetcher = BuildLocalEmbeddingTableFetcherFromConfig(c, &msg);
      } else {
        msg = "Fetcher type: " + fetcher_type->StringValue() +
              " not support yet, supported types: BtEmbeddingServerFetcher, BrpcBtEmbeddingServerFetcher, "
              "ColossusdbEmbeddingServerFetcher, RdmaColossusdbEmbeddingServerFetcher, LocalEmbeddingFetcher";
      }
      if (!fetcher) {
        *err_msg = msg;
        return false;
      }
      fetcher->SetFetcherType(fetcher_type->StringValue());
      embedding_fetcher->emplace_back(fetcher);
    }
  }
  return true;
}

}  // namespace embedding_fetcher
}  // namespace platform
}  // namespace ks
