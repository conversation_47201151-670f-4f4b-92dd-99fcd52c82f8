#pragma once

#include <algorithm>
#include <condition_variable>
#include <memory>
#include <mutex>
#include <string>
#include <typeinfo>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/processor/ext/uni_predict_v2/common/common.h"
#include "dragon/src/processor/ext/uni_predict_v2/embedding_fetcher/embedding_fetcher.h"
#include "dragon/src/processor/ext/uni_predict_v2/embedding_fetcher/embedding_server_fetcher.h"
#include "dragon/src/processor/ext/uni_predict_v2/embedding_manager/embedding_manager.h"
#include "dragon/src/processor/ext/uni_predict_v2/embedding_manager/gpu_cache_embedding_manager.h"
#include "dragon/src/processor/ext/uni_predict_v2/embedding_manager/post_fetch_embedding_manager.h"
#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"
#include "ks/reco_proto/proto/predict_kess_service.kess.grpc.pb.h"
#include "serving_base/utility/timer.h"
#include "teams/reco-arch/uni-predict-v2/src/batching/batch_system.h"
#include "teams/reco-arch/uni-predict-v2/src/batching/batching_option.h"
#include "teams/reco-arch/uni-predict-v2/src/batching/batching_task.h"
#include "teams/reco-arch/uni-predict-v2/src/predictor/predictor.h"

DECLARE_bool(mio_embedding_enable_cache);
DECLARE_int64(mio_embedding_cache_version);
DECLARE_double(mio_embedding_skip_cache_ratio);
DECLARE_bool(force_disable_btq_model_update_debug_only_subject_to_change);
DECLARE_int64(batching_option_alignment);
DECLARE_int64(batching_option_batch_timeout_micros);
DECLARE_int64(batching_option_max_batch_size);
DECLARE_int64(batching_option_max_enqueued_batches);
DECLARE_int64(uni_embedding_manager_thread_pool_size_per_fetcher);
DECLARE_bool(batching_enable_split_task);
DECLARE_int64(gpu_cache_reserve_set_number);

#ifdef USE_GPU
DECLARE_bool(use_cuda_graph_memcpy);
#endif

namespace ks {
namespace platform {

#define CONFIG_CHECK(cond, msg)                                             \
  if (!(cond)) {                                                            \
    LOG(ERROR) << "UniPredictFusedItemAttrEnricher init failed! " << (msg); \
    return false;                                                           \
  }

#define COND_VAR(cond, dst, yes, no) \
  if ((cond)) {                      \
    dst = (yes);                     \
  } else {                           \
    dst = (no);                      \
  }

class UniPredictFusedItemAttrEnricher : public CommonRecoBaseEnricher {
 public:
  UniPredictFusedItemAttrEnricher() {}
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;
  std::function<void()> Purge() override {
    if (predictor_) {
      return [=]() { predictor_->LocalStopAndJoin(); };
    }
    return nullptr;
  }

 private:
  bool InitProcessor() override {
    batching_enable_split_task_ = FLAGS_batching_enable_split_task;
    if (!InitPre()) {
      return false;
    }
    if (batching_enable_split_task_ && !InitSplitTask()) {
      return false;
    }
    return true;
  }

  bool InitSplitTask() {
    if (embedding_manager_type_ == "parallel_fetch") {
      LOG(FATAL) << "UniPredict with batching_enable_split_task does support embedding manager type "
                 << embedding_manager_type_;
      return false;
    }
    std::string err_msg;
    CONFIG_CHECK(embedding_fetcher::BuildEmbeddingFetcherFromConfig(config(), &embedding_configs_split_,
                                                                    &embedding_fetchers_split_, &err_msg),
                 err_msg);
    CONFIG_CHECK(embedding_configs_split_.size() == embedding_fetchers_split_.size(),
                 "embedding_config and fetcher's size not equal");
    for (auto fetcher : embedding_fetchers_split_) {
      fetcher->SetModelKey(key_);
    }
    if (embedding_manager_type_ == "post_parallel_fetch") {
      embedding_manager_split_ = std::make_shared<embedding_manager::PostFetchEmbeddingManager>(
          embedding_configs_split_, input_configs_, embedding_fetchers_split_, key_, debug_tensor_);
    } else if (embedding_manager_type_ == "gpu_cache_parallel") {
      embedding_manager_split_ = std::make_shared<embedding_manager::GpuCacheEmbeddingManager>(
          embedding_configs_split_, input_configs_, embedding_fetchers_split_, key_, debug_tensor_);
    }
    return true;
  }

  bool InitPre() {
    // init embedding fetcher
    std::string err_msg;

    CONFIG_CHECK(embedding_fetcher::BuildEmbeddingFetcherFromConfig(config(), &embedding_configs_,
                                                                    &embedding_fetchers_, &err_msg),
                 err_msg);
    CONFIG_CHECK(embedding_configs_.size() == embedding_fetchers_.size(),
                 "embedding_config and fetcher's size not equal");

    CONFIG_CHECK(!config()->GetBoolean("slot_as_attr_name", false), "not support slot_as_attr_name.");

    // predict inputs
    bool only_common_input = true;
    auto *inputs_config = config()->Get("inputs");
    CONFIG_CHECK(inputs_config && inputs_config->IsArray(), "inputs must exist and be and array.");
    for (auto *c : inputs_config->array()) {
      const auto &tensor_name = c->GetString("tensor_name");
      CONFIG_CHECK(!tensor_name.empty(), "tensor_name of inputs must exist.");
      auto &input_config = input_configs_[tensor_name];
      input_config.tensor_name = tensor_name;
      input_config.attr_name = c->GetString("attr_name");
      CONFIG_CHECK(!input_config.attr_name.empty(), "attr_name of inputs " + tensor_name + " must exist.");
      input_config.dim = c->GetInt("dim", -1);
      CONFIG_CHECK(input_config.dim > 0, "dim of input " + tensor_name + " must be positive integer.")
      input_config.common = c->GetBoolean("common", false);
      if (c->Get("compress_group")) {
        auto compress_group = c->GetString("compress_group");
        CONFIG_CHECK(compress_group == "USER",
                     tensor_name + " input get compress_group: " + c->GetString("compress_group") +
                         ", only support USER now");
        if (compress_group == "USER") {
          input_config.common = true;
        }
      }
      if (!input_config.common) only_common_input = false;
    }

    // predict outputs
    bool only_common_output = true;
    auto *outputs_config = config()->Get("outputs");
    CONFIG_CHECK(outputs_config && outputs_config->IsArray(), "outputs must exist and be and array.");
    for (auto *c : outputs_config->array()) {
      const auto &tensor_name = c->GetString("tensor_name");
      CONFIG_CHECK(!tensor_name.empty(), "tensor_name of outputs must exist.");
      outputs_.emplace_back();
      auto &tensor_attr = outputs_.back();
      tensor_attr.common = c->GetBoolean("common", false);
      tensor_attr.tensor_name = tensor_name;
      if (!tensor_attr.common) only_common_output = false;
      // 更合理的支持一个 model output 写入多个 context attr
      auto attr = c->Get("attr_name");
      if (attr->IsString()) {
        tensor_attr.attr_names.emplace_back(attr->StringValue());
      } else if (attr->IsArray()) {
        for (auto *ele : attr->array()) {
          tensor_attr.attr_names.emplace_back(ele->StringValue());
        }
      }
      CONFIG_CHECK(!tensor_attr.attr_names.back().empty(), "attr_name is required");
    }

    all_outputs_no_.clear();
    for (auto i = 0; i < outputs_.size(); ++i) all_outputs_no_.emplace_back(i);

    // static graph 标注这是否是一个流式模型，并不是所有场景都需要更新
    auto is_static_graph = config()->GetBoolean("static_graph", false);
    // predict options
    if (!is_static_graph) {
      queue_prefix_ = config()->GetString("queue_prefix");
      CONFIG_CHECK(!queue_prefix_.empty(), "queue_prefix is required.");
    }

    key_ = config()->GetString("key");
    CONFIG_CHECK(!key_.empty(), "key is required");
    embedding_manager_type_ = config()->GetString("embedding_manager_type", "parallel_fetch");
    bool open_gpu_cache = false;
    if (embedding_manager_type_ == "gpu_cache_parallel") {
#ifdef USE_GPU
      open_gpu_cache = true;
#else
      LOG(FATAL) << "gpu_cache_parallel need MACRO USE_GPU";
#endif
      for (const auto fetcher : embedding_fetchers_) {
        if (fetcher->GetFetcherType() != "BtEmbeddingServerFetcher" &&
            fetcher->GetFetcherType() != "ColossusdbEmbeddingServerFetcher" &&
            fetcher->GetFetcherType() != "RdmaColossusdbEmbeddingServerFetcher") {
          LOG(FATAL) << "gpu cache only support BtEmbeddingServerFetcher / ColossusdbEmbeddingServerFetcher "
                        "/ RdmaColossusdbEmbeddingServerFetcher. here is "
                     << fetcher->GetFetcherType();
        }
      }
    }

    auto model_loader_config = config()->Get("model_loader_config");
    COND_VAR(model_loader_config && model_loader_config->Get("type"), model_loader_type_,
             model_loader_config->GetString("type", "MioTFExecutedByTensorFlowModelLoader"),
             "MioTFExecutedByTensorFlowModelLoader");
    CONFIG_CHECK(!model_loader_type_.empty(), "model loader type is required");

    bool enable_fp16 = false;
    COND_VAR(model_loader_config && model_loader_config->Get("enable_fp16"), enable_fp16,
             model_loader_config->GetBoolean("enable_fp16", false), false);
    // if (enable_fp16 && model_loader_type_ == "MioTFExecutedByTensorFlowModelLoader") {
    //   LOG(ERROR) << "TensorFlow executor do not support fp16, please use TensorRT instead.";
    //   return false;
    // }

    std::string service_name = "grpc_" + GlobalHolder::GetServiceIdentifier();
    base::Json config_json(base::StringToJson(base::JsonToString(config()->get())));
    config_json.set("__kess_name__", service_name);

    auto is_model_update =
        (!is_static_graph && !FLAGS_force_disable_btq_model_update_debug_only_subject_to_change);

    reco_arch::uni_predict::BatchingSystemOption batching_options;
    batching_options.batch_gpu_cache_option.enable_fp16 = enable_fp16;

    if (open_gpu_cache) {
      auto &cache_config = batching_options.batch_gpu_cache_option.gpu_cache_config;
      // LOG(WARNING) << "open gpu cache batching_options.alignment must 0";
      batching_options.batch_gpu_cache_option.open_gpu_cache = true;
      auto gpu_cache_config = config()->Get("gpu_cache_config");
      if (gpu_cache_config) {
        COND_VAR(gpu_cache_config->Get("capacity"), cache_config.gpu_cache_capacity,
                 gpu_cache_config->GetInt("capacity", FLAGS_gpu_cache_reserve_set_number),
                 FLAGS_gpu_cache_reserve_set_number);
        COND_VAR(gpu_cache_config->Get("capacity_of_tables"), cache_config.capacity_of_tables,
                 gpu_cache_config->GetString("capacity_of_tables", ""), "");
        COND_VAR(gpu_cache_config->Get("ignore_clean_kess_names"), cache_config.ignore_clean_kess_names,
                 gpu_cache_config->GetString("ignore_clean_kess_names", ""), "");
        auto *update_category = gpu_cache_config->Get("update_category");
        if (update_category) {
          CONFIG_CHECK(update_category->IsArray(), "update_category must exist and be and array.");
          for (auto *c : update_category->array()) {
            reco_arch::uni_predict::EmbeddingServerUpdateCategory category;
            category.kess_name = c->GetString("kess_name");
            category.dim = c->GetInt("dim", 0);
            category.lru = c->GetBoolean("lru", false);
            category.clean_time_sec = c->GetInt("clean_time_sec", 0);
            category.capacity = c->GetInt("capacity", 0);
            category.enabled = true;
            if (category.kess_name == "" || category.dim <= 0) {
              LOG(FATAL) << "update_category kess_name and dim must exist: " << category.kess_name
                         << "; dim: " << category.dim;
            }
            cache_config.update_category.emplace_back(std::move(category));
          }
        }
      }
    }
    auto batch_config = config()->Get("batching_config");

    if (batch_config && batch_config->Get("device_mask")) {
      batching_options.device_mask = batch_config->GetInt("device_mask", 0);
    }
    COND_VAR(batch_config && batch_config->Get("alignment"), batching_options.alignment,
             batch_config->GetInt("alignment", 256), FLAGS_batching_option_alignment);
    COND_VAR(batch_config && batch_config->Get("batch_timeout_micros"), batching_options.batch_timeout_micros,
             batch_config->GetInt("batch_timeout_micros", 8000), FLAGS_batching_option_batch_timeout_micros);
    COND_VAR(batch_config && batch_config->Get("max_enqueued_batches"), batching_options.max_enqueued_batches,
             batch_config->GetInt("max_enqueued_batches", 16), FLAGS_batching_option_max_enqueued_batches);
    COND_VAR(batch_config && batch_config->Get("max_batch_size"), batching_options.max_batch_size,
             batch_config->GetInt("max_batch_size", 512), FLAGS_batching_option_max_batch_size);
    COND_VAR(batch_config && batch_config->Get("process_thread_num"), batching_options.process_thread_num,
             batch_config->GetInt("process_thread_num", 0), 0);
    std::string batch_task_type = "BasicBatchingTask";
    COND_VAR(batch_config && batch_config->Get("batch_task_type"), batch_task_type,
             batch_config->GetString("batch_task_type", "BasicBatchingTask"), "BasicBatchingTask");
    if (batch_task_type == "BasicBatchingTask") {
      batching_options.task_type = reco_arch::uni_predict::BatchTaskType::kBasicBatchingTask;
    } else if (batch_task_type == "BatchGPUMergeTask") {
#ifdef USE_GPU
      batching_options.task_type = reco_arch::uni_predict::BatchTaskType::kBatchGPUMergeTask;
      GetSlotNum(batching_options, embedding_configs_, &(batching_options.batch_gpu_merge_kernel_option));
#else
      MISMATCH_DEVICE_TYPE(GPU);
#endif
    } else if (batch_task_type == "BatchTensorflowTask") {
      batching_options.task_type = reco_arch::uni_predict::BatchTaskType::kBatchTensorflowTask;
    } else if (batch_task_type == "BatchTVMTask") {
      batching_options.task_type = reco_arch::uni_predict::BatchTaskType::kBatchTVMTask;
    } else {
      LOG(ERROR) << "batch_task_type = " << batch_task_type << ", not implemented yet!";
      return false;
    }
    batching_options.type = batch_task_type;
    task_type_ = batching_options.task_type;
    batching_options.cpu_only = false;
    batching_options.model_key = key_;

    FillEmbeddingFetcherConfig(embedding_configs_, embedding_fetchers_,
                               &batching_options.batch_gpu_cache_option.embedding_fetcher_config);
    // NOTE(zfy): initialize global pool before predictor, because in predicitor init function, some op may
    // call dynamic embedding fetcher, see https://docs.corp.kuaishou.com/d/home/<USER>
    embedding_manager::EmbeddingManagerGlobalHelper::GetInstance().Initialization(
        FLAGS_uni_embedding_manager_thread_pool_size_per_fetcher * embedding_fetchers_.size(), key_);

    predictor_ = reco_arch::uni_predict::Predictor::GetOrCreateInstance(key_);
    predictor_->Initialize(is_model_update, config_json.ToString(), model_loader_type_);
    batch_system_ = reco_arch::uni_predict::BatchingSystem::GetOrCreateInstance(key_);

    // debug option
    debug_tensor_ = config()->GetBoolean("debug_tensor", false);

    // embedding_fetchers_
    // create embedding manager
    CHECK(CheckTaskTypeAndEmbeddingManagerType(batching_options.task_type, embedding_manager_type_))
        << "config embedding_manager_type is not compatible with batch_task_type";

    if (embedding_manager_type_ == "parallel_fetch") {
      embedding_manager_ = std::make_shared<embedding_manager::ParallelFetchEmbeddingManager>(
          embedding_configs_, input_configs_, embedding_fetchers_, key_, debug_tensor_, batching_options);
    } else if (embedding_manager_type_ == "post_parallel_fetch") {
      embedding_manager_ = std::make_shared<embedding_manager::PostFetchEmbeddingManager>(
          embedding_configs_, input_configs_, embedding_fetchers_, key_, debug_tensor_);
#ifdef USE_GPU
    } else if (embedding_manager_type_ == "gpu_cache_parallel") {
      if (FLAGS_use_cuda_graph_memcpy) {
        LOG(FATAL) << "FLAGS_use_cuda_graph_memcpy must be false when using gpu_cache_parallel";
      }
      embedding_manager_ = std::make_shared<embedding_manager::GpuCacheEmbeddingManager>(
          embedding_configs_, input_configs_, embedding_fetchers_, key_, debug_tensor_);
#endif
    } else {
      LOG(ERROR) << "UniPredict does support embedding manager type " << embedding_manager_type_;
      return false;
    }

    LOG(INFO) << "init batch_system";
    if (!batch_system_->Initialize(batching_options, predictor_)) {
      LOG(ERROR) << "Batch system init failed.";
      return false;
    }

    CONFIG_CHECK(predictor_->GetCurrentExecutor()->GetIOTensorInfo(&tensor_info_),
                 "get io_tensor_info failed");

    // invalid predict value process method
    keep_invalid_value_ = config()->GetBoolean("keep_invalid_value", true);

    // runtime init
    init_runtime_ = false;

    // embedding fether set model key
    for (auto fetcher : embedding_fetchers_) {
      fetcher->SetModelKey(key_);
    }
    dynamic_targets_attr_ = config()->GetString("dynamic_targets_attr", "");

    // common predict 场景，无需 item
    only_common_predict_ =
        only_common_input && only_common_output &&
        (batch_task_type == "BasicBatchingTask" ||
         batch_task_type == "BatchTensorflowTask" || batch_task_type == "BatchTVMTask");
    LOG(INFO) << GetName() << " only_common_predict: " << only_common_predict_
              << ", only_common_input: " << only_common_input
              << ", only_common_output: " << only_common_output << ", batch_task_type: " << batch_task_type;

    return true;
  }

  void FillEmbeddingFetcherConfig(
      const std::vector<EmbeddingConfig> &embedding_configs,
      const std::vector<std::shared_ptr<embedding_fetcher::BaseEmbeddingFetcher>> &embedding_fetchers,
      reco_arch::uni_predict::EmbeddingFetcherConfig *embedding_fetcher) {
    // TODO(lixinhua03)
    // 临时放置，测试 memset 加速
    auto &input_slot_expand = embedding_fetcher->input_slot_expand;

    for (int idx = 0; idx < embedding_configs.size(); ++idx) {
      for (auto iter = embedding_configs[idx].slot_configs.begin();
           iter != embedding_configs[idx].slot_configs.end(); ++iter) {
        input_slot_expand[iter->second.input_name] =
            std::make_pair(iter->second.slots.size(), iter->second.expand);
      }
    }
    auto &ef_splitter = embedding_fetcher->ef_splitter;
    // auto &kess_dtype_to_idx = embedding_fetcher->kess_dtype_to_idx;
    // auto &input_name_to_splitter_idx = embedding_fetcher->input_name_to_splitter_idx;
    std::unordered_map<std::string, std::unordered_map<EmbCompressType, int>> kess_dtype_to_idx;
    std::unordered_map<std::string, int> input_name_to_splitter_idx;
    for (size_t i = 0; i < embedding_configs.size(); ++i) {
      std::string kess_service_shadow = embedding_configs[i].kess_service +
                                        embedding_configs[i].colossusdb_embd_model_name +
                                        embedding_configs[i].colossusdb_embd_table_name;
      std::string fetcher_type = embedding_configs[i].fetcher_type;
      auto &slot_configs = embedding_configs[i].slot_configs;
      for (auto iter = slot_configs.begin(); iter != slot_configs.end(); ++iter) {
        auto &slot_config = iter->second;
        auto &dtype = slot_config.dtype;
        size_t splitter_idx = ef_splitter.size();
        auto iter0 = kess_dtype_to_idx.find(kess_service_shadow);
        auto create_ef_splitter = [&](int idx) -> reco_arch::uni_predict::EmbeddingFetcherSplitter {
          reco_arch::uni_predict::EmbeddingFetcherSplitter ef;
          ef.num_shard = embedding_configs[idx].shards;
          ef.dtype = slot_config.dtype;
          ef.kess_service = embedding_configs[idx].kess_service;
          ef.use_kconf_client = embedding_configs[idx].use_kconf_client;
          ef.colossusdb_embd_model_name = embedding_configs[idx].colossusdb_embd_model_name;
          ef.colossusdb_embd_table_name = embedding_configs[idx].colossusdb_embd_table_name;
          ef.fetcher_type = embedding_configs[idx].fetcher_type;
          ef.max_signs_per_request = embedding_configs[idx].max_signs_per_request;
          ef.timeout_ms = embedding_configs[idx].timeout_ms;
          // for rdma
          ef.rdma_max_async_num = embedding_configs[idx].rdma_max_async_num;
          ef.embedding_server_idx = idx;
          return ef;
        };
        if (iter0 == kess_dtype_to_idx.end()) {
          ef_splitter.emplace_back(create_ef_splitter(i));
        } else {
          auto iter1 = iter0->second.find(dtype);
          if (iter1 == iter0->second.end()) {
            ef_splitter.emplace_back(create_ef_splitter(i));
          } else {
            splitter_idx = iter1->second;
          }
        }
        ef_splitter[splitter_idx].input_name.emplace_back(slot_config.input_name);
        kess_dtype_to_idx[kess_service_shadow][dtype] = splitter_idx;
        input_name_to_splitter_idx[slot_config.input_name] = splitter_idx;
      }
    }
    for (size_t i = 0; i < ef_splitter.size(); ++i) {
      std::string input_name_str = "";
      for (size_t j = 0; j < ef_splitter[i].input_name.size(); ++j) {
        input_name_str += ef_splitter[i].input_name[j];
        input_name_str += ", ";
      }
      LOG(INFO) << "ef_splitter , key: " << key_ << ", i: " << i
                << "; dtype: " << ks::emb_util::EmbCompressor::CompressTypeToString(ef_splitter[i].dtype)
                << "; kess_service: " << ef_splitter[i].kess_service
                << "; use_kconf_client: " << ef_splitter[i].use_kconf_client
                << "; colossusdb_embd_model_name: " << ef_splitter[i].colossusdb_embd_model_name
                << "; colossusdb_embd_table_name: " << ef_splitter[i].colossusdb_embd_table_name
                << "; service_idx: " << ef_splitter[i].embedding_server_idx
                << ", rdma_max_async_num: " << ef_splitter[i].rdma_max_async_num
                << "; input_names: " << input_name_str;
    }
  }

 private:
  bool CheckTaskTypeAndEmbeddingManagerType(reco_arch::uni_predict::BatchTaskType task_type,
                                            const std::string &embedding_manager_type) {
    if (embedding_manager_type == "post_parallel_fetch" &&
        task_type != reco_arch::uni_predict::BatchTaskType::kBasicBatchingTask &&
        task_type != reco_arch::uni_predict::BatchTaskType::kBatchTensorflowTask &&
        task_type != reco_arch::uni_predict::BatchTaskType::kBatchTVMTask) {
      LOG(ERROR) << "embedding_manager_type:post_parallel_fetch only support "
                 << "batch_task_type:BasicBatchingTask or BatchTensorflowTask";
      return false;
    }
    return true;
  }

  bool MakePredictionBasicBatchingTask(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                       RecoResultConstIter end,
                                       int num_items);  // embedding pooling + predict, BasicBatchingTask

  bool MakePredictionBatchTVMTask(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                  RecoResultConstIter end,
                                  int num_items);  // embedding pooling + predict, BasicBatchingTask

  bool MakePredictionTFCPUBatchingTask(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                       RecoResultConstIter end,
                                       int num_items);  // embedding pooling + predict, TFCPUBatchingTask
#ifdef USE_GPU
  bool MakePredictionBatchGPUMergeTask(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                       RecoResultConstIter end,
                                       int num_items);  // BatchGPUMergeTask

  void GetSlotNum(const reco_arch::uni_predict::BatchingSystemOption &batch_option,
                  const std::vector<EmbeddingConfig> &embedding_configs,
                  reco_arch::uni_predict::BatchGPUMergeKernelOption *gpu_merge_option) const;  // init option
                                                                                               // for gpu
#endif
                                                                                               // merge

  void SetProcessorName();

  reco_arch::uni_predict::CallbackStatus FetchOutput(
      MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end, int num_items,
      const reco_arch::uni_predict::PreallocatedTensors &preallocated);

 private:
  // in terms of fetching embedding
  std::vector<EmbeddingConfig> embedding_configs_;
  std::vector<std::shared_ptr<embedding_fetcher::BaseEmbeddingFetcher>> embedding_fetchers_;
  std::string embedding_manager_type_;

  // predcit inputs, outputs and params
  Map<std::string, InputConfig> input_configs_;  // tensor_name->InputConfig
  std::vector<OutputConfig> outputs_;  // 一个 tensor 可以对应多个 attr, 以选择打平输出至 common attr
  std::vector<int64> all_outputs_no_;
  reco_arch::uni_predict::BatchTaskType task_type_;

  // predict options
  std::string queue_prefix_;
  std::string key_;
  std::string model_loader_type_;
  std::string dynamic_targets_attr_;
  bool only_common_predict_ = false;

  // predict objects
  std::shared_ptr<reco_arch::uni_predict::Predictor> predictor_;
  std::shared_ptr<reco_arch::uni_predict::BatchingSystem> batch_system_;
  reco_arch::uni_predict::IOTensorInfo tensor_info_;

  // debug options
  bool debug_tensor_;

  bool keep_invalid_value_;
  // embedding merger
  std::shared_ptr<embedding_manager::BaseEmbeddingManager> embedding_manager_;

  // split task
  std::vector<EmbeddingConfig> embedding_configs_split_;
  std::vector<std::shared_ptr<embedding_fetcher::BaseEmbeddingFetcher>> embedding_fetchers_split_;
  std::shared_ptr<embedding_manager::BaseEmbeddingManager> embedding_manager_split_;
  size_t batch_split_idx_;
  std::mutex mu_fetch_;
  bool batching_enable_split_task_ = false;

  // runtime once
  bool init_runtime_;
  serving_base::Timer timer_;
  DISALLOW_COPY_AND_ASSIGN(UniPredictFusedItemAttrEnricher);
};

}  // namespace platform
}  // namespace ks
