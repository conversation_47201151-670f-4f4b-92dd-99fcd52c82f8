#include "dragon/src/processor/ext/ad/uni/embedding_producer/ad_embedding_producer_feature_enricher.h"

#include <utility>

#include "dragon/src/processor/ext/ad/uni/embedding_producer/ad_embedding_producer.h"

namespace ks {
namespace platform {

using ks::ad_nn::SparseFeatureOfOneField;
using ks::ad_nn::SparseFeatureOfOneSample;
using ks::ad_nn::Feature;
using ks::ad_nn::VectorToString;

#define CONFIG_CHECK(cond, msg)                                                   \
  if (!(cond)) {                                                                  \
    CL_LOG(ERROR) << "AdEmbeddingProducerFeatureEnricher init failed! " << (msg); \
    return false;                                                                 \
  }

void AdEmbeddingProducerFeatureEnricher::Enrich(MutableRecoContextInterface *context,
                                                RecoResultConstIter begin,
                                                RecoResultConstIter end) {
  for (auto it = begin; it != end; ++it) {
    auto item_id = it->item_key;
    auto feature = ProduceEmbeddingCore::Singleton().GetFeature(item_id);
    if (feature == nullptr) {
      CL_LOG(ERROR) << "Null feature, item id " << item_id;
      context->SetIntItemAttr(item_id, feature_miss_attr_, 1);
      continue;
    }
    // sparse feature
    if (has_sparse_item_feature_) {
      std::vector<int64_t> sparse_fields;
      std::vector<int64_t> sparse_signs;
      SparseFeatureOfOneSample sparse_orig;
      bool ret = feature->FetchSparse(&sparse_orig);
      if (!ret) {
        CL_LOG(ERROR) << "Fetch sparse feature failed, item id: " << item_id;
        context->SetIntItemAttr(item_id, feature_miss_attr_, 2);
        continue;
      }
      for (uint32_t i = 0; i < sparse_orig.size(); ++i) {
        const SparseFeatureOfOneField& sign_vec = sparse_orig[i];
        int64_t sparse_field = item_field_start_ + i;
        sparse_fields.insert(sparse_fields.end(), sign_vec.size(), sparse_field);
        for (const auto &extract_result : sign_vec) {
          sparse_signs.emplace_back(extract_result.sign);
        }
      }
      context->SetIntListItemAttr(item_id, sparse_fields_attr_, std::move(sparse_fields));
      context->SetIntListItemAttr(item_id, sparse_signs_attr_, std::move(sparse_signs));
    }
    // dense feature
    if (has_dense_item_feature_) {
      std::vector<float> dense_float_values(dense_item_features_length_);
      bool ret = feature->FetchDense(&dense_float_values);
      if (!ret) {
        CL_LOG(ERROR) << "Fetch dense feature failed, item id: " << item_id;
        context->SetIntItemAttr(item_id, feature_miss_attr_, 3);
        continue;
      }
      context->SetIntListItemAttr(item_id, dense_fields_attr_,
                                  std::vector(dense_item_fields_.begin(),
                                              dense_item_fields_.end()));
      context->SetDoubleListItemAttr(item_id, dense_values_attr_,
                                     std::vector<double>(dense_float_values.begin(),
                                                         dense_float_values.end()));
    }
  }
}

bool AdEmbeddingProducerFeatureEnricher::InitProcessor() {
  sparse_fields_attr_ = config()->GetString("sparse_fields_attr");
  CONFIG_CHECK(!sparse_fields_attr_.empty(), "sparse_fields_attr is empty");

  sparse_signs_attr_ = config()->GetString("sparse_signs_attr");
  CONFIG_CHECK(!sparse_signs_attr_.empty(), "sparse_signs_attr is empty");

  dense_fields_attr_ = config()->GetString("dense_fields_attr");
  CONFIG_CHECK(!dense_fields_attr_.empty(), "dense_fields_attr is empty");

  dense_values_attr_ = config()->GetString("dense_values_attr");
  CONFIG_CHECK(!dense_values_attr_.empty(), "dense_values_attr is empty");

  feature_miss_attr_ = config()->GetString("feature_miss_attr", "feature_miss_attr");
  CONFIG_CHECK(!feature_miss_attr_.empty(), "feature_miss_attr is empty");

  const auto& feature_struct_info = ProduceEmbeddingCore::Singleton().GetFeatureStructInfo();
  item_field_start_ = feature_struct_info.item_slot_start;
  item_field_end_ = feature_struct_info.item_slot_end;
  sparse_item_features_length_ = feature_struct_info.sparse_item_features_length;
  dense_item_field_start_ = feature_struct_info.dense_item_slot_start;
  dense_item_field_end_ = feature_struct_info.dense_item_slot_end;
  dense_item_features_length_ = feature_struct_info.dense_item_features_length;
  if (item_field_start_ < 0) {
    has_sparse_item_feature_ = false;
  }
  if (dense_item_field_start_ < 0) {
    has_dense_item_feature_ = false;
  }

  for (int32_t field = dense_item_field_start_; field <= dense_item_field_end_; ++field) {
    uint32_t size = feature_struct_info.dense_field_sizes[field];
    dense_item_fields_.insert(dense_item_fields_.end(), size, field);
  }
  CL_LOG(INFO) << "feature extractor init succeeds"
               << ", item_field_start_: " << item_field_start_
               << ", item_field_end_: " << item_field_end_
               << ", sparse_item_features_length_: " << sparse_item_features_length_
               << ", dense_item_field_start_: " << dense_item_field_start_
               << ", dense_item_field_end_: " << dense_item_field_end_
               << ", dense_item_features_length_: " << dense_item_features_length_
               << ", has_sparse_item_feature_: " << has_sparse_item_feature_
               << ", has_dense_item_feature_: " << has_dense_item_feature_;
  CL_LOG(INFO) << "dense_item_fields_: " << VectorToString<int64_t>(dense_item_fields_);

  return true;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdEmbeddingProducerFeatureEnricher, AdEmbeddingProducerFeatureEnricher)

}  // namespace platform
}  // namespace ks
