#pragma once

#include "dragon/src/processor/base/common_reco_base_retriever.h"

namespace ks {
namespace platform {

class AdEmbeddingProducerRetriever : public CommonRecoBaseRetriever {
 public:
  AdEmbeddingProducerRetriever() {
  }

  virtual ~AdEmbeddingProducerRetriever() {
  }

  void Retrieve(AddibleRecoContextInterface *context) override;

  bool IsAsync() const override {
    return false;
  }

 private:
  bool InitProcessor() override;

 private:
  uint64_t producer_item_version_ = 0;

  DISALLOW_COPY_AND_ASSIGN(AdEmbeddingProducerRetriever);
};

}  // namespace platform
}  // namespace ks
