#pragma once

#include <string>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"

namespace ks {
namespace platform {

class AdEmbeddingProducerFeatureEnricher : public CommonRecoBaseEnricher {
 public:
  AdEmbeddingProducerFeatureEnricher() {}
  virtual ~AdEmbeddingProducerFeatureEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;

 private:
  std::string sparse_fields_attr_;
  std::string sparse_signs_attr_;
  std::string dense_fields_attr_;
  std::string dense_values_attr_;
  std::string feature_miss_attr_;

  // Length of all the sparse item features.
  uint32_t sparse_item_features_length_ = 0;
  // Length of all the dense item features.
  uint32_t dense_item_features_length_ = 0;

  // spare item 特征首末 field
  int64_t item_field_start_ = 0;
  int64_t item_field_end_ = 0;
  // dense item 特征首末 field
  int64_t dense_item_field_start_ = 0;
  int64_t dense_item_field_end_ = 0;

  bool has_sparse_item_feature_ = true;
  bool has_dense_item_feature_ = true;

  // dense item 特征 field 信息是固定的, 初始化生成一次即可
  std::vector<int64_t> dense_item_fields_;

  DISALLOW_COPY_AND_ASSIGN(AdEmbeddingProducerFeatureEnricher);
};

}  // namespace platform
}  // namespace ks
