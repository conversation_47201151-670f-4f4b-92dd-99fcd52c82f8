#pragma once

#include <memory>
#include <map>
#include <string>
#include <unordered_map>
#include <vector>

#include "absl/container/flat_hash_map.h"
#include "serving_base/jansson/json.h"
#include "teams/ad/ad_nn/embedding_data/item_info_impl.h"
#include "teams/ad/ad_nn/embedding_data/preallocated_embedding_group.h"
#include "teams/ad/ad_nn/embedding_data/statistical_filter.h"
#include "teams/ad/ad_nn/feature/feature_extractor_bundle.h"
#include "teams/ad/ad_nn/feature/feature_file_info.h"
#include "teams/ad/ad_nn/feature/type_define.h"
#include "teams/ad/ad_nn/feature_store/common.h"
#include "teams/ad/ad_nn/feature_store/extract_feature_store.h"
#include "teams/ad/ad_nn/feature_store/feature_manager.h"
#include "teams/ad/ad_nn/kconf_manager/cluster_type.h"
#include "teams/ad/ad_nn/kconf_manager/embedding_producer_config.h"
#include "teams/ad/ad_nn/kconf_manager/embedding_model_config.h"
#include "teams/ad/ad_nn/kconf_manager/target_config.h"
#include "teams/ad/ad_nn/kconf_manager/trigger_config.h"
#include "teams/ad/ad_nn/kconf_manager/twin_towers_kconf_manager.h"
#include "teams/ad/ad_nn/model/hybrid_embedding.h"
#include "teams/ad/ad_nn/model/utils.h"
#include "teams/ad/ad_nn/twin_towers_ps/common.h"
#include "teams/ad/ad_nn/twin_towers_ps/define/get_bid.h"
#include "teams/ad/ad_nn/twin_towers_ps/define/get_cluster_id.h"

using ks::ad_twin_towers::ModelConfig;
using ks::ad_nn::AdFeatureExtractorBundle;
using ks::ad_nn::ClusterType;
using ks::ad_nn::EmbeddingSpec;
using ks::ad_nn::ExtractFeatureStore;
using ks::ad_nn::Feature;
using ks::ad_nn::FeatureManager;
using ks::ad_nn::FeatureData;
using ks::ad_nn::FeatureFileInfo;
using ks::ad_nn::FeatureStructInfo;
using ks::ad_nn::GraphType;
using ks::ad_nn::Operation;
using ks::ad_nn::FeatureOrgLayout;
using ks::ad_nn::ExtractType;
using ks::ad_nn::PreallocatedEmbeddingGroup;
using ks::ad_nn::StorageType;
using ks::ad_twin_towers::TriggerSourceConfig;
using kuaishou::retr::UnitSpec;

namespace ks {
namespace platform {

class ProduceEmbeddingCore {
 public:
  ~ProduceEmbeddingCore();

  static ProduceEmbeddingCore &Singleton() {
    static ProduceEmbeddingCore instance;
    return instance;
  }

  void Start();
  bool PrepareCalcItemDnn(int slot_idx);

  std::vector<uint64_t>& GetItemIds() {
    return try_to_calculate_dnn_ids_;
  }

  Feature GetFeature(uint64_t item_id) {
    return active_feature_->GetFeature(item_id);
  }

  const FeatureStructInfo& GetFeatureStructInfo() {
    return feature_struct_info_;
  }

 private:
  ProduceEmbeddingCore() {}

  inline bool IsBtqModel(const std::string &model_src) { return model_src == "btq"; }

  void InitFilterConfig();

  const absl::flat_hash_map<uint64_t, UnitSpec*>& GetUnitSpec(
      const std::string& set_name, bool wait_for_first_version = false,
      int wait_seconds = -1);
  void StatSnapshot();
  void GetX7Id();
  void AcquireFeatureInfo(const std::vector<FeatureData>& data, Operation op);
  uint32_t GetEmbeddingDim(uint32_t field);
  void FetchFeatureInfo(const std::string &feature_path);
  void StartFeatureManager();
  void StartFeasibleSetManager();
  void InitEmbedding();

 private:
  base::Json *config_ = nullptr;

  // calc item dnn data
  std::vector<uint64_t> try_to_calculate_dnn_ids_;
  std::vector<uint64_t> failed_ids_;
  std::vector<uint64_t> successed_ids_;
  std::vector<uint64_t> try_to_calculate_rnode_ids_;
  std::vector<uint64_t> try_to_calculate_vnode_ids_;
  std::unordered_map<uint64_t, uint64_t> item_id_to_cluster_id_;

  std::shared_ptr<ks::ad_twin_towers::ModelOption> model_option_;
  std::shared_ptr<ks::ad_nn::VectorInfos> vector_infos_;
  std::unordered_map<std::string, EmbeddingSpec> embedding_options_;
  std::shared_ptr<ks::ad_twin_towers::ModelConfig> model_config_;
  std::shared_ptr<ks::ad_twin_towers::EmbeddingSourceConfig> embedding_config_;
  std::shared_ptr<ks::ad_nn::ClusterSearchConfig> cluster_search_config_;
  std::shared_ptr<ks::ad_nn::TargetConfig> target_config_;
  std::shared_ptr<TriggerSourceConfig> trigger_source_config_;

  std::string feature_path_;
  bool cluster_search_ = false;
  ClusterType cluster_type_ = ClusterType::CREATIVE;
  GraphType graph_type_ = GraphType::E2E;
  int score_threshold_version_ = -1;
  bool enable_bid_ = false;
  bool enable_sid_ = false;
  int x7_cateid_common_attr_nameid_ = 61085;
  bool sort_bid_ = false;
  bool mio_model_ = false;
  bool enable_continuous_check_ = true;
  bool reuse_unchanged_feature_emb_ = false;

  ks::ad_twin_towers::FilterCreativeConfig filter_config_;
  ks::ad_twin_towers::StatisticalFilter statistical_filter_;

  ks::ad_nn::ConcurrentItemInfoMap updating_ids_;
  absl::flat_hash_map<uint64_t, std::shared_ptr<ItemInfo>> snapshot_;
  PreallocatedEmbeddingGroup embedding_data_;
  PreallocatedEmbeddingGroup cluster_embedding_data_;

  std::map<std::string, ks::ad_nn::FieldEmbeddingInfoMap> emp_shard_infos_;

  // Used to manager all the embedding feature.
  std::unique_ptr<FeatureManager> feature_manager_;
  ExtractFeatureStore* active_feature_ = nullptr;
  std::shared_ptr<FeatureFileInfo> feature_file_info_;
  std::unique_ptr<AdFeatureExtractorBundle> fea_ext_bundle_;
  FeatureStructInfo feature_struct_info_;

  // stat
  absl::flat_hash_map<uint64_t, int> stat_photo_id_;
  absl::flat_hash_map<uint64_t, int> stat_photo_ocpc_id_;
  absl::flat_hash_map<uint64_t, int> stat_photo_ocpc_bidtype_id_;
  absl::flat_hash_map<uint64_t, int> stat_unit_id_;
  absl::flat_hash_map<uint64_t, int> stat_product_id_;
  absl::flat_hash_map<uint64_t, int> stat_spu_id_v2_;
  absl::flat_hash_map<uint64_t, int> stat_spu_id_bucket_;
  absl::flat_hash_map<uint64_t, int> stat_dup_photo_id_;
  absl::flat_hash_map<uint64_t, int> stat_dup_photo_product_ocpc_bidtype_id_;

  DISALLOW_COPY_AND_ASSIGN(ProduceEmbeddingCore);
};

}  // namespace platform
}  // namespace ks
