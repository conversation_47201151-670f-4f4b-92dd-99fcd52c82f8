#include "dragon/src/processor/ext/ad/uni/embedding_producer/ad_embedding_producer.h"

#include <algorithm>
#include <limits>

#include "serving_base/util/dynamic_config.h"
#include "teams/ad/ad_nn/embedding_data/embedding_impl.h"
#include "teams/ad/ad_nn/model/utils.h"
#include "teams/ad/ad_nn/role/role.h"
#include "teams/ad/ad_nn/target/defines.h"
#include "teams/ad/ad_nn/tools/common_tools.h"
#include "teams/ad/ad_nn/twin_towers_ps/common.h"
#include "teams/ad/ad_nn/twin_towers_ps/define/bid_type.h"
#include "teams/ad/ad_nn/twin_towers_ps/define/get_cluster_id.h"
#include "teams/ad/ad_nn/twin_towers_ps/feasible_set.h"
#include "teams/ad/ad_nn/twin_towers_ps/target_filter/target_filter.h"
#include "teams/ad/ad_nn/twin_towers_ps/target_filter/target_filter_i18n.h"

using ks::ad_nn::ReaderOption;
using ks::ad_nn::StoreOption;
using ks::ad_twin_towers::BidType;
using ks::ad_twin_towers::BuildBitsIndex;
using ks::ad_twin_towers::BuildCrowdPackBitsIndex;
using ks::ad_twin_towers::FeasibleSetOptions;
using ks::ad_twin_towers::FeasibleSetManager;
using ks::ad_twin_towers::GetClusterId;
using ks::ad_twin_towers::GetTwinTowersModelConfig;
using ks::ad_twin_towers::TwinTowersKconfManager;
using ks::ad_twin_towers::ModelConfig;

namespace ks {
namespace platform {

ProduceEmbeddingCore::~ProduceEmbeddingCore() {
  LOG(INFO) << "Producer embedding core deconstruct.";
  if (feature_manager_ != nullptr) {
    feature_manager_->Stop();
  }
  LOG(INFO) << "Feature manager stop.";
  // TODO(yangwen): 打通了再补监控
  // if (feature_stat_ != nullptr) {
  //   feature_stat_->Stop();
  // }
  FeasibleSetManager::Instance()->Stop();
  LOG(INFO) << "Producer embedding core deconstruct finish.";
}

void ProduceEmbeddingCore::Start() {
  model_option_ = GetTwinTowersModelConfig();
  if (model_option_ == nullptr) {
    LOG(FATAL) << "Invalid model config.";
  }

  config_ = base::DynamicJsonConfig::GetConfig(false, false);
  InitFilterConfig();

  vector_infos_ = ks::ad_nn::GetVectorInfos();
  enable_bid_ = vector_infos_->enable_bid;
  graph_type_ = static_cast<GraphType>(vector_infos_->graph_type);

  target_config_ =
      TwinTowersKconfManager::Instance()->GetBlockConfig<ks::ad_nn::TargetConfig>(
          "target");
  trigger_source_config_ =
      TwinTowersKconfManager::Instance()->GetBlockConfig<TriggerSourceConfig>(
          "trigger_source");
  model_config_ =
      TwinTowersKconfManager::Instance()->GetBlockConfig<ModelConfig>("model");
  embedding_config_ =
      TwinTowersKconfManager::Instance()->GetBlockConfig<ks::ad_twin_towers::EmbeddingSourceConfig>(
          "embedding_source");
  cluster_search_config_ =
      TwinTowersKconfManager::Instance()->GetBlockConfig<ClusterSearchConfig>(
          "cluster_search");

  cluster_search_ = (cluster_search_config_ != nullptr);
  if (graph_type_ == GraphType::PHOTO_OCPC_SEARCH) {
    cluster_search_ = true;
  }

  cluster_type_ = static_cast<ClusterType>(config_->GetInt("cluster_type", 16));
  if (cluster_search_config_ != nullptr &&
      cluster_search_config_->cluster_type != ClusterType::MAX) {
    cluster_type_ = cluster_search_config_->cluster_type;
  }
  LOG(INFO) << "Cluster type: " << static_cast<int>(cluster_type_);

  mio_model_ = config_->GetBoolean("mio_model", false);
  enable_continuous_check_ = config_->GetBoolean("enable_continuous_check", true);
  reuse_unchanged_feature_emb_ = config_->GetBoolean("reuse_unchanged_feature_emb", false);

  StartFeasibleSetManager();
  InitEmbedding();

  // feature info
  feature_path_ = config_->GetString("feature_path", "");
  if (feature_path_.empty()) {
    LOG(FATAL) << "empty feature_path";
    return;
  }
  FetchFeatureInfo(feature_path_);
  StartFeatureManager();
}

void ProduceEmbeddingCore::InitFilterConfig() {
  filter_config_.max_items_size_per_photo =
      config_->GetInt("max_items_size_per_photo", 0);
  sort_bid_ = config_->GetBoolean("sort_bid", false);
  filter_config_.count_name =
      static_cast<ClusterType>(config_->GetInt("field_count_name", 1));
  filter_config_.score_type =
      static_cast<QualityScoreType>(config_->GetInt("filter_score_type", 8));
  filter_config_.up_or_below = config_->GetInt("up_or_below", 0);
  filter_config_.count_item_threshold = config_->GetInt("count_item_threshold", 0);
  if (filter_config_.max_items_size_per_photo > 0) {
    filter_config_.count_item_threshold = filter_config_.max_items_size_per_photo;
  }
  filter_config_.filter_by_photo_unit =
      config_->GetBoolean("filter_by_photo_unit", false);
  filter_config_.filter_by_new_creative_tag =
      config_->GetInt("filter_by_new_creative_tag", -1);
  filter_config_.univ_filter_by_new_creative_tag =
      config_->GetBoolean("univ_filter_by_new_creative_tag", false);
  filter_config_.filter_zero_photo_id = config_->GetInt("filter_zero_photo_id", -1);
  filter_config_.filter_by_coldstart_creative_tag =
      config_->GetInt("filter_by_coldstart_creative_tag", -1);
  filter_config_.filter_by_new_coldstart_creative_tag =
      config_->GetInt("filter_by_new_coldstart_creative_tag", -1);
  filter_config_.filter_by_inner_photo_coldstart =
      config_->GetBoolean("filter_by_inner_photo_coldstart", false);
  filter_config_.filter_by_inner_live_coldstart =
      config_->GetBoolean("filter_by_inner_live_coldstart", false);
  filter_config_.filter_min_rate = config_->GetFloat("filter_min_rate", 0.1);
  statistical_filter_.SetConfig(filter_config_);
  LOG(INFO) << "Filter by config: " << filter_config_.DebugString()
            << ", whether to sort bid: " << sort_bid_
            << ", filter zero photo id: " << filter_config_.filter_zero_photo_id;
}

void ProduceEmbeddingCore::AcquireFeatureInfo(const std::vector<FeatureData>& data,
                                              Operation op) {
  thread_local std::vector<uint64_t> batch_item_ids;
  batch_item_ids.clear();
  if (op == Operation::DELETE) {
    for (int i = 0; i < data.size(); i++) {
      batch_item_ids.emplace_back(data[i].item_id);
    }
    updating_ids_.Delete(batch_item_ids);
    return;
  }

  // Keep priority for value from kconf.
  bool filter_zero_photo_id = false;
  if (filter_config_.filter_zero_photo_id == -1) {
    if (graph_type_ == GraphType::PHOTO_OCPC_SEARCH &&
        cluster_type_ != ClusterType::MIXED_P2L_LIVE_OCPC) {
      filter_zero_photo_id = true;
      LOG_FIRST_N(INFO, 2) << "Filter zero photo id.";
    }
  } else {
    filter_zero_photo_id = (filter_config_.filter_zero_photo_id > 0) ? true : false;
  }

  for (int i = 0; i < data.size(); i++) {
    auto item_id = data[i].item_id;
    auto* add_item = data[i].item.get();
    auto& campaign = add_item->ad_dsp_info().campaign().base();
    auto& creative = add_item->ad_dsp_info().creative().base();
    uint64_t photo_id = creative.photo_id();
    uint64_t dup_photo_id = creative.dup_photo_id();
    if (dup_photo_id == 0) {
      dup_photo_id = photo_id;
    }
    uint64_t account_id = add_item->ad_dsp_info().creative().base().account_id();
    uint64_t unit_id = creative.unit_id();
    uint64_t cluster_id = add_item->item_tag().cluster_id();
    // uint64_t live_id = add_item->fans_top_live_info().live_info().id();
    uint64_t live_id = add_item->ad_dsp_info().live_info().id();
    uint64_t industry_id_v3 = add_item->ad_dsp_info().creative().base().industry_id_v3();
    uint64_t author_id = add_item->ad_dsp_info().live_info().author_info().id();
    uint64_t photo_author_id = add_item->ad_dsp_info().photo_info().author_info().id();
    uint64_t author_id_comb = (author_id > 0 ? author_id : photo_author_id);
    int ocpc_action_type = add_item->ad_dsp_info().unit().base().ocpc_action_type();
    int campaign_type = add_item->ad_dsp_info().campaign().base().type();
    uint64_t campaign_id = add_item->ad_dsp_info().campaign().base().id();
    std::string product_name = add_item->ad_dsp_info().advertiser_base().product_name();
    // TODO(yangwen): 只对 embedding_source 里开了 fill_bid_to_item_info 的生效, 先不对齐
    // teams/ad/ad_nn/embedding_producer/twin_towers_producer/producer.cc line 372
    // FillRealtimeInfoToItemInfo(add_item);
    if (embedding_config_ != nullptr && embedding_config_->biz_type_ == "i18n") {
      unit_id = add_item->ad_dsp_info().unit().base().id();
      LOG_EVERY_N(INFO, 100000)
          << "I18n config, unit id: " << unit_id << ", creative id: " << item_id;
    }

    if (filter_zero_photo_id && photo_id == 0) {
      LOG_EVERY_N(ERROR, 80000)
          << "Photo search, item id: " << item_id << ", its photo id is 0, skip it.";
      continue;
    }

    if (filter_zero_photo_id && dup_photo_id == 0) {
      LOG_EVERY_N(ERROR, 30000)
          << "Item id: " << item_id << ", its photo id is 0, skip it.";
      falcon::Inc("twin_towers_service.zero_photo_id", 1);
      continue;
    }
    int64_t auto_cpa_bid = 0;
    int type = -1;
    type = static_cast<int>(creative.live_creative_type());
    // 0: photo, 1: live
    if (cluster_type_ == ClusterType::MIXED_P2L_LIVE_OCPC) {
      if (type != 1 && type != 2) {
        LOG_EVERY_N(ERROR, 10000) << "Unsupported type: " << type;
        continue;
      }
      if ((type == 2 && photo_id == 0) || (type == 1 && live_id == 0)) {
        LOG_EVERY_N(ERROR, 3000)
            << "Invalid photo, creative id: " << item_id << ", type: " << type
            << ", photo id: " << photo_id << ", live id: " << live_id;
        continue;
      }
    }
    // 0:photo, 1: live 2: photo2live
    if (cluster_type_ == ClusterType::MIXED_PHOTO_LIVE ||
        cluster_type_ == ClusterType::MIXED_PHOTO_LIVE_OCPC) {
      if (type != 1 && type != 2 && type != 0) {
        LOG_EVERY_N(ERROR, 10000) << "Unsupported type for MIXED_PHOTO_LIVE: " << type;
        continue;
      }
      if ((type != 1 && photo_id == 0) || (type == 1 && live_id == 0)) {
        LOG_EVERY_N(ERROR, 3000)
            << "Invalid photo, creative id: " << item_id << ", type: " << type
            << ", photo id: " << photo_id << ", live id: " << live_id;
        continue;
      }
    }
    // only traversal common_info_attr for one time
    float threshold = 1e-10f;
    score_threshold_version_ = config_->GetInt("score_threshold_version", -1);
    enable_sid_ = config_->GetBoolean("enable_sid", false);
    bool set_threshold = true;
    int64_t merchant_product_id = 0, merchant_common_attr_value = -1,
            merchant_spu_id = -1;
    bool set_merchant_product_id = true, set_merchant_common_attr_value = true,
         set_merchant_spu_id = true;
    int64_t merchant_x7_category_level_1_id = -1;
    int64_t merchant_x7_category_level_2_id = -1;
    int64_t merchant_x7_category_level_3_id = -1;
    int64_t merchant_x7_category_level_4_id = -1;
    bool set_merchant_x7_category_id = true;
    int64_t cost_total = 0, spu_id_v2 = -1;
    bool set_cost_total = true, set_spu_id_v2 = true;
    float shop_star = 1e-10f, mmu_score = 1e-10f, mmu_hq_score = 1e-10f;
    bool set_shop_star = true, set_mmu_score = true, set_mmu_hq_score = true;
    int32_t quality_bucket_index = 0;
    bool set_quality_bucket_index = true;
    int64_t dpa_product_id = -1;
    for (const ::kuaishou::ad::CommonInfoAttr& attr :
         add_item->ad_dsp_info().common_info_attr()) {
      uint64_t name_value = attr.name_value();
      // set threshold
      if (set_threshold) {
        if (score_threshold_version_ == 1) {
          if (name_value ==
              ::kuaishou::ad::CommonInfoAttr_Name_SMART_MATCHING_CREATIVE_THRES_FEED) {
            threshold = attr.float_value();
            set_threshold = false;
          }
        } else if (score_threshold_version_ == 2) {
          if (name_value ==
              ::kuaishou::ad::CommonInfoAttr_Name_SMART_MATCHING_CREATIVE_THRES_NEBULA) {
            threshold = attr.float_value();
            set_threshold = false;
          }
        } else if (score_threshold_version_ == 3) {
          if (name_value ==
              ::kuaishou::ad::CommonInfoAttr_Name_SMART_MATCHING_CREATIVE_THRES_FEED2) {
            threshold = attr.float_value();
            set_threshold = false;
          }
        } else if (score_threshold_version_ == 21) {
          if (name_value ==
              ::kuaishou::ad::CommonInfoAttr_Name_SMART_MATCHING_CREATIVE_THRES_UNION) {
            threshold = attr.float_value();
            set_threshold = false;
          }
        } else if (score_threshold_version_ == 61) {
          if (name_value ==
              ::kuaishou::ad::CommonInfoAttr_Name_LOOKALIKE_CREATIVE_THRES_NEBULA) {
            threshold = attr.float_value();
            set_threshold = false;
          }
        } else {
          if (name_value ==
              ::kuaishou::ad::CommonInfoAttr_Name_SMART_MATCHING_CREATIVE_THRES_NEBULA2) {
            threshold = attr.float_value();
            set_threshold = false;
          }
        }
      }
      // set merchant value
      if (name_value == ::kuaishou::ad::CommonInfoAttr_Name_MERCHANT_ITEM_ID_NEW &&
          set_merchant_product_id) {
        merchant_product_id = attr.int_value();
        set_merchant_product_id = false;
      }
      if (name_value == x7_cateid_common_attr_nameid_ && set_merchant_common_attr_value) {
        merchant_common_attr_value = attr.int_value();
        set_merchant_common_attr_value = false;
      }
      if (name_value == 61081 && set_merchant_spu_id) {  // merchant spu id
        merchant_spu_id = attr.int_value();
        set_merchant_spu_id = false;
      }
      // set_merchant_x7_category_id
      if (set_merchant_x7_category_id) {
        if (name_value == 61086) {  // 商品 四级类目 （有缺失） int64
          merchant_x7_category_level_4_id = attr.int_value();
        }
        if (name_value == 61085) {  // 商品 三级类目 int64
          merchant_x7_category_level_3_id = attr.int_value();
        }
        if (name_value == 61084) {  // 商品 二级类目 int64
          merchant_x7_category_level_2_id = attr.int_value();
        }
        if (name_value == 61083) {  // 商品 一级类目 int64
          merchant_x7_category_level_1_id = attr.int_value();
        }
        if (merchant_x7_category_level_4_id > 0 ||
            (merchant_x7_category_level_4_id >= 0 &&
             merchant_x7_category_level_3_id >= 0 &&
             merchant_x7_category_level_2_id >= 0 &&
             merchant_x7_category_level_1_id >= 0)) {
          set_merchant_x7_category_id = false;
        }
      }
      // set cost_total
      if (set_cost_total) {
        if (attr.int_list_value().empty()) {
          set_cost_total = false;
        }
        if (set_cost_total) {
          for (auto val : attr.int_list_value()) {
            cost_total += val;
          }
        }
      }

      if (attr.name_value() == 18001 && set_shop_star) {
        shop_star = attr.float_value();
        set_shop_star = false;
      }
      if (attr.name_value() == 18015 && set_mmu_score) {
        mmu_score = attr.float_value();
        set_mmu_score = false;
      }
      if (attr.name_value() == 18018 && set_mmu_hq_score) {
        mmu_hq_score = attr.float_value();
        set_mmu_hq_score = false;
      }
      if (name_value ==
              ::kuaishou::ad::CommonInfoAttr_NameExtendTwo_ECOM_PHOTO_SPU_CLUSTER_V2 &&
          set_spu_id_v2) {
        spu_id_v2 = attr.int_value();
        set_spu_id_v2 = false;
      }
      if (name_value == ::kuaishou::ad::
                            CommonInfoAttr_NameExtendTwo_ECOM_PHOTO_MERCHANDISE_QUALITY &&
          set_quality_bucket_index) {
        quality_bucket_index = attr.int_value();
        set_quality_bucket_index = false;
      }
      if (enable_sid_) {
        if (name_value == 5105664) {
          dpa_product_id = attr.int_value();
        }
      } else {
        // set dpa product id
        if (name_value ==
            ::kuaishou::ad::CommonInfoAttr_NameExtendOne_SDPA_ALL_PRODUCT_ID) {
          dpa_product_id = attr.int_value();
        }
      }
    }
    int64_t hgm_spu_id = merchant_spu_id;
    if (hgm_spu_id <= 0 && author_id_comb > 0) {
      // default set spu_id to module 100000, 8xxxxx
      hgm_spu_id = 88000000 + author_id_comb % 1000000;
    }
    if (hgm_spu_id <= 0 && merchant_spu_id <= 0) {
      LOG_EVERY_N(INFO, 10000) << "author_id:" << author_id_comb
                               << ", merchant_spu_id:" << merchant_spu_id
                               << ", spu_id: " << hgm_spu_id;
    }
    int64_t merchant_x7_category_id = 0;
    if (merchant_x7_category_level_4_id > 0) {
      merchant_x7_category_id = merchant_x7_category_level_4_id;
    } else if (merchant_x7_category_level_3_id > 0) {
      merchant_x7_category_id = merchant_x7_category_level_3_id;
    } else if (merchant_x7_category_level_2_id > 0) {
      merchant_x7_category_id = merchant_x7_category_level_2_id;
    } else {
      merchant_x7_category_id = merchant_x7_category_level_1_id;
    }
    if (merchant_x7_category_id <= 0 && author_id_comb > 0) {
      // set default category_id
      merchant_x7_category_id = 880000 + author_id_comb % 3000;
    }

    LOG_EVERY_N(INFO, 1000000) << "author_id: " << author_id_comb
                               << ", category_id: " << merchant_x7_category_id
                               << ", spu_id: " << hgm_spu_id
                               << ", merchant_spu_id: " << merchant_spu_id;
    int64_t duration_ms =
        add_item->ad_dsp_info().photo_info().photo_attribute().duration_ms();
    int64_t show =
        add_item->ad_dsp_info().photo_info().author_info().explore_count().show();
    int64_t real_show =
        add_item->ad_dsp_info().photo_info().author_info().explore_count().real_show();
    int64_t click =
        add_item->ad_dsp_info().photo_info().author_info().explore_count().click();
    int64_t like =
        add_item->ad_dsp_info().photo_info().author_info().explore_count().like();
    int64_t follow =
        add_item->ad_dsp_info().photo_info().author_info().explore_count().follow();

    batch_item_ids.emplace_back(item_id);
    auto item_info = std::make_shared<ItemInfo>();
    item_info->set_product_name(product_name);
    item_info->set_dup_photo_id(dup_photo_id);
    item_info->set_account_id(account_id);
    item_info->set_unit_id(unit_id);
    item_info->set_score(0);
    item_info->set_threshold_score(threshold);
    item_info->set_cluster_id(cluster_id);
    item_info->set_live_id(live_id);
    item_info->set_photo_id(photo_id);
    item_info->set_industry_id_v3(industry_id_v3);
    // 0:photo, 1: live 2: photo2live
    if (type == 1 || type == 2) {  // 直投直播 & p2l
      item_info->set_author_id(author_id);
    } else {  // photo
      item_info->set_author_id(photo_author_id);
    }
    item_info->set_auto_cpa_bid(auto_cpa_bid);
    item_info->set_ocpc_action_type(ocpc_action_type);
    item_info->set_build_time(add_item->build_time());
    item_info->set_type(type);
    item_info->set_dpa_product_id(dpa_product_id);
    // Fill by the unit realtime info.
    item_info->set_city_product_id(0);
    item_info->set_merchant_product_id(merchant_product_id);
    item_info->set_campaign_type(campaign_type);
    item_info->set_campaign_id(campaign_id);
    item_info->set_merchant_common_attr_value(merchant_common_attr_value);
    item_info->set_merchant_spu_id(merchant_spu_id);
    item_info->set_spu_id_v2(spu_id_v2);
    item_info->set_quality_bucket_index(quality_bucket_index);
    item_info->set_merchant_x7_category_id(merchant_x7_category_id);
    item_info->set_hgm_spu_id_v2(hgm_spu_id);
    item_info->set_duration_ms(duration_ms);
    item_info->set_show(show);
    item_info->set_real_show(real_show);
    item_info->set_click(click);
    item_info->set_like(like);
    item_info->set_follow(follow);
    item_info->set_shop_star(shop_star);
    item_info->set_mmu_score(mmu_score);
    item_info->set_mmu_hq_score(mmu_hq_score);
    item_info->set_cost_total(cost_total);
    ks::ad_nn::ResetBitsIndex(item_info->mutable_target_index());
    ks::ad_nn::ResetBitsIndexI18N(item_info->mutable_target_index_i18n());

    if (embedding_config_ != nullptr) {
      BuildCrowdPackBitsIndex(*item_info, embedding_config_->prophet_config_.crowd_pack,
                              item_info->mutable_target_index());
    }

    updating_ids_.InsertOrUpdate(item_id, item_info);
  }
  LOG_EVERY_N(INFO, 8000) << "Extract incr cached items feature, item count: "
                          << batch_item_ids.size()
                          << ", filter zero photo id: " << filter_zero_photo_id;
}

void ProduceEmbeddingCore::GetX7Id() {
  x7_cateid_common_attr_nameid_ =
      config_->GetInt("merchant_common_attr_name_id", 61085);
  LOG(INFO) << "get x7 id: " << x7_cateid_common_attr_nameid_;
}

uint32_t ProduceEmbeddingCore::GetEmbeddingDim(uint32_t field) {
  for (const auto &[shard, infos] : emp_shard_infos_) {
    auto iter = infos.find(field);
    if (iter != infos.end()) {
      if (iter->second.op == ks::ad_nn::EmbeddingOperation::CONCAT) {
        return iter->second.concat_len;
      }
      return iter->second.embedding_len;
    }
  }
  LOG(FATAL) << "can not find embedding info in emp_service, field: " << field;
  return 0;
}

void ProduceEmbeddingCore::FetchFeatureInfo(const std::string &feature_path) {
  feature_file_info_ = std::make_shared<FeatureFileInfo>();
  if (FeatureFileInfo::LoadFeatureFile(feature_path, feature_file_info_.get(),
                                       true, false) != 0) {
    LOG(FATAL) << "load feature file failed: " << feature_path;
    return;
  }
  // feature extractor 对齐 base 逻辑:
  // teams/ad/ad_nn/model/model_manager.cc line 455~474
  bool open_nohash_embedding =
        config_->GetBoolean("enable_nohash_embedding", false);
  std::string model_src = config_->GetString("model_src", "local");
  ExtractType ext_type =
        static_cast<ExtractType>(config_->GetInt("extract_type", 0));
  FeatureOrgLayout layout = static_cast<FeatureOrgLayout>(config_->GetInt("in_fea_org_layout", 1));
  fea_ext_bundle_ = std::make_unique<AdFeatureExtractorBundle>(
          feature_file_info_, ext_type, layout,
          open_nohash_embedding || IsBtqModel(model_src));
  if (fea_ext_bundle_ == nullptr) {
    LOG(FATAL) << "Create feature extractor bundle failed";
  }
  fea_ext_bundle_->SetZeroAdlogTime(
      config_->GetBoolean("zero_adlog_time", false));
  fea_ext_bundle_->SetHashByForce(
      config_->GetBoolean("hash_by_force", false));

  feature_file_info_->sparse_ret_indices =
      fea_ext_bundle_->sparse_ret_indices();
  feature_file_info_->dense_ret_indices =
      fea_ext_bundle_->dense_ret_indices();
  feature_file_info_->layout = layout;

  // feature_struct_info_ 对齐 base 逻辑:
  // teams/ad/ad_nn/model/tf_model.cc line 454
  feature_struct_info_.dense_field_sizes = feature_file_info_->dense_slots_size;
  // teams/ad/ad_nn/model/tf_model.cc line 308~309
  FeatureStructInfo::GetFeatureInfo(*feature_file_info_, &feature_struct_info_,
                                    !mio_model_ & enable_continuous_check_);

  // teams/ad/ad_nn/model/tf_model.cc line 2813~2820
  auto emp_config = config_->Get("emp_service");
  if (emp_config == nullptr) {
    CL_LOG(FATAL) << "no `emp_service` found for model: ";
  }
  for (const auto &[shard, config] : emp_config->objects()) {
    if (config->IsArray() &&
        !ks::ad_nn::GetShardFieldConfig(*emp_config, shard, &emp_shard_infos_[shard])) {
      LOG(FATAL) << "parse shard config failed from `emp_service`: " << shard;
    }
  }

  int field_count = feature_file_info_->slots_count;
  std::vector<uint32_t> embedding_vector_lens(field_count, 0);
  for (int i = 0; i < field_count; ++i) {
    embedding_vector_lens[i] = GetEmbeddingDim(i);
  }
  feature_struct_info_.UpdateSparseEmbeddingLength(false,
                                                   embedding_vector_lens);
  LOG(INFO) << "Feature info: " << feature_struct_info_.ToString();
  if (field_count > 0) {
    LOG(INFO) << "embedding vector lens size: " << embedding_vector_lens.size()
              << ", sparse field number: " << feature_file_info_->slots.size()
              << ", first length: " << embedding_vector_lens[0]
              << ", total length of all sparse item features: "
              << feature_struct_info_.sparse_item_features_length;
  } else {
    LOG(INFO) << "embedding vector lens size: " << embedding_vector_lens.size()
              << ", sparse field number: " << feature_file_info_->slots.size()
              << ", total length of all sparse item features: "
              << feature_struct_info_.sparse_item_features_length;
  }
}

void ProduceEmbeddingCore::StartFeatureManager() {
  feature_manager_ = std::make_unique<FeatureManager>();
  // TODO(yangwen): 对齐 base 逻辑
  bool is_prerank = false;

  StoreOption store_option;
  ReaderOption reader_option;
  ks::ad_nn::GetFeatureStoreOptions(&reader_option, &store_option);
  reader_option.full_source_name =
      config_->GetString("btq_full_topic", "ad_predict_item_full_v2");
  reader_option.incr_source_name =
      config_->GetString("btq_incr_topic", "ad_predict_item_info_v2");
  reader_option.forbidden_backtrack = config_->GetBoolean("forbidden_backtrack", false);

  bool disable_filter_for_prerank =
      config_->GetBoolean("disable_filter_for_prerank", true);
  if (is_prerank) {
    reader_option.disable_filter = disable_filter_for_prerank;
  } else {
    reader_option.disable_filter = false;
  }
  store_option.type = StorageType::ST_EXTRACT_FEATURE;
  GetX7Id();
  reader_option.is_producer = true;
  // TODO(yangwen): 打通了再来补监控
  // ks::ad_nn::monitor::StartupMonitor::Instance().StartTask("LoadItem", {});

  // TODO(yangwen): 这里逻辑再确认下是否需要
  // if (trigger_source_config_->stat_instance_num_ > 0) {
  //   int stat_fea_value_mode =
  //       ks::ad_nn::SetStatFeatureMode(trigger_source_config_->stat_instance_num_);
  //   if (stat_fea_value_mode == 1) {
  //     feature_stat_ = std::make_unique<ks::ad_nn::FeatureStat>();
  //     feature_stat_->SetStatFeaValueMode(ks::ad_nn::GetCmdOfDocker(),
  //                                        stat_fea_value_mode);
  //     feature_stat_->SetStatFeaValueFreq(trigger_source_config_->stat_fea_value_freq_);
  //     twin_towers_model_->GetExtractor()->SetFeatureStat(feature_stat_.get());
  //   }
  // }

  // Active feature store.
  active_feature_ = feature_manager_->RegisterFeatureStore<ExtractFeatureStore>(
      "creative", store_option);
  // Start readers.
  auto active_creative_reader =
      feature_manager_->RegisterReader("creative", reader_option);

  active_feature_->Subscribe(active_creative_reader);
  reader_option.incr_source_name = config_->GetString("second_incr_topic", "");

  // Protobuf extractor.
  active_feature_->InitializeCaches(feature_struct_info_,
                                    fea_ext_bundle_.get());
  active_feature_->SetCustomDefineCallback(
      std::bind(&ProduceEmbeddingCore::AcquireFeatureInfo, this, std::placeholders::_1,
                std::placeholders::_2));

  active_feature_->SetEvictCallback([this](const uint64_t& key) {
    updating_ids_.Delete({key});
  });

  if (!feature_manager_->Start()) {
    LOG(FATAL) << "Failed to start feature manager.";
    return;
  }
  feature_manager_->WaitUntilReady();
  // TODO(yangwen): 打通了再补监控
  // ks::ad_nn::monitor::StartupMonitor::Instance().MarkTaskAsFinished("LoadItem", {});
  LOG(INFO) << "Loaded item count for full version: " << active_feature_->Size();
  LOG(INFO) << "Loaded item count: " << active_feature_->Size();

  LOG(INFO) << "Start feature manager success.";
}

void ProduceEmbeddingCore::StartFeasibleSetManager() {
  auto realtime_btq_configs = ks::ad_nn::TopicsFromKconf();
  LOG(INFO) << "Try to register " << realtime_btq_configs->size()
            << " btq realtime topics";

  FeasibleSetOptions options;
  for (auto&& topic_config : *realtime_btq_configs) {
    options.name = topic_config.first;
    options.topic = topic_config.second.topic;
    options.backtrace_offset = topic_config.second.backtrace_offset;
    options.dump_unit_to_disk = topic_config.second.dump_unit_to_disk;
    options.enable_monitor = topic_config.second.enable_monitor;
    options.cluster_type = topic_config.second.cluster_type;
    options.mock_filename = topic_config.second.mock_filename;
    FeasibleSetManager::Instance()->Register(options);
  }
}

void ProduceEmbeddingCore::InitEmbedding() {
  if (model_config_ != nullptr) {
    for (auto&& output : model_config_->embedding_outputs_) {
      auto& name = output.first;
      auto& spec = embedding_options_[name];
      spec.length = output.second.dims[1];
      spec.max_capacity = model_option_->lru_capacity;
      spec.buf_spec.dtype = tensorflow::DT_HALF;
      if (!embedding_config_->store_by_half_ ||
          embedding_config_->store_by_float_.count(name) != 0) {
        spec.buf_spec.dtype = tensorflow::DT_FLOAT;
        LOG(INFO) << "Store by float op: " << name;
      }
    }
  } else {
    auto& spec = embedding_options_[kDefaultEmbeddingName];
    spec.length = vector_infos_->item_vector_length;
    spec.max_capacity = model_option_->lru_capacity;
    spec.buf_spec.is_pinned_memory = false;
    spec.buf_spec.dtype = tensorflow::DT_HALF;
    if (!embedding_config_->store_by_half_) {
      spec.buf_spec.dtype = tensorflow::DT_FLOAT;
      LOG(INFO) << "Store by float op: " << kDefaultEmbeddingName;
    }
  }
  embedding_data_.Initialize(embedding_options_);

  if (cluster_search_) {
    for (auto&& kv : embedding_options_) {
      kv.second.max_capacity = cluster_search_config_->max_cluster_count;
    }
    cluster_embedding_data_.Initialize(embedding_options_);
  }
}

const absl::flat_hash_map<uint64_t, UnitSpec*>& ProduceEmbeddingCore::GetUnitSpec(
    const std::string& set_name, bool wait_for_first_version, int wait_seconds) {
  static absl::flat_hash_map<uint64_t, UnitSpec*> empty_set;
  auto realtime_info_list = FeasibleSetManager::Instance()->Get(set_name);
  if (realtime_info_list == nullptr) {
    LOG_FIRST_N(ERROR, 3) << "Feasible set `" << set_name << "` is not registered.";
    return empty_set;
  }
  if (wait_for_first_version) {
    realtime_info_list->WaitForFirstVersion(wait_seconds);
  }
  const auto& unit_set = realtime_info_list->GetUnitSet();
  if (unit_set.empty()) {
    LOG_EVERY_N(ERROR, 10) << "Unit set is empty.";
    return empty_set;
  }
  return unit_set;
}

bool ProduceEmbeddingCore::PrepareCalcItemDnn(int slot_idx) {
  if (!reuse_unchanged_feature_emb_) {
    embedding_data_.Reset();
    if (cluster_search_) {
      cluster_embedding_data_.Reset();
    }
  }

  snapshot_.clear();
  updating_ids_.Snapshot(&snapshot_);
  std::vector<uint64_t>* erased_list_ptr = nullptr;

  statistical_filter_.FilterCreativeByList(&snapshot_, erased_list_ptr);
  statistical_filter_.FilterCreativeByCount(&snapshot_, erased_list_ptr);
  statistical_filter_.FilterCreativeByRealtimeInfo(&snapshot_, erased_list_ptr);
  statistical_filter_.UnivFilterCreativeByRealtimeInfo(&snapshot_, erased_list_ptr);
  statistical_filter_.FilterCreativeByColdStartInfo(&snapshot_, erased_list_ptr);
  statistical_filter_.FilterCreativeByNewColdStartInfo(&snapshot_, erased_list_ptr);
  statistical_filter_.FilterCreativeByInnerPhotoColdStart(&snapshot_, erased_list_ptr);
  statistical_filter_.FilterCreativeByInnerLiveColdStart(&snapshot_, erased_list_ptr);

  try_to_calculate_dnn_ids_.clear();
  item_id_to_cluster_id_.clear();

  BidType bid_type = static_cast<BidType>(vector_infos_->bid_type);
  int miss_bid = 0;
  int zero_bid_cnt = 0;
  int64_t bid_max = std::numeric_limits<int64_t>::min();
  int64_t bid_min = std::numeric_limits<int64_t>::max();
  int64_t bid_average = 0, bid_value = 0;
  int item_count = snapshot_.size();
  std::shared_ptr<base::Json> target_map;
  if (target_config_ != nullptr && !target_config_->i18n_target_map_kconf_.empty()) {
    target_map = ks::ad_nn::PredictServiceKconfUtil::GetKconfValue(
        target_config_->i18n_target_map_kconf_, false);
    if (target_map == nullptr) {
      LOG(ERROR) << "Get target map failed, path: "
                 << target_config_->i18n_target_map_kconf_;
    }
  }
  const auto& unit_spec = GetUnitSpec("realtime_info", true, 600);
  if (enable_bid_ && unit_spec.empty()) {
    LOG(ERROR) << "Empty unit spec, slot idx: " << slot_idx;
    return false;
  }
  const absl::flat_hash_map<uint64_t, FilterSpec*>* creative_set = nullptr;
  auto realtime_info_list = FeasibleSetManager::Instance()->Get("realtime_info");
  if (realtime_info_list == nullptr) {
    LOG(INFO) << "Realtime info list is not found.";
  } else {
    auto& c_set = realtime_info_list->GetCreativeSet();
    if (!c_set.empty()) {
      falcon::Stat("twin_towers_service.creative_set_count", c_set.size());
      LOG(INFO) << "Creative set count: " << c_set.size();
      creative_set = &c_set;
    }
  }

  int creative_miss_count = 0, override_dup_photo_cnt = 0;
  // Fill unit info.
  for (auto item_iter = snapshot_.begin(); item_iter != snapshot_.end(); item_iter++) {
    auto creative_id = item_iter->first;
    try_to_calculate_dnn_ids_.emplace_back(creative_id);
    const kuaishou::retr::FilterSpec* creative_spec = nullptr;
    if (creative_set != nullptr) {
      auto it = creative_set->find(creative_id);
      if (it == creative_set->end()) {
        ++creative_miss_count;
      } else {
        if (embedding_config_->dup_photo_id_type_ != 0) {
          uint64_t override_dup_photo = embedding_config_->dup_photo_id_type_ == 1
                                            ? it->second->dup_photo_id_b()
                                            : it->second->dup_photo_id_c();
          if (override_dup_photo != 0) {
            item_iter->second->set_dup_photo_id(override_dup_photo);
            ++override_dup_photo_cnt;
          }
        }
        creative_spec = it->second;
      }
    }

    if (unit_spec.empty()) {
      LOG_FIRST_N(INFO, 3) << "Not found realtime info, just skip assign bid";
      item_id_to_cluster_id_.emplace(
          creative_id, GetClusterId(cluster_type_, *item_iter->second, creative_id));
      continue;
    }
    uint64_t unit_id = item_iter->second->unit_id();
    auto it = unit_spec.find(unit_id);
    if (it != unit_spec.end()) {
      bid_value = GetBid(bid_type, it->second, creative_spec);
      item_iter->second->set_account_type(it->second->account_type());
      item_iter->second->set_bid_strategy(it->second->bid_strategy());
      item_iter->second->set_speed_type(it->second->speed_type());
      item_iter->second->set_auto_cpa_bid(bid_value);
      item_iter->second->set_bid(bid_value);
      if (bid_value == 0) {
        zero_bid_cnt++;
      }
      item_iter->second->set_deep_conversion_type(it->second->deep_conversion_type());
      if (it->second->bid_type() == kuaishou::ad::AdEnum_BidType::AdEnum_BidType_MCB) {
        item_iter->second->set_bid_type(kuaishou::ad::AdEnum_BidType::AdEnum_BidType_MCB);
      } else {
        item_iter->second->set_bid_type(0);
      }
      item_iter->second->set_city_product_id(it->second->city_product_id());
      BuildBitsIndex(it->second, item_iter->second->mutable_target_index());
      if (target_map != nullptr) {
        ks::ad_nn::ResetBitsIndexI18N(item_iter->second->mutable_target_index_i18n());
        BuildBitsIndex(it->second, target_map.get(),
                       item_iter->second->mutable_target_index_i18n());
      }
      bid_max = std::max<int64_t>(bid_value, bid_max);
      bid_min = std::min<int64_t>(bid_value, bid_min);
      bid_average += bid_value;
    } else {
      ++miss_bid;
      LOG_EVERY_N(INFO, 60000) << "Bid is not found, unit id: " << unit_id
                               << ", item id: " << creative_id;
      falcon::Inc("twin_towers_service.creative_bid_not_found", 1);
    }
    item_id_to_cluster_id_.emplace(
        creative_id, GetClusterId(cluster_type_, *item_iter->second, creative_id));
  }
  bid_average /= (item_count - miss_bid - zero_bid_cnt + 1);
  int bid_miss_rate = (miss_bid * 1000000) / (item_count + 1);
  falcon::Stat("twin_towers_service.bid_miss_rate", bid_miss_rate);
  falcon::Stat("twin_towers_service.bid_max", bid_max);
  falcon::Stat("twin_towers_service.bid_min", bid_min);
  falcon::Stat("twin_towers_service.bid_average", bid_average);
  falcon::Stat("twin_towers_service.zero_bid_cnt", zero_bid_cnt);
  falcon::Stat("twin_towers_service.miss_bid_count", miss_bid);
  LOG(INFO) << "Unit spec size: " << unit_spec.size() << ", miss bid: " << miss_bid
            << ", item size: " << item_count << ", zero bid count " << zero_bid_cnt
            << ", creative miss cnt: " << creative_miss_count
            << ", override dup photo: " << override_dup_photo_cnt;
  StatSnapshot();
  return true;
}

void ProduceEmbeddingCore::StatSnapshot() {
  stat_photo_id_.clear();
  stat_unit_id_.clear();
  stat_product_id_.clear();
  stat_dup_photo_id_.clear();
  // Combine id.
  stat_photo_ocpc_id_.clear();
  stat_photo_ocpc_bidtype_id_.clear();
  stat_dup_photo_product_ocpc_bidtype_id_.clear();
  stat_spu_id_v2_.clear();
  stat_spu_id_bucket_.clear();

  stat_photo_id_[0] = 0;
  stat_unit_id_[0] = 0;
  stat_product_id_[0] = 0;
  stat_dup_photo_id_[0] = 0;
  stat_photo_ocpc_id_[0] = 0;
  stat_photo_ocpc_bidtype_id_[0] = 0;
  stat_dup_photo_product_ocpc_bidtype_id_[0] = 0;

  auto add_counter = [](const ClusterType& type, const ItemInfo& info, uint64 cid,
                        absl::flat_hash_map<uint64_t, int>* counter_map) {
    auto id = GetClusterId(type, info, cid);
    auto it = counter_map->find(id);
    if (it == counter_map->end()) {
      counter_map->emplace(id, 1);
    } else {
      it->second++;
    }
  };
  for (auto& kv : snapshot_) {
    auto& info_ptr = kv.second;
    auto cid = kv.first;
    add_counter(ClusterType::PHOTO, *info_ptr, cid, &stat_photo_id_);
    add_counter(ClusterType::UNIT, *info_ptr, cid, &stat_unit_id_);
    add_counter(ClusterType::CITY_PRODUCT_ID, *info_ptr, cid, &stat_product_id_);
    add_counter(ClusterType::DUP_PHOTO_ID, *info_ptr, cid, &stat_dup_photo_id_);
    add_counter(ClusterType::COMBINE_PHOTO_OCPX, *info_ptr, cid, &stat_photo_ocpc_id_);
    add_counter(ClusterType::COMBINE_PHOTO_OCPC_BIDTYPE, *info_ptr, cid,
                &stat_photo_ocpc_bidtype_id_);
    add_counter(ClusterType::COMBINE_DUP_PHOTO_PRODUCT_OCPX_BID_TYPE, *info_ptr, cid,
                &stat_dup_photo_product_ocpc_bidtype_id_);
    add_counter(ClusterType::SPU_ID_V2, *info_ptr, cid, &stat_spu_id_v2_);
    add_counter(ClusterType::QUALITY_BUCKET_INDEX, *info_ptr, cid, &stat_spu_id_bucket_);
  }
  LOG(INFO) << "Photo id count: " << stat_photo_id_.size()
            << ", zero photo id count: " << stat_photo_id_[0]
            << ", unit id count: " << stat_unit_id_.size()
            << ", zero unit id count: " << stat_unit_id_[0]
            << ", product id count: " << stat_product_id_.size()
            << ", spu id count: " << stat_spu_id_v2_.size()
            << ", zero product id count: " << stat_product_id_[0]
            << ", dup photo id count: " << stat_dup_photo_id_.size()
            << ", zero dup photo id count: " << stat_dup_photo_id_[0]
            << ", photo ocpc count: " << stat_photo_ocpc_id_.size()
            << ", zero photo ocpc id count: " << stat_dup_photo_id_[0]
            << ", photo ocpc bidtype id count: " << stat_photo_ocpc_bidtype_id_.size()
            << ", zero photo ocpc bidtype id: " << stat_photo_ocpc_bidtype_id_[0]
            << ", dup photo product ocpc bidtype id count: "
            << stat_dup_photo_product_ocpc_bidtype_id_.size()
            << ", zero dup photo product ocpc bidtype id: "
            << stat_dup_photo_product_ocpc_bidtype_id_[0]
            << ", creative count: " << snapshot_.size() << ", quality bucket: "
            << ks::ad_nn::MapToString<absl::flat_hash_map<uint64_t, int>>(
                   stat_spu_id_bucket_);
  falcon::Set("twin_towers_service.photo_id_count", stat_photo_id_.size(),
              falcon::kNonAdditiveGauge);
  falcon::Set("twin_towers_service.unit_id_count", stat_unit_id_.size(),
              falcon::kNonAdditiveGauge);
  falcon::Set("twin_towers_service.product_id_count", stat_product_id_.size(),
              falcon::kNonAdditiveGauge);
  falcon::Set("twin_towers_service.zero_photo_id_count", stat_photo_id_[0],
              falcon::kNonAdditiveGauge);
  falcon::Set("twin_towers_service.zero_unit_id_count", stat_unit_id_[0],
              falcon::kNonAdditiveGauge);
}

}  // namespace platform
}  // namespace ks
