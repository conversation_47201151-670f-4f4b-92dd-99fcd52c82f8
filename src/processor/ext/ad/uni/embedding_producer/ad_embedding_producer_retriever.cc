#include "dragon/src/processor/ext/ad/uni/embedding_producer/ad_embedding_producer_retriever.h"

#include <string>

#include "dragon/src/processor/ext/ad/uni/embedding_producer/ad_embedding_producer.h"
#include "teams/reco-arch/embedding_manager/embedding_data/parameters.h"
#include "teams/reco-arch/embedding_manager/tools/common_tools.h"
#include "teams/ad/ad_nn/monitor/monitor.h"
#include "teams/ad/ad_nn/const_config/const_config.h"

namespace ks {
namespace platform {

using reco_arch::emb_manager::Parameters;
using reco_arch::tools::VerifyCode;

bool AdEmbeddingProducerRetriever::InitProcessor() {
  LOG(INFO) << "ad_embedding_producer_retriever init succeeds";
  return true;
}

void AdEmbeddingProducerRetriever::Retrieve(AddibleRecoContextInterface *context) {
  int next_version = producer_item_version_ + 1;
  int slot_idx = next_version % 2;
  bool rt = ProduceEmbeddingCore::Singleton().PrepareCalcItemDnn(slot_idx);
  if (!rt) {
    CL_LOG(ERROR) << "Failed to prepare item info.";
    return;
  }
  auto item_ids = ProduceEmbeddingCore::Singleton().GetItemIds();
  for (auto item_id : item_ids) {
    context->AddCommonRecoResult(item_id, 0, 0.0, 0);
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdEmbeddingProducerRetriever, AdEmbeddingProducerRetriever)
}  // namespace platform
}  // namespace ks
