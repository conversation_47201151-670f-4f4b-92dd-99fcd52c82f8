#include "dragon/src/processor/ext/ad/gsu/enricher/ad_live_v1_livev4_match_dense_enricher.h"

#include <algorithm>
#include <cmath>
#include <ctime>
#include <iostream>
#include <limits>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "base/thread/thread_pool.h"
#include "kconf/kconf.h"
#include "learning/kuiba/parameter/parameter.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"

namespace ks {
namespace platform {

bool AdLiveV1Livev4MatchDenseEnricher::InitProcessor() {
  colossus_timestamp_attr_ = config()->GetString("colossus_timestamp_attr", "timestamp_list");
  colossus_author_id_attr_ = config()->GetString("colossus_author_id_attr", "author_id_list");
  colossus_play_time_attr_ = config()->GetString("colossus_play_time_attr", "play_time_list");
  colossus_cluster_id_attr_ = config()->GetString("colossus_cluster_id_attr", "cluster_id_list");
  colossus_label_attr_ = config()->GetString("colossus_label_attr", "label_list");
  colossus_audience_count_attr_ = config()->GetString("colossus_audience_count_attr", "audience_count_list");
  colossus_order_price_attr_ = config()->GetString("colossus_order_price_attr", "order_price_list");
  colossus_auto_play_time_attr_ = config()->GetString("colossus_auto_play_time_attr", "auto_play_time_list");
  target_aids_attr_ = config()->GetString("target_aids_attr", "aid");

  context_match_dense_attr_ =
      config()->GetString("context_match_dense_attr", "ad_liveitemv4_context_match_dense");
  target_match_dense_attr_ =
      config()->GetString("target_match_dense_attr", "ad_liveitemv4_target_match_dense");

  limit_num_ = config()->GetInt("limit_num", 2000);
  min_seconds_ago_ = config()->GetInt("min_seconds_ago", 60);
  max_seconds_ago_ = config()->GetInt("max_seconds_ago", 5 * 365 * 24 * 3600);
  only_merchant_live_attr_ = config()->GetBoolean("only_merchant_live_attr", false);

  CL_LOG_EVERY_N(INFO, 1000) << "AdLiveV1Livev4MatchDenseEnricher: "
                             << ", colossus_timestamp_attr_: " << colossus_timestamp_attr_
                             << ", colossus_author_id_attr_: " << colossus_author_id_attr_
                             << ", colossus_play_time_attr_: " << colossus_play_time_attr_
                             << ", colossus_cluster_id_attr_: " << colossus_cluster_id_attr_
                             << ", colossus_audience_count_attr_: " << colossus_audience_count_attr_
                             << ", colossus_order_price_attr_: " << colossus_order_price_attr_
                             << ", limit_user_num_: " << limit_num_
                             << ", min_seconds_ago_: " << min_seconds_ago_
                             << ", max_seconds_ago_: " << max_seconds_ago_
                             << ", target_aids_attr: " << target_aids_attr_;
  return true;
}

void AdLiveV1Livev4MatchDenseEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                              RecoResultConstIter end) {
  auto timestamps = context->GetIntListCommonAttr(colossus_timestamp_attr_);
  auto author_ids = context->GetIntListCommonAttr(colossus_author_id_attr_);
  auto play_times = context->GetIntListCommonAttr(colossus_play_time_attr_);
  auto cluster_ids = context->GetIntListCommonAttr(colossus_cluster_id_attr_);
  auto labels = context->GetIntListCommonAttr(colossus_label_attr_);
  auto audience_counts = context->GetIntListCommonAttr(colossus_audience_count_attr_);
  auto order_prices = context->GetIntListCommonAttr(colossus_order_price_attr_);
  auto auto_play_times = context->GetIntListCommonAttr(colossus_auto_play_time_attr_);

  if (!timestamps || !author_ids || !play_times || !cluster_ids || !labels || !audience_counts ||
      !order_prices || !auto_play_times || timestamps->empty()) {
    FB_LOG_EVERY_MS(WARNING, 1000) << "colossus no data";
    return;
  }

  int colossus_length = timestamps->size();
  if (timestamps->size() != colossus_length || author_ids->size() != colossus_length ||
      play_times->size() != colossus_length || cluster_ids->size() != colossus_length ||
      labels->size() != colossus_length || audience_counts->size() != colossus_length ||
      order_prices->size() != colossus_length) {
    FB_LOG_EVERY_MS(WARNING, 1000) << "colossus length invalid: " << colossus_length
                                   << ", timestamps->size()=" << timestamps->size();
    return;
  }

  std::unordered_set<uint32_t> target_aid_set;
  std::vector<uint32_t> target_aid_list;  // 序列中的 aid 是 32 位，进行统一
  auto item_attr_accessor = context->GetItemAttrAccessor(target_aids_attr_);

  for (auto it = begin; it != end; ++it) {
    if (context->HasItemAttr(*it, item_attr_accessor)) {
      auto target_aid = (uint32_t)(*context->GetIntItemAttr(*it, item_attr_accessor));
      target_aid_list.emplace_back(target_aid);
      target_aid_set.insert(target_aid);
    } else {
      target_aid_list.emplace_back(0);
    }
  }

  thread_local folly::F14FastMap<int, std::vector<double>> target_dense_result;
  target_dense_result.clear();

  int64_t total_exposure = 0;                // 总曝光次数
  int64_t first_timestamp = -1;              // 最新时间戳（倒序第一个）
  int64_t last_timestamp = -1;               // 最旧时间戳（倒序最后一个）
  int64_t total_play_time = 0;               // 总播放时长
  int64_t max_play_time = 0;                 // 最大播放时长
  int64_t total_auto_play_time = 0;          // 自动播放总时长
  int positive_feedback_count = 0;           // 正反馈计数
  int high_engagement_count = 0;             // 高互动计数
  const int HIGH_ENGAGEMENT_THRESHOLD = 30;  // 高互动阈值（秒）

  enum TimeBucket {
    LAST_1H = 0,    // 0-1 小时
    LAST_6H = 1,    // 1-6 小时
    LAST_24H = 2,   // 6-24 小时
    LAST_3D = 3,    // 1-3 天
    LAST_7D = 4,    // 3-7 天
    LAST_30D = 5,   // 7-30 天
    HISTORICAL = 6  // 30 天以上
  };

  struct BucketStats {
    int exposure_count = 0;            // 曝光次数
    int64_t total_play_time = 0;       // 总播放时长
    int positive_feedback_count = 0;   // 正反馈次数
    int64_t total_auto_play_time = 0;  // 自动播放总时长
  };

  auto log_transform = [](double x) {
    return std::log1p(x);  // log(1+x)
  };

  auto logit_transform = [](double p, double epsilon = 1e-6) {
    p = std::clamp(p, epsilon, 1.0 - epsilon);  // clip
    return std::log(p / (1.0 - p));
  };

  int seq_cnt = 0;
  int64_t request_time = context->GetRequestTime() / 1000;
  std::array<BucketStats, 7> time_buckets;                       // 7 个时间桶
  folly::F14FastMap<uint32_t, std::vector<int>> aid_to_indices;  // aid 索引
  // 倒序取, 最新的数据优先
  for (int i = colossus_length - 1; i >= 0; --i) {
    // 过滤超出最大长度
    if (++seq_cnt > limit_num_) {
      break;
    }

    int64_t timestamp = timestamps->at(i);
    int64_t play_time = play_times->at(i);
    int64_t auto_play_time = auto_play_times->at(i);
    int64_t label = labels->at(i);

    // 近一分钟的交互不用，防穿越
    if (timestamp > request_time - min_seconds_ago_) {
      continue;
    }

    // 过滤非电商样本
    if (only_merchant_live_attr_) {
      if (((label >> 8) & 1) == 0) {
        continue;
      }
    }

    // 记录时间戳范围（用于计算平均曝光间隔）
    if (first_timestamp == -1) {
      first_timestamp = timestamp;  // 倒序第一个（最新）
    }
    last_timestamp = timestamp;  // 倒序最后一个（最旧）

    // 播放时长统计
    total_play_time += play_time;
    if (play_time > max_play_time) {
      max_play_time = play_time;
    }

    // 自动播放统计
    total_auto_play_time += auto_play_time;

    // 互动质量统计
    if (play_time > 0) {
      positive_feedback_count++;
    }
    if (play_time > HIGH_ENGAGEMENT_THRESHOLD) {
      high_engagement_count++;
    }
    total_exposure++;

    int64_t time_diff = request_time - timestamp;

    // 确定时间桶索引
    int bucket_idx = HISTORICAL;
    if (time_diff < 3600) {  // <1 小时
      bucket_idx = LAST_1H;
    } else if (time_diff < 6 * 3600) {  // 1-6 小时
      bucket_idx = LAST_6H;
    } else if (time_diff < 24 * 3600) {  // 6-24 小时
      bucket_idx = LAST_24H;
    } else if (time_diff < 3 * 24 * 3600) {  // 1-3 天
      bucket_idx = LAST_3D;
    } else if (time_diff < 7 * 24 * 3600) {  // 3-7 天
      bucket_idx = LAST_7D;
    } else if (time_diff < 30 * 24 * 3600) {  // 7-30 天
      bucket_idx = LAST_30D;
    }
    BucketStats &bucket = time_buckets[bucket_idx];

    // 更新桶统计
    bucket.exposure_count++;
    bucket.total_play_time += play_time;
    if (play_time > 0) {
      bucket.positive_feedback_count++;
    }
    bucket.total_auto_play_time += auto_play_time;

    // 更新 aid 索引
    for (int i = 0; i < colossus_length; i++) {
      uint32_t aid = author_ids->at(i);
      if (target_aid_set.find(aid) != target_aid_set.end()) {
        aid_to_indices[aid].push_back(i);
      }
    }
  }

  // ========== 计算全局统计特征 ==========
  double avg_exposure_interval = 0.0;
  if (total_exposure > 1 && first_timestamp != -1 && last_timestamp != -1) {
    int64_t time_span = first_timestamp - last_timestamp;
    avg_exposure_interval = static_cast<double>(time_span) / (total_exposure - 1) / 3600.0;
  }
  double avg_play_time = 0.0;
  if (total_exposure > 0) {
    avg_play_time = static_cast<double>(total_play_time) / total_exposure;
  }
  double auto_play_ratio = 0.0;
  if (total_play_time > 0) {
    auto_play_ratio = static_cast<double>(total_auto_play_time) / total_play_time;
  }
  double positive_feedback_rate = 0.0;
  double high_engagement_rate = 0.0;
  if (total_exposure > 0) {
    positive_feedback_rate = static_cast<double>(positive_feedback_count) / total_exposure;
    high_engagement_rate = static_cast<double>(high_engagement_count) / total_exposure;
  }
  double last_interaction_gap = last_timestamp > 0 ? (request_time - last_timestamp) / 3600.0 : 0.0;
  CL_LOG_EVERY_N(INFO, 1000) << "Context dense complete: "
                             << ", total_exposure: " << total_exposure
                             << ", total_play_time: " << total_play_time
                             << ", positive_feedback_count: " << positive_feedback_count;
  std::vector<double> dense_features;

  // 构建全局特征向量（10 维）
  dense_features.push_back(static_cast<double>(log_transform(total_exposure)));        // 总曝光次数
  dense_features.push_back(avg_exposure_interval);                                     // 平均曝光间隔（小时）
  dense_features.push_back(static_cast<double>(log_transform(total_play_time)));       // 总播放时长
  dense_features.push_back(avg_play_time);                                             // 平均播放时长
  dense_features.push_back(static_cast<double>(log_transform(max_play_time)));         // 最大播放时长
  dense_features.push_back(static_cast<double>(log_transform(total_auto_play_time)));  // 自动播放总时长
  dense_features.push_back(logit_transform(auto_play_ratio));                          // 自动播放占比
  dense_features.push_back(logit_transform(positive_feedback_rate));                   // 正反馈率
  dense_features.push_back(logit_transform(high_engagement_rate));                     // 高互动率
  dense_features.push_back(static_cast<double>(log_transform(last_interaction_gap)));  // 最近互动时间间隔 (
                                                                                       // 小时 )

  // ========== 计算时间桶特征 ==========
  for (int bucket_idx = 0; bucket_idx < 7; ++bucket_idx) {
    const BucketStats &bucket = time_buckets[bucket_idx];
    // 1. 曝光次数
    dense_features.push_back(static_cast<double>(log_transform(bucket.exposure_count)));
    // 2. 总播放时长
    dense_features.push_back(static_cast<double>(log_transform(bucket.total_play_time)));
    // 3. 平均播放时长
    double avg_play_time = 0.0;
    if (bucket.exposure_count > 0) {
      avg_play_time = static_cast<double>(bucket.total_play_time) / bucket.exposure_count;
    }
    dense_features.push_back(avg_play_time);
    // 4. 正反馈率
    double positive_rate = 0.0;
    if (bucket.exposure_count > 0) {
      positive_rate = static_cast<double>(bucket.positive_feedback_count) / bucket.exposure_count;
    }
    dense_features.push_back(logit_transform(positive_rate));
    // 5. 自动播放占比
    double auto_play_ratio = 0.0;
    if (bucket.total_play_time > 0) {
      auto_play_ratio = static_cast<double>(bucket.total_auto_play_time) / bucket.total_play_time;
    }
    dense_features.push_back(logit_transform(auto_play_ratio));
  }

  // ========== 计算 target aid 特征 ============
  for (int item_idx = 0; item_idx < target_aid_list.size(); ++item_idx) {
    uint32_t aid = target_aid_list[item_idx];
    std::vector<double> features;

    auto it = aid_to_indices.find(aid);
    if (it == aid_to_indices.end()) {
      features = std::vector<double>(80, 0.0);  // 没有历史行为，填充默认值
    } else {
      const auto &indices = it->second;

      // ========== 全局统计特征 ==========
      int target_total_exposure = indices.size();
      int64_t target_total_play_time = 0;
      int64_t target_max_play_time = 0;
      int64_t target_total_auto_play_time = 0;
      int target_positive_feedback_count = 0;
      int target_high_engagement_count = 0;
      int64_t target_last_timestamp = 0;
      int64_t target_first_timestamp = -1;

      // ========== 时间桶统计 ==========
      std::array<BucketStats, 7> target_time_buckets;

      for (int idx : indices) {
        int64_t play_time = play_times->at(idx);
        int64_t auto_play_time = auto_play_times->at(idx);
        int64_t timestamp = timestamps->at(idx);

        // 计算时间差（秒）
        int64_t time_diff = request_time - timestamp;

        // 确定时间桶索引
        int bucket_idx = HISTORICAL;
        if (time_diff < 3600) {  // <1 小时
          bucket_idx = LAST_1H;
        } else if (time_diff < 6 * 3600) {  // 1-6 小时
          bucket_idx = LAST_6H;
        } else if (time_diff < 24 * 3600) {  // 6-24 小时
          bucket_idx = LAST_24H;
        } else if (time_diff < 3 * 24 * 3600) {  // 1-3 天
          bucket_idx = LAST_3D;
        } else if (time_diff < 7 * 24 * 3600) {  // 3-7 天
          bucket_idx = LAST_7D;
        } else if (time_diff < 30 * 24 * 3600) {  // 7-30 天
          bucket_idx = LAST_30D;
        }

        // 更新全局统计
        target_total_play_time += play_time;
        if (play_time > target_max_play_time) target_max_play_time = play_time;
        target_total_auto_play_time += auto_play_time;
        if (play_time > 0) target_positive_feedback_count++;
        if (play_time > HIGH_ENGAGEMENT_THRESHOLD) target_high_engagement_count++;
        if (timestamp > target_last_timestamp) target_last_timestamp = timestamp;
        if (target_first_timestamp == -1) target_first_timestamp = timestamp;

        // 更新时间桶统计
        BucketStats &bucket = target_time_buckets[bucket_idx];
        bucket.exposure_count++;
        bucket.total_play_time += play_time;
        if (play_time > 0) {
          bucket.positive_feedback_count++;
        }
        bucket.total_auto_play_time += auto_play_time;
      }

      // ========== 计算全局统计特征 ==========
      double target_avg_exposure_interval = 0.0;
      if (target_total_exposure > 1 && target_first_timestamp != -1 && target_last_timestamp != -1) {
        int64_t time_span = first_timestamp - target_last_timestamp;
        target_avg_exposure_interval = static_cast<double>(time_span) / (target_total_exposure - 1) / 3600.0;
      }
      double target_avg_play_time = 0.0;
      if (target_total_exposure > 0) {
        target_avg_play_time = static_cast<double>(target_total_play_time) / target_total_exposure;
      }
      double target_auto_play_ratio = 0.0;
      if (target_total_play_time > 0) {
        target_auto_play_ratio = static_cast<double>(target_total_auto_play_time) / target_total_play_time;
      }
      double target_positive_feedback_rate = 0.0;
      double target_high_engagement_rate = 0.0;
      if (target_total_exposure > 0) {
        target_positive_feedback_rate =
            static_cast<double>(target_positive_feedback_count) / target_total_exposure;
        target_high_engagement_rate =
            static_cast<double>(target_high_engagement_count) / target_total_exposure;
      }
      double last_interaction_gap = last_timestamp > 0 ? (request_time - last_timestamp) / 3600.0 : 0.0;

      // 构建全局特征向量（10 维）
      features.push_back(log_transform(target_total_exposure));            // 总曝光次数
      features.push_back(target_avg_exposure_interval);                    // 平均曝光间隔（小时）
      features.push_back(log_transform(target_total_play_time));           // 总播放时长
      features.push_back(target_avg_play_time);                            // 平均播放时长
      features.push_back(log_transform(target_max_play_time));             // 最大播放时长
      features.push_back(log_transform(target_total_auto_play_time));      // 自动播放总时长
      features.push_back(logit_transform(target_auto_play_ratio));         // 自动播放占比
      features.push_back(logit_transform(target_positive_feedback_rate));  // 正反馈率
      features.push_back(logit_transform(target_high_engagement_rate));    // 高互动率
      features.push_back(log_transform(last_interaction_gap));             // 最近互动时间间隔

      // ========== 计算时间桶特征 ==========
      for (int bucket_idx = 0; bucket_idx < 7; ++bucket_idx) {
        const BucketStats &bucket = target_time_buckets[bucket_idx];
        // 1. 曝光次数
        features.push_back(static_cast<double>(log_transform(bucket.exposure_count)));
        // 2. 总播放时长
        features.push_back(static_cast<double>(log_transform(bucket.total_play_time)));
        // 3. 平均播放时长
        double avg_play_time = 0.0;
        if (bucket.exposure_count > 0) {
          avg_play_time = static_cast<double>(bucket.total_play_time) / bucket.exposure_count;
        }
        features.push_back(avg_play_time);
        // 4. 正反馈率
        double positive_rate = 0.0;
        if (bucket.exposure_count > 0) {
          positive_rate = static_cast<double>(bucket.positive_feedback_count) / bucket.exposure_count;
        }
        features.push_back(logit_transform(positive_rate));
        // 5. 自动播放占比
        double auto_play_ratio = 0.0;
        if (bucket.total_play_time > 0) {
          auto_play_ratio = static_cast<double>(bucket.total_auto_play_time) / bucket.total_play_time;
        }
        features.push_back(logit_transform(auto_play_ratio));
      }
    }

    target_dense_result[item_idx] = features;
  }

  context->SetDoubleListCommonAttr(context_match_dense_attr_, std::move(dense_features));

  int item_idx = 0;
  for (auto result_iter = begin; result_iter != end; ++result_iter) {
    uint64 item_key = (*result_iter).item_key;
    context->SetDoubleListItemAttr(item_key, target_match_dense_attr_,
                                   std::move(target_dense_result[item_idx++]));
  }

  FB_LOG_EVERY_MS(INFO, 10000) << "request_time: " << request_time << ", seq_cnt: " << seq_cnt
                               << ", colossus length: " << colossus_length
                               << ", target_aid_size: " << target_aid_list.size();
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, AdLiveV1Livev4MatchDenseEnricher, AdLiveV1Livev4MatchDenseEnricher);

}  // namespace platform
}  // namespace ks
