#include "dragon/src/processor/ext/ad/gsu/enricher/eshop_long_gsu_enricher.h"

#include <algorithm>
#include <iostream>
#include <unordered_map>
#include <queue>
#include <tuple>

#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "base/thread/thread_pool.h"
#include "kconf/kconf.h"
#include "learning/kuiba/parameter/parameter.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"

namespace ks {
namespace platform {
bool EshopLongGsuEnricher::GetIntList(const std::string &key, std::vector<int> *vec) {
  // 若不配则使用默认值，若配则长度必须等于和默认值长度相等
  const auto json_obj = config()->Get(key);
  if (!json_obj) {
    LOG(WARNING) << "key not find, the default slots config will be used, key = " << key;
    return true;
  }
  if (!json_obj->IsArray()) {
    LOG(ERROR) << "object is not a list, key = " << key;
    return false;
  }
  std::vector<int> cur_vec;
  for (auto *item : json_obj->array()) {
    int val = 0;
    cur_vec.push_back(item->IntValue(val));
  }
  vec->resize(cur_vec.size());
  std::copy(cur_vec.begin(), cur_vec.end(), vec->begin());
  return true;
}

bool EshopLongGsuEnricher::InitProcessor() {
  timestamp_attr_ = config()->GetString("timestamp_attr", "");
  photo_id_attr_ = config()->GetString("photo_id_attr", "");
  author_id_attr_ = config()->GetString("author_id_attr", "");
  play_time_attr_ = config()->GetString("play_time_attr", "");
  duration_attr_ = config()->GetString("duration_attr", "");
  channel_attr_ = config()->GetString("channel_attr", "");
  label_attr_ = config()->GetString("label_attr", "");
  category_attr_ = config()->GetString("category_attr", "");
  spu_id_attr_ = config()->GetString("spu_id_attr", "");

  limit_num_ = config()->GetInt("limit_num", 100);
  filter_play_time_ = config()->GetInt("filter_play_time", 0);
  label_mask_not_ = config()->GetInt("label_mask_not", 0);
  label_mask_and_ = config()->GetInt("label_mask_and", 0);
  label_mask_or_ = config()->GetInt("label_mask_or", 0);
  if (!GetIntList("label_mask_list", &label_mask_list_)) return false;
  label_mask_cnt_ = label_mask_list_.size();
  sort_by_time_ = config()->GetBoolean("sort_by_time", false);

  // remap_util 初始化
  fused_slot_sign_remap_ = config()->GetBoolean("fused_slot_sign_remap", false);
  if (fused_slot_sign_remap_ && !remap_util_.Init(config())) {
    LOG(ERROR) << "remap util init failed";
    return false;
  }
  output_sign_attr_ = config()->GetString("output_sign_attr", "");
  output_slot_attr_ = config()->GetString("output_slot_attr", "");
  if (!GetIntList("slots_id", &slots_ids_)) return false;
  if (!GetIntList("mio_slots_id", &mio_slots_ids_)) return false;
  return true;
}

void EshopLongGsuEnricher::Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
                                 RecoResultConstIter end) {
  timer_.Start();
  auto timestamp_list = context->GetIntListCommonAttr(timestamp_attr_);
  auto photo_id_list = context->GetIntListCommonAttr(photo_id_attr_);
  auto author_id_list = context->GetIntListCommonAttr(author_id_attr_);
  auto play_time_list = context->GetIntListCommonAttr(play_time_attr_);
  auto duration_list = context->GetIntListCommonAttr(duration_attr_);
  auto channel_list = context->GetIntListCommonAttr(channel_attr_);
  auto label_list = context->GetIntListCommonAttr(label_attr_);
  auto category_list = context->GetIntListCommonAttr(category_attr_);
  auto spu_id_list = context->GetIntListCommonAttr(spu_id_attr_);
  uint64_t uid = context->GetUserId();

  if (!timestamp_list || !photo_id_list || !author_id_list || !play_time_list || !duration_list ||
      !channel_list || !label_list || !category_list || !spu_id_list || timestamp_list->empty()) {
    FB_LOG_EVERY_MS(WARNING, 3000) << "colossus no data" << ", user_id: " << uid;
    return;
  }
  int length = timestamp_list->size();
  if (timestamp_list->size() != length ||
      photo_id_list->size() != length ||
      author_id_list->size() != length ||
      play_time_list->size() != length ||
      duration_list->size() != length ||
      channel_list->size() != length ||
      label_list->size() != length ||
      category_list->size() != length ||
      spu_id_list->size() != length) {
    FB_LOG_EVERY_MS(WARNING, 3000) << "colossus length invalid: " << length
                                   << ", user_id: " << uid
                                   << ", timestamp_list->size()=" << timestamp_list->size();
    return;
  }
  CL_LOG(INFO) << "user_id: " << uid << ", history items size: " << length;
  timer_.AppendCostMs("get_resp");

  using MyTuple = std::tuple<int, uint32_t, uint32_t>;
  // 大顶堆，从小到大排序
  auto comparator = [](const MyTuple &a, const MyTuple &b) {
    if (std::get<1>(a) != std::get<1>(b)) {
      return std::get<1>(a) > std::get<1>(b);
    }
    return std::get<2>(a) > std::get<2>(b);
  };

  std::unordered_set<uint64_t> photo_set;
  int64_t timestamp;
  int64_t photo_id;
  uint32_t label;
  int64 filter_time_end = context->GetRequestTime() / 1000 - 60;
  std::vector<int> idxs;
  std::priority_queue<MyTuple, std::vector<MyTuple>, decltype(comparator)> pq(comparator);
  for (int i = length - 1; i >= 0; --i) {
    timestamp = (*timestamp_list)[i];
    photo_id = (*photo_id_list)[i];
    if (timestamp > filter_time_end || photo_set.find(photo_id) != photo_set.end()) {
      continue;
    }
    if (filter_play_time_ > 0 && (*play_time_list)[i] < filter_play_time_) {
      continue;
    }
    if (label_mask_not_ > 0 && ((label_mask_not_ & (*label_list)[i]) != 0)) {
      continue;
    }
    if (label_mask_and_ > 0 && ((label_mask_and_ & (*label_list)[i]) != label_mask_and_)) {
      continue;
    }
    if (label_mask_or_ > 0 && ((label_mask_or_ & (*label_list)[i]) == 0)) {
      continue;
    }

    label = 0;
    for (int j = 0; j < label_mask_cnt_; ++j) {
      label <<= 1;
      if (label_mask_list_[j] & (*label_list)[i]) {
        label |= 1;
      }
    }
    pq.emplace(i, label, (*play_time_list)[i]);
    if (pq.size() > limit_num_) {
      pq.pop();
    }
    photo_set.insert(photo_id);
  }
  while (!pq.empty()) {
    idxs.emplace_back(std::get<0>(pq.top()));
    pq.pop();
  }
  if (sort_by_time_) {
    std::sort(idxs.begin(), idxs.end());
  }
  std::reverse(idxs.begin(), idxs.end());
  timer_.AppendCostMs("priority_queue");

  std::vector<int64> output_slot;
  std::vector<int64> output_sign;
#define ADD_SIGN(i, expr)                                                                                \
  do {                                                                                                   \
    if (!fused_slot_sign_remap_) {                                                                       \
      output_sign.emplace_back(kuiba::Parameter(slots_ids_[i], expr).GetParameterSign());                \
      output_slot.emplace_back(mio_slots_ids_[i]);                                                      \
    } else {                                                                                             \
      uint16_t slot_new = 0;                                                                             \
      uint64_t sign_new = 0;                                                                             \
      if (remap_util_.RemapSlotSign(mio_slots_ids_[i],                                                   \
                                    kuiba::Parameter(slots_ids_[i], expr).GetParameterSign(), &slot_new, \
                                    &sign_new)) {                                                        \
        output_sign.emplace_back(sign_new);                                                             \
        output_slot.emplace_back(mio_slots_ids_[i]);                                                      \
      }                                                                                                  \
    }                                                                                                    \
  } while (0)

  uint64_t category;
  for (const auto &i : idxs) {
    timestamp = (*timestamp_list)[i];
    photo_id = (*photo_id_list)[i];

    ADD_SIGN(0, photo_id);
    ADD_SIGN(1, (*author_id_list)[i]);
    ADD_SIGN(2, (*play_time_list)[i]);
    ADD_SIGN(3, (*duration_list)[i]);
    ADD_SIGN(4, timestamp / 3600 % 24);             // hour_of_day
    ADD_SIGN(5, (filter_time_end - timestamp) / 3600);  // 时间 gap 小时
    ADD_SIGN(6, (*channel_list)[i]);
    ADD_SIGN(7, (*label_list)[i]);

    category = (*category_list)[i];           // cate3_id << 32 | cate2_id << 16 | cate1_id
    ADD_SIGN(8, category & 0xFFFF);           // category1
    ADD_SIGN(9, (category >> 16) & 0xFFFF);  // category2
    ADD_SIGN(10, (category >> 32) & 0xFFFF);  // category3

    ADD_SIGN(11, (*spu_id_list)[i]);
  }
#undef ADD_SIGN
  timer_.AppendCostMs("extract_signs");

  context->SetIntListCommonAttr(output_slot_attr_, std::move(output_slot));
  context->SetIntListCommonAttr(output_sign_attr_, std::move(output_sign));
  timer_.AppendCostMs("fill_attr");

  FB_LOG_EVERY_MS(INFO, 30000) << " output_sign_name: " << output_sign_attr_
                               << ", filter_time_end: " << filter_time_end
                               << ", user_id: " << uid
                               << ", colossus length: " << length
                               << ", result size: " << idxs.size()
                               << ", candidate_num: " << end - begin
                               << ", timer: " << timer_.display()
                               << ", total:" << (timer_.Stop() / 1000.f) << "ms";
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, EshopLongGsuEnricher, EshopLongGsuEnricher);

}  // namespace platform
}  // namespace ks
