#include "dragon/src/processor/ext/ad/gsu/enricher/seq_sideinfo_enricher.h"

#include <iostream>
#include <algorithm>
#include <cmath>

#include "kconf/kconf.h"
#include "base/thread/thread_pool.h"
#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "learning/kuiba/parameter/parameter.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"
#include "teams/reco-arch/colossus/client/rpc_client.h"
#include "teams/reco-arch/colossus/client/snack_client.h"
#include "teams/reco-arch/colossus/proto/long_term_service.pb.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"

namespace ks {
namespace platform {

bool SeqSideinfoEnricher::InitProcessor() {
  // 读取输入属性配置
  input_category_attr_ = config()->GetString("input_category", "");
  input_detail_content_stay_time_attr_ = config()->GetString("input_detail_content_stay_time", "");
  input_timestamp_attr_ = config()->GetString("input_timestamp", "");
  input_price_attr_ = config()->GetString("input_price", "");
  input_click_flow_type_attr_ = config()->GetString("input_click_flow_type", "");
  input_label_attr_ = config()->GetString("input_label", "");
  input_item_id_attr_ = config()->GetString("input_item_id", "");

  // 读取输出属性配置
  output_cate1_attr_ = config()->GetString("output_cate1", "");
  output_cate2_attr_ = config()->GetString("output_cate2", "");
  output_cate3_attr_ = config()->GetString("output_cate3", "");
  output_stay_time_bucket_attr_ = config()->GetString("output_stay_time_bucket", "");
  output_day_gap_attr_ = config()->GetString("output_day_gap", "");
  output_hour_gap_attr_ = config()->GetString("output_hour_gap", "");
  output_hour_gap_v1_attr_ = config()->GetString("output_hour_gap_v1", "");
  output_minute_gap_attr_ = config()->GetString("output_minute_gap", "");
  output_price_bucket_attr_ = config()->GetString("output_price_bucket", "");
  output_flow_type_attr_ = config()->GetString("output_flow_type", "");
  // label 输出
  output_order_attr_ = config()->GetString("output_order", "");
  output_shopping_attr_ = config()->GetString("output_shopping", "");
  output_interaction_attr_ = config()->GetString("output_interaction", "");

  output_index_attr_ = config()->GetString("output_index", "");
  output_count_index_attr_ = config()->GetString("output_count_index", "");

  return true;
}


void SeqSideinfoEnricher::ExtractCategoryLevels(absl::Span<const int64> category_list,
                                               std::vector<int64> *cate1_list,
                                               std::vector<int64> *cate2_list,
                                               std::vector<int64> *cate3_list) {
  cate1_list->clear();
  cate2_list->clear();
  cate3_list->clear();
  for (int64 category : category_list) {
    // 从高到低四个字 (16 bit) 分别解析为 1 级类目 2 级类目 3 级类目 4 级类目
    int64 cate1 = (category >> 48) & 0xffff;  // 最高 16 位
    int64 cate2 = (category >> 32) & 0xffff;  // 次高 16 位
    int64 cate3 = (category >> 16) & 0xffff;  // 第三 16 位
    cate1_list->emplace_back(cate1);
    cate2_list->emplace_back(cate2);
    cate3_list->emplace_back(cate3);
  }
}

void SeqSideinfoEnricher::BucketizeStayTime(absl::Span<const int64> stay_time_list,
                                           std::vector<int64> *bucket_list) {
  bucket_list->clear();
  for (int64 stay_time : stay_time_list) {
    // 非线性分 10 个桶，使用对数分桶
    int64 bucket = 0;
    if (stay_time <= 0) {
      bucket = 0;
    } else if (stay_time <= 1) {
      bucket = 1;
    } else if (stay_time <= 3) {
      bucket = 2;
    } else if (stay_time <= 5) {
      bucket = 3;
    } else if (stay_time <= 10) {
      bucket = 4;
    } else if (stay_time <= 30) {
      bucket = 5;
    } else if (stay_time <= 60) {
      bucket = 6;
    } else if (stay_time <= 120) {
      bucket = 7;
    } else if (stay_time <= 300) {
      bucket = 8;
    } else {
      bucket = 9;
    }
    bucket_list->emplace_back(bucket);
  }
}

int SeqSideinfoEnricher::GetMinutesLag(int minutes_diff) {
  int minutes_diff_bucket = 0;

  if (minutes_diff < 1) {
      minutes_diff_bucket = 1;
  } else if (minutes_diff < 3) {
      minutes_diff_bucket = 3;
  } else if (minutes_diff < 5) {
      minutes_diff_bucket = 5;
  } else if (minutes_diff < 10) {
      minutes_diff_bucket = 10;
  } else if (minutes_diff <= 60 * 12) {
      minutes_diff_bucket = ((minutes_diff + 15) / 30 + 1) * 30;
  } else if (minutes_diff <= 60 * 24) {
      minutes_diff_bucket = ((minutes_diff + 45) / 90 + 1) * 90;
  } else if (minutes_diff <= 60 * 24 * 7) {
      minutes_diff_bucket = ((minutes_diff + 90) / 180 + 1) * 180;
  } else {
      minutes_diff_bucket = 999999;
  }

  return minutes_diff_bucket;
}

void SeqSideinfoEnricher::GetCountIndexList(const std::vector<int64> &item_id_list,
                                           const std::vector<int64> &ts_list,
                                           std::vector<int64> *count_index_list) {
  count_index_list->clear();
  std::unordered_map<int64, int> itemid_to_count_dict;
  std::unordered_map<int64, int64> itemid_to_lastTs_dict;

  for (int i = 0; i < item_id_list.size() && i < ts_list.size(); i++) {
    int64 item_id = item_id_list.at(i);
    int64 ts = ts_list.at(i);

    if (itemid_to_count_dict.find(item_id) == itemid_to_count_dict.end()) {
      itemid_to_count_dict[item_id] = 0;
      itemid_to_lastTs_dict[item_id] = -1;
    }

    if (ts != itemid_to_lastTs_dict[item_id]) {
      itemid_to_count_dict[item_id] += 1;
      itemid_to_lastTs_dict[item_id] = ts;
    }

    count_index_list->emplace_back(itemid_to_count_dict[item_id]);
  }
}

void SeqSideinfoEnricher::CalculateTimeGaps(absl::Span<const int64> timestamp_list,
                                          int64 request_time,
                                          std::vector<int64> *day_gap_list,
                                          std::vector<int64> *hour_gap_list,
                                          std::vector<int64> *hour_gap_v1_list,
                                          std::vector<int64> *minute_gap_list) {
  if (day_gap_list) day_gap_list->clear();
  if (hour_gap_list) hour_gap_list->clear();
  if (hour_gap_v1_list) hour_gap_v1_list->clear();
  if (minute_gap_list) minute_gap_list->clear();

  for (int64 timestamp : timestamp_list) {
    int64 time_diff = (request_time - timestamp) / 3600;
    
    // minute_gap calculation
    if (minute_gap_list) {
      int minutes_diff = (request_time - timestamp) / 60;
      int64 minute_gap = GetMinutesLag(minutes_diff);
      minute_gap_list->emplace_back(minute_gap);
    }

    // hour_gap = (request_time - timestamp) / 3600
    if (hour_gap_list) {
      hour_gap_list->emplace_back(time_diff);
    }

    // day_gap = (request_time - timestamp) / 3600 / 24
    if (day_gap_list) {
      int64 day_gap = time_diff / 24;
      day_gap_list->emplace_back(day_gap);
    }

    // hour_gap_v1 = std::min(static_cast<int>(std::floor(log(hour_gap / 2 + 1.0) * 10)), 200)
    if (hour_gap_v1_list) {
      int64 hour_gap_v1 = std::min(static_cast<int>(std::floor(log(time_diff / 2 + 1.0) * 10)), 200);
      hour_gap_v1_list->emplace_back(hour_gap_v1);
    }
  }
}

void SeqSideinfoEnricher::BucketizePriceV2(absl::Span<const int64> price_list,
                                        std::vector<int64> *bucket_list) {
  bucket_list->clear();

  for (int64 price : price_list) {
    int64 adjusted_price = price / 100;
    int64 bucket = 0;
    if (adjusted_price <= 0) {
      bucket = 0;
    } else {
      // 使用 73.9 * (np.log2(64 + x) - 6) 进行非线性分桶,
      // price 0-1000 落在 300 分桶内， 0-5000 落在 500 分桶以内
      bucket = std::max(0L, std::min(static_cast<int64>(std::floor(
          std::log2(static_cast<double>(64 + adjusted_price)) * 73.9 - 6)), 500L));
    }
    bucket_list->emplace_back(bucket);
  }
}

void SeqSideinfoEnricher::BucketizePriceV1(absl::Span<const int64> price_list,
  std::vector<int64> *bucket_list) {
  bucket_list->clear();

  for (int64 price : price_list) {
    int64 adjusted_price = price / 100;
    int64 bucket = 0;
      if (adjusted_price <= 0) {
        bucket = 0;
      } else {
        // 线性分桶 300 个
        bucket = std::max(0L, std::min(adjusted_price, 300L));
      }
      bucket_list->emplace_back(bucket);
  }
}

void SeqSideinfoEnricher::ExtractFlowTypeBits(absl::Span<const int64> flow_type_list,
                                             std::vector<int64> *extracted_bits_list) {
  extracted_bits_list->clear();
  for (int64 flow_type : flow_type_list) {
    // 取 16-23 bit 的部分（8 位）
    int64 extracted_bits = (flow_type >> 16) & 0xff;
    extracted_bits_list->emplace_back(extracted_bits);
  }
}

void SeqSideinfoEnricher::ExtractLabelBits(absl::Span<const int64> label_list,
                                          std::vector<int64> *order_list,
                                          std::vector<int64> *shopping_list,
                                          std::vector<int64> *interaction_list) {
  order_list->clear();
  shopping_list->clear();
  interaction_list->clear();

  for (int64 label : label_list) {
    // bit 0 输出到 order list (bit 位从 0 开始计数，所以 bit 0 是第 1 位)
    int64 order_bit = label & 0x1;
    order_list->emplace_back(order_bit);

    // bit 0 和 1 输出到 shopping list (取 bit 0 和 bit 1)
    int64 shopping_bits = label & 0x3;  // 右移 0 位后取 2 位
    shopping_list->emplace_back(shopping_bits);

    // bit 2 开始的剩下部分输出为 interaction_list
    int64 interaction_bits = label >> 2;  // 右移 2 位，去掉前 2 位 (bit 0,1)
    interaction_list->emplace_back(interaction_bits);
  }
}

void SeqSideinfoEnricher::Enrich(MutableRecoContextInterface *context,
                                RecoResultConstIter begin, RecoResultConstIter end) {
  timer_.Start();
  // 获取输入序列
  if (!input_category_attr_.empty()) {
    auto category_list = context->GetIntListCommonAttr(input_category_attr_);
    if (!category_list || category_list->size() == 0) {
      CL_LOG(WARNING) << "category_list is empty or null";
      return;
    }
    // 处理 category_list，提取 1-3 级类目
    std::vector<int64> cate1_list, cate2_list, cate3_list;
    ExtractCategoryLevels(category_list.value(), &cate1_list, &cate2_list, &cate3_list);
    // 设置 category 相关的输出
    if (output_cate1_attr_.empty()) {
      CL_LOG(WARNING) << "output_cate1_attr_ is empty, skipping SetIntListCommonAttr";
    } else {
      context->SetIntListCommonAttr(output_cate1_attr_, std::move(cate1_list));
    }
    if (output_cate2_attr_.empty()) {
      CL_LOG(WARNING) << "output_cate2_attr_ is empty, skipping SetIntListCommonAttr";
    } else {
      context->SetIntListCommonAttr(output_cate2_attr_, std::move(cate2_list));
    }
    if (output_cate3_attr_.empty()) {
      CL_LOG(WARNING) << "output_cate3_attr_ is empty, skipping SetIntListCommonAttr";
    } else {
      context->SetIntListCommonAttr(output_cate3_attr_, std::move(cate3_list));
    }

    if (!output_index_attr_.empty()) {
      int N = category_list->size();
      std::vector<int64> index_list(N);
      std::iota(index_list.begin(), index_list.end(), 1);
      context->SetIntListCommonAttr(output_index_attr_, std::move(index_list));
    }
    timer_.AppendCostMs("extract_categories");
  }


  // 处理其他可选的输入序列
  if (!input_detail_content_stay_time_attr_.empty()) {
    auto stay_time_list = context->GetIntListCommonAttr(input_detail_content_stay_time_attr_);
    if (stay_time_list && stay_time_list->size() > 0) {
      std::vector<int64> bucket_list;
      BucketizeStayTime(stay_time_list.value(), &bucket_list);
      if (output_stay_time_bucket_attr_.empty()) {
        CL_LOG(WARNING) << "output_stay_time_bucket_attr_ is empty, skipping SetIntListCommonAttr";
      } else {
        context->SetIntListCommonAttr(output_stay_time_bucket_attr_, std::move(bucket_list));
      }
    }
  }

  // 处理时间戳，计算时间间隔
  if (!input_timestamp_attr_.empty()) {
    auto timestamp_list = context->GetIntListCommonAttr(input_timestamp_attr_);
    if (timestamp_list && timestamp_list->size() > 0) {
      int64 request_time = context->GetRequestTime() / 1000;
      std::vector<int64> day_gap_list, hour_gap_list, hour_gap_v1_list, minute_gap_list;
      
      CalculateTimeGaps(timestamp_list.value(), request_time, 
                       output_day_gap_attr_.empty() ? nullptr : &day_gap_list,
                       output_hour_gap_attr_.empty() ? nullptr : &hour_gap_list,
                       output_hour_gap_v1_attr_.empty() ? nullptr : &hour_gap_v1_list,
                       output_minute_gap_attr_.empty() ? nullptr : &minute_gap_list);
      
      // 设置输出
      if (!output_day_gap_attr_.empty()) {
        context->SetIntListCommonAttr(output_day_gap_attr_, std::move(day_gap_list));
      }
      if (!output_hour_gap_attr_.empty()) {
        context->SetIntListCommonAttr(output_hour_gap_attr_, std::move(hour_gap_list));
      }
      if (!output_hour_gap_v1_attr_.empty()) {
        context->SetIntListCommonAttr(output_hour_gap_v1_attr_, std::move(hour_gap_v1_list));
      }
      if (!output_minute_gap_attr_.empty()) {
        context->SetIntListCommonAttr(output_minute_gap_attr_, std::move(minute_gap_list));
      }
      timer_.AppendCostMs("calculate_time_gaps");
    }
  }

  if (!input_price_attr_.empty()) {
    auto price_list = context->GetIntListCommonAttr(input_price_attr_);
    if (price_list && price_list->size() > 0) {
      std::vector<int64> bucket_list;
      BucketizePriceV1(price_list.value(), &bucket_list);
      if (output_price_bucket_attr_.empty()) {
        CL_LOG(WARNING) << "output_price_bucket_attr_ is empty, skipping SetIntListCommonAttr";
      } else {
        context->SetIntListCommonAttr(output_price_bucket_attr_, std::move(bucket_list));
      }
    }
  }

  if (!input_click_flow_type_attr_.empty()) {
    auto flow_type_list = context->GetIntListCommonAttr(input_click_flow_type_attr_);
    if (flow_type_list && flow_type_list->size() > 0) {
      std::vector<int64> extracted_bits_list;
      ExtractFlowTypeBits(flow_type_list.value(), &extracted_bits_list);
      if (output_flow_type_attr_.empty()) {
        CL_LOG(WARNING) << "output_flow_type_attr_ is empty, skipping SetIntListCommonAttr";
      } else {
        context->SetIntListCommonAttr(output_flow_type_attr_, std::move(extracted_bits_list));
      }
    }
  }

  if (!input_label_attr_.empty()) {
    auto label_list = context->GetIntListCommonAttr(input_label_attr_);
    if (label_list && label_list->size() > 0) {
      std::vector<int64> order_list, shopping_list, interaction_list;
      ExtractLabelBits(label_list.value(), &order_list, &shopping_list, &interaction_list);
      if (output_order_attr_.empty()) {
        CL_LOG(WARNING) << "output_order_attr_ is empty, skipping SetIntListCommonAttr";
      } else {
        context->SetIntListCommonAttr(output_order_attr_, std::move(order_list));
      }
      if (output_shopping_attr_.empty()) {
        CL_LOG(WARNING) << "output_shopping_attr_ is empty, skipping SetIntListCommonAttr";
      } else {
        context->SetIntListCommonAttr(output_shopping_attr_, std::move(shopping_list));
      }
      if (output_interaction_attr_.empty()) {
        CL_LOG(WARNING) << "output_interaction_attr_ is empty, skipping SetIntListCommonAttr";
      } else {
        context->SetIntListCommonAttr(output_interaction_attr_, std::move(interaction_list));
      }
    }
  }

  // 处理count_index计算
  if (!output_count_index_attr_.empty() && !input_item_id_attr_.empty() && !input_timestamp_attr_.empty()) {
    auto item_id_list = context->GetIntListCommonAttr(input_item_id_attr_);
    auto timestamp_list = context->GetIntListCommonAttr(input_timestamp_attr_);
    if (item_id_list && timestamp_list && item_id_list->size() > 0 && timestamp_list->size() > 0) {
      std::vector<int64> item_id_vec(item_id_list->begin(), item_id_list->end());
      std::vector<int64> timestamp_vec(timestamp_list->begin(), timestamp_list->end());
      std::vector<int64> count_index_list;
      GetCountIndexList(item_id_vec, timestamp_vec, &count_index_list);
      context->SetIntListCommonAttr(output_count_index_attr_, std::move(count_index_list));
      timer_.AppendCostMs("calculate_count_index");
    }
  }

  timer_.AppendCostMs("process_all_sequences");
  CL_LOG(INFO) << "SeqSideinfoEnricher timer: " << timer_.display()
               << " total:" << (timer_.Stop() / 1000.f) << "ms";
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, SeqSideinfoEnricher, SeqSideinfoEnricher);

}  // namespace platform
}  // namespace ks
