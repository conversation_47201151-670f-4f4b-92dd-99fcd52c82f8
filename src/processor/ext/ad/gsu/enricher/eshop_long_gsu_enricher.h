#pragma once

#include <memory>
#include <string>
#include <utility>
#include <vector>
#include <unordered_set>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/core/common_reco_base.h"
#include "kess/rpc/grpc/grpc_client_builder.h"
#include "ks/reco_proto/proto/predict_kess_service.kess.grpc.pb.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "serving_base/utility/timer.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"
#include "teams/ad/ad_nn/feature_extract/processors/slot_sign_remap_util.h"

namespace ks {
namespace platform {

class EshopLongGsuEnricher : public CommonRecoBaseEnricher {
 public:
  EshopLongGsuEnricher() {}
  ~EshopLongGsuEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;
  bool GetIntList(const std::string& key, std::vector<int> *vec);

 private:
  std::string timestamp_attr_ = "";
  std::string photo_id_attr_ = "";
  std::string author_id_attr_ = "";
  std::string play_time_attr_ = "";
  std::string duration_attr_ = "";
  std::string channel_attr_ = "";
  std::string label_attr_ = "";
  std::string category_attr_ = "";
  std::string spu_id_attr_ = "";

  int limit_num_ = 100;
  int filter_play_time_ = 0;
  int label_mask_not_ = 0;
  int label_mask_and_ = 0;
  int label_mask_or_ = 0;
  bool fused_slot_sign_remap_ = false;
  std::vector<int> label_mask_list_;
  int label_mask_cnt_ = 0;
  bool sort_by_time_ = false;

  std::string output_sign_attr_;
  std::string output_slot_attr_;
  std::vector<int> slots_ids_;
  std::vector<int> mio_slots_ids_;
  SlotSignRemapUtil remap_util_;

  serving_base::Timer timer_;
  DISALLOW_COPY_AND_ASSIGN(EshopLongGsuEnricher);
};

}  // namespace platform
}  // namespace ks
