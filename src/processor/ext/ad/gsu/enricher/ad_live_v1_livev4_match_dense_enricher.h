#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/core/common_reco_base.h"
#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "kess/rpc/grpc/grpc_client_builder.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "ks/reco_proto/proto/predict_kess_service.kess.grpc.pb.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "serving_base/utility/timer.h"
#include "teams/ad/ad_nn/feature_extract/processors/slot_sign_remap_util.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"

namespace ks {
namespace platform {

class AdLiveV1Livev4MatchDenseEnricher : public CommonRecoBaseEnricher {
 public:
  AdLiveV1Livev4MatchDenseEnricher() = default;
  ~AdLiveV1Livev4MatchDenseEnricher() = default;

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override;

 private:
  // seq info
  std::string colossus_timestamp_attr_ = "timestamp_list";
  std::string colossus_author_id_attr_ = "author_id_list";
  std::string colossus_play_time_attr_ = "play_time_list";
  std::string colossus_cluster_id_attr_ = "cluster_id_list";
  std::string colossus_label_attr_ = "label_list";
  std::string colossus_audience_count_attr_ = "audience_count_list";
  std::string colossus_order_price_attr_ = "order_price_list";
  std::string colossus_auto_play_time_attr_ = "auto_play_time_list";
  std::string target_aids_attr_;

  // seq config
  int limit_num_ = 2000;
  int min_seconds_ago_ = 60;
  int max_seconds_ago_ = 5 * 365 * 24 * 3600;  // 5 year
  bool only_merchant_live_attr_ = false;

  // output config
  std::string context_match_dense_attr_;
  std::string target_match_dense_attr_;

  serving_base::Timer timer_;
  DISALLOW_COPY_AND_ASSIGN(AdLiveV1Livev4MatchDenseEnricher);
};

}  // namespace platform
}  // namespace ks
