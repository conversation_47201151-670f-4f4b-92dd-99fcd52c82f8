#include <memory>
#include <utility>

#include "dragon/src/processor/ext/merchant_arch/enricher/merchant_arch_leaf_caller_enricher.h"
#include "ks/reco_proto/proto/ids.pb.h"
#include "ks/reco_proto/proto/product_platform.pb.h"
#include "ks/reco_pub/reco/util/util.h"

namespace ks {
namespace platform {

void MerchantArchLeafCallerEnricher::Enrich(MutableRecoContextInterface *context,
                                                              RecoResultConstIter begin,
                                                              RecoResultConstIter end) {
  // clear
  response_.Clear();
  // 构造 request
  std::string kess_service = GetStringProcessorParameter(context, "kess_service");
  if (kess_service.empty()) {
    LOG(ERROR) << "MerchantArchLeafCallerEnricher init failed!"
               << " Missing 'kess_service' config.";
    return;
  }
  request_.set_request_id(context->GetRequestId());
  std::string request_type = GetStringProcessorParameter(context, "request_type");
  request_.set_request_type(request_type.empty() ? context->GetRequestType() : request_type);
  request_.set_user_id(context->GetUserId());
  request_.set_device_id(context->GetDeviceId());
  request_.set_time_ms(context->GetRequestTime());
  request_.set_debug(context->IsDebugRequest());
  request_.set_need_traceback(context->NeedTraceback());
  if (context->GetRequest()->has_abtest_mapping_id()) {
    request_.mutable_abtest_mapping_id()->CopyFrom(context->GetRequest()->abtest_mapping_id());
  }
  int64 request_num = GetIntProcessorParameter(context, "request_num", 15);
  request_.set_request_num(request_num > 0 ? request_num : context->GetRequestNum());
  const CommonRecoRequest *request = context->GetRequest();

  if (send_browse_set_) {
    if (request->has_browse_set_package()) {
      request_.set_allocated_browse_set_package(
          const_cast<ks::platform::BrowseSetPackage *>(&(request->browse_set_package())));
    }
    if (request->has_browse_set()) {
      request_.set_allocated_browse_set(const_cast<ks::reco::BrowseSet *>(&(request->browse_set())));
    }
  }
  auto *request_common_attrs = request_.mutable_common_attr();
  if (send_common_attrs_in_request_) {
    for (const auto &attr : request->common_attr()) {
      request_common_attrs->AddAllocated(const_cast<kuiba::SampleAttr *>(&attr));
    }
  }
  std::vector<std::unique_ptr<kuiba::SampleAttr>> send_attrs;
  send_attrs.reserve(send_common_attrs_.size());
  for (const auto &attr : send_common_attrs_) {
    send_attrs.push_back(std::make_unique<kuiba::SampleAttr>());
    auto *sample_attr = send_attrs.back().get();
    if (!interop::LoadSampleAttrFromCommonAttr(context, attr.name, sample_attr, attr.alias, attr.readonly)) {
      // CL_LOG_EVERY_N(WARNING, 100)
      //     << "MerchantArchLeafCallerEnricher cannot find common attr: " << attr.name
      //     << RecoUtil::GetRequestInfoForLog(context);
      continue;
    }
    request_common_attrs->AddAllocated(sample_attr);
  }
  // 请求调用
  int64 timeout_ms = GetIntProcessorParameter(context, "timeout_ms", 300);
  std::string kess_group = GetStringProcessorParameter(context, "kess_group");
  const auto &pr = [&]() {
    KESS_GRPC_MULTI_EVENTLOOP_ASYNC_RETURN_BY_GROUP(kess_service, kess_cluster_, kess_group, "s0",
      timeout_ms, request_, &response_, kess::CommonRecoLeafService, AsyncRecommend)
  }();
  if (!pr.first) {
    CL_LOG_ERROR_EVERY("MerchantArchLeafCallerEnricher", "send_request_fail:", 100)
        << "failed to send delegate retrieve request, ";
    return;
  }
  // 注册 call back
  auto batch_waiter = std::make_shared<ks::kess::rpc::BatchWaiter>();
  batch_waiter->Add(std::move(pr.second), [this, context](const ::grpc::Status &status,
                                                          CommonRecoResponse *response1) {
    std::string common_reco_response_bytes;
    response1->SerializeToString(&common_reco_response_bytes);
    context->SetPtrCommonAttr("merchant_show_case_response_ptr", response1);
    context->SetStringCommonAttr("merchant_show_case_response_bytes", std::move(common_reco_response_bytes));
  });
  context->SetPtrCommonAttr("merchant_show_case_response_waiter", batch_waiter);
  // clear
  if (request_.has_browse_set_package()) {
    request_.release_browse_set_package();
  }
  if (request_.has_browse_set()) {
    request_.release_browse_set();
  }
  while (request_common_attrs->size() > 0) {
    request_common_attrs->ReleaseLast();
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantArchLeafCallerEnricher,
                 MerchantArchLeafCallerEnricher);

}  // namespace platform
}  // namespace ks

