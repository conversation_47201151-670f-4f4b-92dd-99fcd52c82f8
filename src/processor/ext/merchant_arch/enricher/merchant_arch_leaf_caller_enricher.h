#pragma once

#include <kess/rpc/grpc/grpc_client_builder.h>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "ks/serving_util/kess_grpc_client.h"
#include "serving_base/server_base/kess_client.h"
#include "ks/common_reco/util/common_reco_object_pool.h"
#include "folly/container/F14Map.h"
#include "folly/container/F14Set.h"


namespace ks {
namespace platform {
struct SendCommonAttr {
  SendCommonAttr(const std::string &name, const std::string &alias, bool readonly = false)
      : name(name), alias(alias), readonly(readonly) {}

  std::string name;
  std::string alias;
  bool readonly;
};

class MerchantArchLeafCallerEnricher : public CommonRecoBaseEnricher {
 public:
  MerchantArchLeafCallerEnricher() {}
  ~MerchantArchLeafCallerEnricher() {}

  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  bool InitProcessor() override {
    kess_cluster_ = config()->GetString("kess_cluster", ks::kess::scheduler::Constants::DEFAULT_CLUSTER);
    send_browse_set_ = config()->GetBoolean("send_browse_set", true);
    send_common_attrs_in_request_ = config()->GetBoolean("send_common_attrs_in_request", true);
    if (!ParseCommonAttrsConfig("send_common_attrs", &send_common_attrs_, "No common attr will be sent")) {
      return false;
    }
    // request 不指定 return_attr
    request_.set_return_required_attrs_only(false);
    request_.set_use_packed_item_attr(true);
    return true;
  }

  bool ParseCommonAttrsConfig(const std::string &config_name, std::vector<SendCommonAttr> *attrs_vec,
                              const std::string &default_action_description) {
    auto *attrs_config = config()->Get(config_name);
    if (attrs_config && attrs_config->IsArray()) {
      for (const auto *c : attrs_config->array()) {
        if (c == nullptr) {
          continue;
        } else if (c->IsObject()) {
          const std::string &attr_name = c->GetString("name");
          if (attr_name.empty()) {
            LOG(ERROR) << "MerchantArchLeafCallerEnricher init failed! name is required"
                      << config_name;
            return false;
          }
          const std::string &alias = c->GetString("as", attr_name);
          bool readonly = c->GetBoolean("readonly", false);
          attrs_vec->emplace_back(attr_name, alias, readonly);
        } else if (c->IsString()) {
          const std::string &attr_name = c->StringValue();
          if (attr_name.empty()) {
            LOG(ERROR) << "MerchantArchLeafCallerEnricher init failed! name empty for "
                       << config_name;
            return false;
          }
          attrs_vec->emplace_back(attr_name, attr_name);
        }
      }
    } else {
      LOG_EVERY_N(WARNING, 1) << "MerchantArchLeafCallerEnricher "
      << config_name << " is empty, " << default_action_description;
    }
    return true;
  }

  std::string kess_cluster_;
  CommonRecoRequest request_;
  CommonRecoResponse response_;
  bool send_browse_set_ = true;
  bool send_common_attrs_in_request_ = true;
  std::vector<SendCommonAttr> send_common_attrs_;

  DISALLOW_COPY_AND_ASSIGN(MerchantArchLeafCallerEnricher);
};

}  // namespace platform
}  // namespace ks

