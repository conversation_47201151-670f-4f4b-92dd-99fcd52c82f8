#pragma once
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <set>
#include <algorithm>
#include <utility>
#include <map>
#include <tuple>

#include "folly/container/F14Set.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/mix_rank_result.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/mix_rank_result_list.h"
#include "ks/reco_pub/reco/util/kuiba_attr.h"

namespace ks {
namespace platform {

template <typename T>
std::string vec_to_str(const std::vector<T> &v) {
  std::ostringstream oss;
  for (auto it = v.cbegin(); it != v.cend(); ++it) {
    oss << (*it) << ",";
  }
  return oss.str();
}

// 双列混排服务 Context, 存放双列混排各个队列的结果及混排各个单独处理的 Processor 的交互数据
struct HotMixRankStatsCacheInfo {
  int64 timestamp = 0;  // 当前 xtr 查询的时间戳
  int64 cache_valid_duration = 0;  // 当前 xtr 信息的有效期
  std::string cache_key = "";  // 查询 redis 使用的 key
  bool has_read = false;  // 是否从 redis 读到统计值
  float average = 0.0;
  float standard_deviation = 0.0;
};

// nearby feeling info
struct HotMixRankNearByFeelingInfo {
  std::unordered_set<std::string> biz_names;
  std::string poi_province_name;
  std::string poi_city_name;
  std::string poi_district_name;
};

// 双列混排服务每种类型的透出信息统计
struct HotMixRankFlowControlInfo {
  int64 show_num = 0;
  std::string time = "";
  double rate = 0.0;
  double exp_rate = 1.0;
  double err = 0.0;
};

using HotMixRankCacheMap = std::unordered_map<std::string, HotMixRankStatsCacheInfo>;
using HotMixRankCacheMapIterator = HotMixRankCacheMap::iterator;
using PhotoHetuTagLevelInfo = ::mix::kuaishou::newsmodel::PhotoHetuTagLevelInfo;
using FlowControlInfo = std::unordered_map<std::string, HotMixRankFlowControlInfo>;

// 双列混排服务每种类型的透出数量总计
struct HotMixRankTotalShowCacheInfo {
  int64 timestamp = 0;  // 查询的时间戳
  int64 cache_valid_duration = 0;  // 当前信息的有效期
  std::string cache_key = "";  // 查询 redis 使用的 key
  bool has_read = false;  // 是否从 redis 读到统计
  FlowControlInfo flow_control_info;
};

struct Sequence {
  std::string seq_src = "UNKOWN";  // 来自于哪个策略生成
  std::vector<MixRankResult> items;  // 生成的序列
  Sequence() {}
  explicit Sequence(const std::string& ss) : seq_src(ss) {}
  explicit Sequence(const std::string& ss, const std::vector<MixRankResult>&& is)
      : seq_src(ss), items(is)  {}
  explicit Sequence(const std::string& ss, const std::vector<MixRankResult>& is)
      : seq_src(ss), items(is)  {}
  explicit Sequence(const Sequence& other)
      : seq_src(other.seq_src), items(other.items)  {}
  void operator=(const Sequence& src) {
    seq_src = src.seq_src;
    items = src.items;
  }
  void set_seq_src(const std::string& src) {
    seq_src = src;
  }
};
struct SequenceLess {
  bool operator()(const Sequence& s1, const Sequence& s2) const {
    for (int i = 0; i < s1.items.size() && i < s2.items.size(); ++i) {
      const auto& item1 = s1.items[i];
      const auto& item2 = s2.items[i];
      if (item1.PhotoId() != item2.PhotoId()) {
        return item1.PhotoId() < item2.PhotoId();
      }
    }
    return s1.items.size() < s2.items.size();
  }
};

struct PeopleKMeans {
  int refresh_cnt = 0;
  std::vector<float> distance;
  int group_id = -124;
};

enum PermItemType {
  // 短视频
  TYPE_RECO_PHOTO,
  // 硬广
  TYPE_DSP,
  // 粉条
  TYPE_TOP_FANS,
  // 电商 1pp
  TYPE_MERCHANT_LIVE,
  // 其它直播
  TYPE_NORMAL_LIVE,
  // 商业化直播
  TYPE_AD_LIVE,
  // 超过 8 会导致签名去重出现问题
  // 计数用，必须放在最后
  NUM_TYPE
};

struct HotCandidateSequence {
    std::vector<PermItemType> cand_seq;
    double seq_value = 0.0;
    double reward_value = 0.0;
    std::vector<float> pred_real_show;
    std::vector<float> pred_click;
    std::vector<float> pvtr;
    // 以下成员用于序列评估
    double reco_photo_value = 0.0;
    double relation_photo_value = 0.0;
    double reco_livestream_value = 0.0;
    double dsp_value = 0.0;
    double top_fans_value = 0.0;
    double ad_live_value = 0.0;
    double soft_ad_value = 0.0;
    double inner_fans_value = 0.0;
    double normal_live_value = 0.0;
    double merchant_live_value = 0.0;
    double rocket_live_value = 0.0;
    double revenue_live_value = 0.0;
    double reco_merchant_value = 0.0;
    double next_req_loss_value = 0.0;
    double gap_loss_value = 0.0;
    double lift_loss_value = 0.0;
    double ueq_loss_value = 0.0;
    double scatter_loss_value = 0.0;
    double pnext_loss_value = 0.0;
    double live_merchant_gap_loss_value = 0.0;
    folly::F14FastMap<PermItemType, folly::F14FastSet<int>> dynamic_banned_idx_map;
    folly::F14FastMap<PermItemType, int> cnt_stat_map;
    int same_type_min_idx = 0;
    int dsp_num = 0;
    int first_ad_pos = -1;
    int first_hard_ad_pos = -1;
    int reco_num = 0;
    int live_num = 0;
    int merchant_live_num = 0;
    int ad_live_num = 0;
    int max_item_pos = 0;
    int plugin_replace_reco_num = 0;
    bool is_base_seq = false;
    int plugin_num = 0;
    uint128 bit_pattern = 0;
    bool has_ad_gd = false;  // 当前序列是否包含保量广告
    bool fit_ad_gd_pos = false;   // 序列是否满足保量广告保位置要求
    bool is_valid = true;  // 用于过滤
    bool operator==(const HotCandidateSequence &other) const {
      return cand_seq == other.cand_seq;
    }
    std::string to_str() const {
      std::ostringstream oss;
      oss << "cand_seq: {" << vec_to_str(cand_seq) << "}\n";
      return oss.str();
    }
};

struct HotMixRankContext {
  HotMixRankContext() {}
  ~HotMixRankContext() {}

  std::vector<MixRankResult> reco_photo_results;
  std::vector<MixRankResult> random_reco_photo_results;
  std::vector<MixRankResult> relation_reco_photo_results;
  std::vector<MixRankResult> reco_live_results;
  std::vector<MixRankResult> merchant_explore_live_results;
  std::vector<MixRankResult> full_mix_results;
  std::vector<MixRankResult> explore_ad_dsp_results;
  std::vector<MixRankResult> explore_ad_top_fans_results;
  std::vector<MixRankResult> explore_ad_live_results;
  std::vector<MixRankResult> final_results;

  std::unordered_map<uint64, PhotoHetuTagLevelInfo> hetu_infos;
  std::unordered_map<uint64, int64> city_ids;
  std::unordered_map<uint64, HotMixRankNearByFeelingInfo> nearby_feeling_infos;

  std::unordered_map<uint64, std::stringstream> debug_photo;
  bool is_debug = false;
  int ad_pos_no_0_limit = 0;
  bool use_power_calc = false;
  bool use_new_ad_score = false;
  int active_days = 0;
  int low_active_days_thred = 9;
  int high_active_days_thred = 25;
  // 广告 user_level
  int ad_user_level = 0;
  std::string ad_cost_user_level = "inactive";

  // 直播人群
  std::string live_revenue_crowd = "";

  // 多业务竞价候选对列
  std::vector<HotCandidateSequence> mix_list_retrieval;
  bool use_auction_seq_mix = false;

  // 统计信息 map
  HotMixRankCacheMap stats_cache_mp;
  // 特征 xtr 分数统计 mp
  std::unordered_map<uint64, kuiba::PredictItem> pred_xtr_mp;
  // 离线落特征使用，在线打分流程不使用该结构（避免耗时问题）
  std::unordered_map<uint64, kuiba::PredictItem> off_item_feats;
  // xtr 信息
  std::unordered_map<uint64, std::unordered_map<std::string, float>> xtr_info_mp;

  // 以下供 list-wise 模型使用
  // 生成的序列信息
  std::vector<Sequence> reco_photo_seqs;
  std::vector<Sequence> reco_photo_seqs_es;
  std::vector<Sequence> reco_photo_seqs_model1;
  std::vector<Sequence> reco_photo_seqs_model2;
  std::vector<Sequence> rela_photo_seqs;
  std::vector<Sequence> live_photo_seqs;
  std::vector<Sequence> final_reco_photo_seqs;
  // 视频最优序列
  Sequence opt_photo_seq;
  std::vector<double> opt_photo_seq_score_list;

  // 最后供排序用的结果信息
  std::vector<Sequence> generator_results;

  // 基于生态分生成的单序列
  std::vector<Sequence> eco_rela_photo_seqs;
  std::vector<Sequence> eco_live_photo_seqs;
  // 基于生态分生成的混合序列
  std::vector<Sequence> eco_generator_results;

  // 内流历史刷信息
  int64 pre_source_pid = -1;
  int pre_ad_type = -1;
  int pre_ad_pos = -1;
  int pre_page = -1;
  int pre_ad_page_size = 0;
  int pre_live_pos = -1;
  int pre_ad_live_pos = -1;
  int pre_merchant_live_pos = -1;


  // 内流本刷信息
  int64 cur_source_pid = -1;
  int cur_page = -1;

  // 需要统计的 xtr 名字
  std::vector<std::string> xtr_keys;
  std::unordered_set<std::string> xtr_keys_set;
  std::vector<std::string> item_keys;

  std::unordered_set<uint64> live_ids_set;

  // kconf 配置初始化
  int sample_rate = 10;
  int file_mode = 10;
  int total_save_num = 1000000;
  int64 valid_duration = 3600000;
  int32 redis_max_time = 1;
  std::unordered_set<int> permit_type_list;
  int redis_read_timeout = 2;
  int redis_write_timeout = 10;

  // 人群聚类结果
  PeopleKMeans people_polymerization;

  // 用户流控信息
  FlowControlInfo user_flow_control_info;
  std::set<std::string> result_case_keys_set;
  bool get_user_show_info_success = false;

  // 总体曝光数量流控信息
  HotMixRankTotalShowCacheInfo total_show_cache_info;

  // 低活相关字段
  bool is_low_active_user = false;
  int low_active_days = -1;
  int explore_vv = -1;
  int jingxuan_vv = -1;
  int zero_play_days_15d = -1;
  int infer_uv_ctr = -1;
  int nth_page = -1;
  int nth_fresh_type = -1;
  bool hmr_la_personal_exp = false;
  bool is_zero_play_user = false;
  bool is_zero_cold_start = false;
  bool is_low_active_cold_start = false;
  bool is_new_low_vv_user = false;
  bool is_true_new_user = false;

  // 用户信息
  int age = -1;
  int gender = -1;

  // 软广定义是否用内流定义字段
  bool enable_ad_fountain_top_fans_definition = false;

  bool is_fresh_req = false;

  // fountain quantiles;
  std::vector<double> ad_quantiles;
  std::vector<double> fans_quantiles;
  std::vector<double> photo_quantiles;

  // pctr quantiles
  std::vector<double> pctr_quantiles;
  std::pair<int, double> GetQuantileScore(const std::vector<double> &quantiles, double data) {
    int bucket_num = static_cast<int32>(quantiles.size()) - 1;
    if (bucket_num <= 0) {
      return std::make_pair(0, 0.0);
    }
    if (data <= quantiles[0]) {
      return std::make_pair(0, 0.0);
    }
    if (data >= quantiles[bucket_num]) {
      return std::make_pair(bucket_num - 1, 1.0);
    }

    double gap = 1.0 / bucket_num;
    int bkt = std::upper_bound(quantiles.begin(), quantiles.end(), data) -
                              quantiles.begin() - 1;
    bkt = std::max(bkt, 0);
    return std::make_pair(bkt,
      (data - quantiles[bkt]) * gap / std::max(quantiles[bkt + 1] - quantiles[bkt], 1e-5) +
                bkt * gap);
  }

  // pid 2 MixRankResult map
  std::unordered_map<uint64, MixRankResult> pid2result_map;

  // fountain gap matrix, 用 perm_item_type 当索引
  folly::F14FastMap<int, int> pos_map;
  folly::F14FastMap<std::tuple<int, int>, int> gap_map;
  void SetPosMap(const folly::F14FastMap<int, int>& map) {
    pos_map = map;
  }
  void SetGapMap(const folly::F14FastMap<std::tuple<int, int>, int>& map) {
    gap_map = map;
  }
  int GetPos(int x) {
    if (pos_map.count(x) <= 0) return 0;
    return pos_map.at(x);
  }
  int GetGap(int x, int y) {
    auto t = std::make_tuple(x, y);
    if (gap_map.count(t) <= 0) return 0;
    return gap_map.at(t);
  }

  void SetPidFountainFilterReason(uint64 pid, FountainMixFilterReason reason) {
    if (pid2result_map.count(pid) > 0) {
      pid2result_map.at(pid).SetFountainFilterReason(reason);
    } else {
      LOG_EVERY_N(ERROR, 10000) << "SetPidFountainFilterReason: pid: " << pid << " not found.";
    }
  }

  std::vector<MixRankResult> &RecoPhotoResults() {
    return reco_photo_results;
  }

  void AddRecoPhotoResults(MixRankResult res) {
    reco_photo_results.push_back(res);
  }

  std::vector<Sequence>& RecoPhotoSeqs() {
    return reco_photo_seqs;
  }

  void AddRecoPhotoSeq(const Sequence& res) {
    reco_photo_seqs.push_back(res);
  }

  std::vector<MixRankResult> &RandomRecoPhotoResults() {
    return random_reco_photo_results;
  }

  void AddRandomRecoPhotoResults(MixRankResult res) {
    random_reco_photo_results.push_back(res);
  }

  std::vector<MixRankResult> &RelationRecoPhotoResults() {
    return relation_reco_photo_results;
  }

  void AddRelationRecoPhotoResults(MixRankResult res) {
    relation_reco_photo_results.push_back(res);
  }

  std::vector<MixRankResult> &RecoLiveResults() {
    return reco_live_results;
  }

  void AddRecoLiveResults(MixRankResult res) {
    reco_live_results.push_back(res);
  }

  std::vector<MixRankResult> &FullMixResults() {
    return full_mix_results;
  }

  void AddFullMixResults(MixRankResult res) {
    full_mix_results.push_back(res);
  }

  std::vector<MixRankResult> &MerchantExploreLiveResults() {
    return merchant_explore_live_results;
  }

  void AddMerchantExploreLiveResults(MixRankResult res) {
    merchant_explore_live_results.push_back(res);
  }

  std::vector<MixRankResult> &ExploreAdDspResults() {
    return explore_ad_dsp_results;
  }

  void AddExploreAdDspResults(MixRankResult res) {
    explore_ad_dsp_results.push_back(res);
  }

  std::vector<MixRankResult> &ExploreAdTopFansResults() {
    return explore_ad_top_fans_results;
  }

  void AddExploreAdTopFansResults(MixRankResult res) {
    explore_ad_top_fans_results.push_back(res);
  }

  std::vector<MixRankResult> &ExploreAdLiveResults() {
    return explore_ad_live_results;
  }

  void AddExploreAdLiveResults(const MixRankResult& res) {
    explore_ad_live_results.push_back(res);
  }


  std::vector<MixRankResult> &FinalResults() {
    return final_results;
  }

  void AddFinalResults(MixRankResult res) {
    final_results.push_back(res);
  }
};
}  // namespace platform
}  // namespace ks
