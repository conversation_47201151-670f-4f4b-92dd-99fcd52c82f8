#include <algorithm>
#include <functional>
#include <memory>
#include <numeric>
#include <set>
#include <map>
#include <queue>
#include <stack>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include "base/encoding/base64.h"
#include "ks/reco_pub/reco/util/util.h"
#include "kconf/kconf.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/mix_rank_result.h"
#include "dragon/src/processor/ext/ks_mix_rank/hot_mix_ranker/hot_mix_auction_seq_evaluator.h"

namespace ks {
namespace platform {

void HotMixAuctionSeqEvaluator::MixRank(AddibleRecoContextInterface *context) {
  MixRankContext &mix_context = MixCtx();
  auto& hot_context = mix_context.GetHotMixRankContext();
  if (!hot_context.use_auction_seq_mix) {
    return;
  }
  Init(context);
  // 序列评估
  CalSeqValue(context);
  // 最优序列生成
  GenFinalMixRlt(context);

  PrintPerf();
}

void HotMixAuctionSeqEvaluator::Init(AddibleRecoContextInterface *context) {
  MixRankContext &mix_context = MixCtx();
  auto& hot_context = mix_context.GetHotMixRankContext();
  mix_list_retrieval_ = hot_context.mix_list_retrieval;
  is_debug_ = hot_context.is_debug;
  ad_pos_no_0_limit_ = hot_context.ad_pos_no_0_limit;

  origin_normal_live_list_ = hot_context.RecoLiveResults();
  origin_merchant_live_list_ = hot_context.MerchantExploreLiveResults();
  origin_dsp_list_ = hot_context.ExploreAdDspResults();
  origin_top_fans_list_ = hot_context.ExploreAdTopFansResults();
  origin_ad_live_list_ = hot_context.ExploreAdLiveResults();
}

void HotMixAuctionSeqEvaluator::CalSeqValue(AddibleRecoContextInterface *context) {
  /*
    序列评估模块
    计算每个 CandidateSequence 的序列价值, 填入 seq_value
  */
  if (mix_list_retrieval_.size() <= 1) return;
  // reco value
  CalDiscountedPhotoValue(context);
  // normal live value
  CalDiscountedNormalLiveValue(context);
  // merchant live value
  CalDiscountedMerchantLiveValue(context);
  // dsp value
  CalDiscountedDspValue(context);
  // top fasn value
  CalDiscountedTopFansValue(context);
  // ad live value
  CalDiscountedAdLiveValue(context);
  // generate sequence value
  GenSeqValue(context);
}

void HotMixAuctionSeqEvaluator::CalDiscountedPhotoValue(AddibleRecoContextInterface *context) {
  /*
    序列评估 - reco_photo_value 计算
  */
  double reco_pos_discount = GetDoubleProcessorParameter(context,
                              "reco_rpc_explore_auction_photo_pos_discount", 0.8);
  int real_size = MixCtx().MixResults().Size();
  if (0 == real_size) {
    CL_LOG(ERROR) << "RecoPhoto num=0!";
    return;
  }

  std::vector<double> photo_final_score_list;
  for (int i = 0; i < real_size; i++) {
    MixRankResult &item = MixCtx().MixResults().At(i);
    photo_final_score_list.push_back(item.FinalScore());
  }
  for (int seq_idx = 0; seq_idx < mix_list_retrieval_.size(); seq_idx++) {
    int reco_item_idx = 0;
    double reco_photo_value = 0.0;
    std::vector<PermItemType> &mix_list = mix_list_retrieval_.at(seq_idx).cand_seq;
    for (int j = 0; j < mix_list.size(); j++) {
      if (mix_list.at(j) == TYPE_RECO_PHOTO) {
        if (reco_item_idx >= photo_final_score_list.size()) {
          CL_LOG(ERROR) << "RecoPhoto num overflow in Retrieved Sequence!";
          break;
        }
        reco_photo_value += photo_final_score_list.at(reco_item_idx) * std::pow(reco_pos_discount, j);
        base::perfutil::PerfUtilWrapper::IntervalLogStash((
            int64)(photo_final_score_list.at(reco_item_idx) * 10000),
            "reco.mix", "auction.seq.evaluator", "pos.seq.value", "reco_photo_value", std::to_string(j));
        reco_item_idx++;
      }
    }
    mix_list_retrieval_.at(seq_idx).reco_photo_value = reco_photo_value;
  }
}

void HotMixAuctionSeqEvaluator::CalDiscountedNormalLiveValue(AddibleRecoContextInterface *context) {
  /*
    序列评估 - normal_live_value 计算
  */
  if (origin_normal_live_list_.size() < 1) {
    return;
  }
  double normal_live_pos_discount = GetDoubleProcessorParameter(context,
                            "reco_rpc_explore_auction_normal_live_pos_discount", 0.8);
  for (int seq_idx = 0; seq_idx < mix_list_retrieval_.size(); seq_idx++) {
    int normal_live_idx = 0;
    double normal_live_value = 0.0;
    std::vector<PermItemType> &mix_list = mix_list_retrieval_.at(seq_idx).cand_seq;
    for (int j = 0; j < mix_list.size(); j++) {
      if (mix_list.at(j) == TYPE_NORMAL_LIVE) {
        if (normal_live_idx >= origin_normal_live_list_.size()) {
          CL_LOG(ERROR) << "normal_live num overflow in Retrieved Sequence!";
          break;
        }
        double final_score = origin_normal_live_list_.at(normal_live_idx).FinalScore();
        normal_live_value += final_score * std::pow(normal_live_pos_discount, j);
        base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(final_score * 10000),
          "reco.mix", "auction.seq.evaluator", "pos.seq.value", "normal_live_value", std::to_string(j));
        normal_live_idx++;
      }
    }
    mix_list_retrieval_.at(seq_idx).normal_live_value = normal_live_value;
  }
}

void HotMixAuctionSeqEvaluator::CalDiscountedMerchantLiveValue(AddibleRecoContextInterface *context) {
  /*
    序列评估 - merchant_live_value 计算
  */
  if (origin_merchant_live_list_.size() < 1) {
    return;
  }
  double merchant_live_pos_discount = GetDoubleProcessorParameter(context,
                                    "reco_rpc_explore_auction_merchant_live_pos_discount", 0.8);
  for (int seq_idx = 0; seq_idx < mix_list_retrieval_.size(); seq_idx++) {
    int merchant_live_idx = 0;
    double merchant_live_value = 0.0;
    std::vector<PermItemType> &mix_list = mix_list_retrieval_.at(seq_idx).cand_seq;
    for (int j = 0; j < mix_list.size(); j++) {
      if (mix_list.at(j) == TYPE_MERCHANT_LIVE) {
        if (merchant_live_idx >= origin_merchant_live_list_.size()) {
          CL_LOG(ERROR) << "merchant_live num overflow in Retrieved Sequence!";
          break;
        }
        double final_score = origin_merchant_live_list_.at(merchant_live_idx).FinalScore();
        merchant_live_value += final_score * std::pow(merchant_live_pos_discount, j);
        base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(final_score * 10000),
          "reco.mix", "auction.seq.evaluator", "pos.seq.value", "merchant_live_value", std::to_string(j));
        merchant_live_idx++;
      }
    }
    mix_list_retrieval_.at(seq_idx).merchant_live_value = merchant_live_value;
  }
}

void HotMixAuctionSeqEvaluator::CalDiscountedAdLiveValue(AddibleRecoContextInterface *context) {
  /*
    序列评估 - dsp_value 计算
  */
  if (origin_ad_live_list_.size() < 1) {
    return;
  }
  double ad_live_pos_discount = GetDoubleProcessorParameter(context,
                                        "reco_rpc_explore_auction_ad_live_pos_discount", 0.8);
  for (int seq_idx = 0; seq_idx < mix_list_retrieval_.size(); seq_idx++) {
    int ad_live_idx = 0;
    double ad_live_value = 0.0;
    std::vector<PermItemType> &mix_list = mix_list_retrieval_.at(seq_idx).cand_seq;
    for (int j = 0; j < mix_list.size(); j++) {
      if (mix_list.at(j) == TYPE_AD_LIVE) {
        if (ad_live_idx >= origin_ad_live_list_.size()) {
          CL_LOG(ERROR) << "ad_live overflow in Retrieved Sequence!";
          break;
        }
        double final_score = origin_ad_live_list_.at(ad_live_idx).FinalScore();
        ad_live_value += final_score * std::pow(ad_live_pos_discount, j);
        base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(final_score * 10000),
          "reco.mix", "auction.seq.evaluator", "pos.seq.value", "ad_live_value", std::to_string(j));
        ad_live_idx++;
      }
    }
    mix_list_retrieval_.at(seq_idx).ad_live_value = ad_live_value;
  }
}

void HotMixAuctionSeqEvaluator::CalDiscountedDspValue(AddibleRecoContextInterface *context) {
  /*
    序列评估 - dsp_value 计算
  */
  if (origin_dsp_list_.size() < 1) {
    return;
  }
  double dsp_pos_discount = GetDoubleProcessorParameter(context,
                                        "reco_rpc_explore_auction_dsp_pos_discount", 0.8);
  for (int seq_idx = 0; seq_idx < mix_list_retrieval_.size(); seq_idx++) {
    int dsp_idx = 0;
    double dsp_value = 0.0;
    std::vector<PermItemType> &mix_list = mix_list_retrieval_.at(seq_idx).cand_seq;
    for (int j = 0; j < mix_list.size(); j++) {
      if (mix_list.at(j) == TYPE_DSP) {
        if (dsp_idx >= origin_dsp_list_.size()) {
          CL_LOG(ERROR) << "dsp num overflow in Retrieved Sequence!";
          break;
        }
        double final_score = origin_dsp_list_.at(dsp_idx).FinalScore();
        dsp_value += final_score * std::pow(dsp_pos_discount, j);
        base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(final_score * 10000),
          "reco.mix", "auction.seq.evaluator", "pos.seq.value", "dsp_value", std::to_string(j));
        dsp_idx++;
      }
    }
    mix_list_retrieval_.at(seq_idx).dsp_value = dsp_value;
  }
}

void HotMixAuctionSeqEvaluator::CalDiscountedTopFansValue(AddibleRecoContextInterface *context) {
  /*
    序列评估 - dsp_value 计算
  */
  if (origin_top_fans_list_.size() < 1) {
    return;
  }
  double top_fans_pos_discount = GetDoubleProcessorParameter(context,
                                        "reco_rpc_explore_auction_top_fans_pos_discount", 0.8);
  for (int seq_idx = 0; seq_idx < mix_list_retrieval_.size(); seq_idx++) {
    int top_fans_idx = 0;
    double top_fans_value = 0.0;
    std::vector<PermItemType> &mix_list = mix_list_retrieval_.at(seq_idx).cand_seq;
    for (int j = 0; j < mix_list.size(); j++) {
      if (mix_list.at(j) == TYPE_TOP_FANS) {
        if (top_fans_idx >= origin_top_fans_list_.size()) {
          CL_LOG(ERROR) << "top_fans num overflow in Retrieved Sequence!";
          break;
        }
        double final_score = origin_top_fans_list_.at(top_fans_idx).FinalScore();
        top_fans_value += final_score * std::pow(top_fans_pos_discount, j);
        base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(final_score * 10000),
          "reco.mix", "auction.seq.evaluator", "pos.seq.value", "top_fans_value", std::to_string(j));
        top_fans_idx++;
      }
    }
    mix_list_retrieval_.at(seq_idx).top_fans_value = top_fans_value;
  }
}

void HotMixAuctionSeqEvaluator::GenSeqValue(AddibleRecoContextInterface *context) {
  /*
    序列评估 - seq_value 计算
  */
  double photo_weight =
      GetDoubleProcessorParameter(context, "reco_rpc_explore_auction_photo_weight", 1.0);
  double normal_live_weight =
      GetDoubleProcessorParameter(context, "reco_rpc_explore_auction_normal_live_weight", 1.0);
  double merchant_live_weight =
      GetDoubleProcessorParameter(context, "reco_rpc_explore_auction_merchant_live_weight", 1.0);
  double dsp_weight =
      GetDoubleProcessorParameter(context, "reco_rpc_explore_auction_dsp_weight", 1.0);
  double top_fans_weight =
      GetDoubleProcessorParameter(context, "reco_rpc_explore_auction_top_fans_weight", 1.0);
  double ad_live_weight =
      GetDoubleProcessorParameter(context, "reco_rpc_explore_auction_ad_live_weight", 1.0);
  bool enable_seq_filter = GetBoolProcessorParameter(context,
      "hot_mix_auction_enable_seq_filter", false);
  bool enable_seq_filter_by_reco_num = GetBoolProcessorParameter(context,
      "hot_mix_auction_enable_seq_filter_by_reco_num", false);
  std::string seq_value_thres_str =
    GetStringProcessorParameter(context, "hot_mix_auction_seq_value_thres_str", "");
  folly::F14FastMap<std::string, double> seq_value_thres;
  if (enable_seq_filter) {
    ParseThresParams(seq_value_thres_str, &seq_value_thres);
  }
  auto iter = mix_list_retrieval_.begin();
  while (iter != mix_list_retrieval_.end()) {
    auto &seq = *iter;
    seq.seq_value = seq.reco_photo_value * photo_weight + seq.normal_live_value * normal_live_weight +
        seq.dsp_value * dsp_weight + seq.merchant_live_value * merchant_live_weight +
        seq.top_fans_value * top_fans_weight + seq.ad_live_value * ad_live_weight;;
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(seq.reco_photo_value * 10000),
        "reco.mix", "auction.seq.evaluator", "seq.value", "reco_photo_value");
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(seq.normal_live_value * 10000),
        "reco.mix", "auction.seq.evaluator", "seq.value", "normal_live_value");
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(seq.merchant_live_value * 10000),
        "reco.mix", "auction.seq.evaluator", "seq.value", "merchant_live_value");
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(seq.dsp_value * 10000),
        "reco.mix", "auction.seq.evaluator", "seq.value", "dsp_value");
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(seq.top_fans_value * 10000),
        "reco.mix", "auction.seq.evaluator", "seq.value", "top_fans_value");
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(seq.ad_live_value * 10000),
        "reco.mix", "auction.seq.evaluator", "seq.value", "ad_live_value");
    if (enable_seq_filter && mix_list_retrieval_.size() > 1) {
      double thres = 0.0;
      int seq_length = seq.cand_seq.size();
      if (enable_seq_filter_by_reco_num) {
        int reco_num = seq.reco_num;
        std::ostringstream key;
        key << reco_num << "_" << seq_length;
        auto it = seq_value_thres.find(key.str());
        thres = (it != seq_value_thres.end()) ? it->second :
          seq_value_thres["D_" + std::to_string(seq_length)];
      } else {
        thres = seq_value_thres[std::to_string(seq_length)];
      }
      if (seq.seq_value < thres && !seq.is_base_seq) {
        iter = mix_list_retrieval_.erase(iter);
        continue;
      }
    }
    if (is_debug_) {
      LOG(INFO) << "HotMixAuctionSeqEvaluator "
                << "new_candidate: " << seq.to_str() << ','
                << "reco_photo_value: " << seq.reco_photo_value << ','
                << "normal_live_value: " << seq.normal_live_value << ','
                << "dsp_value: " << seq.dsp_value << ','
                << "merchant_live_value: " << seq.merchant_live_value << ','
                << "top_fans_value: " << seq.top_fans_value << ','
                << "seq_value: " << seq.seq_value;
    }
    iter++;
  }
  return;
}

void HotMixAuctionSeqEvaluator::GenFinalMixRlt(AddibleRecoContextInterface *context) {
  /*
    最优序列生成
  */
  if (mix_list_retrieval_.empty()) return;
  auto cand_cmp = [](const HotCandidateSequence &a, const HotCandidateSequence &b) {
    return a.seq_value > b.seq_value;
  };
  std::sort(mix_list_retrieval_.begin(), mix_list_retrieval_.end(), cand_cmp);

  int dsp_num = 0;
  int normal_live_num = 0;
  int merchant_live_num = 0;
  int top_fans_num = 0;
  int ad_live_num = 0;
  auto &retrieved_list = mix_list_retrieval_.at(0).cand_seq;
  auto &mix_list = MixCtx().MixResults();
  int pre_size = Size();
  for (int idx = 0; idx < retrieved_list.size(); idx++) {
    if (retrieved_list.at(idx) == TYPE_DSP) {
      Insert(idx, origin_dsp_list_.at(dsp_num));
      dsp_num++;
    } else if (retrieved_list.at(idx) == TYPE_NORMAL_LIVE) {
      Insert(idx, origin_normal_live_list_.at(normal_live_num));
      normal_live_num++;
    } else if (retrieved_list.at(idx) == TYPE_MERCHANT_LIVE) {
      Insert(idx, origin_merchant_live_list_.at(merchant_live_num));
      merchant_live_num++;
    } else if (retrieved_list.at(idx) == TYPE_TOP_FANS) {
      Insert(idx, origin_top_fans_list_.at(top_fans_num));
      top_fans_num++;
    } else if (retrieved_list.at(idx) == TYPE_AD_LIVE) {
      Insert(idx, origin_ad_live_list_.at(ad_live_num));
      ad_live_num++;
    }
  }
  int post_size = Size();
  if (is_debug_) {
    LOG(INFO) << "HotMixAuctionSeqEvaluator "
              << "GenFinalMixRlt pre_size" << pre_size << ','
              << "post_size: " << post_size;
  }
  if (origin_normal_live_list_.size() > 0) {
    base::perfutil::PerfUtilWrapper::CountLogStash("reco.mix", "auction.evaluator.delete",
        "normal_live", std::to_string(origin_normal_live_list_.size() - normal_live_num));
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
                                (int64)(origin_normal_live_list_.size() - normal_live_num),
                                "common.leaf.mix_rank", "hot_auction",
                                GlobalHolder::GetServiceIdentifier(),
                                "normal_live_filter_num", context->GetRequestType());
  }
  if (origin_merchant_live_list_.size() > 0) {
    base::perfutil::PerfUtilWrapper::CountLogStash("reco.mix", "auction.evaluator.delete",
        "merchant_live", std::to_string(origin_merchant_live_list_.size() - merchant_live_num));
    base::perfutil::PerfUtilWrapper::IntervalLogStash(
                                (int64)(origin_merchant_live_list_.size() - merchant_live_num),
                                "common.leaf.mix_rank", "hot_auction",
                                GlobalHolder::GetServiceIdentifier(),
                                "merchant_live_filter_num", context->GetRequestType());
  }
  if (origin_dsp_list_.size() > 0) {
    base::perfutil::PerfUtilWrapper::CountLogStash("reco.mix", "auction.evaluator.delete",
        "dsp", std::to_string(origin_dsp_list_.size() - dsp_num));
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(origin_dsp_list_.size() - dsp_num),
                                "common.leaf.mix_rank", "hot_auction",
                                GlobalHolder::GetServiceIdentifier(),
                                "dsp_filter_num", context->GetRequestType());
  }
  if (origin_top_fans_list_.size() > 0) {
    base::perfutil::PerfUtilWrapper::CountLogStash("reco.mix", "auction.evaluator.delete",
        "dsp", std::to_string(origin_top_fans_list_.size() - top_fans_num));
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(origin_top_fans_list_.size() - top_fans_num),
                                "common.leaf.mix_rank", "hot_auction",
                                GlobalHolder::GetServiceIdentifier(),
                                "top_fans_filter_num", context->GetRequestType());
  }
  if (origin_ad_live_list_.size() > 0) {
    base::perfutil::PerfUtilWrapper::CountLogStash("reco.mix", "auction.evaluator.delete",
        "ad_live", std::to_string(origin_ad_live_list_.size() - ad_live_num));
    base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(origin_ad_live_list_.size() - ad_live_num),
                                "common.leaf.mix_rank", "hot_auction",
                                GlobalHolder::GetServiceIdentifier(),
                                "ad_live_filter_num", context->GetRequestType());
  }
}

void HotMixAuctionSeqEvaluator::PrintPerf() {
  // 打印完全插入后，透出位置的分布情况
  for (int i = 0; i < Size(); i++) {
    std::string result_case = GetResultCaseName(At(i));
    if (At(i).IsFansTop()) {
      result_case = "AD_TOP_FANS";
    }
    base::perfutil::PerfUtilWrapper::CountLogStash("reco.mix",
        "auction.evaluator.position", result_case, std::to_string(i),
        GlobalHolder::GetServiceIdentifier(), std::to_string(ad_pos_no_0_limit_));
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, HotMixAuctionSeqEvaluator, HotMixAuctionSeqEvaluator);
}  // namespace platform
}  // namespace ks
