#include <math.h>
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>
#include <set>
#include <algorithm>
#include <sstream>

#include "kconf/kconf.h"
#include "base/random/true_random.h"
#include "dragon/src/processor/ext/ks_mix_rank/util/mix_rank_util.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/mix_rank_result.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/hot_mix_rank_context.h"
#include "dragon/src/processor/ext/ks_mix_rank/hot_mix_ranker/hot_mix_auction_seq_retrieval.h"

namespace ks {
namespace platform {

void HotMixAuctionSeqRetrieval::MixRank(AddibleRecoContextInterface *context) {
  MixRankContext &mix_context = MixCtx();
  auto& hot_context = mix_context.GetHotMixRankContext();
  if (!hot_context.use_auction_seq_mix) {
    return;
  }
  Clear();
  Init(context);
  SortAndMerge(context);
  RetrieveMixList(context);
  FilterRetrieveMixList(context);
  hot_context.mix_list_retrieval = mix_list_retrieval_;
}

void HotMixAuctionSeqRetrieval::Init(AddibleRecoContextInterface *context) {
  MixRankContext &mix_context = MixCtx();
  auto& hot_context = mix_context.GetHotMixRankContext();
  is_fresh_req_ = hot_context.is_fresh_req;

  // 广告冷热启首刷前 4 限制实验参数设置
  //// 是否启用前 4 限制, 可选对全人群生效或是只对低活用户
  bool enable_new_fresh_limit_ad = GetBoolProcessorParameter(context,
    "reco_rpc_explore_enable_new_fresh_limit_ad", false);
  bool enable_new_fresh_limit_ad_la = GetBoolProcessorParameter(context,
    "reco_rpc_explore_enable_new_fresh_limit_ad_la", false);
  bool enable_new_fresh_limit_ad_new_la = GetBoolProcessorParameter(context,
    "reco_rpc_explore_enable_new_fresh_limit_ad_new_la", false);
  bool is_low_active = hot_context.is_low_active_user;
  bool is_new_low_vv = hot_context.is_new_low_vv_user;
  is_new_fresh_limit_ad_ = enable_new_fresh_limit_ad || (enable_new_fresh_limit_ad_la && is_low_active)
      || (enable_new_fresh_limit_ad_new_la && is_new_low_vv);
  // 拆分全人群、低消低活的屏蔽参数
  is_new_fresh_limit_ad_other_ = enable_new_fresh_limit_ad
                                 && !((enable_new_fresh_limit_ad_la && is_low_active)
                                      || (enable_new_fresh_limit_ad_new_la && is_new_low_vv));
  //// 限制是否对热启动生效
  is_warm_start_ = mix_context.RecoClientRequestInfo()->req_type().pcursor() == "";
  bool enable_new_fresh_ad_warm_start = GetBoolProcessorParameter(context,
    "reco_rpc_explore_enable_new_fresh_ad_warm_start", false);
  is_new_fresh_ad_warm_start_ = enable_new_fresh_ad_warm_start && is_warm_start_;

  // 是否启用软广内流定义
  enable_fountain_top_fans_ =
    hot_context.enable_ad_fountain_top_fans_definition;

  // live 付费用户字段, 有四种: big_g, high_pay, pay, not_pay
  live_revenue_crowd_ = GetStringProcessorParameter(context, "live_revenue_crowd", "");

  // 插入营收业务最大数量-竞价截断
  auction_inserted_max_num_ =
      GetIntProcessorParameter(context, "reco_rpc_explore_auction_inserted_max_num", 5);
  is_debug_ = hot_context.is_debug;
  ad_pos_no_0_limit_ = hot_context.ad_pos_no_0_limit;
}

void HotMixAuctionSeqRetrieval::SortAndMerge(
                      AddibleRecoContextInterface *context) {
  MixRankContext &mix_ctx = MixCtx();
  HotMixRankContext &hot_context = mix_ctx.GetHotMixRankContext();
  auto score_cmp = [](const MixRankResult &a, const MixRankResult &b) {
    return a.FinalScore() > b.FinalScore();
  };

  std::sort(hot_context.reco_live_results.begin(),
                      hot_context.reco_live_results.end(), score_cmp);
  std::sort(hot_context.merchant_explore_live_results.begin(),
                      hot_context.merchant_explore_live_results.end(), score_cmp);
  std::sort(hot_context.explore_ad_dsp_results.begin(),
                      hot_context.explore_ad_dsp_results.end(), score_cmp);
  std::sort(hot_context.explore_ad_top_fans_results.begin(),
                      hot_context.explore_ad_top_fans_results.end(), score_cmp);
  std::sort(hot_context.explore_ad_live_results.begin(),
                      hot_context.explore_ad_live_results.end(), score_cmp);

  MergeItemList(&(hot_context.reco_live_results));
  MergeItemList(&(hot_context.merchant_explore_live_results));
  MergeItemList(&(hot_context.explore_ad_dsp_results));
  MergeItemList(&(hot_context.explore_ad_top_fans_results));
  MergeItemList(&(hot_context.explore_ad_live_results));
  // 混排漏斗 - 竞价截断前
  AuctionObserver(context, "mix_mtb_funnel.before_auction");
  std::sort(origin_inserted_item_list_.begin(), origin_inserted_item_list_.end(), score_cmp);
  if (origin_inserted_item_list_.size() > auction_inserted_max_num_) {
    // 待插入 items 限制
    origin_inserted_item_list_.erase(origin_inserted_item_list_.begin() + auction_inserted_max_num_,
                                     origin_inserted_item_list_.end());
  }
  // 混排漏斗 - 竞价截断后
  AuctionObserver(context, "mix_mtb_funnel.after_auction");
  if (is_debug_) {
    LOG(INFO) << "HotMixAuctionSeqRetrieval: "
        << " reco_live_results size=" << hot_context.reco_live_results.size() << ", "
        << " merchant_explore_live_results size=" << hot_context.merchant_explore_live_results.size() << ", "
        << " explore_ad_dsp_results size=" << hot_context.explore_ad_dsp_results.size()
        << " explore_ad_top_fans_results size=" << hot_context.explore_ad_top_fans_results.size()
        << " explore_ad_live_results size=" << hot_context.explore_ad_live_results.size();
  }
}

void HotMixAuctionSeqRetrieval::MergeItemList(std::vector<MixRankResult> *origin_item_list) {
  origin_inserted_item_list_.insert(origin_inserted_item_list_.end(),
    origin_item_list->begin(), origin_item_list->end());
}

void HotMixAuctionSeqRetrieval::AuctionObserver(AddibleRecoContextInterface *context,
                                                    std::string stage) {
  std::unordered_map<std::string, int> record{
      {"normal_live", 0}, {"merchant_live", 0},
      {"ad_dsp", 0}, {"ad_top_fans", 0}};
  for (auto &item : origin_inserted_item_list_) {
    const auto& result_case = item.RawResultCase();
    switch (result_case) {
      case MixRecoResult::ResultCase::kRecoLivestream:
        record["normal_live"]++;
        break;
      case MixRecoResult::ResultCase::kMerchantExploreLive:
        record["merchant_live"]++;
        break;
      case MixRecoResult::ResultCase::kAdDsp:
        if (item.IsDsp()) {
          record["ad_dsp"]++;
        }
        if (item.IsFansTop()) {
          record["ad_top_fans"]++;
        }
        break;
      default:
        // do nothing
        break;
    }
  }

  for_each(record.begin(), record.end(),
           [this, stage](const std::unordered_map<std::string, int>::value_type &it) {
             base::perfutil::PerfUtilWrapper::IntervalLogStash(
                 it.second, "common.leaf.mix_rank", stage, it.first, std::to_string(it.second));
           });
}

void HotMixAuctionSeqRetrieval::RetrieveMixList(AddibleRecoContextInterface *context) {
  /*
    序列召回模块 V2 版本
    召回满足业务规则的混排序列, 填入 mix_list_retrieval_
  */
  LoadAllGapConf(context);
  GetMixHistory(context);
  // 初始化静态位置限制
  std::map<PermItemType, int> min_available_idx_map;
  CalStaticBannedPos(context, &min_available_idx_map);
  // 初始化待插入 item 序列
  std::vector<PermItemType> item_to_insert;
  InitItemToInsert(&item_to_insert, context);
  if (item_to_insert.size() < 1) return;

  // 初始化 CandidateSequence
  InitCandidateSequence(context, item_to_insert);

  int reserve_space = 500;
  mix_list_retrieval_.reserve(reserve_space);
  int last_idx = mix_list_retrieval_.size();
  PermItemType pre_type = TYPE_RECO_PHOTO;
  for (int k = 0; k < item_to_insert.size(); k++) {
    PermItemType cur_item = item_to_insert.at(k);
    // 遍历取 base candidate
    // 当当前 type 和上一个 type 相同时， 基于的 base candidate 下标从 last_idx 开始， 而非 0 开始
    int start_idx = (cur_item == pre_type) ? last_idx : 0;
    // 记录本次新增 candidate 的起始位置 last_idx
    last_idx = mix_list_retrieval_.size();
    for (int i = start_idx; i < last_idx; i++) {
      // 记录该 base candidate 中 cur_item 的可插入位置
      std::vector<int> available_idx_list;
      int min_available_idx = (cur_item == pre_type) ? std::max(min_available_idx_map.at(cur_item),
                                                                mix_list_retrieval_.at(i).same_type_min_idx)
                                                      : min_available_idx_map.at(cur_item);
      for (int j = min_available_idx; j <= mix_list_retrieval_.at(i).cand_seq.size(); j++) {
        auto banned_iter = mix_list_retrieval_.at(i).dynamic_banned_idx_map.find(cur_item);
        if (banned_iter != mix_list_retrieval_.at(i).dynamic_banned_idx_map.end()) {
          if (banned_iter->second.count(j) > 0) continue;
        }
        available_idx_list.push_back(j);
      }
      // 依据可插入位置，构造 new_candidate
      for (int new_idx : available_idx_list) {
        HotCandidateSequence new_candidate;
        new_candidate.cand_seq.assign(mix_list_retrieval_.at(i).cand_seq.begin(),
                                      mix_list_retrieval_.at(i).cand_seq.end());
        new_candidate.cand_seq.insert(new_candidate.cand_seq.begin() + new_idx, cur_item);
        new_candidate.same_type_min_idx = new_idx + 1;
        new_candidate.dsp_num = mix_list_retrieval_.at(i).dsp_num;
        new_candidate.live_num = mix_list_retrieval_.at(i).live_num;
        new_candidate.merchant_live_num = mix_list_retrieval_.at(i).merchant_live_num;
        new_candidate.ad_live_num = mix_list_retrieval_.at(i).ad_live_num;
        new_candidate.reco_num = mix_list_retrieval_.at(i).reco_num;
        switch (cur_item) {
          case TYPE_DSP:
            new_candidate.dsp_num += 1;
            break;
          case TYPE_TOP_FANS:
            new_candidate.dsp_num += 1;
            break;
          case TYPE_NORMAL_LIVE:
            new_candidate.live_num += 1;
            break;
          case TYPE_MERCHANT_LIVE:
            new_candidate.merchant_live_num += 1;
            break;
          case TYPE_AD_LIVE:
            new_candidate.ad_live_num += 1;
            break;
          default:
            // do nothing
            break;
        }
        if (k != item_to_insert.size() - 1) {
          // 最后一个插入的 item 不需要算 banned pos
          CalDynamicBannedPos(&new_candidate);
        }
        mix_list_retrieval_.push_back(new_candidate);
      }
    }
    pre_type = cur_item;
  }
  if (is_debug_) {
    LOG(INFO) << "HotMixAuctionSeqRetrieval: "
        << " mix_list_retrieval size=" << mix_list_retrieval_.size();
  }
}

void HotMixAuctionSeqRetrieval::FilterRetrieveMixList(AddibleRecoContextInterface *context) {
  MixRankContext &mix_ctx = MixCtx();
  HotMixRankContext &hot_context = mix_ctx.GetHotMixRankContext();

  bool enable_filter = GetBoolProcessorParameter(
                          context, "reco_rpc_explore_auction_enable_filter_list", true);
  int allow_filter_ad_num = GetIntProcessorParameter(
                          context, "reco_rpc_explore_auction_allow_filter_ad_num", 100);
  int ad_live_to_other_live_gap = GetIntProcessorParameter(
                          context, "explore_ad_live_to_other_live_gap", 3);

  if (!enable_filter ||
        (hot_context.explore_ad_dsp_results.empty() && hot_context.explore_ad_top_fans_results.empty())) {
    return;
  }

  // 过滤不满足 ad 数量的 list
  int dsp_total_num = hot_context.explore_ad_dsp_results.size() +
                              hot_context.explore_ad_top_fans_results.size();
  int size_before = mix_list_retrieval_.size();

  const auto& dsp_list = hot_context.ExploreAdDspResults();
  const auto& fan_list = hot_context.ExploreAdTopFansResults();

  // 主逻辑
  auto ad_end_iter = std::remove_if(mix_list_retrieval_.begin(), mix_list_retrieval_.end(),
      [&](const HotCandidateSequence& iter_cand_seq) {
    // 1. 广告数量兜底逻辑
    if (dsp_total_num - iter_cand_seq.dsp_num > allow_filter_ad_num) {
      return true;
    }

    // 2. 商业化直播 gap 逻辑
    if (ad_live_to_other_live_gap <= 0) return false;

    int last_other_live_idx = -1;
    int last_ad_live_idx = -1;
    int dsp_idx = 0;
    int fan_idx = 0;
    for (int i = 0; i < iter_cand_seq.cand_seq.size(); ++i) {
      const auto& type = iter_cand_seq.cand_seq[i];
      if (type == TYPE_RECO_PHOTO) continue;
      // 2.1. 其他直播 to 商业化直播 gap
      if (type == TYPE_NORMAL_LIVE || type == TYPE_MERCHANT_LIVE) {
        last_other_live_idx = i;
        if (last_ad_live_idx >= 0 && (i - last_ad_live_idx) <= ad_live_to_other_live_gap) {
          return true;
        }
        continue;
      }
      // 2.2. 商业化直播 to 其他直播 gap
      if ((type == TYPE_DSP && dsp_idx < dsp_list.size() && IsLiveItem(dsp_list[dsp_idx++]))
          || (type == TYPE_TOP_FANS && fan_idx < fan_list.size() && IsLiveItem(fan_list[fan_idx++]))) {
        last_ad_live_idx = i;
        if (last_other_live_idx >= 0 && (i - last_other_live_idx) <= ad_live_to_other_live_gap) {
          return true;
        }
      }
    }
    return false;
  });
  mix_list_retrieval_.erase(ad_end_iter, mix_list_retrieval_.end());

  // Perf
  base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(size_before),
                                "common.leaf.mix_rank", "hot_auction",
                                GlobalHolder::GetServiceIdentifier(),
                                "mix_list_size_before", context->GetRequestType());
  base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(size_before - mix_list_retrieval_.size()),
                                "common.leaf.mix_rank", "hot_auction",
                                GlobalHolder::GetServiceIdentifier(),
                                "mix_list_filter_num", context->GetRequestType());
  base::perfutil::PerfUtilWrapper::IntervalLogStash((int64)(mix_list_retrieval_.size()),
                                "common.leaf.mix_rank", "hot_auction",
                                GlobalHolder::GetServiceIdentifier(),
                                "mix_list_retrieval_size", context->GetRequestType());
}

void HotMixAuctionSeqRetrieval::LoadAllGapConf(AddibleRecoContextInterface *context) {
  // 首刷冷启限制
  int fresh_limit_leaf_live =
      GetIntProcessorParameter(context, "reco_rpc_explore_fresh_limit_leaf_live", 2);
  int fresh_limit_merchant_live =
      GetIntProcessorParameter(context, "reco_rpc_explore_fresh_limit_merchant_live", 2);
  int fresh_limit_ad =
      GetIntProcessorParameter(context, "reco_rpc_explore_fresh_limit_ad", 6);
  int fresh_limit_top_fans =
      GetIntProcessorParameter(context, "reco_rpc_explore_fresh_limit_top_fans", 6);
  int fresh_limit_ad_live =
      GetIntProcessorParameter(context, "reco_rpc_explore_fresh_limit_ad_live", 6);
  // 首刷热启限制，default 不限制
  int warm_start_limit_leaf_live =
      GetIntProcessorParameter(context, "reco_rpc_explore_warm_start_limit_leaf_live", 1);
  // leafLive gap 约束
  int leafLive_leafLive_gap =
      GetIntProcessorParameter(context, "reco_rpc_explore_leafLive_leafLive_gap", 3);
  int leafLive_merchantLive_gap =
      GetIntProcessorParameter(context, "reco_rpc_explore_leafLive_merchantLive_gap", 3);
  int leafLive_ad_gap =
      GetIntProcessorParameter(context, "reco_rpc_explore_leafLive_ad_gap", 2);
  int leafLive_topFans_gap =
      GetIntProcessorParameter(context, "reco_rpc_explore_leafLive_topFans_gap", 2);
  int leafLive_adLive_gap =
    GetIntProcessorParameter(context, "reco_rpc_explore_leafLive_adLive_gap", 2);
  // 电商 1pp gap 约束
  int merchantLive_merchantLive_gap =
      GetIntProcessorParameter(context, "reco_rpc_explore_merchantLive_merchantLive_gap", 3);
  int merchantLive_ad_gap =
      GetIntProcessorParameter(context, "reco_rpc_explore_merchantLive_ad_gap", 2);
  int merchantLive_topFans_gap =
      GetIntProcessorParameter(context, "reco_rpc_explore_merchantLive_topFans_gap", 2);
  int merchantLive_adLive_gap =
      GetIntProcessorParameter(context, "reco_rpc_explore_merchantLive_adLive_gap", 2);
  // 硬广 gap 约束
  int ad_ad_gap =
      GetIntProcessorParameter(context, "reco_rpc_explore_ad_ad_gap", 4);
  int ad_topFans_gap =
      GetIntProcessorParameter(context, "reco_rpc_explore_ad_topFans_gap", 4);
  int ad_adLive_gap =
      GetIntProcessorParameter(context, "reco_rpc_explore_ad_adLive_gap", 4);
  // 粉条 gap 约束
  int topFans_topFans_gap =
      GetIntProcessorParameter(context, "reco_rpc_explore_topFans_topFans_gap", 4);
  int topFans_adLive_gap =
      GetIntProcessorParameter(context, "reco_rpc_explore_topFans_adLive_gap", 4);
  // 商业化直播 gap 约束
  int adLive_adLive_gap =
      GetIntProcessorParameter(context, "reco_rpc_explore_adLive_adLive_gap", 4);

  // 自然直播直播付费人群 gap, limit 实验
  //// 人群 gap
  int new_gap = leafLive_leafLive_gap;
  if (live_revenue_crowd_ == "big_g") {
    new_gap = GetIntProcessorParameter(context, "reco_rpc_explore_live_big_g_user_gap", -1);
  } else if (live_revenue_crowd_ == "high_pay") {
    new_gap = GetIntProcessorParameter(context, "reco_rpc_explore_live_high_pay_user_gap", -1);
  } else if (live_revenue_crowd_ == "pay") {
    new_gap = GetIntProcessorParameter(context, "reco_rpc_explore_live_pay_user_gap", -1);
  } else if (live_revenue_crowd_ == "not_pay") {
    new_gap = GetIntProcessorParameter(context, "reco_rpc_explore_live_not_pay_user_gap", -1);
  }
  //// parse limit map
  std::string live_crowd_cold_start_limit_map_str =
    GetStringProcessorParameter(context, "reco_rpc_explore_live_crowd_cold_start_limit_map_str", "");
  std::string live_crowd_warm_start_limit_map_str =
    GetStringProcessorParameter(context, "reco_rpc_explore_live_crowd_warm_start_limit_map_str", "");
  //// 获取该人群的 limit
  int new_cold_start_limit =
    GetLiveCrowdIntValueFromMapStr(live_revenue_crowd_, live_crowd_cold_start_limit_map_str);
  int new_warm_start_limit =
    GetLiveCrowdIntValueFromMapStr(live_revenue_crowd_, live_crowd_warm_start_limit_map_str);
  //// 最终 assign
  leafLive_leafLive_gap = new_gap == -1 ? leafLive_leafLive_gap : new_gap;
  fresh_limit_leaf_live = new_cold_start_limit == -1 ? fresh_limit_leaf_live : new_cold_start_limit;
  warm_start_limit_leaf_live = new_warm_start_limit == -1 ? warm_start_limit_leaf_live : new_warm_start_limit;

  // 广告是否启用首刷前 4 限制, 可设定软硬广, -1 为使用原本的 limit
  if (is_new_fresh_limit_ad_) {
    int new_fresh_limit_ad =
      GetIntProcessorParameter(context, "reco_rpc_explore_new_fresh_limit_ad", -1);
    int new_fresh_limit_top_fans =
      GetIntProcessorParameter(context, "reco_rpc_explore_new_fresh_limit_top_fans", -1);
    int new_fresh_limit_ad_live =
      GetIntProcessorParameter(context, "reco_rpc_explore_new_fresh_limit_ad_live", -1);
    fresh_limit_ad = new_fresh_limit_ad == -1 ? fresh_limit_ad : new_fresh_limit_ad;

    // 软广对于冷启阶段的判断加一个开关
    bool enable_cold_start_new_fresh_limit_top_fans =
      GetBoolProcessorParameter(context, "reco_rpc_explore_enable_cold_start_new_fresh_limit_top_fans", true);
    if (!(is_fresh_req_ && !enable_cold_start_new_fresh_limit_top_fans)) {
      fresh_limit_top_fans = new_fresh_limit_top_fans == -1 ? fresh_limit_top_fans : new_fresh_limit_top_fans;
    }

    fresh_limit_ad_live = new_fresh_limit_ad_live == -1 ? fresh_limit_ad_live : new_fresh_limit_ad_live;
  }
  // 确认是否是非低活低消用户
  if (is_new_fresh_limit_ad_other_) {
    // 取非低活低消人群的参数
    int new_fresh_limit_ad =
      GetIntProcessorParameter(context, "reco_rpc_explore_new_fresh_limit_ad_other", -1);
    int new_fresh_limit_top_fans =
      GetIntProcessorParameter(context, "reco_rpc_explore_new_fresh_limit_top_fans_other", -1);
    int new_fresh_limit_ad_live =
      GetIntProcessorParameter(context, "reco_rpc_explore_new_fresh_limit_ad_live", -1);
    fresh_limit_ad = new_fresh_limit_ad == -1 ? fresh_limit_ad : new_fresh_limit_ad;
    fresh_limit_top_fans = new_fresh_limit_top_fans == -1 ? fresh_limit_top_fans : new_fresh_limit_top_fans;
    fresh_limit_ad_live = new_fresh_limit_ad_live == -1 ? fresh_limit_ad_live : new_fresh_limit_ad_live;
  }

  // leafLive gap
  gap_conf_.insert(std::make_pair("leafLive_leafLive", leafLive_leafLive_gap));
  gap_conf_.insert(std::make_pair("leafLive_merchantLive", leafLive_merchantLive_gap));
  gap_conf_.insert(std::make_pair("leafLive_ad", leafLive_ad_gap));
  gap_conf_.insert(std::make_pair("leafLive_topFans", leafLive_topFans_gap));
  gap_conf_.insert(std::make_pair("leafLive_adLive", leafLive_adLive_gap));
  // 电商 1pp gap
  gap_conf_.insert(std::make_pair("merchantLive_merchantLive", merchantLive_merchantLive_gap));
  gap_conf_.insert(std::make_pair("merchantLive_ad", merchantLive_ad_gap));
  gap_conf_.insert(std::make_pair("merchantLive_topFans", merchantLive_topFans_gap));
  gap_conf_.insert(std::make_pair("merchantLive_adLive", merchantLive_adLive_gap));
  // 硬广 gap
  gap_conf_.insert(std::make_pair("ad_ad", ad_ad_gap));
  gap_conf_.insert(std::make_pair("ad_topFans", ad_topFans_gap));
  gap_conf_.insert(std::make_pair("ad_adLive", ad_adLive_gap));
  // 粉条 gap
  gap_conf_.insert(std::make_pair("topFans_topFans", topFans_topFans_gap));
  gap_conf_.insert(std::make_pair("topFans_adLive", topFans_adLive_gap));
  // 商业化直播 gap
  gap_conf_.insert(std::make_pair("adLive_adLive", adLive_adLive_gap));
  // 冷启首刷限制
  gap_conf_.insert(std::make_pair("fresh_limit_leaf_live", fresh_limit_leaf_live - 1));
  gap_conf_.insert(std::make_pair("fresh_limit_merchant_live", fresh_limit_merchant_live - 1));
  gap_conf_.insert(std::make_pair("fresh_limit_ad", fresh_limit_ad - 1));
  gap_conf_.insert(std::make_pair("fresh_limit_top_fans", fresh_limit_top_fans - 1));
  gap_conf_.insert(std::make_pair("fresh_limit_ad_live", fresh_limit_ad_live - 1));
  // 热启首刷限制
  gap_conf_.insert(std::make_pair("warm_start_limit_leaf_live", warm_start_limit_leaf_live - 1));
  // 填充 gap 相关配置， 透传到二阶段
  if (!gap_conf_.empty()) {
    MixCtx().SetUniverseGapConfig(gap_conf_);
  }
}

int HotMixAuctionSeqRetrieval::GetLiveCrowdIntValueFromMapStr(const std::string &crowd,
                                                              const std::string &input_string) {
  folly::F14FastMap<std::string, int> parse_map;
  ParseStringIntMap(input_string, &parse_map);
  if (parse_map.count(crowd) > 0) {
    return parse_map.at(crowd);
  }
  return -1;
}

void HotMixAuctionSeqRetrieval::ParseStringIntMap(const std::string &input_string,
                                                  folly::F14FastMap<std::string, int> *parse_map) {
  // input_string 格式："k1:v1;k2:v2..."
  std::vector<std::string> pair_strs;
  std::vector<std::string> pair;
  int val = -1;
  base::SplitStringWithOptions(input_string, ";", true, true, &pair_strs);
  for (const std::string &pair_str : pair_strs) {
    pair.clear();
    base::SplitStringWithOptions(pair_str, ":", true, true, &pair);
    if (pair.size() != 2) {
      LOG_EVERY_N(ERROR, 10000) << "HotMixAuctionSeqRetrieval::ParseStringIntMap: "
        << "pair size != 2";
      continue;
    }
    if (!absl::SimpleAtoi(pair[1], &val)) {
      LOG_EVERY_N(ERROR, 10000) << "HotMixAuctionSeqRetrieval::ParseStringIntMap: "
        << "fail to parse int";
      continue;
    }
    parse_map->insert(std::make_pair(pair[0], val));
  }
}

void HotMixAuctionSeqRetrieval::CalStaticBannedPos(AddibleRecoContextInterface *context,
                                        std::map<PermItemType, int> *min_available_idx_map) {
  /*
    序列召回 - 初始化静态位置限制
    静态限制是指 由于刷间间隔和 fresh 刷次规则造成的位置限制，在序列召回过程中此限制不会动态变化
    将所有待插入业务的 item 的最高可透出位置存入 min_available_idx_map
  */
  bool is_fresh_req = is_fresh_req_;
  bool is_fresh_req_ad = is_fresh_req || (is_new_fresh_ad_warm_start_ && is_new_fresh_limit_ad_);
  bool is_warm_start = is_warm_start_;
  int limit_pos = 0;

  int fresh_limit_leaf_live = is_fresh_req ? GetGapInterValue("fresh_limit_leaf_live") : limit_pos;
  int fresh_limit_merchant_live = is_fresh_req ? GetGapInterValue("fresh_limit_merchant_live") : limit_pos;
  int fresh_limit_ad = is_fresh_req_ad ? GetGapInterValue("fresh_limit_ad") : limit_pos;
  int fresh_limit_top_fans = is_fresh_req_ad ? GetGapInterValue("fresh_limit_top_fans") : limit_pos;
  int fresh_limit_ad_live = is_fresh_req_ad ? GetGapInterValue("fresh_limit_ad_live") : limit_pos;

  // 冷启时 warm_start 也会是 true，在这里排除掉。(非冷启的首刷)
  if (is_warm_start && !is_fresh_req) {
    fresh_limit_leaf_live = GetGapInterValue("warm_start_limit_leaf_live");
  }

  // 获取历史刷次各个业务的 pos
  int pre_ad = GetHistoryPreMapValue(TYPE_DSP);
  int pre_top_fans = GetHistoryPreMapValue(TYPE_TOP_FANS);
  int pre_live = GetHistoryPreMapValue(TYPE_NORMAL_LIVE);
  int pre_merchant_live = GetHistoryPreMapValue(TYPE_MERCHANT_LIVE);
  int pre_ad_live = GetHistoryPreMapValue(TYPE_AD_LIVE);

  // leafLive 静态位
  int live_idx_from_live = GetGapInterValue("leafLive_leafLive") - pre_live;
  int live_idx_from_merchant = GetGapInterValue("leafLive_merchantLive") - pre_merchant_live;
  int live_idx_from_ad = GetGapInterValue("leafLive_ad") - pre_ad;
  int live_idx_from_top_fans = GetGapInterValue("leafLive_topFans") - pre_top_fans;
  int live_idx_from_ad_live = GetGapInterValue("leafLive_adLive") - pre_ad_live;
  int live_min_idx = std::max({0, fresh_limit_leaf_live,
                                    live_idx_from_live, live_idx_from_merchant,
                                    live_idx_from_ad, live_idx_from_top_fans,
                                    live_idx_from_ad_live});
  min_available_idx_map->insert(std::make_pair(TYPE_NORMAL_LIVE,
                                live_min_idx));
  // merchantLive 静态位
  int merchant_live_idx_from_merchant = GetGapInterValue("merchantLive_merchantLive") - pre_merchant_live;
  int merchant_live_idx_from_live = GetGapInterValue("leafLive_merchantLive") - pre_live;
  int merchant_live_idx_from_ad = GetGapInterValue("merchantLive_ad") - pre_ad;
  int merchant_live_idx_from_top_fans = GetGapInterValue("merchantLive_topFans") - pre_top_fans;
  int merchant_live_idx_from_ad_live = GetGapInterValue("merchantLive_adLive") - pre_ad_live;
  int merchant_live_min_idx = std::max({0, fresh_limit_merchant_live,
                                    merchant_live_idx_from_merchant, merchant_live_idx_from_live,
                                    merchant_live_idx_from_ad, merchant_live_idx_from_top_fans,
                                    merchant_live_idx_from_ad_live});
  min_available_idx_map->insert(std::make_pair(TYPE_MERCHANT_LIVE,
                                merchant_live_min_idx));
  // ad 静态位
  int ad_idx_from_merchant = GetGapInterValue("merchantLive_ad") - pre_merchant_live;
  int ad_idx_from_live = GetGapInterValue("leafLive_ad") - pre_live;
  int ad_idx_from_ad = GetGapInterValue("ad_ad") - pre_ad;
  int ad_idx_from_top_fans = GetGapInterValue("ad_topFans") - pre_top_fans;
  int ad_idx_from_ad_live = GetGapInterValue("ad_adLive") - pre_ad_live;
  int ad_min_idx = std::max({0, fresh_limit_ad, ad_pos_no_0_limit_,
                                    ad_idx_from_merchant, ad_idx_from_live,
                                    ad_idx_from_ad, ad_idx_from_top_fans,
                                    ad_idx_from_ad_live});
  min_available_idx_map->insert(std::make_pair(TYPE_DSP, ad_min_idx));
  // top fans 静态位
  int top_fans_idx_from_merchant = GetGapInterValue("merchantLive_topFans") - pre_merchant_live;
  int top_fans_idx_from_live = GetGapInterValue("leafLive_topFans") - pre_live;
  int top_fans_idx_from_ad = GetGapInterValue("ad_topFans") - pre_ad;
  int top_fans_idx_from_top_fans = GetGapInterValue("topFans_topFans") - pre_top_fans;
  int top_fans_idx_from_ad_live = GetGapInterValue("topFans_adLive") - pre_ad_live;
  int top_fans_min_idx = std::max({0, fresh_limit_top_fans,
                                    top_fans_idx_from_merchant, top_fans_idx_from_live,
                                    top_fans_idx_from_ad, top_fans_idx_from_top_fans,
                                    top_fans_idx_from_ad_live});
  min_available_idx_map->insert(std::make_pair(TYPE_TOP_FANS,
                                top_fans_min_idx));
  // ad live 静态位
  int ad_live_idx_from_merchant = GetGapInterValue("merchantLive_adLive") - pre_merchant_live;
  int ad_live_idx_from_live = GetGapInterValue("leafLive_adLive") - pre_live;
  int ad_live_idx_from_ad = GetGapInterValue("ad_adLive") - pre_ad;
  int ad_live_idx_from_top_fans = GetGapInterValue("topFans_adLive") - pre_top_fans;
  int ad_live_idx_from_ad_live = GetGapInterValue("adLive_adLive") - pre_ad_live;
  int ad_live_min_idx = std::max({0, fresh_limit_ad_live,
                                    ad_live_idx_from_merchant, ad_live_idx_from_live,
                                    ad_live_idx_from_ad, ad_live_idx_from_top_fans,
                                    ad_live_idx_from_ad_live});
  min_available_idx_map->insert(std::make_pair(TYPE_AD_LIVE,
                                ad_live_min_idx));
}

int HotMixAuctionSeqRetrieval::GetGapInterValue(const std::string &gap_key) {
  if (gap_conf_.count(gap_key) > 0) {
    return gap_conf_.at(gap_key);
  } else {
    return 0;
  }
}

int HotMixAuctionSeqRetrieval::GetHistoryPreMapValue(const PermItemType target_type) {
  return (historical_pre_pos_map_.count(target_type) > 0) ?
         historical_pre_pos_map_.at(target_type) : 0;
}

void HotMixAuctionSeqRetrieval::GetMixHistory(AddibleRecoContextInterface *context) {
  /*
   从混排历史中读取所有业务的最后下发位置, 存入 historical_pre_pos_map_, 供全局使用
  */
  std::vector<PermItemType> type_key_list = {TYPE_DSP, TYPE_TOP_FANS,
                                    TYPE_MERCHANT_LIVE, TYPE_NORMAL_LIVE,
                                    TYPE_AD_LIVE};
  for (int i = 0; i < type_key_list.size(); i++) {
    historical_pre_pos_map_.insert(std::make_pair(type_key_list.at(i), DEFAULT_PRE_POS_));
  }
  bool open_history_gap_control = GetBoolProcessorParameter(context,
        "reco_rpc_hot_mix_open_history_gap_control", false);
  // fresh 刷次及空历史时无需读取混排下发历史
  const auto &mix_result_history = MixCtx().MixRankResultHistory();
  if (!open_history_gap_control || is_fresh_req_ || (mix_result_history.size() < 1)) return;

  // 统计历史刷次位置
  int gap = 0;
  for (auto history_list = mix_result_history.rbegin(); history_list != mix_result_history.rend();
      ++history_list) {
    for (auto history_meta = history_list->rbegin(); history_meta != history_list->rend(); ++history_meta) {
      auto meta = (*history_meta)->data();
      if (meta.ad_mixed_source_type() == mix::kuaishou::ad::AdMixedInfo::AD_DSP) {
        if (historical_pre_pos_map_.count(TYPE_DSP) > 0 &&
                    historical_pre_pos_map_.at(TYPE_DSP) == DEFAULT_PRE_POS_) {
          historical_pre_pos_map_.at(TYPE_DSP) = gap;
        }
        if (historical_pre_pos_map_.count(TYPE_AD_LIVE) > 0 &&
                    historical_pre_pos_map_.at(TYPE_AD_LIVE) == DEFAULT_PRE_POS_) {
          historical_pre_pos_map_.at(TYPE_AD_LIVE) = gap;
        }
      } else if (meta.ad_mixed_source_type() == mix::kuaishou::ad::AdMixedInfo::AD_FANS_TOP ||
                 meta.ad_mixed_source_type() == mix::kuaishou::ad::AdMixedInfo::AD_FAKE_SOFT_TYPE) {
        if (historical_pre_pos_map_.count(TYPE_TOP_FANS) > 0 &&
                historical_pre_pos_map_.at(TYPE_TOP_FANS) == DEFAULT_PRE_POS_) {
          historical_pre_pos_map_.at(TYPE_TOP_FANS) = gap;
        }
        if (historical_pre_pos_map_.count(TYPE_AD_LIVE) > 0 &&
                    historical_pre_pos_map_.at(TYPE_AD_LIVE) == DEFAULT_PRE_POS_) {
          historical_pre_pos_map_.at(TYPE_AD_LIVE) = gap;
        }
      } else if (meta.type() == mix::kuaishou::reco::MixRecoResult::ResultCase::kMerchantExploreLive) {
        // 电商 1pp 直播
        if (historical_pre_pos_map_.count(TYPE_MERCHANT_LIVE) > 0 &&
            historical_pre_pos_map_.at(TYPE_MERCHANT_LIVE) == DEFAULT_PRE_POS_) {
          historical_pre_pos_map_.at(TYPE_MERCHANT_LIVE) = gap;
        }
      } else if (meta.type() == mix::kuaishou::reco::MixRecoResult::ResultCase::kRecoLivestream) {
        // 自然直播 / 直播 1pp / 冬奥
        if (historical_pre_pos_map_.count(TYPE_NORMAL_LIVE) > 0 &&
              historical_pre_pos_map_.at(TYPE_NORMAL_LIVE) == DEFAULT_PRE_POS_) {
          historical_pre_pos_map_.at(TYPE_NORMAL_LIVE) = gap;
        }
      }
      gap++;
    }
    // check 下是否需要继续遍历
    bool continue_flag = false;
    for (int i = 0; i < type_key_list.size(); i++) {
      if (historical_pre_pos_map_.count(type_key_list.at(i)) > 0
          && historical_pre_pos_map_.at(type_key_list.at(i)) == DEFAULT_PRE_POS_) {
        continue_flag = true;
        break;
      }
    }
    if (!continue_flag) break;
  }
}

void HotMixAuctionSeqRetrieval::InitItemToInsert(std::vector<PermItemType> *item_to_insert,
                                                     AddibleRecoContextInterface *context) {
  bool enable_ad_live = GetBoolProcessorParameter(context, "enable_hot_mix_ad_live", false);
  bool enable_hot_mix_calc_score_opt = GetBoolProcessorParameter(context,
    "enable_hot_mix_calc_score_opt", false);
  if (origin_inserted_item_list_.size() < 1) return;
  reverse(origin_inserted_item_list_.begin(), origin_inserted_item_list_.end());

  for (int i = 0; i < origin_inserted_item_list_.size(); ++i) {
    MixRankResult &item = origin_inserted_item_list_.at(i);
    const auto& result_case = item.RawResultCase();
    switch (result_case) {
      case MixRecoResult::ResultCase::kRecoLivestream:
        item_to_insert->push_back(TYPE_NORMAL_LIVE);
        break;
      case MixRecoResult::ResultCase::kMerchantExploreLive:
        item_to_insert->push_back(TYPE_MERCHANT_LIVE);
        break;
      case MixRecoResult::ResultCase::kAdDsp:
        if (enable_ad_live && enable_hot_mix_calc_score_opt && IsLiveItem(item)) {
          item_to_insert->push_back(TYPE_AD_LIVE);
        } else if ((!enable_fountain_top_fans_ && item.IsFansTop())
            || (enable_fountain_top_fans_ && item.IsFountainFansTop())) {
          item_to_insert->push_back(TYPE_TOP_FANS);
        } else {
          item_to_insert->push_back(TYPE_DSP);
        }
        break;
      default:
        // do nothing
        break;
    }
    if (item_to_insert->size() > 0) {
      item.SetPermItemType(item_to_insert->back());
    }
  }
  return;
}

void HotMixAuctionSeqRetrieval::InitCandidateSequence(
    AddibleRecoContextInterface *context,
    std::vector<PermItemType> item_to_insert) {
  HotCandidateSequence init_seq;
  auto &rank_results = MixCtx().MixResults();
  int real_size = (int)rank_results.Size();
  for (int i = 0; i < real_size; i++) {
    init_seq.cand_seq.push_back(TYPE_RECO_PHOTO);
    auto &reco_item = MixCtx().MixResults().At(i);
    if (init_seq.cand_seq.size() > 0) {
      reco_item.SetPermItemType(init_seq.cand_seq.back());
    }
  }
  init_seq.live_num = 0;
  init_seq.dsp_num = 0;
  init_seq.is_base_seq = true;
  init_seq.reco_num = init_seq.cand_seq.size();

  std::vector<PermItemType> all_insert_type(item_to_insert.begin(), item_to_insert.end());
  all_insert_type.insert(all_insert_type.begin(), init_seq.cand_seq.begin(), init_seq.cand_seq.end());
  gm_.SetGapConfig(gap_conf_);
  gm_.SetInsertCandicate(all_insert_type);

  CalDynamicBannedPos(&init_seq);

  mix_list_retrieval_.push_back(init_seq);
  return;
}

void HotMixAuctionSeqRetrieval::GapManager::SetGapConfig(const std::map<std::string, int> &gap_conf) {
  std::function<int(std::string)> GetGap = [&gap_conf](std::string key) {
    auto iter = gap_conf.find(key);
    if (iter != gap_conf.end()) {
      return iter->second;
    } else {
      return 0;
    }
  };

  this->leafLive_leafLive = GetGap("leafLive_leafLive");
  this->leafLive_merchantLive = GetGap("leafLive_merchantLive");
  this->leafLive_ad = GetGap("leafLive_ad");
  this->leafLive_topFans = GetGap("leafLive_topFans");
  this->leafLive_adLive = GetGap("leafLive_adLive");
  this->merchantLive_merchantLive = GetGap("merchantLive_merchantLive");
  this->merchantLive_ad = GetGap("merchantLive_ad");
  this->merchantLive_topFans = GetGap("merchantLive_topFans");
  this->merchantLive_adLive = GetGap("merchantLive_adLive");
  this->ad_ad = GetGap("ad_ad");
  this->ad_topFans = GetGap("ad_topFans");
  this->ad_adLive = GetGap("ad_adLive");
  this->topFans_topFans = GetGap("topFans_topFans");
  this->topFans_adLive = GetGap("topFans_adLive");
  this->adLive_adLive = GetGap("adLive_adLive");
}

void HotMixAuctionSeqRetrieval::GapManager::SetInsertCandicate(
    const std::vector<PermItemType> &candidate_type) {
  std::unordered_set<PermItemType> type_set(candidate_type.begin(), candidate_type.end());
  // dynamic_live_banned_idx
  this->has_leaf_live = type_set.find(TYPE_NORMAL_LIVE) != type_set.end();
  // dynamic_merchant_banned_idx
  this->has_merchant_live = type_set.find(TYPE_MERCHANT_LIVE) != type_set.end();
  // dynamic_ad_banned_idx
  this->has_dsp_photo = type_set.find(TYPE_DSP) != type_set.end();
  // dynamic_ad_banned_idx
  this->has_top_fans_photo = type_set.find(TYPE_TOP_FANS) != type_set.end();
  // dynamic_ad_live_banned_idx
  this->has_ad_live = type_set.find(TYPE_AD_LIVE) != type_set.end();
}

void HotMixAuctionSeqRetrieval::CalDynamicBannedPos(HotCandidateSequence *candidate_sequence) {
  // 记录序列中的业务位置
  std::vector<int> leaf_live_idx_list;
  std::vector<int> merchant_idx_list;
  std::vector<int> ad_idx_list;
  std::vector<int> top_fans_idx_list;
  std::vector<int> ad_live_idx_list;
  int seq_size = candidate_sequence->cand_seq.size();
  for (int i = 0; i < seq_size; i++) {
    PermItemType &item = candidate_sequence->cand_seq.at(i);
    if (item == TYPE_DSP) {
      ad_idx_list.push_back(i);
    } else if (item == TYPE_NORMAL_LIVE) {
      leaf_live_idx_list.push_back(i);
    } else if (item == TYPE_MERCHANT_LIVE) {
      merchant_idx_list.push_back(i);
    } else if (item == TYPE_TOP_FANS) {
      top_fans_idx_list.push_back(i);
    } else if (item == TYPE_AD_LIVE) {
      ad_live_idx_list.push_back(i);
    }
  }

  // 存储序列各业务的 BannedPos
  Set<int> leaf_live_banned_idx;
  Set<int> merchant_banned_idx;
  Set<int> ad_banned_idx;
  Set<int> top_fans_banned_idx;
  Set<int> ad_live_banned_idx;
  // 依据 leafLive 计算 BannedPos
  for (auto idx : leaf_live_idx_list) {
    CalBannedPosHelper(idx, gm_.leafLive_leafLive, seq_size, &leaf_live_banned_idx, gm_.has_leaf_live);
    CalBannedPosHelper(idx, gm_.leafLive_merchantLive, seq_size, &merchant_banned_idx,
                            gm_.has_merchant_live);
    CalBannedPosHelper(idx, gm_.leafLive_ad, seq_size, &ad_banned_idx, gm_.has_dsp_photo);
    CalBannedPosHelper(idx, gm_.leafLive_topFans, seq_size, &top_fans_banned_idx, gm_.has_top_fans_photo);
    CalBannedPosHelper(idx, gm_.leafLive_adLive, seq_size, &ad_live_banned_idx, gm_.has_ad_live);
  }
  // 依据 merchantLive 计算 BannedPos
  for (auto idx : merchant_idx_list) {
    CalBannedPosHelper(idx, gm_.leafLive_merchantLive, seq_size, &leaf_live_banned_idx, gm_.has_leaf_live);
    CalBannedPosHelper(idx, gm_.merchantLive_merchantLive, seq_size, &merchant_banned_idx,
                            gm_.has_merchant_live);
    CalBannedPosHelper(idx, gm_.merchantLive_ad, seq_size, &ad_banned_idx, gm_.has_dsp_photo);
    CalBannedPosHelper(idx, gm_.merchantLive_topFans, seq_size, &top_fans_banned_idx, gm_.has_top_fans_photo);
    CalBannedPosHelper(idx, gm_.merchantLive_adLive, seq_size, &ad_live_banned_idx, gm_.has_ad_live);
  }
  // 依据 addsp 计算 BannedPos
  for (auto idx : ad_idx_list) {
    CalBannedPosHelper(idx, gm_.leafLive_ad, seq_size, &leaf_live_banned_idx, gm_.has_leaf_live);
    CalBannedPosHelper(idx, gm_.merchantLive_ad, seq_size, &merchant_banned_idx, gm_.has_merchant_live);
    CalBannedPosHelper(idx, gm_.ad_ad, seq_size, &ad_banned_idx, gm_.has_dsp_photo);
    CalBannedPosHelper(idx, gm_.ad_topFans, seq_size, &top_fans_banned_idx, gm_.has_top_fans_photo);
    CalBannedPosHelper(idx, gm_.ad_adLive, seq_size, &ad_live_banned_idx, gm_.has_ad_live);
  }

  // 依据 topFans 计算 BannedPos
  for (auto idx : top_fans_idx_list) {
    CalBannedPosHelper(idx, gm_.leafLive_topFans, seq_size, &leaf_live_banned_idx, gm_.has_leaf_live);
    CalBannedPosHelper(idx, gm_.merchantLive_topFans, seq_size, &merchant_banned_idx, gm_.has_merchant_live);
    CalBannedPosHelper(idx, gm_.ad_topFans, seq_size, &ad_banned_idx, gm_.has_dsp_photo);
    CalBannedPosHelper(idx, gm_.topFans_topFans, seq_size, &top_fans_banned_idx, gm_.has_top_fans_photo);
    CalBannedPosHelper(idx, gm_.topFans_adLive, seq_size, &ad_live_banned_idx, gm_.has_ad_live);
  }

  // 依据 adLive 计算 BannedPos
  for (auto idx : ad_live_idx_list) {
    CalBannedPosHelper(idx, gm_.leafLive_adLive, seq_size, &leaf_live_banned_idx, gm_.has_leaf_live);
    CalBannedPosHelper(idx, gm_.merchantLive_adLive, seq_size, &merchant_banned_idx, gm_.has_merchant_live);
    CalBannedPosHelper(idx, gm_.ad_adLive, seq_size, &ad_banned_idx, gm_.has_dsp_photo);
    CalBannedPosHelper(idx, gm_.topFans_adLive, seq_size, &top_fans_banned_idx, gm_.has_top_fans_photo);
    CalBannedPosHelper(idx, gm_.adLive_adLive, seq_size, &ad_live_banned_idx, gm_.has_ad_live);
  }

  // 注意这里确保 move 后 banner_idx 不再使用
  candidate_sequence->dynamic_banned_idx_map.insert(
      std::make_pair(TYPE_NORMAL_LIVE, leaf_live_banned_idx));
  candidate_sequence->dynamic_banned_idx_map.insert(
      std::make_pair(TYPE_MERCHANT_LIVE, std::move(merchant_banned_idx)));
  candidate_sequence->dynamic_banned_idx_map.insert(std::make_pair(TYPE_DSP, std::move(ad_banned_idx)));
  candidate_sequence->dynamic_banned_idx_map.insert(std::make_pair(TYPE_TOP_FANS,
                                                    std::move(top_fans_banned_idx)));
  candidate_sequence->dynamic_banned_idx_map.insert(std::make_pair(TYPE_AD_LIVE,
                                                    std::move(ad_live_banned_idx)));
  return;
}

void HotMixAuctionSeqRetrieval::CalBannedPosHelper(int idx, int interval_limit, int seq_size,
                                                         Set<int> *banned_pos_set, bool need_calc) {
  if (!need_calc) {
    return;
  }
  if (interval_limit < 1) return;
  // 保序, 查找 idx 后面的 bannedPos 即可
  int max_idx = std::min(seq_size, idx + interval_limit);
  int min_idx = std::max(0, idx - interval_limit + 1);
  int start_idx = min_idx;
  for (int i = start_idx; i <= max_idx; i++) {
    banned_pos_set->insert(i);
  }
  return;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, HotMixAuctionSeqRetrieval, HotMixAuctionSeqRetrieval);

}  // namespace platform
}  // namespace ks

