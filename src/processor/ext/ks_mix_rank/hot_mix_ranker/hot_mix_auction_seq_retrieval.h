#pragma once
#include <set>
#include <string>
#include <utility>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <map>
#include <memory>

#include "folly/container/F14Map.h"
#include "ks/serving_util/infra_redis_client.h"
#include "ks/reco_proto/mix_rank/mix_rank.pb.h"
#include "dragon/src/processor/ext/ks_mix_rank/util/mix_rank_util.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/hot_mix_rank_context.h"
#include "dragon/src/processor/ext/ks_mix_rank/base/mix_rank_base_processor.h"
#include "dragon/src/processor/ext/ks_mix_rank/hot_mix_ranker/hot_mix_rank_item_score.h"

namespace ks {
namespace platform {

class HotMixAuctionSeqRetrieval : public BaseMixRankProcessor {
  template <typename Key, typename Value>
  using Map = folly::F14FastMap<Key, Value>;
  template <typename Value>
  using Set = folly::F14FastSet<Value>;

 public:
  HotMixAuctionSeqRetrieval() {}

  /**
   * @brief 返回 RESULT_NOT_SET ，表示处理所有类型的结果
   *
   * @return RESULT_NOT_SET
   */
  std::vector<MixRecoResult::ResultCase> ProcessItemType() {
    return {MixRecoResult::ResultCase::RESULT_NOT_SET};
  }

  // 混排处理 Live
  void MixRank(AddibleRecoContextInterface *context);

 private:
  void Clear() {
    mix_list_retrieval_.clear();
    gap_conf_.clear();
    origin_reco_photo_list_.clear();
    origin_normal_live_list_.clear();
    origin_merchant_live_list_.clear();
    origin_dsp_list_.clear();
    origin_inserted_item_list_.clear();
    historical_pre_pos_map_.clear();
  }

 private:
  struct GapManager {
    void SetGapConfig(const std::map<std::string, int>& gap_conf);
    void SetInsertCandicate(const std::vector<PermItemType> &candidate_type);

    int leafLive_leafLive = 0;
    int leafLive_merchantLive = 0;
    int leafLive_ad = 0;
    int leafLive_topFans = 0;
    int leafLive_adLive = 0;
    int merchantLive_merchantLive = 0;
    int merchantLive_ad = 0;
    int merchantLive_topFans = 0;
    int merchantLive_adLive = 0;
    int ad_ad = 0;
    int ad_topFans = 0;
    int ad_adLive = 0;
    int topFans_topFans = 0;
    int topFans_adLive = 0;
    int adLive_adLive = 0;

    // 参与序列生成的子业务类型
    bool has_leaf_live = false;
    bool has_merchant_live = false;
    bool has_dsp_photo = false;
    bool has_top_fans_photo = false;
    bool has_ad_live = false;
  };
  friend GapManager;

 private:
  void Init(AddibleRecoContextInterface *context);
  void SortAndMerge(AddibleRecoContextInterface *context);
  void MergeItemList(std::vector<MixRankResult> *origin_item_list);
  void AuctionObserver(AddibleRecoContextInterface *context, std::string stage);
  void RetrieveMixList(AddibleRecoContextInterface *context);
  void LoadAllGapConf(AddibleRecoContextInterface *context);
  void CalStaticBannedPos(AddibleRecoContextInterface *context,
                                        std::map<PermItemType, int> *min_available_idx_map);
  void InitItemToInsert(std::vector<PermItemType> *item_to_insert,
                                                     AddibleRecoContextInterface *context);
  void InitCandidateSequence(
    AddibleRecoContextInterface *context,
    std::vector<PermItemType> item_to_insert);
  void CalDynamicBannedPos(HotCandidateSequence *candidate_sequence);
  void CalBannedPosHelper(int idx, int interval_limit, int seq_size,
                                                         Set<int> *banned_pos_set, bool need_calc);
  int GetGapInterValue(const std::string &gap_key);
  void FilterRetrieveMixList(AddibleRecoContextInterface *context);
  void GetMixHistory(AddibleRecoContextInterface *context);
  int GetHistoryPreMapValue(const PermItemType target_type);
  void ParseStringIntMap(const std::string &input_string, folly::F14FastMap<std::string, int> *parse_map);
  int GetLiveCrowdIntValueFromMapStr(const std::string &crowd, const std::string &input_string);

 private:
  bool is_fresh_req_ = false;
  bool is_warm_start_ = false;
  bool is_new_fresh_limit_ad_ = false;
  bool is_new_fresh_ad_warm_start_ = false;
  bool is_new_fresh_limit_ad_other_ = false;
  bool enable_fountain_top_fans_ = false;
  std::string live_revenue_crowd_ = "";
  std::map<std::string, int> gap_conf_;
  std::vector<HotCandidateSequence> mix_list_retrieval_;
  std::vector<MixRankResult> origin_reco_photo_list_;
  std::vector<MixRankResult> origin_normal_live_list_;
  std::vector<MixRankResult> origin_merchant_live_list_;
  std::vector<MixRankResult> origin_dsp_list_;
  std::vector<MixRankResult> origin_inserted_item_list_;
  int auction_inserted_max_num_ = 5;
  bool is_debug_ = false;
  int ad_pos_no_0_limit_ = 0;
  int DEFAULT_PRE_POS_ = 100;
  GapManager gm_;
  std::map<PermItemType, int> historical_pre_pos_map_;

  DISALLOW_COPY_AND_ASSIGN(HotMixAuctionSeqRetrieval);
};

}  // namespace platform
}  // namespace ks
