#pragma once

#include <string>

#include "dragon/src/processor/ext/livestream/module/filter/base_filter.h"

#include "dragon/src/processor/ext/livestream/util/grade_distribute_matcher_v2.h"
#include "dragon/src/processor/ext/livestream/util/grade_distribute_util_v2.h"

namespace ks {
namespace platform {
namespace livestream {

class NewGradeLabelSysFilter : public livestream::BaseFilter {
 public:
  NewGradeLabelSysFilter() {}
  void Init(
      ReadableRecoContextInterface *context,
      const base::Json *config) override;
  bool ShouldRemove(const CommonRecoResult &result) override;

 private:
  int64 uinfo_is_reflux_ = 0;
  int64 uinfo_is_la_ = 0;
  int64 uinfo_is_hp_ = 0;
  std::string grade_distribute_rule_version_ = "default";
  int64 grade_distribute_item_label_version_ = 0;
  int64 temp_grade_dis_effect_way_ = 0;
  std::string grade_label_rules_kconf_str_;
  std::string grade_label_cate_kconf_str_;
  UserLabel cur_user_label_;
  bool no_need_filter_ = false;
  int64 enable_new_grade_label_sys_aid_ = 0;
  GradeDistributeMatcherV2 matcher_;

  bool no_need_filter2_ = false;
  GradeDistributeMatcherV2 matcher2_;
  std::string grade_distribute_rule_version2_ = "default";
  int64 grade_distribute_item_label_version2_ = 0;
  int64 enable_follow_skip_new_grade_filter_ = 0;
  folly::F14FastSet<int64> follow_aid_list_;
  int64 enable_markcode_new_grade_filter_ = 0;
  folly::F14FastSet<int64> markcode_list_;
  int64 enable_sepcific_markcode_skip_new_grade_filter_ = 0;
  folly::F14FastSet<int64> markcode_skip_list_;
  int64 enable_has_markcode_skip_new_grade_filter_ = 0;
  double sepcific_markcode_random_skip_new_grade_filter_ratio_ = 1.0;
  double has_markcode_random_skip_new_grade_filter_ratio_ = 1.0;
  base::PseudoRandom random_;
  int64 enable_new_grade_label_exemption_ = 0;
  folly::F14FastSet<int64> new_grade_label_exemption_set_;
  int64 enable_sensitive_user_with_some_markcode_disable_new_grade_label_exemption_ = 0;
  int64 is_sensitive_user_disable_new_grade_label_exemption_ = 0;
  folly::F14FastSet<int64> sensitive_user_disable_new_grade_label_exemption_markcode_list_;
  DISALLOW_COPY_AND_ASSIGN(NewGradeLabelSysFilter);
};

}  // namespace livestream
}  // namespace platform
}  // namespace ks
