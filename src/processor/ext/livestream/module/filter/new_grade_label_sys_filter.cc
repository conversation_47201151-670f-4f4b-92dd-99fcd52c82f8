#include "dragon/src/processor/ext/livestream/module/filter/new_grade_label_sys_filter.h"

#include <utility>
#include <vector>

namespace ks {
namespace platform {
namespace livestream {

void NewGradeLabelSysFilter::Init(
    ReadableRecoContextInterface *context, const base::Json *config) {
  uinfo_is_reflux_ = 0;
  uinfo_is_la_ = 0;
  uinfo_is_hp_ = 0;
  grade_distribute_rule_version_ = "default";
  grade_distribute_item_label_version_ = 0;
  no_need_filter_ = false;
  no_need_filter2_ = false;
  grade_distribute_rule_version2_ = "default";
  grade_distribute_item_label_version2_ = 0;

  auto uinfo_is_reflux_temp = livestream::GetIntCommonAttr(
      context, config, "uinfo_is_reflux_temp_attr").value_or(-1);
  if (uinfo_is_reflux_temp == 3) {
    uinfo_is_reflux_ = 1;
  }
  auto uinfo_is_la_temp = livestream::GetIntCommonAttr(
      context, config, "uinfo_is_la_temp_attr").value_or(-1);
  if (uinfo_is_la_temp == 0) {
    uinfo_is_la_ = 1;
  }
  auto uinfo_is_hp_temp = livestream::GetIntCommonAttr(
      context, config, "uinfo_is_hp_temp_attr");
  if (uinfo_is_hp_temp.has_value() && uinfo_is_hp_temp.value() != 0) {
    uinfo_is_hp_ = 1;
  }
  auto enable_new_grade_label_sys_use_new_version = livestream::GetIntCommonAttr(
      context, config, "enable_new_grade_label_sys_use_new_version_attr").value_or(0);
  if (enable_new_grade_label_sys_use_new_version == 1) {
    grade_distribute_rule_version_ = static_cast<std::string>(livestream::GetStringCommonAttr(
        context, config, "new_grade_label_sys_rule_version_attr").value_or("new_grade_sys"));
    grade_distribute_item_label_version_ = livestream::GetIntCommonAttr(
        context, config, "new_grade_label_sys_item_version_attr").value_or(4);
  }
  temp_grade_dis_effect_way_ = livestream::GetIntCommonAttr(
      context, config, "temp_grade_dis_effect_way_attr").value_or(0);
  grade_label_rules_kconf_str_ = static_cast<std::string>(livestream::GetStringCommonAttr(
      context, config, "grade_label_rules_kconf_str_attr").value_or("reco.live.onlineLiveGradeLabelRules"));
  grade_label_cate_kconf_str_  = static_cast<std::string>(livestream::GetStringCommonAttr(
      context, config, "grade_label_cate_kconf_str_attr")
      .value_or("audit.liveStream.auditLiveGradingTagInfo"));
  cur_user_label_.gender = livestream::GetIntCommonAttr(context, config, "uinfo_gender_attr").value_or(-1);
  cur_user_label_.age = livestream::GetIntCommonAttr(context, config, "uinfo_age_seg_attr").value_or(-1);
  cur_user_label_.city = static_cast<std::string>(livestream::GetStringCommonAttr(
      context, config, "uinfo_city_level_attr").value_or(""));
  cur_user_label_.os = livestream::GetIntCommonAttr(context, config, "uinfo_os_attr").value_or(-1);
  cur_user_label_.price = livestream::GetIntCommonAttr(context, config, "uinfo_price_attr").value_or(-1);
  cur_user_label_.act_30d = livestream::GetIntCommonAttr(context, config, "uinfo_act30d_attr").value_or(-1);
  cur_user_label_.is_reflux = uinfo_is_reflux_;
  cur_user_label_.is_la = uinfo_is_la_;
  cur_user_label_.is_hp = uinfo_is_hp_;
  cur_user_label_.is_tnu = livestream::GetIntCommonAttr(context, config, "uinfo_is_tnu_attr").value_or(0);
  GradeDistributeHelperV2::GetInstance()->Init(grade_label_rules_kconf_str_, grade_label_cate_kconf_str_);
  if (grade_distribute_rule_version_.empty() ||
      !matcher_.Init(grade_distribute_rule_version_, cur_user_label_)) {
    CL_LOG_ERROR("livestream_retrieval_filter", "new_grade_label_sys_filter")
        << "Grade distribute matcher error version = " << grade_distribute_rule_version_;
    no_need_filter_ = true;
  }

  enable_new_grade_label_sys_aid_ = livestream::GetIntCommonAttr(
      context, config, "enable_new_grade_label_sys_aid_attr").value_or(0);
  if (enable_new_grade_label_sys_aid_ == 1) {
    grade_distribute_rule_version2_ = static_cast<std::string>(livestream::GetStringCommonAttr(
        context, config, "new_grade_label_sys_rule_version_aid_attr").value_or("new_grade_sys_aid"));
    grade_distribute_item_label_version2_ = livestream::GetIntCommonAttr(
        context, config, "new_grade_label_sys_item_version_aid_attr").value_or(4);
    if (grade_distribute_rule_version2_.empty() ||
        !matcher2_.Init(grade_distribute_rule_version2_, cur_user_label_)) {
      CL_LOG_ERROR("livestream_retrieval_filter", "new_grade_label_sys_filter")
          << "Grade distribute matcher error version = " << grade_distribute_rule_version2_;
      no_need_filter2_ = true;
    }
  }
  enable_follow_skip_new_grade_filter_ = livestream::GetIntCommonAttr(
    context, config, "enable_follow_skip_new_grade_filter_attr").value_or(0);
  follow_aid_list_.clear();
  auto follow_aid_list = livestream::GetIntListCommonAttr(
    context, config, "follow_aid_list_attr").value_or(absl::Span<const int64>());
  follow_aid_list_.insert(follow_aid_list.begin(), follow_aid_list.end());
  enable_markcode_new_grade_filter_ = livestream::GetIntCommonAttr(
    context, config, "enable_markcode_new_grade_filter_attr").value_or(0);
  auto markcode_list_new_grade_filter_str = livestream::GetStringCommonAttr(
    context, config, "markcode_list_new_grade_filter_str_attr").value_or("");
  markcode_list_.clear();
  std::vector<absl::string_view> markcode_list_str_split = absl::StrSplit(
    markcode_list_new_grade_filter_str, absl::ByAnyChar(";"), absl::SkipEmpty());
  if (!markcode_list_str_split.empty()) {
    for (const auto str : markcode_list_str_split) {
      int64 val;
      if (absl::SimpleAtoi(str, &val)) {
        markcode_list_.insert(val);
      }
    }
  }
  enable_sepcific_markcode_skip_new_grade_filter_ = livestream::GetIntCommonAttr(
    context, config, "enable_sepcific_markcode_skip_new_grade_filter_attr").value_or(0);
  if (enable_sepcific_markcode_skip_new_grade_filter_ > 0) {
    sepcific_markcode_random_skip_new_grade_filter_ratio_ = livestream::GetDoubleCommonAttr(
      context, config, "sepcific_markcode_random_skip_new_grade_filter_ratio_attr").value_or(1.0);
    auto markcode_list_skip_new_grade_filter_str = livestream::GetStringCommonAttr(
      context, config, "markcode_list_skip_new_grade_filter_str_attr").value_or("");
    markcode_skip_list_.clear();
    std::vector<absl::string_view> markcode_skip_list_str_split = absl::StrSplit(
      markcode_list_skip_new_grade_filter_str, absl::ByAnyChar(";"), absl::SkipEmpty());
    if (!markcode_skip_list_str_split.empty()) {
      for (const auto str : markcode_skip_list_str_split) {
        int64 val;
        if (absl::SimpleAtoi(str, &val)) {
          markcode_skip_list_.insert(val);
        }
      }
    }
  }
  enable_has_markcode_skip_new_grade_filter_ = livestream::GetIntCommonAttr(
    context, config, "enable_has_markcode_skip_new_grade_filter_attr").value_or(0);
  has_markcode_random_skip_new_grade_filter_ratio_ = livestream::GetDoubleCommonAttr(
    context, config, "has_markcode_random_skip_new_grade_filter_ratio_attr").value_or(1.0);
  random_.Reset(base::GetTimestamp());
  // 特定 category 豁免
  enable_new_grade_label_exemption_ = livestream::GetIntCommonAttr(
    context, config, "enable_new_grade_label_exemption_attr").value_or(0);
  auto new_grade_label_exemption_set_str = livestream::GetStringCommonAttr(
    context, config, "new_grade_label_exemption_set_str_attr").value_or("");
  new_grade_label_exemption_set_.clear();
  std::vector<absl::string_view> new_grade_label_exemption_set_str_split = absl::StrSplit(
    new_grade_label_exemption_set_str, absl::ByAnyChar(";"), absl::SkipEmpty());
  if (!new_grade_label_exemption_set_str_split.empty()) {
    for (const auto str : new_grade_label_exemption_set_str_split) {
      int64 val;
      if (absl::SimpleAtoi(str, &val)) {
        new_grade_label_exemption_set_.insert(val);
      }
    }
  }
  // 敏感人群的特定 markcode 取消 grade label 豁免
  enable_sensitive_user_with_some_markcode_disable_new_grade_label_exemption_ = livestream::GetIntCommonAttr(
    context, config,
    "enable_sensitive_user_with_some_markcode_disable_new_grade_label_exemption_attr").value_or(0);
  is_sensitive_user_disable_new_grade_label_exemption_ = livestream::GetIntCommonAttr(
    context, config, "is_sensitive_user_disable_new_grade_label_exemption_attr").value_or(0);
  auto sensitive_user_disable_new_grade_label_exemption_markcode_list_str = livestream::GetStringCommonAttr(
    context, config, "sensitive_user_disable_new_grade_label_exemption_markcode_list_str_attr").value_or("");
  sensitive_user_disable_new_grade_label_exemption_markcode_list_.clear();
  std::vector<absl::string_view> sensitive_user_disable_new_grade_label_exemption_markcode_list_str_split =
  absl::StrSplit(sensitive_user_disable_new_grade_label_exemption_markcode_list_str,
    absl::ByAnyChar(","), absl::SkipEmpty());
  if (!sensitive_user_disable_new_grade_label_exemption_markcode_list_str_split.empty()) {
    for (const auto str : sensitive_user_disable_new_grade_label_exemption_markcode_list_str_split) {
      int64 val;
      if (absl::SimpleAtoi(str, &val)) {
        sensitive_user_disable_new_grade_label_exemption_markcode_list_.insert(val);
      }
    }
  }
}

bool NewGradeLabelSysFilter::ShouldRemove(
    const CommonRecoResult &result) {
  auto l_distribution_mark_code_list = item_attr_container_->GetLiveDistributionMarkCodeList()
    .value_or(absl::Span<const int64>());
  // 完全概率豁免
  if (enable_has_markcode_skip_new_grade_filter_ > 0) {
    if (random_.GetDouble() < has_markcode_random_skip_new_grade_filter_ratio_) {
      return false;
    }
  }
  // 特定 markcode 豁免
  if (enable_sepcific_markcode_skip_new_grade_filter_ > 0) {
    for (const auto &tag : l_distribution_mark_code_list) {
      if (markcode_skip_list_.find(tag) != markcode_skip_list_.end()) {
        if (random_.GetDouble() < sepcific_markcode_random_skip_new_grade_filter_ratio_) {
          return false;
        } else {
          break;
        }
      }
    }
  }
  // 对于特定的 markcode 不进行已关豁免
  int skip_flag = 1;
  if (enable_markcode_new_grade_filter_ > 0) {
    auto l_distribution_mark_code_list = item_attr_container_->GetLiveDistributionMarkCodeList()
      .value_or(absl::Span<const int64>());
    for (const auto &tag : l_distribution_mark_code_list) {
      if (markcode_list_.find(tag) != markcode_list_.end()) {
        skip_flag = 0;
      }
    }
  }
  if (enable_follow_skip_new_grade_filter_ > 0 && skip_flag > 0) {
    auto author_id = item_attr_container_->GetAuthorId().value_or(0);
    bool is_follow_aid = (follow_aid_list_.find(author_id) != follow_aid_list_.end());
    if (is_follow_aid) {
      return false;
    }
  }
  if (no_need_filter_) {
    return false;
  }
  // 敏感人群的特定 markcode 取消 grade label 豁免
  int is_sensitive_user_and_target_markcode = 0;
  if (enable_sensitive_user_with_some_markcode_disable_new_grade_label_exemption_ > 0 &&
    is_sensitive_user_disable_new_grade_label_exemption_ > 0) {
    for (const auto &tag : l_distribution_mark_code_list) {
      if (sensitive_user_disable_new_grade_label_exemption_markcode_list_.find(tag) !=
      sensitive_user_disable_new_grade_label_exemption_markcode_list_.end()) {
        is_sensitive_user_and_target_markcode = 1;
        break;
      }
    }
  }
  auto grade_label_for_temp = item_attr_container_->GetGradeLabelForTemp()
      .value_or(absl::Span<const int64>());
  cate_info_t cate_info_vec;
  if (grade_label_for_temp.size() > 2) {
    int i = 0;
    while (i < grade_label_for_temp.size()) {
      int sub_list_size = grade_label_for_temp[i];
      if (sub_list_size > 2 && i + sub_list_size <= grade_label_for_temp.size()) {
        punish_info_t punish_info;
        int64 category = grade_label_for_temp[i + 1];
        int time_len = (sub_list_size - 2) / 2;
        for (int sub_i = 0; sub_i < time_len; ++sub_i) {
          int left_idx = i + 2 + sub_i;
          int right_idx = i + 2 + time_len + sub_i;
          punish_info.emplace_back(grade_label_for_temp[left_idx], grade_label_for_temp[right_idx]);
        }
        // 特定 category 豁免
        bool should_exemption = (enable_new_grade_label_exemption_ > 0) &&
          (new_grade_label_exemption_set_.find(category) != new_grade_label_exemption_set_.end() &&
          is_sensitive_user_and_target_markcode == 0);
        if (!should_exemption) {
          cate_info_vec.emplace_back(category, std::move(punish_info));
        }
      }
      i += sub_list_size;
    }
    if (!cate_info_vec.empty()) {
      auto res =
          matcher_.ItemShouldFilterForTemp(cate_info_vec,
              grade_distribute_item_label_version_, temp_grade_dis_effect_way_);
      if (res.first) {
        base::perfutil::PerfUtilWrapper::IntervalLogStash(1, kPerfNs, "livestream.grade_dist_for_temp",
                                                          std::to_string(res.second));
        return true;
      }
    }
  }
  if (enable_new_grade_label_sys_aid_ == 1) {
    if (no_need_filter2_) {
      return false;
    }
    auto grade_label_for_aid_temp = item_attr_container_->GetGradeLabelForAidTemp()
        .value_or(absl::Span<const int64>());
    cate_info_t cate_info_vec2;
    if (grade_label_for_aid_temp.size() > 2) {
      int i = 0;
      while (i < grade_label_for_aid_temp.size()) {
        int sub_list_size = grade_label_for_aid_temp[i];
        if (sub_list_size > 2 && i + sub_list_size <= grade_label_for_aid_temp.size()) {
          punish_info_t punish_info2;
          int64 category = grade_label_for_aid_temp[i + 1];
          int time_len = (sub_list_size - 2) / 2;
          for (int sub_i = 0; sub_i < time_len; ++sub_i) {
            int left_idx = i + 2 + sub_i;
            int right_idx = i + 2 + time_len + sub_i;
            punish_info2.emplace_back(grade_label_for_aid_temp[left_idx],
                                      grade_label_for_aid_temp[right_idx]);
          }
          // 特定 category 豁免
          bool should_exemption_aid = (enable_new_grade_label_exemption_ > 0) &&
            (new_grade_label_exemption_set_.find(category) != new_grade_label_exemption_set_.end() &&
            is_sensitive_user_and_target_markcode == 0);
          if (!should_exemption_aid) {
            cate_info_vec2.emplace_back(category, std::move(punish_info2));
          }
        }
        i += sub_list_size;
      }
      if (!cate_info_vec2.empty()) {
        auto res =
            matcher2_.ItemShouldFilterForTemp(cate_info_vec2,
                grade_distribute_item_label_version2_, temp_grade_dis_effect_way_);
        if (res.first) {
          base::perfutil::PerfUtilWrapper::IntervalLogStash(1, kPerfNs, "livestream.grade_dist_for_temp",
                                                            std::to_string(res.second));
          return true;
        }
      }
    }
  }
  return false;
}

}  // namespace livestream
}  // namespace platform
}  // namespace ks
