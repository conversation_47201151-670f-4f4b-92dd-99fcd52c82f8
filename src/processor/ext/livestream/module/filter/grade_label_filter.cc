#include <string>
#include <vector>
#include "dragon/src/processor/ext/livestream/module/filter/grade_label_filter.h"
#include "ks/reco_proto/proto/reco.pb.h"

namespace ks {
namespace platform {
namespace livestream {

void GradeLabelFilter::Init(
    ReadableRecoContextInterface *context, const base::Json *config) {
  auto user_info = config->GetString("user_info_attr");
  auto user_info_ptr = context->GetProtoMessagePtrCommonAttr<ks::reco::UserInfo>(user_info);
  auto grade_rule_version = livestream::GetStringCommonAttr(
    context, config, "grade_distribute_rule_version_attr");
  auto item_label_version_ptr = livestream::GetIntCommonAttr(
    context, config, "grade_distribute_item_label_version_attr");
  enable_follow_skip_grade_filter_ = livestream::GetIntCommonAttr(
    context, config, "enable_follow_skip_grade_filter_attr").value_or(0);
  follow_aid_list_.clear();
  auto follow_aid_list = livestream::GetIntListCommonAttr(
    context, config, "follow_aid_list_attr").value_or(absl::Span<const int64>());
  follow_aid_list_.insert(follow_aid_list.begin(), follow_aid_list.end());
  enable_markcode_grade_filter_ = livestream::GetIntCommonAttr(
    context, config, "enable_markcode_grade_filter_attr").value_or(0);
  auto markcode_list_grade_filter_str = livestream::GetStringCommonAttr(
    context, config, "markcode_list_grade_filter_str_attr").value_or("");
  markcode_list_.clear();
  std::vector<absl::string_view> markcode_list_str_split = absl::StrSplit(
    markcode_list_grade_filter_str, absl::ByAnyChar(";"), absl::SkipEmpty());
  if (!markcode_list_str_split.empty()) {
    for (const auto str : markcode_list_str_split) {
      int64 val;
      if (absl::SimpleAtoi(str, &val)) {
        markcode_list_.insert(val);
      }
    }
  }
  enable_sepcific_markcode_skip_grade_filter_ = livestream::GetIntCommonAttr(
    context, config, "enable_sepcific_markcode_skip_grade_filter_attr").value_or(0);
  if (enable_sepcific_markcode_skip_grade_filter_ > 0) {
    sepcific_markcode_random_skip_grade_filter_ratio_ = livestream::GetDoubleCommonAttr(
      context, config, "sepcific_markcode_random_skip_grade_filter_ratio_attr").value_or(1.0);
    auto markcode_list_skip_grade_filter_str = livestream::GetStringCommonAttr(
      context, config, "markcode_list_skip_grade_filter_str_attr").value_or("");
    markcode_skip_list_.clear();
    std::vector<absl::string_view> markcode_skip_list_str_split = absl::StrSplit(
      markcode_list_skip_grade_filter_str, absl::ByAnyChar(";"), absl::SkipEmpty());
    if (!markcode_skip_list_str_split.empty()) {
      for (const auto str : markcode_skip_list_str_split) {
        int64 val;
        if (absl::SimpleAtoi(str, &val)) {
          markcode_skip_list_.insert(val);
        }
      }
    }
  }
  enable_has_markcode_skip_grade_filter_ = livestream::GetIntCommonAttr(
    context, config, "enable_has_markcode_skip_grade_filter_attr").value_or(0);
  has_markcode_random_skip_grade_filter_ratio_ = livestream::GetDoubleCommonAttr(
    context, config, "has_markcode_random_skip_grade_filter_ratio_attr").value_or(1.0);
  random_.Reset(base::GetTimestamp());
  // 特定 grade label 豁免
  enable_grade_label_exemption_ = livestream::GetIntCommonAttr(
    context, config, "enable_grade_label_exemption_attr").value_or(0);
  auto grade_label_exemption_set_str = livestream::GetStringCommonAttr(
    context, config, "grade_label_exemption_set_str_attr").value_or("");
  grade_label_exemption_set_.clear();
  std::vector<absl::string_view> grade_label_exemption_set_str_split = absl::StrSplit(
    grade_label_exemption_set_str, absl::ByAnyChar(";"), absl::SkipEmpty());
  if (!grade_label_exemption_set_str_split.empty()) {
    for (const auto str : grade_label_exemption_set_str_split) {
      int64 val;
      if (absl::SimpleAtoi(str, &val)) {
        grade_label_exemption_set_.insert(val);
      }
    }
  }
  // 敏感人群的特定 markcode 取消 grade label 豁免
  enable_sensitive_user_with_some_markcode_disable_grade_label_exemption_ = livestream::GetIntCommonAttr(
    context, config,
    "enable_sensitive_user_with_some_markcode_disable_grade_label_exemption_attr").value_or(0);
  is_sensitive_user_disable_grade_label_exemption_ = livestream::GetIntCommonAttr(
    context, config, "is_sensitive_user_disable_grade_label_exemption_attr").value_or(0);
  auto sensitive_user_disable_grade_label_exemption_markcode_list_str = livestream::GetStringCommonAttr(
    context, config, "sensitive_user_disable_grade_label_exemption_markcode_list_str_attr").value_or("");
  sensitive_user_disable_grade_label_exemption_markcode_list_.clear();
  std::vector<absl::string_view> sensitive_user_disable_grade_label_exemption_markcode_list_str_split =
  absl::StrSplit(sensitive_user_disable_grade_label_exemption_markcode_list_str,
    absl::ByAnyChar(","), absl::SkipEmpty());
  if (!sensitive_user_disable_grade_label_exemption_markcode_list_str_split.empty()) {
    for (const auto str : sensitive_user_disable_grade_label_exemption_markcode_list_str_split) {
      int64 val;
      if (absl::SimpleAtoi(str, &val)) {
        sensitive_user_disable_grade_label_exemption_markcode_list_.insert(val);
      }
    }
  }

  if (!grade_rule_version.has_value() || !item_label_version_ptr.has_value()) {
    CL_LOG_EVERY_N(ERROR, 100) << "null item_label_version or grade_rule_version";
    return;
  }
  item_label_version_ = item_label_version_ptr.value();
  if (grade_rule_version.value().empty() || item_label_version_ == 0) {
    CL_LOG_EVERY_N(ERROR, 100) << "Grade distribute matcher error config, version:"
                               << grade_rule_version.value()
                               << " item_label_version:" << base::Int64ToString(item_label_version_);
    return;
  }
  grade_distribute_matcher_ = std::make_shared<ks::reco::grade_distribute::GradeDistributeMatcher>();
  if (!grade_distribute_matcher_ ||
      !grade_distribute_matcher_->Init(std::string(grade_rule_version.value()), user_info_ptr)) {
    CL_LOG_EVERY_N(ERROR, 100) << "Grade distribute matcher error version = " << grade_rule_version.value();
    return;
  }
}

bool GradeLabelFilter::ShouldRemove(
    const CommonRecoResult &result) {
  auto l_distribution_mark_code_list = item_attr_container_->GetLiveDistributionMarkCodeList()
    .value_or(absl::Span<const int64>());
  // 完全概率豁免
  if (enable_has_markcode_skip_grade_filter_ > 0) {
    if (random_.GetDouble() < has_markcode_random_skip_grade_filter_ratio_) {
      return false;
    }
  }
  // 特定 markcode 豁免
  if (enable_sepcific_markcode_skip_grade_filter_ > 0) {
    for (const auto &tag : l_distribution_mark_code_list) {
      if (markcode_skip_list_.find(tag) != markcode_skip_list_.end()) {
        if (random_.GetDouble() < sepcific_markcode_random_skip_grade_filter_ratio_) {
          return false;
        } else {
          break;
        }
      }
    }
  }
  // 对于特定的 markcode 不进行已关豁免
  int skip_flag = 1;
  if (enable_markcode_grade_filter_ > 0) {
    auto l_distribution_mark_code_list = item_attr_container_->GetLiveDistributionMarkCodeList()
      .value_or(absl::Span<const int64>());
    for (const auto &tag : l_distribution_mark_code_list) {
      if (markcode_list_.find(tag) != markcode_list_.end()) {
        skip_flag = 0;
      }
    }
  }
  if (enable_follow_skip_grade_filter_ > 0 && skip_flag > 0) {
    auto author_id = item_attr_container_->GetAuthorId().value_or(0);
    bool is_follow_aid = (follow_aid_list_.find(author_id) != follow_aid_list_.end());
    if (is_follow_aid) {
      return false;
    }
  }
  if (!grade_distribute_matcher_ || item_label_version_ == 0) {
    CL_LOG_EVERY_N(ERROR, 1000) << "null grade_distribute_matcher_ or item_label_version_";
    return false;
  }

  // 敏感人群的特定 markcode 取消 grade label 豁免
  int is_sensitive_user_and_target_markcode = 0;
  if (enable_sensitive_user_with_some_markcode_disable_grade_label_exemption_ > 0 &&
    is_sensitive_user_disable_grade_label_exemption_ > 0) {
    for (const auto &tag : l_distribution_mark_code_list) {
      if (sensitive_user_disable_grade_label_exemption_markcode_list_.find(tag) !=
      sensitive_user_disable_grade_label_exemption_markcode_list_.end()) {
        is_sensitive_user_and_target_markcode = 1;
        break;
      }
    }
  }

  std::vector<int64> item_label_use;
  auto label_list = item_attr_container_->GetGradeLabel();
  // sub_list_size 是其到下一个 sub_list_size 的距离
  // key 是直播间标签版本
  // label_list = [sub_list_size1, key1, value[0], value[1], sub_list_size2, key2, ...]
  // list 查找 key
  if (label_list.has_value() && label_list.value().size() > 2) {
    int i = 0;
    while (i < label_list.value().size()) {
      int sub_list_size = label_list.value().at(i);
      if (sub_list_size > 2 && i + sub_list_size <= label_list.value().size()) {
        if (label_list.value().at(i + 1) == item_label_version_) {
          for (int idx = i + 2; idx < i + sub_list_size; ++idx) {
            // 如果命中要豁免的 grade label 就跳过写入
            if (enable_grade_label_exemption_ > 0 &&
              is_sensitive_user_and_target_markcode == 0) {
              if (grade_label_exemption_set_.find(label_list.value().at(idx)) !=
                grade_label_exemption_set_.end()) {
                  continue;
                }
            }
            item_label_use.push_back(label_list.value().at(idx));
          }
          break;
        }
      }
      i += sub_list_size;
    }
    if (!item_label_use.empty()) {
      bool res = grade_distribute_matcher_->ItemShouldFilter(item_label_use);
      if (res) {
        std::string item_label_print;
        for (auto label : item_label_use) {
          item_label_print.append(base::Int64ToString(label) + "-");
        }
        base::perfutil::PerfUtilWrapper::IntervalLogStash(1, kPerfNs, "livestream.grade_dist",
                                                          item_label_print);
      }
      return res;
    }
  }
  return false;
}

}  // namespace livestream
}  // namespace platform
}  // namespace ks
