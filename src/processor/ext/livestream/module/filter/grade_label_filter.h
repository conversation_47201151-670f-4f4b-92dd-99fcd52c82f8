#pragma once
#include <memory>

#include "dragon/src/processor/ext/livestream/module/filter/base_filter.h"
#include "ks/reco_pub/reco/grade_distribute/grade_distribute_matcher.h"

namespace ks {
namespace platform {
namespace livestream {

class GradeLabelFilter : public livestream::BaseFilter {
 public:
  GradeLabelFilter() {}
  void Init(
      ReadableRecoContextInterface *context,
      const base::Json *config) override;
  bool ShouldRemove(const CommonRecoResult &result) override;

 private:
  std::shared_ptr<ks::reco::grade_distribute::GradeDistributeMatcher> grade_distribute_matcher_;
  int64 item_label_version_ = 0;
  int64 enable_follow_skip_grade_filter_ = 0;
  folly::F14FastSet<int64> follow_aid_list_;
  int64 enable_markcode_grade_filter_ = 0;
  folly::F14FastSet<int64> markcode_list_;
  int64 enable_sepcific_markcode_skip_grade_filter_ = 0;
  folly::F14FastSet<int64> markcode_skip_list_;
  int64 enable_has_markcode_skip_grade_filter_ = 0;
  double sepcific_markcode_random_skip_grade_filter_ratio_ = 1.0;
  double has_markcode_random_skip_grade_filter_ratio_ = 1.0;
  base::PseudoRandom random_;
  int64 enable_grade_label_exemption_ = 0;
  folly::F14FastSet<int64> grade_label_exemption_set_;
  int64 enable_sensitive_user_with_some_markcode_disable_grade_label_exemption_ = 0;
  int64 is_sensitive_user_disable_grade_label_exemption_ = 0;
  folly::F14FastSet<int64> sensitive_user_disable_grade_label_exemption_markcode_list_;
  DISALLOW_COPY_AND_ASSIGN(GradeLabelFilter);
};

}  // namespace livestream
}  // namespace platform
}  // namespace ks
