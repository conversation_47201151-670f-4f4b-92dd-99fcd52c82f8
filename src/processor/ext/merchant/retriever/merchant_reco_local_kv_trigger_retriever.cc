#include "dragon/src/processor/ext/merchant/retriever/merchant_reco_local_kv_trigger_retriever.h"


DEFINE_int32(kv_trigger_print_attr_index_key_num, 5, "");
DEFINE_bool(kv_trigger_attr_index_debug, false, "");

#define KV_TRIGGER_ATTR_INDEX_DEBUG(...)       \
  do { if (FLAGS_kv_trigger_attr_index_debug)  \
      { LOG(INFO) << __VA_ARGS__; }\
  } while (0);

namespace ks {
namespace platform {

bool MerchantRecoLocalKvTriggerRetriever::InitProcessor() {
  attr_index_kconf_path_ = config()->GetString("attr_index_kconf_path", "");
  kv_trigger_kconf_path_ = config()->GetString("kv_triger_kconf_path", "");
  kv_trigger_kconf_gray_path_ = config()->GetString("kv_trigger_kconf_gray_path", "");
  table_name_ = "uni_table";
  ks::platform::AttrIndexManager::Instance().Init(table_name_, attr_index_kconf_path_);
  if (!ks::platform::AttrIndexManager::Instance().GetAttrIndex(table_name_)) {
    LOG(ERROR) << "init attr_index failed!" << "kconf_path:"
            << attr_index_kconf_path_ << ", table_name:" << table_name_;
    return false;
  }
  if (kv_trigger_kconf_path_.empty() && kv_trigger_kconf_gray_path_.empty()) {
    LOG(ERROR) << "kv_trigger_kconf_path and kv_trigger_kconf_gray_path is all empty!";
    return false;
  }
  LOG(INFO) << "MerchantRecoLocalKvTriggerRetriever init success!";
  return true;
}

void MerchantRecoLocalKvTriggerRetriever::Retrieve(AddibleRecoContextInterface *context) {
  reco::AttrIndex * attr_index = ks::platform::AttrIndexManager::Instance().GetAttrIndex(table_name_);
  if (!attr_index) {
    LOG_EVERY_N(ERROR, 1000)
        << "cannot get item attrs from local index, no attr index available!";
    return;
  }
  int64 start_ts = base::GetTimestamp();
  int64 all_recall_num = 0;
  ParseKvTriggerConfig(context);
  for (auto& kv_trigger : kv_trigger_config_vec_) {
    if (FLAGS_kv_trigger_attr_index_debug) {
      std::string attr_index_key_list;
      for (int flag = 0; flag < attr_index->GetPartKeys().size(); flag++) {
        if (flag > FLAGS_kv_trigger_print_attr_index_key_num) {
          KV_TRIGGER_ATTR_INDEX_DEBUG("table attr_index part_key: " << attr_index_key_list);
          break;
        }
        attr_index_key_list.append(std::to_string(attr_index->GetPartKeys()[flag])).append(",");
      }
    }
    if (kv_trigger.retrieve_num <= 0) {
      LOG_EVERY_N(WARNING, 1000)
          << "MerchantRecoLocalKvTriggerRetriever cancelled: retrieve_num=" << kv_trigger.retrieve_num;
      continue;
    }

    std::string key_prefix = "";
    std::vector<std::string> string_keys;
    string_keys.clear();
    if (kv_trigger.key.empty()) {
      key_prefix = kv_trigger.key_prefix;
    }
    InitStringKeys(context, kv_trigger, &string_keys, key_prefix);
    if (string_keys.empty()) {
      LOG_EVERY_N(ERROR, 1000) << "MerchantRecoLocalKvTriggerRetriever cancelled: empty keys.";
      continue;
    }

    absl::optional<absl::Span<const int64>> reason_list;
    if (!kv_trigger.reason_from_attr.empty()) {
      reason_list = context->GetIntListCommonAttr(kv_trigger.reason_from_attr);
      if (!reason_list) {
        KV_TRIGGER_ATTR_INDEX_DEBUG("reason_from_attr = " << kv_trigger.reason_from_attr << " not found");
      }
    }

    std::vector<KvIndexItemConfig> values;
    values.resize(string_keys.size());

    for (int i = 0; i < string_keys.size(); ++i) {
      values[i].origin_key = string_keys[i];
      values[i].primary_key = base::CityHash64(values[i].origin_key.c_str(), values[i].origin_key.size());
      values[i].reason  = (reason_list && i < reason_list->size()) ? reason_list->at(i) : kv_trigger.reason;
      KV_TRIGGER_ATTR_INDEX_DEBUG("origin_key: " << values[i].origin_key << ", primary_key: "
          << values[i].primary_key << ", reason: " << values[i].reason);
    }

    std::vector<CommonRecoRetrieveResult> retrieve_items;
    retrieve_items.reserve(kv_trigger.retrieve_num);
    for (int i = 0; i < values.size(); ++i) {
      if (kv_trigger.retrieve_num > 0 && retrieve_items.size() >= kv_trigger.retrieve_num) {
        KV_TRIGGER_ATTR_INDEX_DEBUG("GetRetrieveResultFromLocalIndex break,  retrieve_num:"
            << kv_trigger.retrieve_num << ", retrieve_items_size:" << retrieve_items.size()
            << ", index:" << i);
        break;
      }
      GetRetrieveResultFromLocalIndex(context, &kv_trigger, attr_index, values[i], &retrieve_items);
    }
    if (!kv_trigger.save_result_to_common_attr.empty()) {
      AddItemIdToCommonAttr(context, retrieve_items, kv_trigger.save_result_to_common_attr);
    } else {
      AddToRecoResults(context, retrieve_items);
    }
    all_recall_num += retrieve_items.size();
  }
  int64 response_ts = base::GetTimestamp();
  KV_TRIGGER_ATTR_INDEX_DEBUG("processor: " << GetName() << ", request_type: " << context->GetRequestType()
                 << ", device_id: " << context->GetDeviceId() << ", user_id: " << context->GetUserId()
                 << ", request_time: " << (response_ts - start_ts) / 1000.0 << "ms"
                 << ", retrieve num: " << all_recall_num);
  return;
}

void MerchantRecoLocalKvTriggerRetriever::AddItemIdToCommonAttr(
          MutableRecoContextInterface *context,
          const std::vector<CommonRecoRetrieveResult>& retrieve_items,
          const std::string& output_common_attr_name) {
  std::vector<int64> retrieve_items_id;
  retrieve_items_id.reserve(retrieve_items.size());
  for (const auto& item : retrieve_items) {
    retrieve_items_id.push_back(item.item_key);
  }
  context->SetIntListCommonAttr(
      output_common_attr_name, std::move(retrieve_items_id));
  return;
}

bool MerchantRecoLocalKvTriggerRetriever::ParseAttrsConfig(
      const ::Json::Value& attrs_config, std::vector<ExtraItemColInfo> *cols_vector) {
  for (const auto& item : attrs_config) {
    std::string attr_name = item["name"].empty() ? "" : item["name"].asString();
    if (attr_name.empty()) {
      LOG_EVERY_N(ERROR, 1000)
          << "MerchantRecoLocalKvTriggerRetriever init failed! name is required for "
          << attrs_config.toStyledString();
      return false;
    }
    std::string as_name = item["as_name"].empty() ? "" : item["as_name"].asString();
    cols_vector->emplace_back(attr_name, as_name);
    KV_TRIGGER_ATTR_INDEX_DEBUG("add extra item attr: " << attr_name << ", as_name: " << as_name);
  }
  return true;
}

bool MerchantRecoLocalKvTriggerRetriever::ParseKvTriggerConfig(
      MutableRecoContextInterface *context) {
  auto default_val = std::make_shared<::Json::Value>();
  std::string kv_trigger_kconf_path_name = kv_trigger_kconf_path_;
  if (!kv_trigger_kconf_gray_path_.empty()) {
    kv_trigger_kconf_path_name = std::string(context->GetStringCommonAttr(kv_trigger_kconf_gray_path_) \
        .value_or(kv_trigger_kconf_path_).data());
  }
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<::Json::Value>>> trigger_config;
  trigger_config = ks::infra::KConf().Get(kv_trigger_kconf_path_name, default_val);

  if (!trigger_config || !trigger_config->Get()) {
    CL_LOG_EVERY_N(ERROR, 1000) << "init kconf data config failed! kconf name is " \
        << kv_trigger_kconf_path_name;
    return false;
  }

  std::shared_ptr<::Json::Value> json_config = trigger_config->Get();
  std::vector<MerchantKvTriggerConfig> kv_trigger_config_vec;

  if (json_config && json_config->isArray()) {
    try {
      const ::Json::Value& configArray = *json_config;
      for (const auto& item : configArray) {
        std::string switch_open = item["switch_open"].empty() ? "" : item["switch_open"].asString();
        if (switch_open.empty()) {
          continue;
        }
        auto is_switch_open = context->GetIntCommonAttr(switch_open).value_or(0);
        if (is_switch_open <= 0) {
          continue;
        }
        MerchantKvTriggerConfig kv_trigger_config;
        kv_trigger_config.reason = item["reason"].empty() ? -999 : item["reason"].asInt();
        kv_trigger_config.recall_assort = item["recall_assort"].empty() ? 0 : item["recall_assort"].asInt();
        kv_trigger_config.table_name =
            item["table_name"].empty() ? "uni_table" : item["table_name"].asString();
        kv_trigger_config.is_huge_recall_attr_name = item["is_huge_recall_attr_name"].empty()
            ? "is_huge_recall" : item["is_huge_recall_attr_name"].asString();
        kv_trigger_config.key =
            item["key"].empty() ? "" : item["key"].asString();
        kv_trigger_config.reason_from_attr =
            item["reason_from_attr"].empty() ? "" : item["reason_from_attr"].asString();
        kv_trigger_config.retrieve_item_attr =
            item["retrieve_item_id_attr"].empty()
                ? "recall_item_ids" : item["retrieve_item_id_attr"].asString();
        kv_trigger_config.score_item_attr =
            item["retrieve_item_score_attr"].empty()
                ? "recall_scores" : item["retrieve_item_score_attr"].asString();
        kv_trigger_config.save_src_key_to_attr =
            item["save_src_key_to_attr"].empty()
                ? "trigger_item" : item["save_src_key_to_attr"].asString();
        kv_trigger_config.append_src_key_to_attr =
            item["append_src_key_to_attr"].empty()
                ? "trigger_list" : item["append_src_key_to_attr"].asString();
        kv_trigger_config.retrieval_item_type =
            item["retrieval_item_type"].empty() ? 99 : item["retrieval_item_type"].asInt();
        kv_trigger_config.save_result_to_common_attr =
            item["save_result_to_common_attr"].empty() ? "" : item["save_result_to_common_attr"].asString();
        kv_trigger_config.key_from_attr =
            item["key_from_attr"].empty() ? "" : item["key_from_attr"].asString();
        std::string key_from_attr = kv_trigger_config.key_from_attr;
        if (!key_from_attr.empty()) {
          std::vector<std::string> key_from_attr_vec =
              absl::StrSplit(key_from_attr, ":", absl::SkipWhitespace());
          if (key_from_attr_vec.size() == 2) {
            kv_trigger_config.key_from_attr =
                context->GetStringCommonAttr(key_from_attr_vec[0]).value_or(key_from_attr_vec[1]).data();
          }
        }
        std::string key_prefix = item["key_prefix"].empty() ? "" : item["key_prefix"].asString();
        if (!key_prefix.empty()) {
          std::vector<std::string> key_prefix_vec = absl::StrSplit(key_prefix, ":", absl::SkipWhitespace());
          if (key_prefix_vec.size() == 2) {
            kv_trigger_config.key_prefix =
                context->GetStringCommonAttr(key_prefix_vec[0]).value_or(key_prefix_vec[1]).data();
          }
        }

        kv_trigger_config.is_huge_recall = false;
        std::string is_huge_recall_str =
            item["is_huge_recall"].empty() ? "" : item["is_huge_recall"].asString();
        if (!is_huge_recall_str.empty()) {
          std::vector<std::string> is_huge_recall_vec =
              absl::StrSplit(is_huge_recall_str, ":", absl::SkipWhitespace());
          if (is_huge_recall_vec.size() == 2) {
            kv_trigger_config.is_huge_recall =
                context->GetIntCommonAttr(is_huge_recall_vec[0])\
                .value_or(stoi(is_huge_recall_vec[1])) > 0 ? true : false;
          }
        }

        std::string retrieve_item_num_str = std::to_string(kv_trigger_config.reason);
        retrieve_item_num_str.append("_uni_recall_retrieve_num:1000");
        const std::string& retrieve_item_num =
            item["retrieve_num"].empty() ? retrieve_item_num_str : item["retrieve_num"].asString();
        std::vector<std::string> retrieve_item_num_vec =
            absl::StrSplit(retrieve_item_num, ":", absl::SkipWhitespace());
        if (retrieve_item_num_vec.size() == 2) {
          kv_trigger_config.retrieve_num =
              context->GetIntCommonAttr(retrieve_item_num_vec[0]).value_or(stoi(retrieve_item_num_vec[1]));
        }

        std::string retrieve_num_per_key_str = std::to_string(kv_trigger_config.reason);
        retrieve_num_per_key_str.append("_uni_recall_retrieve_num_per_key:200");
        const std::string& retrieve_num_per_key =
            item["retrieve_num_per_key"].empty()
                ? retrieve_num_per_key_str : item["retrieve_num_per_key"].asString();
        std::vector<std::string> retrieve_num_per_key_vec =
            absl::StrSplit(retrieve_num_per_key, ":", absl::SkipWhitespace());
        if (retrieve_num_per_key_vec.size() == 2) {
          kv_trigger_config.retrieve_num_per_key_num =
              context->GetIntCommonAttr(retrieve_num_per_key_vec[0])\
              .value_or(stoi(retrieve_num_per_key_vec[1]));
        }
        if (kv_trigger_config.key.empty() && kv_trigger_config.key_from_attr.empty()) {
          LOG_EVERY_N(ERROR, 1000)
            << "MerchantRecoLocalKvTriggerRetriever init failed! 'key_from_attr' and 'key' are both empty.";
          continue;
        }
        if (!item["extra_item_attrs"].empty()) {
          if (!ParseAttrsConfig(item["extra_item_attrs"], &(kv_trigger_config.extra_item_col_infos))) {
            LOG_EVERY_N(ERROR, 1000)
              << "MerchantRecoLocalKvTriggerRetriever init failed! extra_item_attrs is invalid.";
          }
        }
        if (kv_trigger_config.retrieve_num <= 0) {
          continue;
        }
        kv_trigger_config_vec.push_back(kv_trigger_config);
      }
    } catch(const std::exception& e) {
        CL_LOG_EVERY_N(ERROR, 1000) << "catch exception:" << e.what();
        return false;
    } catch (...) {  // 捕获所有其他类型的异常
        CL_LOG_EVERY_N(ERROR, 1000) << "catch unknown exception";
        return false;
    }
  } else {
    CL_LOG_EVERY_N(ERROR, 1000) << "Error: json_config expected an array but got something else.";
    return false;
  }

  if (kv_trigger_config_vec.empty()) {
    CL_LOG_EVERY_N(ERROR, 1000) << "Error, kv_trigger_config_vec is empty, retrieve_kconf_path:" \
        << kv_trigger_kconf_path_name.c_str();
    return false;
  }
  kv_trigger_config_vec_ = std::move(kv_trigger_config_vec);
  return true;
}

void MerchantRecoLocalKvTriggerRetriever::GetRetrieveResultFromLocalIndex(
    MutableRecoContextInterface *context,
    MerchantKvTriggerConfig* kv_trigger_p,
    ks::reco::AttrIndex *attr_index, const KvIndexItemConfig &trigger_key,
    std::vector<CommonRecoRetrieveResult> *retrieve_result) {
  std::unique_ptr<ks::reco::protoutil::AttrKVAutoReader> reader
      = attr_index->GetKVAutoReader(trigger_key.primary_key);
  if (!reader || !kv_trigger_p) {
    KV_TRIGGER_ATTR_INDEX_DEBUG("cannot get attr reader. index key:" << trigger_key.primary_key
        << ", origin key:" << trigger_key.origin_key
        << ", reason:" << trigger_key.reason);
    return;
  }
  auto kv_trigger = *kv_trigger_p;
  if (!reader->IsValid()) {
    KV_TRIGGER_ATTR_INDEX_DEBUG("Invalid attr reader. index key:" << trigger_key.primary_key
        << ", origin key:" << trigger_key.origin_key
        << ", reason:" << trigger_key.reason);
    return;
  }

  KV_TRIGGER_ATTR_INDEX_DEBUG("GetDetailInfo: " << reader->GetDetailInfo()
      << "\n DebugString:" << reader->DebugString());

  ks::reco::protoutil::AttrType retrieve_attr_type = ks::reco::protoutil::AttrType::kInvalid;
  std::vector<int64> item_id_list;
  uint64 retrieve_item_attr_key = 0;
  attr_index->GetAttrKeyAndType(kv_trigger.retrieve_item_attr, &retrieve_item_attr_key, &retrieve_attr_type);
  item_id_list.clear();
  if (!reader->GetIntListValue(retrieve_item_attr_key, &item_id_list)) {
    LOG_EVERY_N(ERROR, 10000) << "GetIntListValue failed. key:" << retrieve_item_attr_key
        << ", retrieve_item_attr:" << kv_trigger.retrieve_item_attr << ", retrieve_attr_type"
        << (int)retrieve_attr_type;
    return;
  }

  ks::reco::protoutil::AttrType retrieve_score_type = ks::reco::protoutil::AttrType::kInvalid;
  std::vector<double> item_score_list;
  uint64 retrieve_item_score_key = 0;
  attr_index->GetAttrKeyAndType(kv_trigger.score_item_attr, &retrieve_item_score_key, &retrieve_score_type);
  item_score_list.clear();
  if (!reader->GetFloatListValue(retrieve_item_score_key, &item_score_list)) {
    // LOG_EVERY_N(ERROR, 10000) << "GetIntListValue failed. key:" << retrieve_item_score_key
    //     << ", retrieve_item_attr: " << score_item_attr_ << ", retrieve_score_type:"
    //     << (int)retrieve_score_type;
    // return;
  }
  bool use_score = item_score_list.size() > 0 && item_score_list.size() == item_id_list.size();
  KV_TRIGGER_ATTR_INDEX_DEBUG("item_id_list size:" << item_id_list.size() << ", item_score_list size:"
      << item_score_list.size());

  std::unordered_map<uint64, std::vector<int64>> int_list_attr_map;
  std::unordered_map<uint64, std::vector<std::string>> str_list_attr_map;
  std::unordered_map<uint64, std::vector<float>> float_list_attr_map;
  for (int i = 0; i < item_id_list.size() && i < kv_trigger.retrieve_num_per_key_num; i++) {
    if (kv_trigger.retrieve_num > 0 && retrieve_result->size() >= kv_trigger.retrieve_num) {
      KV_TRIGGER_ATTR_INDEX_DEBUG("GetRetrieveResultFromLocalIndex one key break,  retrieve_num:"
          << kv_trigger.retrieve_num << ", retrieve_items_size:" << retrieve_result->size()
          << ", index:" << i);
      break;
    }
    int64 item_key = retrieve_item_attr_key > 0 ?
        Util::GenKeysign(kv_trigger.retrieval_item_type, item_id_list[i]) : item_id_list[i];
    double item_score = 0.0;
    if (use_score) {
      item_score = item_score_list[i];
    }
    retrieve_result->emplace_back(item_key, trigger_key.reason, item_score);
    KV_TRIGGER_ATTR_INDEX_DEBUG("item_id:" << item_id_list[i] << ", item_key:" << item_key
              << ", reason:" << trigger_key.reason << ", item_score:" << item_score
              << ", item_score_list size:" << item_score_list.size());
    if (kv_trigger.is_huge_recall) {
      context->SetIntItemAttr(item_key, kv_trigger.is_huge_recall_attr_name, 1);
    }
    context->SetIntItemAttr(item_key, "recall_assort", kv_trigger.recall_assort);
    if (!kv_trigger.save_src_key_to_attr.empty()) {
      context->SetStringItemAttr(
          item_key, kv_trigger.save_src_key_to_attr, trigger_key.origin_key);
    }
    if (!kv_trigger.append_src_key_to_attr.empty()) {
      context->AppendStringListItemAttr(
          item_key, kv_trigger.append_src_key_to_attr, trigger_key.origin_key);
    }
    for (int j = 0; j < kv_trigger.extra_item_col_infos.size(); j++) {
      auto attr_name = kv_trigger.extra_item_col_infos[j].attr_name;
      attr_index->GetAttrKeyAndType(attr_name, &(kv_trigger.extra_item_col_infos[j].attr_key),
          &(kv_trigger.extra_item_col_infos[j].attr_type));
      SetExtraItemAttr(context,
          retrieve_result,
          &int_list_attr_map,
          &str_list_attr_map,
          &float_list_attr_map,
          item_key,
          kv_trigger.extra_item_col_infos[j],
          reader,
          i);
    }
  }
  return;
}

bool MerchantRecoLocalKvTriggerRetriever::SetExtraItemAttr(MutableRecoContextInterface *context,
                              std::vector<CommonRecoRetrieveResult> *retrieve_items,
                              std::unordered_map<uint64, std::vector<int64>> *int_list_attr_map_p,
                              std::unordered_map<uint64, std::vector<std::string>> *str_list_attr_map_p,
                              std::unordered_map<uint64, std::vector<float>> *float_list_attr_map_p,
                              int64 item_key, const ExtraItemColInfo& col_info,
                              const std::unique_ptr<ks::reco::protoutil::AttrKVAutoReader> &reader,
                              const int &index) {
  if (!int_list_attr_map_p || !str_list_attr_map_p || !float_list_attr_map_p) {
    return false;
  }
  auto int_list_attr_map = *int_list_attr_map_p;
  auto str_list_attr_map = *str_list_attr_map_p;
  auto float_list_attr_map = *float_list_attr_map_p;
  const std::string &attr_name = col_info.attr_name;
  const auto &type = col_info.attr_type;  // index attr type
  const uint64 &attr_key = col_info.attr_key;  // index attr value
  std::string as_name = col_info.as_name.empty() ? attr_name : col_info.as_name;
  switch (type) {
    case ks::reco::protoutil::AttrType::kStringList: {
      if (str_list_attr_map.find(attr_key) == str_list_attr_map.end()) {
        std::vector<std::string> str_list_attr;
        if (!reader->GetStringListValue(attr_key, &str_list_attr)) {
          LOG_EVERY_N(ERROR, 10000) << "GetStringListValue failed. key:" << as_name;
          return false;
        }
        str_list_attr_map[attr_key] = str_list_attr;
      }
      context->SetStringItemAttr(item_key, as_name, str_list_attr_map[attr_key][index]);
      KV_TRIGGER_ATTR_INDEX_DEBUG("set string extra item attr: " << as_name << ", type: "
          << (int)type << ", as_name: " << as_name << ", value: " << str_list_attr_map[attr_key][index]);
      return true;
    }
    case ks::reco::protoutil::AttrType::kIntList: {
      if (int_list_attr_map.find(attr_key) == int_list_attr_map.end()) {
        std::vector<int64> int_list_attr;
        if (!reader->GetIntListValue(attr_key, &int_list_attr)) {
          LOG_EVERY_N(ERROR, 10000) << "GetIntValue failed. key:" << as_name;
          return false;
        }
        int_list_attr_map[attr_key] = int_list_attr;
      }
      context->SetIntItemAttr(item_key, as_name, int_list_attr_map[attr_key][index]);
      KV_TRIGGER_ATTR_INDEX_DEBUG("set int extra item attr: " << as_name
          << ", type: " << (int)type << ", as_name: "
          << as_name << ", value: " << int_list_attr_map[attr_key][index]);
      return true;
    }
    case ks::reco::protoutil::AttrType::kFloatList: {
      if (float_list_attr_map.find(attr_key) == float_list_attr_map.end()) {
        std::vector<float> float_list_attr;
        if (!reader->GetFloatListValue(attr_key, &float_list_attr)) {
          LOG_EVERY_N(ERROR, 10000) << "GetDoubleValue failed. key:" << as_name;
          return false;
        }
        float_list_attr_map[attr_key] = float_list_attr;
      }
      context->SetDoubleItemAttr(item_key, as_name, float_list_attr_map[attr_key][index]);
      KV_TRIGGER_ATTR_INDEX_DEBUG("set double extra item attr: " << as_name << ", type: " << (int)type
          << ", as_name: " << as_name << ", value: " << float_list_attr_map[attr_key][index]);
    }
    default:
      KV_TRIGGER_ATTR_INDEX_DEBUG("set extra item col info failed! attr_name: " << attr_name
          << ", index_type:" << int(type) << ", processor: " << GetName());
  }
  return true;
}

void MerchantRecoLocalKvTriggerRetriever::InitStringKeys(MutableRecoContextInterface *context,
                                                   const MerchantKvTriggerConfig& kv_trigger,
                                                   std::vector<std::string> *string_keys,
                                                   const std::string &key_prefix) {
  if (!string_keys) return;

  if (!kv_trigger.key.empty()) {
    string_keys->emplace_back(kv_trigger.key);
  }

  if (auto p_int = context->GetIntCommonAttr(kv_trigger.key_from_attr)) {
    string_keys->emplace_back(key_prefix + std::to_string(static_cast<uint64>(*p_int)));
  } else if (auto p_string = context->GetStringCommonAttr(kv_trigger.key_from_attr)) {
    string_keys->emplace_back(key_prefix + std::string(p_string->data(), p_string->size()));
  } else if (auto p_int_list = context->GetIntListCommonAttr(kv_trigger.key_from_attr)) {
    for (const int64 key : *p_int_list) {
      string_keys->emplace_back(key_prefix + std::to_string(static_cast<uint64>(key)));
    }
  } else if (auto p_string_list = context->GetStringListCommonAttr(kv_trigger.key_from_attr)) {
    for (auto sv : *p_string_list) {
      string_keys->emplace_back(key_prefix + std::string(sv));
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantRecoLocalKvTriggerRetriever, MerchantRecoLocalKvTriggerRetriever);
}  // namespace platform
}  // namespace ks
