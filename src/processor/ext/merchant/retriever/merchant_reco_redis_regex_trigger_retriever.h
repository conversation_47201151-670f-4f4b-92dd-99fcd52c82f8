#pragma once

#include <memory>
#include <regex>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "dragon/src/processor/base/common_reco_base_retriever.h"
#include "ks/serving_util/infra_redis_client.h"
#include "redis_proxy_client/redis_proxy_client.h"
#include "serving_base/kv_client_wrapper/redis_cache_client.h"
#include "third_party/abseil/absl/strings/str_split.h"

namespace ks {
namespace platform {
class MerchantRecoRedisRegexTriggerRetriever : public CommonRecoBaseRetriever {
 public:
  MerchantRecoRedisRegexTriggerRetriever() {}

  void Retrieve(AddibleRecoContextInterface *context) override;

 protected:
  bool InitProcessor() override;

 private:
  struct ExtraItemAttrConfig {
    std::string name;
    AttrType type = AttrType::UNKNOWN;
    bool as_score = false;

    std::string DebugInfo() const {
      std::stringstream ss;
      ss << "name: " << name << ", type: " << static_cast<int>(type) << ", as_score: " << as_score;
      return ss.str();
    }
  };

  struct MerchantRedisTriggerConfig {
    // 带 cache 功能的 redis client，默认不会使用 cache
    std::shared_ptr<base::RedisCacheClient> redis_client = nullptr;
    // zrange 命令的 redis client
    ks::infra::RedisClient *infra_redis_client = nullptr;
    std::string cache_name;
    int cache_bits = 0;
    int cache_delay_delete_ms = 10 * 1000;
    int cache_expire_second = 60 * 60;

    bool is_huge_recall;
    std::string is_huge_recall_attr_name;
    std::vector<ExtraItemAttrConfig> extra_item_attrs;
    std::regex regex;
    std::string cluster_name;
    std::string key;
    std::string key_from_attr;
    std::string key_prefix;
    std::string reason_from_attr;
    std::string save_src_key_to_attr;
    std::string append_src_key_to_attr;
    std::string item_separator;
    std::string attr_separator;
    std::string save_err_code_to;
    std::string save_miss_key_to;

    int reason;
    int64 retrieve_num;
    int64 retrieve_num_per_key;
    int64 zrange_start;
    int timeout_ms;
    int retrieval_item_type;
    std::string redis_cmd;

    MerchantRedisTriggerConfig() {
      reason = -999;
      retrieval_item_type = 99;
      retrieve_num = 1000;
      retrieve_num_per_key = 200;
      timeout_ms = 10;
      redis_cmd = "get";
      cache_bits = 0;
      cache_expire_second = 3600;
      cache_delay_delete_ms = 10;
      zrange_start = 0;
      is_huge_recall = false;
      is_huge_recall_attr_name = "is_huge_recall";
    }

    std::string DebugInfo() const {
      std::stringstream ss;
      ss << "reason: " << reason << ", retrieve_num: " << retrieve_num
         << ", retrieve_num_per_key: " << retrieve_num_per_key
         << ", retrieval_item_type: " << retrieval_item_type << ", reason_from_attr: " << reason_from_attr
         << ", is_huge_recall: " << is_huge_recall << ", timeout_ms: " << timeout_ms
         << ", redis_cmd: " << redis_cmd << ", cluster_name: " << cluster_name << ", key: " << key
         << ", key_from_attr: " << key_from_attr << ", key_prefix: " << key_prefix
         << ", item_separator: " << item_separator << ", attr_separator: " << attr_separator
         << ", extra_item_attrs: [";
      for (const auto &info : extra_item_attrs) {
        ss << info.DebugInfo() << "; ";
      }
      return ss.str();
    }
  };

 private:
  bool ParseRedisTriggerConfig(MutableRecoContextInterface *context);

  std::shared_ptr<base::RedisCacheClient> GetRedisClient(const MerchantRedisTriggerConfig &redis_trigger);

  bool ParseAttrsConfig(const ::Json::Value &attrs_config, std::vector<ExtraItemAttrConfig> *attrs_vector);

  void InitStringKeys(MutableRecoContextInterface *context, std::vector<std::string> *string_keys,
                      const std::string &key_prefix, const MerchantRedisTriggerConfig &redis_trigger);
  void ExtractRegexString(MutableRecoContextInterface *context, const std::string &value_string,
                          std::vector<CommonRecoRetrieveResult> *retrieve_items, int reason,
                          const std::string &original_key, const MerchantRedisTriggerConfig &redis_trigger);
  void ExtractSplitString(MutableRecoContextInterface *context, const std::string &value_string,
                          std::vector<CommonRecoRetrieveResult> *retrieve_items, int reason,
                          const std::string &original_key, const MerchantRedisTriggerConfig &redis_trigger);
  bool SetItemAttr(MutableRecoContextInterface *context,
                   std::vector<CommonRecoRetrieveResult> *retrieve_items, int64 item_key, int index,
                   const absl::string_view &attr_value, const MerchantRedisTriggerConfig &redis_trigger);

 private:
  std::string redis_trigger_kconf_path_;
  std::string redis_trigger_kconf_gray_path_;
  std::vector<MerchantRedisTriggerConfig> redis_trigger_config_vec_;

  DISALLOW_COPY_AND_ASSIGN(MerchantRecoRedisRegexTriggerRetriever);
};

}  // namespace platform
}  // namespace ks
