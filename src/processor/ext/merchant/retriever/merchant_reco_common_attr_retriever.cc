#include "dragon/src/processor/ext/merchant/retriever/merchant_reco_common_attr_retriever.h"
#include <algorithm>
#include <set>
#include <memory>
#include "folly/container/F14Set.h"

namespace ks {
namespace platform {

void MerchantRecoCommonAttrRetriever::Retrieve(AddibleRecoContextInterface *context) {
  ParseDirectTriggerConfig(context);
  retrieve_items_.clear();
  for (auto& direct_trigger : direct_trigger_config_vec_) {
    folly::F14FastSet<uint64> exclude_items;
    if (!direct_trigger.exclude_items_in_attr.empty()) {
      if (auto int_list = context->GetIntListCommonAttr(direct_trigger.exclude_items_in_attr)) {
        exclude_items.insert(int_list->begin(), int_list->end());
      } else if (auto int_val = context->GetIntCommonAttr(direct_trigger.exclude_items_in_attr)) {
        exclude_items.insert(*int_val);
      } else {
        CL_LOG(INFO) << "cannot find int list common attr: " << direct_trigger.exclude_items_in_attr
                     << ", use empty exclude_items";
      }
    }
    if (auto int_list = context->GetIntListCommonAttr(direct_trigger.common_attr_name)) {
      int int_list_num = int_list->size();
      if (direct_trigger.retrieve_num >= 0
              && direct_trigger.retrieve_num < int_list_num && direct_trigger.random_pick) {
        int random_num = direct_trigger.retrieve_num;
        if (random_num <= 0) continue;
        if (exclude_items.empty() && random_num <= int_list_num / 2) {
          RandomPickNaive(*int_list, random_num, direct_trigger.retrieval_item_type, direct_trigger.reason);
        } else {
          RandomPickReservoirSampling(
            *int_list, random_num, direct_trigger.retrieval_item_type, direct_trigger.reason, exclude_items);
        }
      } else {
        OneByOnePick(*int_list, direct_trigger.retrieve_num,
            direct_trigger.retrieval_item_type, direct_trigger.reason, exclude_items);
      }
    } else if (auto int_val = context->GetIntCommonAttr(direct_trigger.common_attr_name)) {
      int64 item_key = direct_trigger.retrieval_item_type > 0 ?
          Util::GenKeysign(direct_trigger.retrieval_item_type, *int_val) : *int_val;
      if (!exclude_items.empty() && exclude_items.find(item_key) != exclude_items.end()) continue;
      retrieve_items_.emplace_back(item_key, direct_trigger.reason);
      context->SetIntItemAttr(item_key, "recall_assort", direct_trigger.recall_assort);
    } else {
      CL_LOG(INFO) << "cannot find int/int_list common attr: "
          << direct_trigger.common_attr_name << ", skipped retrieve from it.";
    }

    for (const auto& item : retrieve_items_) {
      context->SetIntItemAttr(item.item_key, "recall_assort", direct_trigger.recall_assort);
      if (direct_trigger.is_huge_recall) {
        context->SetIntItemAttr(item.item_key, "is_huge_recall", direct_trigger.is_huge_recall);
      }
    }

    if (!direct_trigger.save_result_to_common_attr.empty()) {
      AddItemIdToCommonAttr(context, retrieve_items_, direct_trigger.save_result_to_common_attr);
    } else {
      AddToRecoResults(context, retrieve_items_);
    }
  }

  CL_LOG(INFO) << "processor: " << GetName() << ", request_type: " << context->GetRequestType()
               << ", device_id: " << context->GetDeviceId() << ", user_id: " << context->GetUserId()
               << ", retrieve num: " << retrieve_items_.size();
}

bool MerchantRecoCommonAttrRetriever::InitProcessor() {
  direct_trigger_kconf_path_ = config()->GetString("direct_triger_kconf_path", "");
  direct_trigger_kconf_gray_path_ = config()->GetString("direct_trigger_kconf_gray_path", "");
  return true;
}

bool MerchantRecoCommonAttrRetriever::ParseDirectTriggerConfig(
      MutableRecoContextInterface *context) {
  auto default_val = std::make_shared<::Json::Value>();
  std::string direct_trigger_kconf_path_name = direct_trigger_kconf_path_;
  if (!direct_trigger_kconf_gray_path_.empty()) {
    direct_trigger_kconf_path_name = std::string(context->GetStringCommonAttr(
      direct_trigger_kconf_gray_path_).value_or(direct_trigger_kconf_path_).data());
  }
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<::Json::Value>>> trigger_config;
  trigger_config = ks::infra::KConf().Get(direct_trigger_kconf_path_name, default_val);

  if (!trigger_config || !trigger_config->Get()) {
    CL_LOG_EVERY_N(ERROR, 100) << "init kconf data config failed! kconf name is "
        << direct_trigger_kconf_path_name;
    return false;
  }
  std::shared_ptr<::Json::Value> json_config = trigger_config->Get();
  std::vector<MerchantDirectTriggerConfig> direct_trigger_config_vec;
  if (json_config && json_config->isArray()) {
    try {
      const ::Json::Value& configArray = *json_config;
      // CL_LOG(INFO) << "kconf_path:" << direct_trigger_kconf_path_name
      //     << ", JSON ITEM configArray.size():" << configArray.size()
      //     << ", toStyledString:" << configArray.toStyledString();
      for (const auto& item : configArray) {
        std::string switch_open = item["switch_open"].empty() ? "" : item["switch_open"].asString();
        if (switch_open.empty()) {
          continue;
        }
        auto is_switch_open = context->GetIntCommonAttr(switch_open).value_or(0);
        if (is_switch_open <= 0) {
          // CL_LOG(INFO) << "kconf_path:" << direct_trigger_kconf_path_name
          //     << ", switch_open_attr:" << switch_open
          //     << ", is_valid:" << is_switch_open;
          continue;
        }
        MerchantDirectTriggerConfig direct_trigger_config;
        direct_trigger_config.reason = item["reason"].empty() ? -999 : item["reason"].asInt();
        direct_trigger_config.recall_assort =
            item["recall_assort"].empty() ? 0 : item["recall_assort"].asInt();
        direct_trigger_config.retrieval_item_type =
            item["retrieval_item_type"].empty() ? 99 : item["retrieval_item_type"].asInt();
        direct_trigger_config.save_result_to_common_attr =
            item["save_result_to_common_attr"].empty() ? "" : item["save_result_to_common_attr"].asString();
        direct_trigger_config.random_pick =
            item["random_pick"].empty() ? false : item["random_pick"].asBool();
        direct_trigger_config.exclude_items_in_attr =
            item["exclude_items_in_attr"].empty() ? "" : item["exclude_items_in_attr"].asString();
        direct_trigger_config.common_attr_name =
            item["common_attr_name"].empty() ? "" : item["common_attr_name"].asString();
        std::string common_attr_name = direct_trigger_config.common_attr_name;
        if (!common_attr_name.empty()) {
          std::vector<std::string> common_attr_name_vec =
              absl::StrSplit(common_attr_name, ":", absl::SkipWhitespace());
          if (common_attr_name_vec.size() == 2) {
            direct_trigger_config.common_attr_name =
                context->GetStringCommonAttr(common_attr_name_vec[0])\
                .value_or(common_attr_name_vec[1]).data();
          }
        }
        direct_trigger_config.is_huge_recall = false;
        std::string is_huge_recall_str =
            item["is_huge_recall"].empty() ? "" : item["is_huge_recall"].asString();
        if (!is_huge_recall_str.empty()) {
          std::vector<std::string> is_huge_recall_vec =
              absl::StrSplit(is_huge_recall_str, ":", absl::SkipWhitespace());
          if (is_huge_recall_vec.size() == 2) {
            direct_trigger_config.is_huge_recall =
                context->GetIntCommonAttr(is_huge_recall_vec[0]).value_or(stoi(is_huge_recall_vec[1]))
                    > 0 ? true : false;
          }
        }
        std::string retrieve_item_num_str = std::to_string(direct_trigger_config.reason);
        retrieve_item_num_str.append("_uni_recall_retrieve_num:1000");
        const std::string& retrieve_item_num =
            item["retrieve_num"].empty() ? retrieve_item_num_str : item["retrieve_num"].asString();
        std::vector<std::string> retrieve_item_num_vec =
            absl::StrSplit(retrieve_item_num, ":", absl::SkipWhitespace());
        if (retrieve_item_num_vec.size() == 2) {
          direct_trigger_config.retrieve_num =
              context->GetIntCommonAttr(retrieve_item_num_vec[0]).value_or(stoi(retrieve_item_num_vec[1]));
        }
        if (direct_trigger_config.retrieve_num <= 0 || direct_trigger_config.common_attr_name.empty()) {
          continue;
        }
        direct_trigger_config_vec.push_back(direct_trigger_config);
        // CL_LOG(INFO) << direct_trigger_config.DebugInfo();
      }
    } catch(const std::exception& e) {
        CL_LOG_EVERY_N(ERROR, 1000) << "catch exception:" << e.what();
        return false;
    } catch (...) {  // 捕获所有其他类型的异常
        CL_LOG_EVERY_N(ERROR, 1000) << "catch unknown exception";
        return false;
    }
  } else {
    CL_LOG_EVERY_N(ERROR, 1000) << "Error: json_config expected an array but got something else.";
    return false;
  }

  if (direct_trigger_config_vec.empty()) {
    CL_LOG_EVERY_N(ERROR, 100) << "Error, kv_trigger_config_map is empty, retrieve_kconf_path:"
        << direct_trigger_kconf_path_name.c_str();
    return false;
  }
  direct_trigger_config_vec_ = std::move(direct_trigger_config_vec);
  return true;
}

void MerchantRecoCommonAttrRetriever::AddItemIdToCommonAttr(
          MutableRecoContextInterface *context,
          const std::vector<CommonRecoRetrieveResult>& retrieve_items,
          const std::string& output_common_attr_name) {
  std::vector<int64> retrieve_items_id;
  retrieve_items_id.reserve(retrieve_items.size());
  for (const auto& item : retrieve_items) {
    retrieve_items_id.push_back(item.item_key);
  }
  context->SetIntListCommonAttr(output_common_attr_name, std::move(retrieve_items_id));
  return;
}

void MerchantRecoCommonAttrRetriever::RandomPickNaive(
                        const absl::Span<const int64> &int_list,
                        int random_num, int item_type, int64 reason) {
  std::set<int64> chosen_indices;
  while (chosen_indices.size() < random_num) {
    int64 chosen_index = random_.GetInt(0, int_list.size() - 1);
    chosen_indices.insert(chosen_index);
  }
  for (int64 chosen_index : chosen_indices) {
    AddToRetrieveItems(int_list[chosen_index], item_type, reason);
  }
}

void MerchantRecoCommonAttrRetriever::RandomPickReservoirSampling(
    const absl::Span<const int64> &int_list, int random_num, int item_type, int64 reason,
    const folly::F14FastSet<uint64> &exclude_items) {
  std::vector<int64> chosen_indices;
  chosen_indices.reserve(random_num);
  int count = 0;
  for (int i = 0; i < int_list.size(); i++) {
    int64 item_key = item_type > 0 ? Util::GenKeysign(item_type, int_list[i]) : int_list[i];
    if (!exclude_items.empty() && exclude_items.find(item_key) != exclude_items.end()) continue;
    if (chosen_indices.size() < random_num) {
      chosen_indices.emplace_back(i);
    } else {
      int replace_index = random_.GetInt(0, count);
      if (replace_index < chosen_indices.size()) {
        chosen_indices[replace_index] = i;
      }
    }
    count++;
  }
  std::sort(chosen_indices.begin(), chosen_indices.end());
  for (int64 chosen_index : chosen_indices) {
    AddToRetrieveItems(int_list[chosen_index], item_type, reason);
  }
}

void MerchantRecoCommonAttrRetriever::OneByOnePick(const absl::Span<const int64> &int_list, int num_limit,
                                                 int item_type, int64 reason,
                                                 const folly::F14FastSet<uint64> &exclude_items) {
  int count = 0;
  for (int64 item_id : int_list) {
    if (num_limit >= 0 && count >= num_limit) break;
    int64 item_key = item_type > 0 ? Util::GenKeysign(item_type, item_id) : item_id;
    if (!exclude_items.empty() && exclude_items.find(item_key) != exclude_items.end()) continue;
    retrieve_items_.emplace_back(item_key, reason);
    ++count;
  }
}

void MerchantRecoCommonAttrRetriever::AddToRetrieveItems(int64 item_id, int item_type, int64 reason) {
  int64 item_key = item_type > 0 ? Util::GenKeysign(item_type, item_id) : item_id;
  retrieve_items_.emplace_back(item_key, reason);
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantRecoCommonAttrRetriever, MerchantRecoCommonAttrRetriever);

}  // namespace platform
}  // namespace ks
