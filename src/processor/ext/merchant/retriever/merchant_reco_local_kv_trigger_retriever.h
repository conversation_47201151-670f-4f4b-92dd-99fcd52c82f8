#pragma once

#include <map>
#include <string>
#include <unordered_set>
#include <unordered_map>
#include <utility>
#include <vector>
#include <memory>

#include "dragon/src/core/common_reco_util.h"
#include "dragon/src/processor/base/common_reco_base_retriever.h"
#include "base/hash_function/city.h"
#include "ks/reco_pub/reco/distributed_photo_info/attr_index/attr_index_util.h"
#include "dragon/src/processor/ext/merchant/retriever/merchant_reco_attr_index_manager.h"

namespace ks {
namespace platform {
class MerchantRecoLocalKvTriggerRetriever : public CommonRecoBaseRetriever {
 public:
  MerchantRecoLocalKvTriggerRetriever() {}

  void Retrieve(AddibleRecoContextInterface *context) override;

  std::function<void()> Purge() override {
    return []() { ks::reco::AttrIndexSingleton()->Stop(); };
  }

 protected:
  bool InitProcessor() override;

 private:
  struct ExtraItemColInfo {
    uint64 attr_key = 0;
    std::string attr_name;
    std::string as_name;
    ks::reco::protoutil::AttrType attr_type = ks::reco::protoutil::AttrType::kInvalid;

    ExtraItemColInfo(const std::string& attr_name, const std::string& as_name) {
      this->attr_name = attr_name;
      this->as_name = as_name;
    }
    ExtraItemColInfo() {}

    std::string DebugInfo() const {
      std::stringstream ss;
      ss << "attr_key: " << attr_key
         << ", attr_name: " << attr_name
         << ", as_name: " << as_name
         << ", attr_type: " << static_cast<int>(attr_type);
      return ss.str();
    }
  };

  struct MerchantKvTriggerConfig {
    int reason;
    int retrieval_item_type;
    int recall_assort;
    bool is_huge_recall;
    int64 retrieve_num;
    int64 retrieve_num_per_key_num;
    std::string key;
    std::string key_from_attr;
    std::string table_name;
    std::string key_prefix;
    std::string reason_from_attr;
    std::string score_item_attr;
    std::string retrieve_item_attr;
    std::string save_result_to_common_attr;
    std::string is_huge_recall_attr_name;
    std::string save_src_key_to_attr;
    std::string append_src_key_to_attr;
    std::vector<ExtraItemColInfo> extra_item_col_infos;

    MerchantKvTriggerConfig() {
      reason = -999;
      retrieval_item_type = 99;
      recall_assort = 0;
      is_huge_recall = false;
      retrieve_num = 1000;
      retrieve_num_per_key_num = 200;
      key = "";
      key_from_attr = "";
      table_name = "uni_table";
      key_prefix = "";
      reason_from_attr = "";
      score_item_attr = "";
      retrieve_item_attr = "";
      is_huge_recall = false;
      is_huge_recall_attr_name = "is_huge_recall";
    }
    std::string DebugInfo() const {
      std::stringstream ss;
      ss << "reason: " << reason
         << ", key: " << key
         << ", key_from_attr: " << key_from_attr
         << ", reason_from_attr: " << reason_from_attr
         << ", is_huge_recall: " << is_huge_recall
         << ", table_name: " << table_name
         << ", key_prefix: " << key_prefix
         << ", retrieve_item_num: " << retrieve_num
         << ", retrieve_num_per_key: " << retrieve_num_per_key_num
         << ", retrieve_item_attr: " << retrieve_item_attr
         << ", score_item_attr: " << score_item_attr
         << ", retrieval_item_type: " << retrieval_item_type
         << ", save_result_to_common_attr: " << save_result_to_common_attr
         << ", extra_item_col_infos: [";
      for (const auto& info : extra_item_col_infos) {
        ss << info.DebugInfo() << "; ";
      }
      return ss.str();
      }
  };

  struct KvIndexItemConfig {
    std::string origin_key;
    double score = 0.0;
    uint64_t primary_key = 0;
    int reason = 0;
    KvIndexItemConfig() {}
  };

 private:
  bool InitAttrIndex();
  void InitStringKeys(MutableRecoContextInterface *context,
                    const MerchantKvTriggerConfig& kv_trigger,
                    std::vector<std::string> *string_keys,
                    const std::string &key_prefix);

  void GetRetrieveResultFromLocalIndex(MutableRecoContextInterface *context,
                MerchantKvTriggerConfig* kv_trigger,
                ks::reco::AttrIndex *attr_index, const KvIndexItemConfig &trigger_key,
                std::vector<CommonRecoRetrieveResult>* attr_accessor);
  bool ParseAttrsConfig(const ::Json::Value& attrs_config, std::vector<ExtraItemColInfo>* attrs_vector);
  bool SetExtraItemAttr(MutableRecoContextInterface* context,
                    std::vector<CommonRecoRetrieveResult>* retrieve_items,
                    std::unordered_map<uint64, std::vector<int64>>* int_list_attr_map,
                    std::unordered_map<uint64, std::vector<std::string>>* str_list_attr_map,
                    std::unordered_map<uint64, std::vector<float>>* float_list_attr_map,
                    int64 item_key, const ExtraItemColInfo& col_info,
                    const std::unique_ptr<ks::reco::protoutil::AttrKVAutoReader>& reader, const int &index);
  bool ParseKvTriggerConfig(MutableRecoContextInterface* context);
  void AddItemIdToCommonAttr(MutableRecoContextInterface* context,
          const std::vector<CommonRecoRetrieveResult>& retrieve_items,
          const std::string& output_common_attr_name);

 private:
  std::string table_name_;
  std::string attr_index_kconf_path_;
  std::string kv_trigger_kconf_path_;
  std::string kv_trigger_kconf_gray_path_;
  std::vector<MerchantKvTriggerConfig> kv_trigger_config_vec_;

  DISALLOW_COPY_AND_ASSIGN(MerchantRecoLocalKvTriggerRetriever);
};

}  // namespace platform
}  // namespace ks
