#pragma once

#include <string>
#include <utility>
#include <vector>

#include "base/random/pseudo_random.h"
#include "dragon/src/processor/base/common_reco_base_retriever.h"

namespace ks {
namespace platform {

class MerchantRecoCommonAttrRetriever : public CommonRecoBaseRetriever {
 public:
  MerchantRecoCommonAttrRetriever() : random_(base::GetTimestamp()) {}
  void Retrieve(AddibleRecoContextInterface *context) override;

 private:
  struct RetrieveAttr {
    std::string name;
    int64 reason = 0;
    int item_type = 0;
    int num_limit = -1;
    bool random_pick = false;
  };

  struct MerchantDirectTriggerConfig {
    int reason;
    int retrieval_item_type;
    int recall_assort;
    bool is_huge_recall = false;
    int64 retrieve_num;
    std::string common_attr_name;
    std::string save_result_to_common_attr;
    bool random_pick = false;
    std::string exclude_items_in_attr;

    MerchantDirectTriggerConfig() {
      reason = -999;
      retrieval_item_type = 99;
      recall_assort = 0;
      is_huge_recall = false;
      retrieve_num = 1000;
      common_attr_name = "";
      save_result_to_common_attr = "";
      random_pick = false;
      exclude_items_in_attr = "";
    }

    MerchantDirectTriggerConfig(const MerchantDirectTriggerConfig& other)
      : reason(other.reason),
        retrieval_item_type(other.retrieval_item_type),
        recall_assort(other.recall_assort),
        is_huge_recall(other.is_huge_recall),
        retrieve_num(other.retrieve_num),
        common_attr_name(other.common_attr_name),
        save_result_to_common_attr(other.save_result_to_common_attr),
        random_pick(other.random_pick),
        exclude_items_in_attr(other.exclude_items_in_attr) {}

      std::string DebugInfo() const {
        std::stringstream ss;
        ss << "reason: " << reason
           << ", retrieval_item_type: " << retrieval_item_type
           << ", retrieve_num: " << retrieve_num
           << ", common_attr_name: " << common_attr_name
           << ", exclude_items_in_attr: " << exclude_items_in_attr
           << ", save_result_to_common_attr: " << save_result_to_common_attr;
        return ss.str();
      }
  };

 protected:
  bool InitProcessor() override;

 private:
  void RandomPickNaive(const absl::Span<const int64> &int_list, int random_num, int item_type, int64 reason);

  void RandomPickReservoirSampling(const absl::Span<const int64> &int_list, int random_num, int item_type,
                                   int64 reason, const folly::F14FastSet<uint64> &exclude_items);
  void OneByOnePick(const absl::Span<const int64> &int_list, int num_limit, int item_type, int64 reason,
                    const folly::F14FastSet<uint64> &exclude_items);
  void AddToRetrieveItems(int64 item_id, int item_type, int64 reason);

  bool ParseDirectTriggerConfig(MutableRecoContextInterface *context);

  void AddItemIdToCommonAttr(MutableRecoContextInterface *context,
          const std::vector<CommonRecoRetrieveResult>& retrieve_items,
          const std::string& output_common_attr_name);

 private:
  std::vector<CommonRecoRetrieveResult> retrieve_items_;

  base::PseudoRandom random_;
  std::string direct_trigger_kconf_path_;
  std::string direct_trigger_kconf_gray_path_;
  std::vector<MerchantDirectTriggerConfig> direct_trigger_config_vec_;

  DISALLOW_COPY_AND_ASSIGN(MerchantRecoCommonAttrRetriever);
};

}  // namespace platform
}  // namespace ks
