#include "dragon/src/processor/ext/merchant/retriever/merchant_reco_redis_regex_trigger_retriever.h"

namespace ks {
namespace platform {

void MerchantRecoRedisRegexTriggerRetriever::Retrieve(AddibleRecoContextInterface *context) {
  int64 all_recall_num = 0;
  int64 redis_request_time = 0;
  int64 regex_parse_time = 0;
  ParseRedisTriggerConfig(context);
  for (auto &redis_trigger : redis_trigger_config_vec_) {
    if (redis_trigger.redis_cmd == "get" && !redis_trigger.redis_client) {
      redis_trigger.redis_client = GetRedisClient(redis_trigger);
    } else if (redis_trigger.redis_cmd == "zrange" && !redis_trigger.infra_redis_client) {
      redis_trigger.infra_redis_client =
          ks::serving_util::InfraReidsClient::Singleton()->GetClientKcc(redis_trigger.cluster_name);
    }
    if (!redis_trigger.redis_client && !redis_trigger.infra_redis_client) {
      CL_LOG_ERROR_EVERY("redis_regex_trigger_retrieve", "client_init_fail:" + redis_trigger.cluster_name,
                         100)
          << "redis client init failed, cluster name: " << redis_trigger.cluster_name
          << ", cache_name: " << redis_trigger.cache_name << ", cache_bits: " << redis_trigger.cache_bits
          << ", cache_delay_delete_ms: " << redis_trigger.cache_delay_delete_ms
          << ", cache_expire_second: " << redis_trigger.cache_expire_second;
      continue;
    }

    if (redis_trigger.retrieve_num <= 0) {
      CL_LOG(INFO) << "MerchantRecoRedisRegexTriggerRetriever cancelled: retrieve_num="
                   << redis_trigger.retrieve_num;
    }

    int64 start_ts = base::GetTimestamp();
    thread_local std::vector<std::string> string_keys;
    string_keys.clear();

    int64 zrange_start = redis_trigger.zrange_start;
    int64 zrange_stop = zrange_start + redis_trigger.retrieve_num;

    std::string key_prefix = "";
    if (redis_trigger.key.empty()) {
      key_prefix = redis_trigger.key_prefix;
    }

    InitStringKeys(context, &string_keys, key_prefix, redis_trigger);
    if (string_keys.empty()) {
      CL_LOG(INFO) << "MerchantRecoRedisRegexTriggerRetriever cancelled: empty keys.";
      continue;
    }

    std::vector<std::string> string_values;
    ks::infra::RedisErrorCode err_code = ks::infra::KS_INF_REDIS_NO_ERROR;
    if (redis_trigger.redis_cmd == "get") {
      string_values.reserve(string_keys.size());
      err_code =
          redis_trigger.redis_client->MGet(string_keys, &string_values, true, redis_trigger.timeout_ms);
    } else if (redis_trigger.redis_cmd == "zrange") {
      string_values.reserve(zrange_stop - zrange_start);
      err_code = redis_trigger.infra_redis_client->ZGetMembersByIndex(
          string_keys[0], zrange_start, zrange_stop, &string_values, nullptr, redis_trigger.timeout_ms);
    } else {
      CL_LOG_ERROR_EVERY("redis_regex_trigger_retrieve",
                         redis_trigger.cluster_name + ":" + "invalid redis_cmd", 100)
          << GetName() << " cancelled:"
          << " redis_cmd_ value: " << redis_trigger.redis_cmd << "is invalid";
      continue;
    }

    if (!redis_trigger.save_err_code_to.empty()) {
      context->SetIntCommonAttr(redis_trigger.save_err_code_to, static_cast<int>(err_code));
    }
    if (err_code != ks::infra::KS_INF_REDIS_NO_ERROR) {
      if (err_code == ks::infra::KS_INF_REDIS_ERR_TIMEOUT) {
        CL_LOG_WARNING_EVERY("redis_regex_retrieve",
                             redis_trigger.cluster_name + ":" + ks::infra::err2str(err_code), 100)
            << "CommonRecoRedisRegexRetriever cancelled: get value failed."
            << " err_code: " << err_code << ", err_msg: " << ks::infra::err2str(err_code)
            << ", cluster_name: " << redis_trigger.cluster_name << ", processor: " << GetName();
      } else {
        CL_LOG_ERROR_EVERY("redis_regex_retrieve",
                           redis_trigger.cluster_name + ":" + ks::infra::err2str(err_code), 100)
            << "CommonRecoRedisRegexRetriever cancelled: get value failed."
            << " err_code: " << err_code << ", err_msg: " << ks::infra::err2str(err_code)
            << ", cluster_name: " << redis_trigger.cluster_name << ", processor: " << GetName();
      }
      continue;
    }

    int64 redis_response_ts = base::GetTimestamp();

    if (!redis_trigger.save_miss_key_to.empty() && string_keys.size() == string_values.size()) {
      std::vector<std::string> miss_key_list;
      for (int i = 0; i < string_values.size(); i++) {
        if (string_values[i].empty() && key_prefix.size() < string_keys[i].size()) {
          miss_key_list.emplace_back(string_keys[i].substr(key_prefix.size()));
        }
      }
      context->SetStringListCommonAttr(redis_trigger.save_miss_key_to, std::move(miss_key_list));
    }

    absl::optional<absl::Span<const int64>> reason_list;
    if (!redis_trigger.reason_from_attr.empty()) {
      reason_list = context->GetIntListCommonAttr(redis_trigger.reason_from_attr);
      if (!reason_list) {
        CL_LOG_EVERY_N(WARNING, 100) << "reason_from_attr=" << redis_trigger.reason_from_attr << " not found";
      }
    }

    std::vector<CommonRecoRetrieveResult> retrieve_items;
    for (int i = 0; i < string_values.size(); ++i) {
      if (redis_trigger.retrieve_num > 0 && retrieve_items.size() >= redis_trigger.retrieve_num) {
        break;
      }
      int reason = redis_trigger.reason;
      if (reason_list && i < reason_list->size()) {
        reason = reason_list->at(i);
      }
      if (!redis_trigger.item_separator.empty() || !redis_trigger.attr_separator.empty()) {
        ExtractSplitString(context, string_values[i], &retrieve_items, reason,
                           i < string_keys.size() ? string_keys.at(i).substr(key_prefix.size()) : "",
                           redis_trigger);
      } else {
        ExtractRegexString(context, string_values[i], &retrieve_items, reason,
                           i < string_keys.size() ? string_keys.at(i).substr(key_prefix.size()) : "",
                           redis_trigger);
      }
    }

    int64 regex_extract_ts = base::GetTimestamp();
    AddToRecoResults(context, retrieve_items);
    all_recall_num += retrieve_items.size();
    redis_request_time += redis_response_ts - start_ts;
    regex_parse_time += regex_extract_ts - redis_response_ts;
  }

  CL_LOG(INFO) << "processor: " << GetName() << ", request_type: " << context->GetRequestType()
               << ", device_id: " << context->GetDeviceId() << ", user_id: " << context->GetUserId()
               << ", redis_request_time: " << redis_request_time / 1000.0 << "ms"
               << ", regex_parse_time: " << regex_parse_time / 1000.0 << "ms"
               << ", retrieve num: " << all_recall_num;
}

bool MerchantRecoRedisRegexTriggerRetriever::InitProcessor() {
  redis_trigger_kconf_path_ = config()->GetString("redis_triger_kconf_path", "");
  redis_trigger_kconf_gray_path_ = config()->GetString("redis_trigger_kconf_gray_path", "");
  return true;
}

bool MerchantRecoRedisRegexTriggerRetriever::ParseRedisTriggerConfig(MutableRecoContextInterface *context) {
  auto default_val = std::make_shared<::Json::Value>();
  std::string redis_trigger_kconf_path_name = redis_trigger_kconf_path_;
  if (!redis_trigger_kconf_gray_path_.empty()) {
    redis_trigger_kconf_path_name = std::string(context->GetStringCommonAttr(redis_trigger_kconf_gray_path_)
                                                    .value_or(redis_trigger_kconf_path_)
                                                    .data());
  }
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<::Json::Value>>> trigger_config;
  trigger_config = ks::infra::KConf().Get(redis_trigger_kconf_path_name, default_val);

  if (!trigger_config || !trigger_config->Get()) {
    CL_LOG_EVERY_N(ERROR, 100) << "init kconf data config failed! kconf name is "
                               << redis_trigger_kconf_path_name;
    return false;
  }
  std::shared_ptr<::Json::Value> json_config = trigger_config->Get();
  std::vector<MerchantRedisTriggerConfig> redis_trigger_config_vec;

  if (json_config && json_config->isArray()) {
    try {
      const ::Json::Value &configArray = *json_config;
      for (const auto &item : configArray) {
        std::string switch_open = item["switch_open"].empty() ? "" : item["switch_open"].asString();
        if (switch_open.empty()) {
          continue;
        }
        auto is_switch_open = context->GetIntCommonAttr(switch_open).value_or(0);
        if (is_switch_open <= 0) {
          continue;
        }
        MerchantRedisTriggerConfig redis_trigger_config;
        redis_trigger_config.cluster_name =
            item["cluster_name"].empty() ? "" : item["cluster_name"].asString();
        redis_trigger_config.timeout_ms = item["timeout_ms"].empty() ? 10 : item["timeout_ms"].asInt();
        redis_trigger_config.cache_name =
            item["cache_name"].empty() ? redis_trigger_config.cluster_name : item["cache_name"].asString();
        redis_trigger_config.cache_bits = item["cache_bits"].empty() ? 0 : item["cache_bits"].asInt();
        redis_trigger_config.cache_delay_delete_ms =
            item["cache_delay_delete_ms"].empty() ? 10 * 1000 : item["cache_delay_delete_ms"].asInt();
        redis_trigger_config.cache_expire_second =
            item["cache_expire_second"].empty() ? 60 * 60 : item["cache_expire_second"].asInt();
        redis_trigger_config.redis_cmd = item["redis_cmd"].empty() ? "get" : item["redis_cmd"].asString();

        if (redis_trigger_config.redis_cmd == "get") {
          redis_trigger_config.redis_client = GetRedisClient(redis_trigger_config);
          if (!redis_trigger_config.redis_client) {
            LOG(ERROR) << GetName() << " init failed! redis client init failed, cluster name: "
                       << redis_trigger_config.cluster_name
                       << ", cache_name: " << redis_trigger_config.cache_name
                       << ", cache_bits: " << redis_trigger_config.cache_bits
                       << ", cache_delay_delete_ms: " << redis_trigger_config.cache_delay_delete_ms
                       << ", cache_expire_second: " << redis_trigger_config.cache_expire_second;
            continue;
          }
        } else if (redis_trigger_config.redis_cmd == "zrange") {
          redis_trigger_config.infra_redis_client =
              ks::serving_util::InfraReidsClient::Singleton()->GetClientKcc(
                  redis_trigger_config.cluster_name);
          if (!redis_trigger_config.infra_redis_client) {
            LOG(ERROR) << GetName() << " init failed! infra redis client init failed, cluster name: "
                       << redis_trigger_config.cluster_name;
            continue;
          }
        } else {
          LOG(ERROR) << GetName() << " init failed!"
                     << " redis_cmd_ value: " << redis_trigger_config.redis_cmd << "is invalid";
          continue;
        }

        redis_trigger_config.is_huge_recall_attr_name = item["is_huge_recall_attr_name"].empty()
                                                            ? "is_huge_recall"
                                                            : item["is_huge_recall_attr_name"].asString();
        redis_trigger_config.key = item["key"].empty() ? "" : item["key"].asString();
        redis_trigger_config.key_from_attr =
            item["key_from_attr"].empty() ? "" : item["key_from_attr"].asString();
        std::string key_from_attr = redis_trigger_config.key_from_attr;
        if (!key_from_attr.empty()) {
          std::vector<std::string> key_from_attr_vec =
              absl::StrSplit(key_from_attr, ":", absl::SkipWhitespace());
          if (key_from_attr_vec.size() == 2) {
            redis_trigger_config.key_from_attr =
                context->GetStringCommonAttr(key_from_attr_vec[0]).value_or(key_from_attr_vec[1]).data();
          }
        }

        if (redis_trigger_config.key.empty() && redis_trigger_config.key_from_attr.empty()) {
          LOG(ERROR) << "MerchantRecoRedisRegexTriggerRetriever init failed! 'key_from_attr' and 'key' are "
                        "both empty.";
          continue;
        }

        std::string key_prefix = item["key_prefix"].empty() ? "" : item["key_prefix"].asString();
        if (!key_prefix.empty()) {
          std::vector<std::string> key_prefix_vec = absl::StrSplit(key_prefix, ":", absl::SkipWhitespace());
          if (key_prefix_vec.size() == 2) {
            redis_trigger_config.key_prefix =
                context->GetStringCommonAttr(key_prefix_vec[0]).value_or(key_prefix_vec[1]).data();
          }
        }

        redis_trigger_config.is_huge_recall = false;
        std::string is_huge_recall_str =
            item["is_huge_recall"].empty() ? "" : item["is_huge_recall"].asString();
        if (!is_huge_recall_str.empty()) {
          std::vector<std::string> is_huge_recall_vec =
              absl::StrSplit(is_huge_recall_str, ":", absl::SkipWhitespace());
          if (is_huge_recall_vec.size() == 2) {
            redis_trigger_config.is_huge_recall =
                context->GetIntCommonAttr(is_huge_recall_vec[0])\
                .value_or(stoi(is_huge_recall_vec[1])) > 0 ? true : false;
          }
        }

        std::string retrieve_item_num_str = std::to_string(redis_trigger_config.reason);
        retrieve_item_num_str.append("_uni_recall_retrieve_num:1000");
        const std::string &retrieve_item_num =
            item["retrieve_num"].empty() ? retrieve_item_num_str : item["retrieve_num"].asString();
        std::vector<std::string> retrieve_item_num_vec =
            absl::StrSplit(retrieve_item_num, ":", absl::SkipWhitespace());
        if (retrieve_item_num_vec.size() == 2) {
          redis_trigger_config.retrieve_num =
              context->GetIntCommonAttr(retrieve_item_num_vec[0]).value_or(stoi(retrieve_item_num_vec[1]));
        }

        std::string retrieve_num_per_key_str = std::to_string(redis_trigger_config.reason);
        retrieve_num_per_key_str.append("_uni_recall_retrieve_num_per_key:200");
        const std::string &retrieve_num_per_key = item["retrieve_num_per_key"].empty()
                                                      ? retrieve_num_per_key_str
                                                      : item["retrieve_num_per_key"].asString();
        std::vector<std::string> retrieve_num_per_key_vec =
            absl::StrSplit(retrieve_num_per_key, ":", absl::SkipWhitespace());
        if (retrieve_num_per_key_vec.size() == 2) {
          redis_trigger_config.retrieve_num_per_key =
              context->GetIntCommonAttr(retrieve_num_per_key_vec[0])\
              .value_or(stoi(retrieve_num_per_key_vec[1]));
        }

        if (redis_trigger_config.retrieve_num <= 0) {
          continue;
        }

        std::string zrange_start = item["zrange_start"].empty() ? "" : item["zrange_start"].asString();
        if (!zrange_start.empty()) {
          std::vector<std::string> zrange_start_vec =
              absl::StrSplit(zrange_start, ":", absl::SkipWhitespace());
          if (zrange_start_vec.size() == 2) {
            redis_trigger_config.zrange_start =
                context->GetIntCommonAttr(zrange_start_vec[0]).value_or(stoi(zrange_start_vec[1]));
          }
        }

        if (!item["extra_item_attrs"].empty()) {
          if (!ParseAttrsConfig(item["extra_item_attrs"], &redis_trigger_config.extra_item_attrs)) {
            LOG(ERROR) << "MerchantRecoRedisRegexTriggerRetriever init failed! extra_item_attrs is invalid.";
            continue;
          }
        }

        redis_trigger_config.save_miss_key_to =
            item["save_miss_key_to"].empty() ? "" : item["save_miss_key_to"].asString();

        std::string extra_item_attr_as_score =
            item["extra_item_attr_as_score"].empty() ? "" : item["extra_item_attr_as_score"].asString();
        if (!extra_item_attr_as_score.empty()) {
          for (auto &cfg : redis_trigger_config.extra_item_attrs) {
            if (cfg.name == extra_item_attr_as_score) {
              cfg.as_score = true;
            }
          }
        }

        redis_trigger_config.reason = item["reason"].empty() ? 0 : item["reason"].asInt();
        redis_trigger_config.reason_from_attr =
            item["reason_from_attr"].empty() ? "" : item["reason_from_attr"].asString();
        redis_trigger_config.retrieval_item_type =
            item["retrieval_item_type"].empty() ? 0 : item["retrieval_item_type"].asInt();
        redis_trigger_config.save_src_key_to_attr =
            item["save_src_key_to_attr"].empty() ? "" : item["save_src_key_to_attr"].asString();
        redis_trigger_config.append_src_key_to_attr =
            item["append_src_key_to_attr"].empty() ? "" : item["append_src_key_to_attr"].asString();

        redis_trigger_config.item_separator =
            item["item_separator"].empty() ? "" : item["item_separator"].asString();
        redis_trigger_config.attr_separator =
            item["attr_separator"].empty() ? "" : item["attr_separator"].asString();
        redis_trigger_config.save_err_code_to =
            item["save_err_code_to"].empty() ? "" : item["save_err_code_to"].asString();
        if (!redis_trigger_config.item_separator.empty() || !redis_trigger_config.attr_separator.empty()) {
          redis_trigger_config_vec.push_back(redis_trigger_config);
          continue;
        }

        std::string item_pattern = item["item_pattern"].empty() ? "" : item["item_pattern"].asString();
        if (item_pattern.empty()) {
          item_pattern = item["item_regex"].empty() ? "" : item["item_regex"].asString();
        }
        if (item_pattern.empty()) {
          LOG(ERROR) << "MerchantRecoRedisRegexTriggerRetriever init failed! item_regex is empty.";
          continue;
        }

        try {
          redis_trigger_config.regex = std::regex(item_pattern);
        } catch (std::exception &e) {
          CL_LOG(ERROR) << "MerchantRecoRedisRegexTriggerRetriever init failed! item_regex is invalid."
                        << " " << e.what() << " Item regex: " << item_pattern;
          continue;
        } catch (...) {
          CL_LOG(ERROR) << "MerchantRecoRedisRegexTriggerRetriever init failed! item_regex is invalid."
                        << " item regex: " << item_pattern;
          continue;
        }

        redis_trigger_config_vec.push_back(redis_trigger_config);
      }
    } catch (const std::exception &e) {
      CL_LOG_EVERY_N(ERROR, 1000) << "catch exception:" << e.what();
      return false;
    } catch (...) {  // 捕获所有其他类型的异常
      CL_LOG_EVERY_N(ERROR, 1000) << "catch unknown exception";
      return false;
    }
  } else {
    CL_LOG_EVERY_N(ERROR, 1000) << "Error: json_config expected an array but got something else.";
    return false;
  }

  if (redis_trigger_config_vec.empty()) {
    CL_LOG_EVERY_N(ERROR, 1000) << "Error, redis_trigger_config_map is empty, retrieve_kconf_path:"
                                << redis_trigger_kconf_path_name.c_str();
    return false;
  }
  redis_trigger_config_vec_ = std::move(redis_trigger_config_vec);
  return true;
}

std::shared_ptr<base::RedisCacheClient> MerchantRecoRedisRegexTriggerRetriever::GetRedisClient(
    const MerchantRedisTriggerConfig &redis_trigger) {
  auto redis_client = std::make_shared<base::RedisCacheClient>();
  if (redis_client->Initialize(redis_trigger.cluster_name, redis_trigger.timeout_ms, redis_trigger.cache_name,
                               redis_trigger.cache_bits, redis_trigger.cache_delay_delete_ms,
                               redis_trigger.cache_expire_second)) {
    return std::move(redis_client);
  }
  return nullptr;
}

bool MerchantRecoRedisRegexTriggerRetriever::ParseAttrsConfig(
    const ::Json::Value &attrs_config, std::vector<ExtraItemAttrConfig> *attrs_vector) {
  for (const auto &item : attrs_config) {
    const std::string &attr_name = item["name"].empty() ? "" : item["name"].asString();
    if (attr_name.empty()) {
      LOG_EVERY_N(ERROR, 1000) << "MerchantRecoRedisRegexTriggerRetriever init failed! name is required for "
                               << attrs_config.toStyledString();
      return false;
    }
    AttrType type = RecoUtil::ParseAttrType(item["type"].empty() ? "string" : item["type"].asString());
    if (type == AttrType::UNKNOWN) {
      LOG(ERROR) << "MerchantRecoRedisRegexTriggerRetriever init failed! invalid 'type' value: "
                 << item["type"];
      return false;
    }
    bool as_score = item["as_score"].empty() ? false : item["as_score"].asBool();
    if (as_score && type != AttrType::FLOAT && type != AttrType::INT) {
      LOG(ERROR)
          << "MerchantRecoRedisRegexTriggerRetriever init failed! as_score is only for int/double attr";
      return false;
    }
    attrs_vector->push_back({
        .name = attr_name,
        .type = type,
        .as_score = as_score,
    });
  }
  return true;
}

void MerchantRecoRedisRegexTriggerRetriever::ExtractRegexString(
    MutableRecoContextInterface *context, const std::string &value_string,
    std::vector<CommonRecoRetrieveResult> *retrieve_items, int reason, const std::string &original_key,
    const MerchantRedisTriggerConfig &redis_trigger) {
  std::regex_iterator<std::string::const_iterator> regex_begin(value_string.cbegin(), value_string.cend(),
                                                               redis_trigger.regex);
  std::vector<int> error_counter;
  error_counter.resize(redis_trigger.extra_item_attrs.size(), 0);
  int extract_item_key_error_counter = 0;
  int extract_attr_field_error_counter = 0;
  int retrieve_num_per_key_counter = 0;
  int reset_item_type = GetResetItemType(context);
  for (auto iter = regex_begin; iter != std::sregex_iterator(); iter++) {
    if ((redis_trigger.retrieve_num > 0 && retrieve_items->size() >= redis_trigger.retrieve_num) ||
        (redis_trigger.retrieve_num_per_key > 0 &&
         retrieve_num_per_key_counter >= redis_trigger.retrieve_num_per_key)) {
      break;
    }
    std::string sub_str = iter->str();
    std::match_results<std::string::iterator> results;
    if (std::regex_match(sub_str.begin(), sub_str.end(), results, redis_trigger.regex)) {
      // results 包含整体+子捕获，results.size 应为 1+子捕获数量
      if (results.size() != redis_trigger.extra_item_attrs.size() + 2) {
        CL_LOG_EVERY_N(INFO, 1000) << "Extract item info failed, attr fields mismatch.";
        ++extract_attr_field_error_counter;
        continue;
      }
      // results.begin=整体的匹配，results.begin+1=匹配到的 item_key
      auto sub_iter = results.begin() + 1;
      uint64 item_key = 0;
      if (!base::StringToUint64(sub_iter->str(), &item_key)) {
        CL_LOG_EVERY_N(INFO, 1000) << "Extract item info failed, invalid pid: " << sub_iter->str();
        ++extract_item_key_error_counter;
        continue;
      }
      if (reset_item_type >= 0) {
        item_key = RecoUtil::ResetItemType(reset_item_type, item_key);
      } else if (redis_trigger.retrieval_item_type > 0) {
        item_key = Util::GenKeysign(redis_trigger.retrieval_item_type, item_key);
      }
      retrieve_items->emplace_back(item_key, reason);
      retrieve_num_per_key_counter++;
      if (!redis_trigger.save_src_key_to_attr.empty()) {
        context->SetStringItemAttr(item_key, redis_trigger.save_src_key_to_attr, original_key);
      }
      if (!redis_trigger.append_src_key_to_attr.empty()) {
        context->AppendStringListItemAttr(item_key, redis_trigger.append_src_key_to_attr, original_key);
      }
      int index = 0;
      for (sub_iter = results.begin() + 2; sub_iter != results.end(); sub_iter++, index++) {
        if (!SetItemAttr(context, retrieve_items, item_key, index, sub_iter->str(), redis_trigger)) {
          ++error_counter[index];
        }
      }
    }
  }

  if (extract_item_key_error_counter > 0) {
    CL_LOG_WARNING_COUNT(extract_item_key_error_counter, "redis_regex_retrieve",
                         redis_trigger.cluster_name + ":extract_item_key_error")
        << "extract_item_key_error " << extract_item_key_error_counter
        << " times, cluster: " << redis_trigger.cluster_name << ", processor: " << GetName();
  }
  if (extract_attr_field_error_counter > 0) {
    CL_LOG_WARNING_COUNT(extract_attr_field_error_counter, "redis_regex_retrieve",
                         redis_trigger.cluster_name + ":extract_attr_field_error")
        << "extract_attr_field_error " << extract_attr_field_error_counter
        << " times, cluster: " << redis_trigger.cluster_name << ", processor: " << GetName();
  }
  for (int i = 0; i < error_counter.size(); ++i) {
    int count = error_counter[i];
    if (count > 0) {
      const std::string &attr_name = redis_trigger.extra_item_attrs[i].name;
      CL_LOG_WARNING_COUNT(count, "redis_regex_retrieve",
                           redis_trigger.cluster_name + ":extract_attr_error:" + attr_name)
          << "extract_attr_error: " << attr_name << " " << count
          << " times, cluster: " << redis_trigger.cluster_name << ", processor: " << GetName();
    }
  }
}

void MerchantRecoRedisRegexTriggerRetriever::ExtractSplitString(
    MutableRecoContextInterface *context, const std::string &value_string,
    std::vector<CommonRecoRetrieveResult> *retrieve_items, int reason, const std::string &original_key,
    const MerchantRedisTriggerConfig &redis_trigger) {
  if (value_string.empty()) {
    return;
  }
  std::vector<int> error_counter;
  error_counter.resize(redis_trigger.extra_item_attrs.size(), 0);
  int extract_item_key_error_counter = 0;
  int extract_attr_field_error_counter = 0;
  int retrieve_num_per_key_counter = 0;
  std::vector<absl::string_view> item_string_vector;
  if (redis_trigger.item_separator.empty()) {
    item_string_vector.emplace_back(value_string);
  } else {
    item_string_vector = absl::StrSplit(value_string, redis_trigger.item_separator);
  }
  int reset_item_type = GetResetItemType(context);
  for (auto &sub_str : item_string_vector) {
    if ((redis_trigger.retrieve_num > 0 && retrieve_items->size() >= redis_trigger.retrieve_num) ||
        (redis_trigger.retrieve_num_per_key > 0 &&
         retrieve_num_per_key_counter >= redis_trigger.retrieve_num_per_key)) {
      break;
    }
    std::vector<absl::string_view> results;
    if (redis_trigger.attr_separator.empty()) {
      results.emplace_back(sub_str);
    } else {
      results = absl::StrSplit(sub_str, redis_trigger.attr_separator);
    }

    if (results.size() != redis_trigger.extra_item_attrs.size() + 1) {
      CL_LOG_EVERY_N(INFO, 1000) << "Extract item info failed, attr fields mismatch. item_sub_string:"
                                 << sub_str;
      ++extract_attr_field_error_counter;
      continue;
    }
    // results.begin=item_key
    auto sub_iter = results.begin();
    uint64 item_key = 0;
    if (!absl::SimpleAtoi(*sub_iter, &item_key)) {
      CL_LOG_EVERY_N(INFO, 1000) << "Extract item info failed, invalid pid: " << *sub_iter;
      ++extract_item_key_error_counter;
      continue;
    }

    if (reset_item_type >= 0) {
      item_key = RecoUtil::ResetItemType(reset_item_type, item_key);
    } else if (redis_trigger.retrieval_item_type > 0) {
      item_key = Util::GenKeysign(redis_trigger.retrieval_item_type, item_key);
    }
    retrieve_items->emplace_back(item_key, reason);
    retrieve_num_per_key_counter++;
    if (redis_trigger.is_huge_recall) {
      context->SetIntItemAttr(item_key, redis_trigger.is_huge_recall_attr_name, 1);
    }
    if (!redis_trigger.save_src_key_to_attr.empty()) {
      context->SetStringItemAttr(item_key, redis_trigger.save_src_key_to_attr, original_key);
    }
    if (!redis_trigger.append_src_key_to_attr.empty()) {
      context->AppendStringListItemAttr(item_key, redis_trigger.append_src_key_to_attr, original_key);
    }
    int index = 0;
    for (auto sub_iter = results.begin() + 1; sub_iter != results.end(); sub_iter++, index++) {
      if (!SetItemAttr(context, retrieve_items, item_key, index, *sub_iter, redis_trigger)) {
        ++error_counter[index];
      }
    }
  }

  if (extract_item_key_error_counter > 0) {
    CL_LOG_WARNING_COUNT(extract_item_key_error_counter, "redis_regex_retrieve",
                         redis_trigger.cluster_name + ":extract_item_key_error")
        << "extract_item_key_error " << extract_item_key_error_counter
        << " times, cluster: " << redis_trigger.cluster_name << ", processor: " << GetName();
  }
  if (extract_attr_field_error_counter > 0) {
    CL_LOG_WARNING_COUNT(extract_attr_field_error_counter, "redis_regex_retrieve",
                         redis_trigger.cluster_name + ":extract_attr_field_error")
        << "extract_attr_field_error " << extract_attr_field_error_counter
        << " times, cluster: " << redis_trigger.cluster_name << ", processor: " << GetName();
  }
  for (int i = 0; i < error_counter.size(); ++i) {
    int count = error_counter[i];
    if (count > 0) {
      const std::string &attr_name = redis_trigger.extra_item_attrs[i].name;
      CL_LOG_WARNING_COUNT(count, "redis_regex_retrieve",
                           redis_trigger.cluster_name + ":extract_attr_error:" + attr_name)
          << "extract_attr_error: " << attr_name << " " << count
          << " times, cluster: " << redis_trigger.cluster_name << ", processor: " << GetName();
    }
  }
}

bool MerchantRecoRedisRegexTriggerRetriever::SetItemAttr(
    MutableRecoContextInterface *context, std::vector<CommonRecoRetrieveResult> *retrieve_items,
    int64 item_key, int index, const absl::string_view &attr_value,
    const MerchantRedisTriggerConfig &redis_trigger) {
  const std::string &attr_name = redis_trigger.extra_item_attrs[index].name;
  const AttrType &type = redis_trigger.extra_item_attrs[index].type;
  bool as_score = redis_trigger.extra_item_attrs[index].as_score;
  switch (type) {
    case AttrType::STRING: {
      context->SetStringItemAttr(item_key, attr_name, std::string(attr_value));
      return true;
    }
    case AttrType::INT: {
      int64 int_attr = 0;
      if (!absl::SimpleAtoi(attr_value, &int_attr)) {
        CL_LOG_EVERY_N(INFO, 1000)
            << "Extract item info failed, attr type mismatch(not an int), invalid attr: " << attr_value;
        return false;
      }
      context->SetIntItemAttr(item_key, attr_name, int_attr);
      if (as_score) {
        retrieve_items->back().score = static_cast<double>(int_attr);
      }
      return true;
    }
    case AttrType::FLOAT: {
      double double_attr = 0;
      if (!absl::SimpleAtod(attr_value, &double_attr)) {
        CL_LOG_EVERY_N(INFO, 1000)
            << "Extract item info failed, attr type mismatch(not a double), invalid attr: " << attr_value;
        return false;
      }
      context->SetDoubleItemAttr(item_key, attr_name, double_attr);
      if (as_score) {
        retrieve_items->back().score = double_attr;
      }
      return true;
    }
    default:
      CL_LOG_ERROR("redis_regex_retrieve",
                   redis_trigger.cluster_name + ":unsupported_value_type=" + RecoUtil::GetAttrTypeName(type))
          << "unsupported redis value type, should could be int/double/string, cluster: "
          << redis_trigger.cluster_name << ", processor: " << GetName();
  }
  return true;
}

void MerchantRecoRedisRegexTriggerRetriever::InitStringKeys(MutableRecoContextInterface *context,
                                                            std::vector<std::string> *string_keys,
                                                            const std::string &key_prefix,
                                                            const MerchantRedisTriggerConfig &redis_trigger) {
  if (!string_keys) return;

  if (!redis_trigger.key.empty()) {
    string_keys->emplace_back(redis_trigger.key);
  }

  if (auto p_int = context->GetIntCommonAttr(redis_trigger.key_from_attr)) {
    string_keys->emplace_back(key_prefix + std::to_string(static_cast<uint64>(*p_int)));
  } else if (auto p_string = context->GetStringCommonAttr(redis_trigger.key_from_attr)) {
    string_keys->emplace_back(key_prefix + std::string(p_string->data(), p_string->size()));
  } else if (auto p_int_list = context->GetIntListCommonAttr(redis_trigger.key_from_attr)) {
    for (const int64 key : *p_int_list) {
      string_keys->emplace_back(key_prefix + std::to_string(static_cast<uint64>(key)));
    }
  } else if (auto p_string_list = context->GetStringListCommonAttr(redis_trigger.key_from_attr)) {
    for (auto sv : *p_string_list) {
      string_keys->emplace_back(key_prefix + std::string(sv));
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantRecoRedisRegexTriggerRetriever,
                 MerchantRecoRedisRegexTriggerRetriever);

}  // namespace platform
}  // namespace ks
