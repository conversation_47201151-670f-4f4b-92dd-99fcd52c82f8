#include "dragon/src/processor/ext/merchant/common/merchant_mix_rank_gen_seed_sequence_base_enricher.h"

#include <math.h>
#include <time.h>
#include <algorithm>
#include <cmath>
#include <random>
#include <set>
#include <unordered_map>
#include <unordered_set>

#include "folly/String.h"

namespace ks {
namespace platform {

constexpr char LISTWISE_ITEM_TYPE_ATTR[] = "listwise_item_type";
constexpr char TRANS_CID1_ATTR[] = "variant_cid1";
constexpr char TRANS_CID2_ATTR[] = "variant_cid2";
constexpr char TRANS_CID3_ATTR[] = "variant_cid3";
constexpr char TRANS_LEAF_ATTR[] = "variant_leaf";
constexpr char BRAND_ID_ATTR[] = "brand_id";
constexpr char VGSID_ATTR[] = "vgs_variant_tag";
constexpr char PWID_ATTR[] = "iGoodsProductWordKV";
constexpr char ORDER_C_LEAF_ATTR[] = "order_c_leaf_flag";
constexpr char SIM_CLUSTER_ID[] = "iGoodsSimilarClusterIdKV";
constexpr char ITEM_CID1_FOR_EMBD_ATTR[] = "item_cid1_for_embd";
constexpr char REBUY_ATTR[] = "rebuy_variant_flag";
constexpr char AID_ATTR[] = "aId";
constexpr char TOP_AID_ATTR[] = "is_top_expert_aid";
constexpr char SPU_ATTR[] = "spu_id";
constexpr char AD_ATTR[] = "is_ad";
constexpr char AD_ECPM[] = "ad_ecpm";
constexpr char RECALL_ATTR[] = "is_recall";
constexpr char IS_TRANS_LEAF_DIVER_ATTR[] = "is_trans_leaf_diversity";
constexpr char MIX_ONX_POSITION[] = "mix_onx_position";
constexpr char LISTWISE_CTR_ATTR[] = "list_wise_ctr";
constexpr char LISTWISE_CVR_ATTR[] = "list_wise_cvr";
constexpr char LISTWISE_PRICE_ATTR[] = "list_wise_price";
constexpr char LISTWISE_BONUS_ATTR[] = "list_wise_goods_bonus";
constexpr char IS_MODEL_GENERATOR_ATTR[] = "is_listwise_model_generator_item_flag";
constexpr char ITEM_MODEL_GENERATOR_INDEX_ATTR[] = "candidate_model_generator_index";
constexpr char GEN_ITEM_LIST_ATTR[] = "generated_variant_lists";
constexpr char GEN_ITEM_TYPE_LIST_ATTR[] = "generated_variant_item_type_lists";
constexpr char GEN_ITEM_CTR_LIST_ATTR[] = "generated_variant_ctr_lists";
constexpr char GEN_ITEM_CVR_LIST_ATTR[] = "generated_variant_cvr_lists";
constexpr char GEN_ITEM_PRICE_LIST_ATTR[] = "generated_variant_price_lists";
constexpr char GEN_ITEM_BONUS_LIST_ATTR[] = "generated_variant_goods_bonus_lists";
constexpr char GEN_ITEM_CID1_LIST_ATTR[] = "generated_variant_trans_cid1_lists";
constexpr char GEN_ITEM_CID2_LIST_ATTR[] = "generated_variant_trans_cid2_lists";
constexpr char GEN_ITEM_CID3_LIST_ATTR[] = "generated_variant_trans_cid3_lists";
constexpr char GEN_ITEM_AID_LIST_ATTR[] = "generated_variant_aid_lists";
constexpr char GEN_ITEM_DIVERSITY_LIST_ATTR[] = "generated_variant_diversity_score_lists";
constexpr char GEN_ITEM_SCORE_FLAG_LIST_ATTR[] = "generated_variant_score_flag_lists";
constexpr char TRANS_CID1_WINDOW_SIZE_ATTR[] = "trans_cid1_window_size";
constexpr char TRANS_CID1_MAX_NUM_ATTR[] = "trans_cid1_window_max_num";
constexpr char TRANS_CID2_WINDOW_SIZE_ATTR[] = "trans_cid2_window_size";
constexpr char TRANS_CID2_MAX_NUM_ATTR[] = "trans_cid2_window_max_num";
constexpr char TRANS_CID3_WINDOW_SIZE_ATTR[] = "trans_cid3_window_size";
constexpr char TRANS_CID3_MAX_NUM_ATTR[] = "trans_cid3_window_max_num";
constexpr char TRANS_LEAF_WINDOW_SIZE_ATTR[] = "trans_leaf_window_size";
constexpr char TRANS_LEAF_MAX_NUM_ATTR[] = "trans_leaf_window_max_num";

bool MerchantMixRankGenSeedSequenceBaseEnricher::InitializeConfig(MutableRecoContextInterface *context) {
  release();
  std::random_device rd;
  gen_.seed(rd());
  auto *queues = config()->Get("queues");
  if (!queues || !queues->IsArray() || queues->array().size() == 0) {
    return false;
  }
  queues_.clear();
  for (const auto *attr_json : queues->array()) {
    if (attr_json) {
      QueueAttr attr;
      if (!attr_json->IsObject()) {
        return false;
      }
      if (!attr_json->GetString("name", &(attr.attr_name))) {
        return false;
      }
      attr.weight_base = GetDoubleProcessorParameter(context, attr_json->Get("weight_base"), 0.0);
      attr.weight_bias_range = GetDoubleProcessorParameter(context, attr_json->Get("bias_range"), 0.0);
      attr.neg_weight = GetBoolProcessorParameter(context, attr_json->Get("neg_weight"), false);
      attr.default_value = GetDoubleProcessorParameter(context, attr_json->Get("default_value"), -10.0);
      attr.weight_lower_bound =
          GetDoubleProcessorParameter(context, attr_json->Get("weight_lower_bound"), 0.0);
      attr.is_skip = GetIntProcessorParameter(context, attr_json->Get("is_skip"), 0);
      if (!attr_json->Get("reverse_order")) {
        attr.reverse_order = false;
      } else {
        attr_json->GetBoolean("reverse_order", &(attr.reverse_order));
      }
      if (!attr_json->Get("merge_type")) {
        attr.merge_type = 0;
      } else {
        attr_json->GetInt("merge_type", &(attr.merge_type));
      }
      if (attr.is_skip < 1) {
        queues_.push_back(attr);
      }
    }
  }
  reddot_force_insert_live_ = GetIntProcessorParameter(context,
      config()->Get("reddot_force_insert_live_enable"), 0);
  beamsearch_insert_ad_diversity_enable_ = GetIntProcessorParameter(context,
      config()->Get("beamsearch_insert_ad_diversity_enable"), 0);
  force_insert_max_pctr_once_enable_ = GetIntProcessorParameter(context,
      config()->Get("force_insert_max_pctr_once_enable"), 0);
  is_max_force_insert_skip_diversity_ = GetIntProcessorParameter(context,
      config()->Get("is_max_force_insert_skip_diversity"), 0);
  is_onx_force_insert_enable_ =  GetIntProcessorParameter(context,
      config()->Get("is_onx_force_insert_enable"), 0);
  is_uniform_gen_ad_first_insert_pos_ =  GetIntProcessorParameter(context,
      config()->Get("is_uniform_gen_ad_first_insert_pos"), 0);
  page_offset_ = GetIntProcessorParameter(context, config()->Get("page_offset"), 0);
  top4_no_ad_when_live_top1_enable_ =
      GetIntProcessorParameter(context, config()->Get("top4_no_ad_when_live_top1_enable"), 0);
  mix_ad_adpos_ = GetIntProcessorParameter(context, config()->Get("bh_mix_ad_adpos"), 99);
  aid_avoid_pos_ = GetIntProcessorParameter(context, config()->Get("aid_avoid_pos"), 4);
  enable_ad_avoid_top_expert_ =
          GetIntProcessorParameter(context, config()->Get("enable_ad_avoid_top_expert"), 0);
  reco_expert_aid_num_ = GetIntProcessorParameter(context, config()->Get("reco_expert_aid_num"), 0);
  int mix_ad_adpos_offset = GetIntProcessorParameter(context, config()->Get("mix_ad_adpos_offset"), 0);
  mix_ad_adpos_ += mix_ad_adpos_offset;
  next_page_ad_offset_ = GetIntProcessorParameter(context, config()->Get("next_page_ad_offset"), 0);
  mix_ad_windows_size_ = GetIntProcessorParameter(context, config()->Get("mix_ad_windows_size"), 4);
  last_request_ad_pos_ = GetIntProcessorParameter(context, config()->Get("last_request_ad_pos"), -99);
  if (last_request_ad_pos_ >= 0) {
    last_request_ad_pos_ = -99;
  }
  is_ad_max_ecpm_first_ = GetIntProcessorParameter(context, config()->Get("is_ad_max_ecpm_first"), 1);
  ad_drop_prob_ = GetIntProcessorParameter(context, config()->Get("ad_drop_prob"), 30);
  ad_insert_prob_ = GetIntProcessorParameter(context, config()->Get("ad_insert_prob"), 50);
  normal_dis_mean_ = GetDoubleProcessorParameter(context, config()->Get("normal_dis_mean"), 4);
  normal_dis_std_ = GetDoubleProcessorParameter(context, config()->Get("normal_dis_std"), 2);
  valid_sampling_length_ = GetIntProcessorParameter(context, config()->Get("valid_sampling_length"), 10);
  return_item_type_ = GetIntProcessorParameter(context, config()->Get("return_item_type"), 2);
  is_need_origin_sequence_ = GetIntProcessorParameter(context, config()->Get("is_need_origin_sequence"), 0);
  max_sequence_num_ = GetIntProcessorParameter(context, config()->Get("max_sequence_num"), 6);
  sequence_max_length_ = GetIntProcessorParameter(context, config()->Get("sequence_max_size"), 50);
  seed_sequence_generated_reason_ = GetIntProcessorParameter(context, config()->Get("reason"), 10);
  gen_seq_sort_type_ = GetIntProcessorParameter(context, config()->Get("gen_seq_sort_type"), 0);
  enable_live_disturbance_ = GetIntProcessorParameter(context, config()->Get("enable_live_disturbance"), 0);
  is_random_force_insert_ad_ = GetIntProcessorParameter(context,
      config()->Get("is_random_force_insert_ad"), 0);
  enable_fill_sequence_diversity_ = GetIntProcessorParameter(context,
      config()->Get("enable_fill_sequence_diversity"), 0);
  enable_soft_diversity_threshold_ = GetIntProcessorParameter(context,
      config()->Get("enable_soft_diversity_threshold"), 0);
  soft_diversity_score_merge_weight_ = GetDoubleProcessorParameter(context,
      config()->Get("soft_diversity_score_merge_weight"), 0.0);
  soft_diversity_threshold_ = GetDoubleProcessorParameter(context,
      config()->Get("soft_diversity_threshold"), 1.0);
  live_merge_weight_ = GetDoubleProcessorParameter(context, config()->Get("live_merge_weight"), 1.0);
  is_release_ad_first_index_enable_ = GetIntProcessorParameter(context,
      config()->Get("is_release_ad_first_index_enable"), 0);

  // 格式为 attr1_w1,attr2_w1,attr3_w1:merge_type1:sort_type1:diversity_w1;...;
  // attr_merge_type1 = 0, 权重作指数，= 1， 权重乘
  // sort_type = 0, 各属性乘， = 1， 各属性加
  // diversity_w1, 多样性软性过滤权重
  std::string weight_group_list_str = GetStringProcessorParameter(context,
      config()->Get("gen_seq_weight_group_list"),
      "1_0_0,0,0,0;0_1_0,0,0,0;0_0_1,0,0,0;1_1_0,0,0,0;1_1_1,0,0,0");
  std::vector<absl::string_view> weight_group_vec = absl::StrSplit(weight_group_list_str, ";");
  int max_weight_group_num = weight_group_vec.size();
  std::vector<int> gen_attr_merge_type_list;
  std::vector<int> gen_seq_sort_type_list;
  std::vector<double> soft_diversity_score_merge_weight_list;
  gen_attr_merge_type_list.reserve(max_weight_group_num);
  gen_seq_sort_type_list.reserve(max_weight_group_num);
  soft_diversity_score_merge_weight_list.reserve(max_weight_group_num);
  if (max_weight_group_num > 0) {
    weight_group_list_.clear();
    for (int i = 0; i < max_weight_group_num; ++i) {
      std::vector<absl::string_view> one_weight_and_type_group = absl::StrSplit(weight_group_vec[i], ",");
      if (one_weight_and_type_group.size() == 4) {
        std::vector<absl::string_view> weight_group = absl::StrSplit(one_weight_and_type_group[0], "_");
        int merge_type = 0;
        int sort_type = 0;
        double merge_weight = 0.0;
        std::vector<double> one_group_weight_list(weight_group.size(), 0);
        if (absl::SimpleAtoi(one_weight_and_type_group[1], &merge_type) &&
            absl::SimpleAtoi(one_weight_and_type_group[2], &sort_type) &&
            absl::SimpleAtod(one_weight_and_type_group[3], &merge_weight)) {
          gen_attr_merge_type_list.emplace_back(std::move(merge_type));
          gen_seq_sort_type_list.emplace_back(std::move(sort_type));
          soft_diversity_score_merge_weight_list.emplace_back(std::move(merge_weight));
        } else {
          gen_attr_merge_type_list.emplace_back(0);
          gen_seq_sort_type_list.emplace_back(0);
          soft_diversity_score_merge_weight_list.emplace_back(0.0);
        }
        for (int j = 0; j < weight_group.size(); ++j) {
          double weight = 0.0;
          if (absl::SimpleAtod(weight_group[j], &weight)) {
            one_group_weight_list[j] = weight;
          }
        }
        weight_group_list_.emplace_back(std::move(one_group_weight_list));
      }
    }
    gen_seq_sort_type_list_ =  std::move(gen_seq_sort_type_list);
    gen_attr_merge_type_list_ =  std::move(gen_attr_merge_type_list);
    soft_diversity_score_merge_weight_list_ =  std::move(soft_diversity_score_merge_weight_list);
  }
  force_insert_max_pctr_pos_set_.clear();
  std::string force_insert_max_pctr_pos_index_list_str = GetStringProcessorParameter(context,
      config()->Get("force_insert_max_pctr_pos_index_list_str"), "99");
  std::vector<absl::string_view> pos_index_vec = absl::StrSplit(
      force_insert_max_pctr_pos_index_list_str, ",");
  int force_inset_max_pctr_pos_num = pos_index_vec.size();
  if (force_inset_max_pctr_pos_num > 0) {
    for (int i = 0; i < force_inset_max_pctr_pos_num; ++i) {
      int pos_index = 99;
      if (absl::SimpleAtoi(pos_index_vec[i], &pos_index)) {
        force_insert_max_pctr_pos_set_.insert(std::move(pos_index));
      }
    }
  }
  max_force_insert_pos_weight_list_map_.clear();
  // 按坑位设置强插权重，格式为 pos_index1:weight_list;...;
  std::string pos_weight_group_list_str = GetStringProcessorParameter(context,
      config()->Get("pos_weight_group_list"), "3:1_0_0,0,0");
  std::vector<absl::string_view> pos_weight_group_vec = absl::StrSplit(pos_weight_group_list_str, ";");
  int max_pos_weight_group_num = pos_weight_group_vec.size();
  if (max_pos_weight_group_num > 0) {
    for (int i = 0; i < max_pos_weight_group_num; ++i) {
      std::vector<absl::string_view> pos_weight_group = absl::StrSplit(pos_weight_group_vec[i], ":");
      int pos_index = -1;
      if (pos_weight_group.size() == 2 && absl::SimpleAtoi(pos_weight_group[0], &pos_index)) {
        std::vector<absl::string_view> one_weight_and_type_group = absl::StrSplit(pos_weight_group[1], ",");
        if (one_weight_and_type_group.size() == 3) {
          std::vector<absl::string_view> weight_group = absl::StrSplit(one_weight_and_type_group[0], "_");
          int merge_type = 0;
          int sort_type = 0;
          std::vector<double> one_group_weight_list(weight_group.size(), 0);
          if (absl::SimpleAtoi(one_weight_and_type_group[1], &merge_type) &&
              absl::SimpleAtoi(one_weight_and_type_group[2], &sort_type)) {
            for (int j = 0; j < weight_group.size(); ++j) {
              double weight = 0.0;
              if (absl::SimpleAtod(weight_group[j], &weight)) {
                one_group_weight_list[j] = weight;
              }
            }
          }
          std::vector<int> merge_sort_vec = {merge_type, sort_type};
          max_force_insert_pos_weight_list_map_[pos_index] =
              std::make_tuple(merge_sort_vec, one_group_weight_list);
        }
      }
    }
  }
  diversity_dtype_valid_item_type_map_.clear();
  std::string dtype_item_type_list_str = GetStringProcessorParameter(context,
      config()->Get("diversity_dtype_item_type_list"), "0:32,99");
  std::vector<absl::string_view> dtype_item_type_vec = absl::StrSplit(dtype_item_type_list_str, ";");
  for (const auto& dtype_item_type_list : dtype_item_type_vec) {
    std::vector<absl::string_view> dtype_item_type = absl::StrSplit(dtype_item_type_list, ":");
    int dtype = 0;
    if (dtype_item_type.size() == 2 && absl::SimpleAtoi(dtype_item_type[0], &dtype)) {
      std::vector<absl::string_view> item_type_list = absl::StrSplit(dtype_item_type[1], ",");
      std::unordered_set<int> valid_item_type_set = {};
      for (const auto& item_type_str : item_type_list) {
        int item_type = 0;
        if (absl::SimpleAtoi(item_type_str, &item_type)) {
          valid_item_type_set.insert(item_type);
        }
      }
      if (!valid_item_type_set.empty()) {
        diversity_dtype_valid_item_type_map_.emplace(dtype, std::move(valid_item_type_set));
      }
    }
  }
  skip_fill_sequence_early_return_ = context->GetIntCommonAttr(
      "skip_fill_sequence_early_return").value_or(0);
  is_merge_ad_diversity_ = context->GetIntCommonAttr("is_merge_ad_diversity").value_or(0);
  listwise_new_generator_ad_enable_ = context->GetIntCommonAttr(
      "listwise_new_generator_ad_enable").value_or(0);
  gen_item_list_accessor_ = context->GetItemAttrAccessor(GEN_ITEM_LIST_ATTR);
  gen_item_type_list_accessor_ = context->GetItemAttrAccessor(GEN_ITEM_TYPE_LIST_ATTR);
  gen_item_ctr_list_accessor_ = context->GetItemAttrAccessor(GEN_ITEM_CTR_LIST_ATTR);
  gen_item_cvr_list_accessor_ = context->GetItemAttrAccessor(GEN_ITEM_CVR_LIST_ATTR);
  gen_item_price_list_accessor_ = context->GetItemAttrAccessor(GEN_ITEM_PRICE_LIST_ATTR);
  gen_item_bonus_list_accessor_ = context->GetItemAttrAccessor(GEN_ITEM_BONUS_LIST_ATTR);
  gen_item_cid1_list_accessor_ = context->GetItemAttrAccessor(GEN_ITEM_CID1_LIST_ATTR);
  gen_item_cid2_list_accessor_ = context->GetItemAttrAccessor(GEN_ITEM_CID2_LIST_ATTR);
  gen_item_cid3_list_accessor_ = context->GetItemAttrAccessor(GEN_ITEM_CID3_LIST_ATTR);
  gen_item_aid_list_accessor_ = context->GetItemAttrAccessor(GEN_ITEM_AID_LIST_ATTR);
  gen_item_diversity_list_accessor_ = context->GetItemAttrAccessor(GEN_ITEM_DIVERSITY_LIST_ATTR);
  gen_item_score_flag_list_accessor_ = context->GetItemAttrAccessor(GEN_ITEM_SCORE_FLAG_LIST_ATTR);
  return true;
}

bool MerchantMixRankGenSeedSequenceBaseEnricher::CovertItemsToScoreInfos(MutableRecoContextInterface *context,
                                  RecoResultConstIter begin,
                                  RecoResultConstIter end,
                                  const int score_list_num,
                                  std::vector<ItemScoreInfo>* score_infos) {
  if (context == nullptr || score_infos == nullptr) {
    return false;
  }
  score_infos->reserve(std::distance(begin, end));
  std::vector<ItemAttr*> queue_attr_accessors(queues_.size(), nullptr);
  mix_onx_position_sets_.clear();
  // 填 pxtr 到 ItemScoreInfo.queue_value_list
  for (int i = 0; i < queues_.size(); ++i) {
    queue_attr_accessors[i] = context->GetItemAttrAccessor(queues_[i].attr_name);
  }
  auto* item_type_attr_accessor = context->GetItemAttrAccessor(LISTWISE_ITEM_TYPE_ATTR);
  auto* trans_cid1_attr_accessor = context->GetItemAttrAccessor(TRANS_CID1_ATTR);
  auto* trans_cid2_attr_accessor = context->GetItemAttrAccessor(TRANS_CID2_ATTR);
  auto* trans_cid3_attr_accessor = context->GetItemAttrAccessor(TRANS_CID3_ATTR);
  auto* aid_attr_accessor = context->GetItemAttrAccessor(AID_ATTR);
  auto* ctr_attr_accessor = context->GetItemAttrAccessor(LISTWISE_CTR_ATTR);
  auto* cvr_attr_accessor = context->GetItemAttrAccessor(LISTWISE_CVR_ATTR);
  auto* price_attr_accessor = context->GetItemAttrAccessor(LISTWISE_PRICE_ATTR);
  auto* bonus_attr_accessor = context->GetItemAttrAccessor(LISTWISE_BONUS_ATTR);
  auto* get_item_is_ad_accessor = context->GetItemAttrAccessor(AD_ATTR);
  auto* get_item_ad_ecpm_accessor = context->GetItemAttrAccessor(AD_ECPM);
  auto* trans_leaf_attr_accessor = context->GetItemAttrAccessor(TRANS_LEAF_ATTR);
  auto* is_trans_leaf_diver_attr_accessor = context->GetItemAttrAccessor(IS_TRANS_LEAF_DIVER_ATTR);
  auto* get_mix_onx_attr_accessor = context->GetItemAttrAccessor(MIX_ONX_POSITION);
  auto* spu_attr_accessor = context->GetItemAttrAccessor(SPU_ATTR);
  auto* model_generator_index_accessor = context->GetItemAttrAccessor(ITEM_MODEL_GENERATOR_INDEX_ATTR);
  auto* is_model_generator_accessor = context->GetItemAttrAccessor(IS_MODEL_GENERATOR_ATTR);
  auto* brand_id_attr_accessor = context->GetItemAttrAccessor(BRAND_ID_ATTR);
  auto* is_recall_attr_accessor = context->GetItemAttrAccessor(RECALL_ATTR);
  auto* is_top_expert_aid_accessor = context->GetItemAttrAccessor(TOP_AID_ATTR);
  auto* vgs_id_attr_accessor = context->GetItemAttrAccessor(VGSID_ATTR);
  auto* item_cid1_for_embd_attr_accessor = context->GetItemAttrAccessor(ITEM_CID1_FOR_EMBD_ATTR);
  auto* trans_cid1_size_attr_accessor = context->GetItemAttrAccessor(TRANS_CID1_WINDOW_SIZE_ATTR);
  auto* trans_cid1_num_attr_accessor = context->GetItemAttrAccessor(TRANS_CID1_MAX_NUM_ATTR);
  auto* trans_cid2_size_attr_accessor = context->GetItemAttrAccessor(TRANS_CID2_WINDOW_SIZE_ATTR);
  auto* trans_cid2_num_attr_accessor = context->GetItemAttrAccessor(TRANS_CID2_MAX_NUM_ATTR);
  auto* trans_cid3_size_attr_accessor = context->GetItemAttrAccessor(TRANS_CID3_WINDOW_SIZE_ATTR);
  auto* trans_cid3_num_attr_accessor = context->GetItemAttrAccessor(TRANS_CID3_MAX_NUM_ATTR);
  auto* trans_leaf_size_attr_accessor = context->GetItemAttrAccessor(TRANS_LEAF_WINDOW_SIZE_ATTR);
  auto* trans_leaf_num_attr_accessor = context->GetItemAttrAccessor(TRANS_LEAF_MAX_NUM_ATTR);
  auto* item_rebuy_attr_accessor = context->GetItemAttrAccessor(REBUY_ATTR);
  auto* pw_id_attr_accessor = context->GetItemAttrAccessor(PWID_ATTR);
  auto* order_c_leaf_attr_accessor = context->GetItemAttrAccessor(ORDER_C_LEAF_ATTR);
  auto* sim_cluster_id_attr_accessor = context->GetItemAttrAccessor(SIM_CLUSTER_ID);
  std::for_each(begin, end, [this, context,
                             item_type_attr_accessor, trans_cid1_attr_accessor,
                             trans_cid2_attr_accessor, trans_cid3_attr_accessor,
                             aid_attr_accessor, ctr_attr_accessor,
                             cvr_attr_accessor, price_attr_accessor,
                             bonus_attr_accessor, get_item_is_ad_accessor,
                             get_item_ad_ecpm_accessor, trans_leaf_attr_accessor,
                             is_trans_leaf_diver_attr_accessor, get_mix_onx_attr_accessor,
                             spu_attr_accessor, model_generator_index_accessor, is_model_generator_accessor,
                             brand_id_attr_accessor, is_recall_attr_accessor, is_top_expert_aid_accessor,
                             vgs_id_attr_accessor, trans_cid2_size_attr_accessor,
                             trans_cid2_num_attr_accessor,
                             trans_cid3_size_attr_accessor, trans_cid3_num_attr_accessor,
                             trans_leaf_size_attr_accessor, trans_leaf_num_attr_accessor,
                             item_cid1_for_embd_attr_accessor, item_rebuy_attr_accessor,
                             trans_cid1_size_attr_accessor, trans_cid1_num_attr_accessor,
                             pw_id_attr_accessor, order_c_leaf_attr_accessor, sim_cluster_id_attr_accessor,
                             &queue_attr_accessors, score_list_num,
                             score_infos](const CommonRecoResult &result) {
    ItemScoreInfo scoreInfo;
    scoreInfo.item_key = result.item_key;
    // 1: 权重融合方式为加法
    if (gen_seq_sort_type_list_.size() == score_list_num) {
      scoreInfo.seq_score_list = std::vector<double>(score_list_num, 1.0);
      for (int i = 0; i < score_list_num; ++i) {
        if (gen_seq_sort_type_list_[i] == 1) {
          scoreInfo.seq_score_list[i] = 0;
        }
      }
    } else {
      if (gen_seq_sort_type_ == 1) {
        scoreInfo.seq_score_list = std::vector<double>(score_list_num, 0.0);
      } else {
        scoreInfo.seq_score_list = std::vector<double>(score_list_num, 1.0);
      }
    }
    scoreInfo.live_merge_score = 1.0;
    scoreInfo.queue_value_list = std::move(std::vector<double>(queues_.size(), 0.0));
    scoreInfo.item_type = context->GetIntItemAttr(result, item_type_attr_accessor).value_or(0);
    scoreInfo.trans_cid1 = context->GetIntItemAttr(result, trans_cid1_attr_accessor).value_or(0);
    scoreInfo.trans_cid2 = context->GetIntItemAttr(result, trans_cid2_attr_accessor).value_or(0);
    scoreInfo.trans_cid3 = context->GetIntItemAttr(result, trans_cid3_attr_accessor).value_or(0);
    scoreInfo.trans_leaf = context->GetIntItemAttr(result, trans_leaf_attr_accessor).value_or(0);
    scoreInfo.brand_id = context->GetIntItemAttr(result, brand_id_attr_accessor).value_or(0);
    scoreInfo.aid = context->GetIntItemAttr(result, aid_attr_accessor).value_or(0);
    scoreInfo.listwise_ctr = context->GetDoubleItemAttr(result, ctr_attr_accessor).value_or(0.0);
    scoreInfo.listwise_cvr = context->GetDoubleItemAttr(result, cvr_attr_accessor).value_or(0.0);
    scoreInfo.listwise_price = context->GetDoubleItemAttr(result, price_attr_accessor).value_or(1.0);
    scoreInfo.listwise_goods_bonus = context->GetDoubleItemAttr(result, bonus_attr_accessor).value_or(1.0);
    scoreInfo.is_ad = context->GetIntItemAttr(result, get_item_is_ad_accessor).value_or(0);
    scoreInfo.is_top_expert_aid = context->GetIntItemAttr(result, is_top_expert_aid_accessor).value_or(0);
    scoreInfo.ad_ecpm = context->GetDoubleItemAttr(result, get_item_ad_ecpm_accessor).value_or(0.0);
    scoreInfo.is_recall = context->GetIntItemAttr(result, is_recall_attr_accessor).value_or(0);
    scoreInfo.vgs_id = context->GetIntItemAttr(result, vgs_id_attr_accessor).value_or(0);
    scoreInfo.rebuy_variant_flag = context->GetIntItemAttr(result, item_rebuy_attr_accessor).value_or(0);
    scoreInfo.pw_id = context->GetIntItemAttr(result, pw_id_attr_accessor).value_or(0);
    scoreInfo.order_c_leaf = context->GetIntItemAttr(result, order_c_leaf_attr_accessor).value_or(0);
    scoreInfo.sim_cluster_id = context->GetIntItemAttr(result, sim_cluster_id_attr_accessor).value_or(0);
    scoreInfo.item_cid1_for_embd = context->GetIntItemAttr(result,
        item_cid1_for_embd_attr_accessor).value_or(0);
    scoreInfo.trans_cid1_slide_info.window_size =
        context->GetIntItemAttr(result, trans_cid1_size_attr_accessor).value_or(4) - 1;
    scoreInfo.trans_cid1_slide_info.window_max_num =
        context->GetIntItemAttr(result, trans_cid1_num_attr_accessor).value_or(4);
    scoreInfo.trans_cid2_slide_info.window_size =
        context->GetIntItemAttr(result, trans_cid2_size_attr_accessor).value_or(4) - 1;
    scoreInfo.trans_cid2_slide_info.window_max_num =
        context->GetIntItemAttr(result, trans_cid2_num_attr_accessor).value_or(1);
    scoreInfo.trans_cid3_slide_info.window_size =
        context->GetIntItemAttr(result, trans_cid3_size_attr_accessor).value_or(4) - 1;
    scoreInfo.trans_cid3_slide_info.window_max_num =
        context->GetIntItemAttr(result, trans_cid3_num_attr_accessor).value_or(1);
    scoreInfo.trans_leaf_slide_info.window_size =
        context->GetIntItemAttr(result, trans_leaf_size_attr_accessor).value_or(4) - 1;
    scoreInfo.trans_leaf_slide_info.window_max_num =
        context->GetIntItemAttr(result, trans_leaf_num_attr_accessor).value_or(1);
    int64 is_trans_leaf_diver = context->GetIntItemAttr(result,
        is_trans_leaf_diver_attr_accessor).value_or(0);
    /*
    if (is_trans_leaf_diver == 0) {
      scoreInfo.skip_d_types.insert(DType::LEAF);
    }
    */
    if (is_merge_ad_diversity_ == 0) {
      if (scoreInfo.is_ad == 1) {
        scoreInfo.item_type = -1 * scoreInfo.item_type;
      }
    }
    scoreInfo.mix_onx_position = context->GetIntItemAttr(result, get_mix_onx_attr_accessor).value_or(-1);
    scoreInfo.spu_id = context->GetIntItemAttr(result, spu_attr_accessor).value_or(0);
    scoreInfo.onx_force_insert_flag = 0;
    // 如果与前面强插坑位重复，则后移一位
    if (scoreInfo.mix_onx_position >= 0) {
      while (scoreInfo.mix_onx_position < 10 &&
             mix_onx_position_sets_.find(scoreInfo.mix_onx_position) != mix_onx_position_sets_.end()) {
        scoreInfo.mix_onx_position += 1;
      }
      mix_onx_position_sets_.insert(scoreInfo.mix_onx_position);
      scoreInfo.onx_force_insert_flag = 1;
    }
    for (size_t i = 0; i < queues_.size(); ++i) {
      scoreInfo.queue_value_list[i] =
        context->GetDoubleItemAttr(result, queue_attr_accessors[i]).value_or(queues_[i].default_value);
        // 如果设置了默认值且该属性值小于等于 0， 为保证后链路不出问题，无属性值物料赋值默认值
        if (queues_[i].default_value > 0 && scoreInfo.queue_value_list[i] <= 0) {
          scoreInfo.queue_value_list[i] = queues_[i].default_value;
        }
    }
    scoreInfo.is_model_generator = context->GetIntItemAttr(result, is_model_generator_accessor).value_or(0);
    if (scoreInfo.is_model_generator > 0) {
      scoreInfo.pos_score_list.reserve(10);
      int64 item_model_generator_index = context->GetIntItemAttr(result,
          model_generator_index_accessor).value_or(-1);
      std::string prob_key = "model_generator_item_prob_";
      prob_key += std::to_string(item_model_generator_index);
      prob_key += "_";
      for (int i = 0; i < 10; ++i) {
        std::string tmp_prob_key = prob_key + std::to_string(i);
        double prob = context->GetDoubleCommonAttr(tmp_prob_key).value_or(-1e9);
        scoreInfo.pos_score_list.emplace_back(prob);
      }
    }

    // 默认按 pctr 填充
    scoreInfo.force_insert_pos_score_list = std::vector<double>(10, scoreInfo.listwise_ctr);
    for (int pos_index = 0; pos_index < 10; ++pos_index) {
      auto iter = max_force_insert_pos_weight_list_map_.find(pos_index);
      if (iter != max_force_insert_pos_weight_list_map_.end()) {
        const std::vector<int> merge_sort =  std::get<0>(iter->second);
        if (merge_sort.size() == 2) {
          double score = 1.0;
          const std::vector<double> weight_group = std::get<1>(iter->second);
          const int valid_attr_num = std::min(weight_group.size(), queues_.size());
          for (int q = 0; q < valid_attr_num; ++q) {
            double temp_value = 1.0;
            if (merge_sort[0] == 1) {
              temp_value = scoreInfo.queue_value_list[q] * weight_group[q];
            } else {
              temp_value = pow(scoreInfo.queue_value_list[q], weight_group[q]);
            }
            if (merge_sort[1] == 1) {
              score += temp_value;
            } else {
              score *= temp_value;
            }
          }
          scoreInfo.force_insert_pos_score_list[pos_index] = score;
        }
      }
    }
    score_infos->emplace_back(std::move(scoreInfo));
  });
  return true;
}

void MerchantMixRankGenSeedSequenceBaseEnricher::FillSequenceToMaxLength(
    SequenceInfos* curr_sequences_infos,
    const SequenceInfos& origin_sequence_infos,
    const int max_length) {
  const int origin_item_num = origin_sequence_infos.score_infos.size();
  if (curr_sequences_infos == nullptr ||
      curr_sequences_infos->selected_flag_list.size() != origin_item_num) {
    return;
  }
  const int diversity_hard_rule_num = hard_diversity_rule_type_.size();
  const int hard_rule_check = (1 << diversity_hard_rule_num) - 1;
  const int fill_max_length = std::min(max_length, origin_item_num);
  while (curr_sequences_infos->score_infos.size() < fill_max_length) {
    int selected_index = FindFirstUnselectedItem(curr_sequences_infos->selected_flag_list);
    double final_diversity_score = 0.0;
    if (curr_sequences_infos->score_infos.size() < 10 ||
        enable_fill_sequence_diversity_) {
      int insert_item_index = -1;
      if (is_random_force_insert_ad_) {
        insert_item_index = SelectedAdIndex(*curr_sequences_infos, origin_sequence_infos);
      }
      // onx 强插优先级最高，可覆盖前面强插
      if (is_onx_force_insert_enable_) {
        int insert_item_index_tmp = SelectedOnxForceInsertIndex(*curr_sequences_infos, origin_sequence_infos);
        if (insert_item_index_tmp >= 0 && insert_item_index_tmp < origin_item_num) {
          insert_item_index = insert_item_index_tmp;
        }
      }
      if (reddot_force_insert_live_ && curr_sequences_infos->score_infos.empty()) {
        int insert_item_index_tmp = SelectedReddotLiveForceInsertIndex(
            *curr_sequences_infos, origin_sequence_infos);
        if (insert_item_index_tmp >= 0 && insert_item_index_tmp < origin_item_num) {
          insert_item_index = insert_item_index_tmp;
        }
      }
      if (insert_item_index >= 0 && insert_item_index < origin_item_num) {
        selected_index = insert_item_index;
      } else {
        int max_valid_num = 0;
        for (int j = selected_index; j < origin_item_num; ++j) {
          if (is_random_force_insert_ad_ && origin_sequence_infos.score_infos[j]->is_ad == 1) {
            continue;
          }
          if (is_onx_force_insert_enable_ &&
              origin_sequence_infos.score_infos[j]->onx_force_insert_flag == 1) {
            continue;
          }
          if (curr_sequences_infos->selected_flag_list[j] != 1) {
            const double diversity_score = DiversityRuleSoftCheck(*curr_sequences_infos,
                                                                  origin_sequence_infos.score_infos[j]);
            if (enable_soft_diversity_threshold_ && diversity_score > soft_diversity_threshold_) {
              continue;
            }
            const int diversity_flag = DiversityRuleHardCheck(*curr_sequences_infos,
                                                              origin_sequence_infos.score_infos[j]);
            // 选择顺序：先比较最高满足多样性级，相同优先级别下选择满足规则数最多的商品
            // 前两项均相同，则选择分数高的商品
            if (diversity_flag > max_valid_num) {
              max_valid_num = diversity_flag;
              selected_index = j;
              final_diversity_score = diversity_score;
            }
            // 靠前满足所有的多样性规则，则直接停止
            if (max_valid_num == hard_rule_check) {
              break;
            }
          }
        }
      }
    }
    if (selected_index >= curr_sequences_infos->selected_flag_list.size() ||
        selected_index < 0) {
      break;
    }
    if (force_insert_max_pctr_pos_set_.find(curr_sequences_infos->score_infos.size()) !=
        force_insert_max_pctr_pos_set_.end() &&
        origin_sequence_infos.score_infos[selected_index]->is_ad != 1 &&
        origin_sequence_infos.score_infos[selected_index]->onx_force_insert_flag != 1 &&
        origin_sequence_infos.score_infos[selected_index]->item_type != 1) {
      const int force_insert_max_pctr_index = SelectedMaxPctrIndex(
        *curr_sequences_infos, origin_sequence_infos);
      if (force_insert_max_pctr_index >= 0 && force_insert_max_pctr_index < origin_item_num) {
        selected_index = force_insert_max_pctr_index;
        final_diversity_score = DiversityRuleSoftCheck(*curr_sequences_infos,
                                                       origin_sequence_infos.score_infos[selected_index]);
      }
    }
    UpdateCurSequence(origin_sequence_infos, selected_index, 0, final_diversity_score, curr_sequences_infos);
  }
  return;
}

void MerchantMixRankGenSeedSequenceBaseEnricher::GenAdFirstInsertIndex(
    const SequenceInfos& origin_sequence_infos,
    SequenceInfos* curr_sequences_infos) {
  if (curr_sequences_infos == nullptr) {
    return;
  }
  // 默认不插入
  curr_sequences_infos->ad_first_insert_pos_index = 99;
  if (curr_sequences_infos == nullptr ||
      origin_sequence_infos.ad_num == 0) {
    return;
  }
  // 一定概率该序列不插入广告
  std::uniform_int_distribution<> dist(0, 99);
  if (dist(gen_) < ad_drop_prob_) {
    return;
  }
  if (page_offset_ > 0) {
    mix_ad_adpos_ = next_page_ad_offset_;
  }
  int first_enable_pos_index = std::max(mix_ad_adpos_, last_request_ad_pos_ + mix_ad_windows_size_);
  first_enable_pos_index = std::min(first_enable_pos_index, 9);
  first_enable_pos_index = std::max(0, first_enable_pos_index);
  if (is_release_ad_first_index_enable_) {
    curr_sequences_infos->ad_first_insert_pos_index = first_enable_pos_index;
  } else if (is_uniform_gen_ad_first_insert_pos_) {
    std::uniform_int_distribution<> dist_index(first_enable_pos_index, 9);
    curr_sequences_infos->ad_first_insert_pos_index = dist_index(gen_);
  } else {
    std::normal_distribution<> normal_dist(first_enable_pos_index + normal_dis_mean_, normal_dis_std_);
    int ad_first_insert_pos_index = std::ceil(normal_dist(gen_));
    ad_first_insert_pos_index = std::max(first_enable_pos_index, ad_first_insert_pos_index);
    ad_first_insert_pos_index = std::min(ad_first_insert_pos_index, 9);
    curr_sequences_infos->ad_first_insert_pos_index = ad_first_insert_pos_index;
  }
  return;
}

int MerchantMixRankGenSeedSequenceBaseEnricher::SelectedAdIndex(const SequenceInfos& curr_sequences_infos,
                                                                const SequenceInfos& origin_sequence_infos) {
  // 默认不选择
  const int default_select_index = -1;
  const int origin_item_num = origin_sequence_infos.score_infos.size();
  const int cur_pos_index = curr_sequences_infos.score_infos.size();
  auto ad_first_insert_pos_index = curr_sequences_infos.ad_first_insert_pos_index;
  // 头达避让
  if (enable_ad_avoid_top_expert_ == 1 && cur_pos_index < aid_avoid_pos_ &&
      curr_sequences_infos.is_insert_top_aid == 0 && reco_expert_aid_num_ > 0) {
    return default_select_index;
  }
  if (page_offset_ == 0 &&
      top4_no_ad_when_live_top1_enable_ == 1 &&
      !curr_sequences_infos.score_infos.empty() &&
      curr_sequences_infos.score_infos.front()->item_type == 1) {
    ad_first_insert_pos_index = std::max(ad_first_insert_pos_index, 4);
  }
  if (origin_sequence_infos.ad_num == 0 ||
      curr_sequences_infos.score_infos.size() < ad_first_insert_pos_index ||
      curr_sequences_infos.selected_flag_list.size() != origin_item_num) {
    return default_select_index;
  }
  const int diversity_hard_rule_num = hard_diversity_rule_type_.size();
  const int hard_rule_check = (1 << diversity_hard_rule_num) - 1;
  // 如果序列未存在广告，则一定优先返回可以使用的广告,否则随机插入广告
  if (curr_sequences_infos.ad_num > 0) {
    std::uniform_int_distribution<> dist(0, 99);
    if (dist(gen_) < ad_insert_prob_) {
      return default_select_index;
    }
  }

  int max_ad_ecpm_index = -1;
  double current_max_ad_ecpm = 0;
  // 寻找分最高的广告商品
  for (int i = 0; i < origin_item_num; ++i) {
    const auto& score_info = origin_sequence_infos.score_infos[i];
    if (score_info->is_ad != 1 || curr_sequences_infos.selected_flag_list[i] == 1) {
      continue;
    }
    // 使用广告传过来的顺序，不保证 ecpm 最大优先
    if (is_ad_max_ecpm_first_ == 0) {
      max_ad_ecpm_index = i;
      break;
    }
    if (score_info->ad_ecpm > current_max_ad_ecpm) {
      max_ad_ecpm_index = i;
      current_max_ad_ecpm = score_info->ad_ecpm;
    }
  }
  if (max_ad_ecpm_index == -1) {
    return default_select_index;
  }
  int diversity_flag = DiversityRuleHardCheck(curr_sequences_infos,
                                              origin_sequence_infos.score_infos[max_ad_ecpm_index]);
  if (diversity_flag != hard_rule_check) {
    return default_select_index;
  }
  return max_ad_ecpm_index;
}

int MerchantMixRankGenSeedSequenceBaseEnricher::SelectedMaxPctrIndex(
    const SequenceInfos& curr_sequences_infos,
    const SequenceInfos& origin_sequence_infos) {
  // 默认不选择
  const int default_select_index = -1;
  const int origin_item_num = origin_sequence_infos.score_infos.size();
  if (curr_sequences_infos.selected_flag_list.size() != origin_item_num) {
    return default_select_index;
  }
  const int diversity_hard_rule_num = hard_diversity_rule_type_.size();
  const int hard_rule_check = (1 << diversity_hard_rule_num) - 1;

  int max_item_pctr_index = -1;
  double current_max_item_pctr = 0.0;
  const int cur_pos_index = curr_sequences_infos.score_infos.size();
  // 寻找 pctr 最高的商品
  for (int i = 0; i < origin_item_num; ++i) {
    const auto& score_info = origin_sequence_infos.score_infos[i];
    if (score_info->is_ad == 1 || score_info->item_type  == 1 ||
        curr_sequences_infos.selected_flag_list[i] == 1 ||
        score_info->onx_force_insert_flag == 1) {
      continue;
    }
    double score = score_info->listwise_ctr;
    if (cur_pos_index < origin_sequence_infos.score_infos[i]->force_insert_pos_score_list.size()) {
      score = origin_sequence_infos.score_infos[i]->force_insert_pos_score_list[cur_pos_index];
    }
    if (score <= current_max_item_pctr) {
      continue;
    }
    if (is_max_force_insert_skip_diversity_ == 0) {
      int diversity_flag = DiversityRuleHardCheck(curr_sequences_infos,
                                                  origin_sequence_infos.score_infos[i]);
      if (diversity_flag != hard_rule_check) {
        continue;
      }
    }
    max_item_pctr_index = i;
    current_max_item_pctr = score;
  }
  return max_item_pctr_index;
}

int MerchantMixRankGenSeedSequenceBaseEnricher::SelectedReddotLiveForceInsertIndexByMode(
    const SequenceInfos &curr_sequences_infos, const SequenceInfos &origin_sequence_infos,
    const std::vector<double> &index_candidate_prob_matrix) {
  // 默认不选择
  const int default_select_index = -1;
  const int origin_item_num = origin_sequence_infos.score_infos.size();
  const int curr_insert_index = curr_sequences_infos.score_infos.size();

  // 寻找对应坑位的商品
  int max_value = -1;  // 初始化为最小值（假设概率值非负）
  int max_index = -1;  // 记录最大值对应的索引
  if (index_candidate_prob_matrix.size() != origin_item_num) {
    return default_select_index;
  }
  for (int i = 0; i < origin_item_num; ++i) {
    const auto &score_info = origin_sequence_infos.score_infos[i];
    // 检查是否满足所有条件
    if (curr_sequences_infos.selected_flag_list[i] != 1 && curr_insert_index == 0 &&
        score_info->item_type == 1 && score_info->is_ad != 1 && score_info->onx_force_insert_flag != 1) {
      // 比较当前值是否比最大值更大
      if (index_candidate_prob_matrix[i] > max_value) {
        max_value = index_candidate_prob_matrix[i];
        max_index = i;  // 更新最大值索引
      }
    }
  }
  return max_index;
}

int MerchantMixRankGenSeedSequenceBaseEnricher::SelectedOnxForceInsertIndex(
    const SequenceInfos& curr_sequences_infos,
    const SequenceInfos& origin_sequence_infos) {
  // 默认不选择
  const int default_select_index = -1;
  const int origin_item_num = origin_sequence_infos.score_infos.size();
  const int curr_insert_index = curr_sequences_infos.score_infos.size();

  if (mix_onx_position_sets_.find(curr_insert_index) == mix_onx_position_sets_.end() ||
      curr_sequences_infos.selected_flag_list.size() != origin_item_num) {
    return default_select_index;
  }
  // 寻找对应坑位的商品
  for (int i = 0; i < origin_item_num; ++i) {
    const auto& score_info = origin_sequence_infos.score_infos[i];
    if (curr_sequences_infos.selected_flag_list[i] != 1 &&
      score_info->mix_onx_position == curr_insert_index) {
      return i;
    }
  }
  return default_select_index;
}

int MerchantMixRankGenSeedSequenceBaseEnricher::SelectedReddotLiveForceInsertIndex(
    const SequenceInfos& curr_sequences_infos,
    const SequenceInfos& origin_sequence_infos) {
  // 默认不选择
  const int default_select_index = -1;
  const int origin_item_num = origin_sequence_infos.score_infos.size();
  const int curr_insert_index = curr_sequences_infos.score_infos.size();

  // 寻找对应坑位的商品
  for (int i = 0; i < origin_item_num; ++i) {
    const auto& score_info = origin_sequence_infos.score_infos[i];
    if (curr_sequences_infos.selected_flag_list[i] != 1 && 0 == curr_insert_index &&
        score_info->item_type == 1 && score_info->is_ad != 1 && score_info->onx_force_insert_flag != 1) {
      return i;
    }
  }
  return default_select_index;
}

void MerchantMixRankGenSeedSequenceBaseEnricher::UpdateCurSequence(const SequenceInfos& origin_sequence_infos,
                    const int select_index,
                    const double score,
                    const double diversity_score,
                    SequenceInfos* curr_sequences_infos) {
  if (curr_sequences_infos == nullptr ||
      origin_sequence_infos.score_infos.size() <= select_index ||
      curr_sequences_infos->selected_flag_list.size() <= select_index ||
      curr_sequences_infos->selected_flag_list[select_index] == 1) {
    return;
  }
  curr_sequences_infos->sequence_score = score;
  curr_sequences_infos->score_infos.push_back(origin_sequence_infos.score_infos[select_index]);
  if (origin_sequence_infos.score_infos[select_index]->is_ad == 1) {
    curr_sequences_infos->ad_num += 1;
  }
  curr_sequences_infos->seed_sequence_diversity_score.push_back(diversity_score);
  double item_score_flag = 1.0;
  if (is_onx_force_insert_enable_ &&
      origin_sequence_infos.score_infos[select_index]->onx_force_insert_flag == 1) {
    item_score_flag = 0.0;
  }
  if (origin_sequence_infos.score_infos[select_index]->is_top_expert_aid == 1 &&
        origin_sequence_infos.score_infos[select_index]->is_ad != 1) {
    curr_sequences_infos->is_insert_top_aid = 1;
  }
  curr_sequences_infos->seed_sequence_score_flags.push_back(item_score_flag);
  curr_sequences_infos->selected_flag_list[select_index] = 1;
  DiversityRuleUpdate(origin_sequence_infos.score_infos[select_index], curr_sequences_infos);
}

void MerchantMixRankGenSeedSequenceBaseEnricher::GenSeedSequenceAndKey(
    SequenceInfos* sequence_infos) {
  if (sequence_infos == nullptr) {
    return;
  }
  sequence_infos->seed_sequence.clear();
  for (int l = 0; l < sequence_infos->score_infos.size(); ++l) {
    sequence_infos->seed_sequence.emplace_back(sequence_infos->score_infos[l]->item_key);
  }
  uint64 hash_code, sequence_id, item_key;
  std::string seed_sequence_str = folly::join(",", sequence_infos->seed_sequence);
  hash_code = base::CityHash64(seed_sequence_str.data(), seed_sequence_str.size());
  sequence_id = Util::GetId(hash_code);
  item_key = Util::GenKeysign(return_item_type_, sequence_id);
  sequence_infos->item_key = item_key;
  return;
}

void MerchantMixRankGenSeedSequenceBaseEnricher::SetSequenceAttrValue(
    SequenceInfos* sequence_infos) {
  if (sequence_infos == nullptr ||
      sequence_infos->score_infos.size() != sequence_infos->seed_sequence.size()) {
    return;
  }
  for (int l = 0; l < sequence_infos->score_infos.size(); ++l) {
    sequence_infos->seed_sequence_item_type.emplace_back(sequence_infos->score_infos[l]->item_type);
    sequence_infos->seed_sequence_ctrs.emplace_back(sequence_infos->score_infos[l]->listwise_ctr);
    sequence_infos->seed_sequence_cvrs.emplace_back(sequence_infos->score_infos[l]->listwise_cvr);
    sequence_infos->seed_sequence_prices.emplace_back(sequence_infos->score_infos[l]->listwise_price);
    sequence_infos->seed_sequence_goods_bonus.emplace_back(
        sequence_infos->score_infos[l]->listwise_goods_bonus);
    sequence_infos->seed_sequence_trans_cid1.emplace_back(sequence_infos->score_infos[l]->trans_cid1);
    sequence_infos->seed_sequence_trans_cid2.emplace_back(sequence_infos->score_infos[l]->trans_cid2);
    sequence_infos->seed_sequence_trans_cid3.emplace_back(sequence_infos->score_infos[l]->trans_cid3);
    sequence_infos->seed_sequence_trans_leaf.emplace_back(sequence_infos->score_infos[l]->trans_leaf);
    sequence_infos->seed_sequence_aid.emplace_back(sequence_infos->score_infos[l]->aid);
  }
return;
}

void MerchantMixRankGenSeedSequenceBaseEnricher::ConvertSequenceAttrToItemAttr(
    MutableRecoContextInterface *context,
    SequenceInfos* sequence_infos) {
    context->SetIntListItemAttr(sequence_infos->item_key, gen_item_list_accessor_,
                                std::move(sequence_infos->seed_sequence));
    context->SetIntListItemAttr(sequence_infos->item_key, gen_item_type_list_accessor_,
        std::move(sequence_infos->seed_sequence_item_type));
    context->SetDoubleListItemAttr(sequence_infos->item_key, gen_item_ctr_list_accessor_,
        std::move(sequence_infos->seed_sequence_ctrs));
    context->SetDoubleListItemAttr(sequence_infos->item_key, gen_item_cvr_list_accessor_,
        std::move(sequence_infos->seed_sequence_cvrs));
    context->SetDoubleListItemAttr(sequence_infos->item_key, gen_item_price_list_accessor_,
        std::move(sequence_infos->seed_sequence_prices));
    context->SetDoubleListItemAttr(sequence_infos->item_key, gen_item_bonus_list_accessor_,
        std::move(sequence_infos->seed_sequence_goods_bonus));
    context->SetDoubleListItemAttr(sequence_infos->item_key, gen_item_diversity_list_accessor_,
        std::move(sequence_infos->seed_sequence_diversity_score));
    context->SetDoubleListItemAttr(sequence_infos->item_key, gen_item_score_flag_list_accessor_,
        std::move(sequence_infos->seed_sequence_score_flags));
    context->SetIntListItemAttr(sequence_infos->item_key, gen_item_cid1_list_accessor_,
                                std::move(sequence_infos->seed_sequence_trans_cid1));
    context->SetIntListItemAttr(sequence_infos->item_key, gen_item_cid2_list_accessor_,
                                std::move(sequence_infos->seed_sequence_trans_cid2));
    context->SetIntListItemAttr(sequence_infos->item_key, gen_item_cid3_list_accessor_,
                                std::move(sequence_infos->seed_sequence_trans_cid3));
    context->SetIntListItemAttr(sequence_infos->item_key, gen_item_aid_list_accessor_,
                                std::move(sequence_infos->seed_sequence_aid));
}

void MerchantMixRankGenSeedSequenceBaseEnricher::InitDiversityInfos(MutableRecoContextInterface *context,
                                                                    RecoResultConstIter begin,
                                                                    RecoResultConstIter end) {
  if (context == nullptr) {
    return;
  }
  auto mmu_cid1_threshold_list = context->GetStringListCommonAttr("mmu_cid1_threshold_list");
  auto mmu_embedding_size = context->GetIntCommonAttr("mmu_embedding_size").value_or(64);
  auto prev_item_key_list = context->GetIntListCommonAttr("prev_item_key_list");
  auto prev_item_mmu_list = context->GetDoubleListCommonAttr("prev_item_mmu_list");

  if (mmu_cid1_threshold_list) {
    for (const auto & s : (*mmu_cid1_threshold_list)) {
      std::vector<absl::string_view> cid1_threshold = absl::StrSplit(s, ':');
      if (cid1_threshold.size() == 2) {
        int64 cid1 = 0;
        double threshold = 0.0;
        if (absl::SimpleAtoi(cid1_threshold[0], &cid1) &&
            absl::SimpleAtod(cid1_threshold[1], &threshold)) {
          diversity_infos_.mmu_cid1_threshold_map[cid1] = threshold;
        }
      }
    }
  }

  if (prev_item_mmu_list && prev_item_key_list &&
    (*prev_item_mmu_list).size() % mmu_embedding_size == 0 &&
    (*prev_item_mmu_list).size() / mmu_embedding_size == (*prev_item_key_list).size()) {
    int pre_mmu_item_num = (*prev_item_mmu_list).size() / mmu_embedding_size;
    for (int i = 0; i < pre_mmu_item_num; ++i) {
      int64 pre_item_key = (*prev_item_key_list)[i];
      diversity_infos_.item_key_2_embedding_map[pre_item_key].assign(
      (*prev_item_mmu_list).begin() +i * mmu_embedding_size,
      (*prev_item_mmu_list).begin() + (i + 1) * mmu_embedding_size);
    }
  }
  auto* item_mmu_embedding_attr_accessor = context->GetItemAttrAccessor("item_mmu_embedding");
  auto* item_type_attr_accessor = context->GetItemAttrAccessor("item_type");
  if (item_mmu_embedding_attr_accessor == nullptr) {
    return;
  }
  std::for_each(begin, end, [&](const CommonRecoResult &result) {
    auto item_type_tmp = context->GetIntItemAttr(result, item_type_attr_accessor).value_or(0);
    // 处理商品 item 的 mmu embedding
    if (item_type_tmp == 32 || item_type_tmp == 99) {
      auto mmu_embedding = context->GetDoubleListItemAttr(result, item_mmu_embedding_attr_accessor);
      if (mmu_embedding && (*mmu_embedding).size() == mmu_embedding_size) {
        diversity_infos_.item_key_2_embedding_map[result.item_key].assign((*mmu_embedding).begin(),
                                                          (*mmu_embedding).end());
      }
    }
  });

  auto soft_weight_list = context->GetStringCommonAttr("soft_weight_infos");

  if (soft_weight_list) {
    std::vector<absl::string_view> key_weights = absl::StrSplit(*soft_weight_list, ':');
    if (key_weights.size() == 2) {
      std::vector<absl::string_view> weights_struct = absl::StrSplit(key_weights[1], '_');
      if (weights_struct.size() == 4) {
        int64 key = 0;
        double rule_weight_w1 = 0;
        double pos_gap_weight_w2 = 0;
        double bias_b1 = 0;
        double bias_b2 = 0;
        if (absl::SimpleAtoi(key_weights[0], &key) &&
            absl::SimpleAtod(weights_struct[0], &rule_weight_w1) &&
            absl::SimpleAtod(weights_struct[1], &pos_gap_weight_w2) &&
            absl::SimpleAtod(weights_struct[2], &bias_b1) &&
            absl::SimpleAtod(weights_struct[3], &bias_b2)) {
          SoftDiversityWeightInfos weight_infos;
          weight_infos.rule_weight_w1 = rule_weight_w1;
          weight_infos.pos_gap_weight_w2 = pos_gap_weight_w2;
          weight_infos.bias_b1 = bias_b1;
          weight_infos.bias_b2 = bias_b2;
          diversity_infos_.soft_key_to_weight_map.emplace(key, weight_infos);
        }
      }
    }
  }
  diversity_infos_.default_mmu_cid1_threshold_ = context->
      GetDoubleCommonAttr("default_mmu_cid1_threshold").value_or(0.5);
    diversity_infos_.pos_gap_offset_ = context->
      GetDoubleCommonAttr("pos_gap_offset").value_or(0);
}

void MerchantMixRankGenSeedSequenceBaseEnricher::InitDiversityRule(MutableRecoContextInterface *context,
                                                  SequenceInfos* sequence_infos) {
  if (context == nullptr) {
    return;
  }
  // 优先级 1: 滑动窗口, 商品三级类目 4 个最多出 2 个, 跨屏
  // 优先级 2: 滑动窗口, 商品二级类目 10 个最多出 4 个, 跨屏
  // 优先级 3: 滑动窗口, 商品店铺 5 个最多出 1 个, 跨屏
  // 优先级 4: top 窗口, top10 最多出 4 个直播
  // 优先级 5: 滑动窗口, 直播 4 个最多出 2 个, 跨屏
  // 优先级 6: 滑动窗口, 商品叶子类目 4 个最多出 2 个, 跨屏
  // 优先级 7: 滑动窗口，mmu 计算相似度， 3 个最多出 1 个, 跨屏
  auto cid3_variant_win_size = context->GetIntCommonAttr("cid3_variant_win_size").value_or(4) - 1;
  auto cid3_variant_max_num = context->GetIntCommonAttr("cid3_variant_max_num").value_or(2);
  auto cid2_variant_win_size = context->GetIntCommonAttr("cid2_variant_win_size").value_or(10) - 1;
  auto cid2_variant_max_num = context->GetIntCommonAttr("cid2_variant_max_num").value_or(4);
  auto aid_variant_win_size = context->GetIntCommonAttr("aid_variant_win_size").value_or(5) - 1;
  auto aid_variant_max_num = context->GetIntCommonAttr("aid_variant_max_num").value_or(1);
  auto live_variant_win_size = context->GetIntCommonAttr("live_variant_win_size").value_or(4) - 1;
  auto live_variant_max_num = context->GetIntCommonAttr("live_variant_max_num").value_or(2);
  auto mmu_variant_win_size = context->GetIntCommonAttr("mmu_variant_win_size").value_or(3) - 1;
  auto mmu_variant_max_num = context->GetIntCommonAttr("mmu_variant_max_num").value_or(1);
  auto ad_variant_win_size = context->GetIntCommonAttr("ad_variant_win_size").value_or(4) - 1;
  auto ad_variant_max_num = context->GetIntCommonAttr("ad_variant_max_num").value_or(1);
  auto leaf_variant_win_size = context->GetIntCommonAttr("leaf_variant_win_size").value_or(4) - 1;
  auto leaf_variant_max_num = context->GetIntCommonAttr("leaf_variant_max_num").value_or(1);
  auto spu_variant_win_size = context->GetIntCommonAttr("spu_variant_win_size").value_or(50) - 1;
  auto spu_variant_max_num = context->GetIntCommonAttr("spu_variant_max_num").value_or(1);
  auto brand_variant_win_size = context->GetIntCommonAttr("brand_variant_win_size").value_or(4) - 1;
  auto brand_variant_max_num = context->GetIntCommonAttr("brand_variant_max_num").value_or(1);
  auto recall_variant_win_size = context->GetIntCommonAttr("recall_variant_win_size").value_or(10) - 1;
  auto recall_variant_max_num = context->GetIntCommonAttr("recall_variant_max_num").value_or(2);
  auto vgs_id_variant_win_size = context->GetIntCommonAttr("vgs_id_variant_win_size").value_or(4) - 1;
  auto vgs_id_variant_max_num = context->GetIntCommonAttr("vgs_id_variant_max_num").value_or(1);
  auto multi_cid1_max_win_size  = context->GetIntCommonAttr("multi_cid1_max_win_size").value_or(10) - 1;
  auto multi_cid2_max_win_size = context->GetIntCommonAttr("multi_cid2_max_win_size").value_or(10) - 1;
  auto multi_cid3_max_win_size = context->GetIntCommonAttr("multi_cid3_max_win_size").value_or(10) - 1;
  auto multi_leaf_max_win_size = context->GetIntCommonAttr("multi_leaf_max_win_size").value_or(10) - 1;
  auto rebuy_variant_win_size = context->GetIntCommonAttr("rebuy_variant_win_size").value_or(10) - 1;
  auto rebuy_variant_max_num = context->GetIntCommonAttr("rebuy_variant_max_num").value_or(1);
  auto pw_id_variant_win_size = context->GetIntCommonAttr("pw_id_variant_win_size").value_or(4) - 1;
  auto pw_id_variant_max_num = context->GetIntCommonAttr("pw_id_variant_max_num").value_or(1);
  auto order_c_leaf_variant_win_size =
      context->GetIntCommonAttr("order_c_leaf_variant_win_size").value_or(5) - 1;
  auto order_c_leaf_variant_max_num =
      context->GetIntCommonAttr("order_c_leaf_variant_max_num").value_or(1);
  auto sim_cluster_id_variant_win_size =
      context->GetIntCommonAttr("sim_cluster_id_variant_win_size").value_or(4) - 1;
  auto sim_cluster_id_variant_max_num =
      context->GetIntCommonAttr("sim_cluster_id_variant_max_num").value_or(1);

  std::unordered_set<int> cid3_valid_item_type_set = get_valid_item_type_set(DType::CID3, {32, 99});
  std::unordered_set<int> cid2_valid_item_type_set = get_valid_item_type_set(DType::CID2, {32, 99});
  std::unordered_set<int> cid1_valid_item_type_set = get_valid_item_type_set(DType::CID1, {1, 32, 99});
  std::unordered_set<int> aid_valid_item_type_set = get_valid_item_type_set(DType::AID, {32, 99});
  std::unordered_set<int> leaf_valid_item_type_set = get_valid_item_type_set(DType::LEAF, {32, 99});
  std::unordered_set<int> live_valid_item_type_set = get_valid_item_type_set(DType::LIVE, {1});
  std::unordered_set<int> ad_valid_item_type_set = get_valid_item_type_set(DType::AD, {});
  std::unordered_set<int> spu_valid_item_type_set = get_valid_item_type_set(DType::SPU, {32, 99});
  std::unordered_set<int> mmu_valid_item_type_set = get_valid_item_type_set(DType::MMU, {32, 99});
  std::unordered_set<int> session_live_valid_item_type_set =
      get_valid_item_type_set(DType::SESSION_LIVE, {1});
  std::unordered_set<int> brand_valid_item_type_set = get_valid_item_type_set(DType::BRAND_ID, {1, 32, 99});
  std::unordered_set<int> recall_valid_item_type_set = get_valid_item_type_set(DType::RECALL, {1, 32, 99});
  std::unordered_set<int> vgs_id_valid_item_type_set = get_valid_item_type_set(DType::VGS_ID, {1, 32, 99});
  std::unordered_set<int> rebuy_valid_item_type_set = get_valid_item_type_set(DType::REBUY, {1, 32, 99});
  std::unordered_set<int> pw_id_valid_item_type_set = get_valid_item_type_set(DType::PW_ID, {1, 32, 99});
  std::unordered_set<int> order_c_leaf_valid_item_type_set =
      get_valid_item_type_set(DType::ORDER_C_LEAF, {1, 32, 99});
  std::unordered_set<int> sim_cluster_id_valid_item_type_set =
      get_valid_item_type_set(DType::SIM_CLUSTER_ID, {1, 32, 99});

  // 开始创建多样性规则对象，除了直播多样性针对直播类型物料，其他规则均只针对商品做多样性设置
  std::shared_ptr<SlideWindowCounter> slide_cid3_counter = std::make_shared<SlideWindowCounter>(
      DType::CID3, cid3_variant_win_size, cid3_variant_max_num, cid3_valid_item_type_set);
  std::shared_ptr<SlideWindowCounter> slide_cid2_counter = std::make_shared<SlideWindowCounter>(
      DType::CID2, cid2_variant_win_size, cid2_variant_max_num, cid2_valid_item_type_set);
  std::shared_ptr<SlideWindowCounter> slide_aid_counter = std::make_shared<SlideWindowCounter>(
      DType::AID, aid_variant_win_size, aid_variant_max_num, aid_valid_item_type_set);
  std::shared_ptr<SlideWindowCounter> slide_leaf_counter = std::make_shared<SlideWindowCounter>(
      DType::LEAF, leaf_variant_win_size, leaf_variant_max_num, leaf_valid_item_type_set);
  std::shared_ptr<SlideWindowCounter> slide_live_counter = std::make_shared<SlideWindowCounter>(
      DType::LIVE, live_variant_win_size, live_variant_max_num, live_valid_item_type_set);
  std::shared_ptr<SlideWindowCounter> slide_ad_counter = std::make_shared<SlideWindowCounter>(
      DType::AD, ad_variant_win_size, ad_variant_max_num, ad_valid_item_type_set);
  std::shared_ptr<SlideWindowCounter> slide_spu_counter = std::make_shared<SlideWindowCounter>(
      DType::SPU, spu_variant_win_size, spu_variant_max_num, spu_valid_item_type_set);
  std::shared_ptr<SlideWindowListCalCounter> slide_mmu_counter = std::make_shared<SlideWindowListCalCounter>(
      DType::MMU, mmu_variant_win_size, mmu_variant_max_num, mmu_valid_item_type_set);
  // 直播屏内 10 出 4 限制
  std::shared_ptr<SlideWindowCounter> session_live_counter = std::make_shared<SlideWindowCounter>(
      DType::SESSION_LIVE, 9, 4, session_live_valid_item_type_set);
  std::shared_ptr<SlideWindowCounter> slide_brand_counter = std::make_shared<SlideWindowCounter>(
      DType::BRAND_ID, brand_variant_win_size, brand_variant_max_num, brand_valid_item_type_set);
  std::shared_ptr<SlideWindowCounter> slide_recall_counter = std::make_shared<SlideWindowCounter>(
      DType::RECALL, recall_variant_win_size, recall_variant_max_num, recall_valid_item_type_set);
  std::shared_ptr<SlideWindowCounter> slide_vgs_id_counter = std::make_shared<SlideWindowCounter>(
      DType::VGS_ID, vgs_id_variant_win_size, vgs_id_variant_max_num, vgs_id_valid_item_type_set);
  std::shared_ptr<MultiSlideWindowCounter> slide_multi_cid1_counter =
      std::make_shared<MultiSlideWindowCounter>(
          DType::MULTI_CID1, multi_cid1_max_win_size, 1, cid1_valid_item_type_set);
  std::shared_ptr<MultiSlideWindowCounter> slide_multi_cid2_counter =
      std::make_shared<MultiSlideWindowCounter>(
          DType::MULTI_CID2, multi_cid2_max_win_size, 1, cid2_valid_item_type_set);
  std::shared_ptr<MultiSlideWindowCounter> slide_multi_cid3_counter =
      std::make_shared<MultiSlideWindowCounter>(
          DType::MULTI_CID3, multi_cid3_max_win_size, 1, cid3_valid_item_type_set);
  std::shared_ptr<MultiSlideWindowCounter> slide_multi_leaf_counter =
      std::make_shared<MultiSlideWindowCounter>(
          DType::MULTI_LEAF, multi_leaf_max_win_size, 1, leaf_valid_item_type_set);
  std::shared_ptr<SlideWindowCounter> slide_rebuy_counter = std::make_shared<SlideWindowCounter>(
      DType::REBUY, rebuy_variant_win_size, rebuy_variant_max_num, rebuy_valid_item_type_set);
  std::shared_ptr<SlideWindowCounter> slide_pw_id_counter = std::make_shared<SlideWindowCounter>(
      DType::PW_ID, pw_id_variant_win_size, pw_id_variant_max_num, pw_id_valid_item_type_set);
  std::shared_ptr<SlideWindowCounter> slide_order_c_leaf_counter = std::make_shared<SlideWindowCounter>(
      DType::ORDER_C_LEAF, order_c_leaf_variant_win_size, order_c_leaf_variant_max_num,
      order_c_leaf_valid_item_type_set);
  std::shared_ptr<SlideWindowCounter> slide_sim_cluster_id_counter = std::make_shared<SlideWindowCounter>(
      DType::SIM_CLUSTER_ID, sim_cluster_id_variant_win_size, sim_cluster_id_variant_max_num,
      sim_cluster_id_valid_item_type_set);

  auto prev_trans_cid1_list = context->GetIntListCommonAttr("prev_trans_cid1_list");
  auto prev_trans_cid2_list = context->GetIntListCommonAttr("prev_trans_cid2_list");
  auto prev_trans_cid3_list = context->GetIntListCommonAttr("prev_trans_cid3_list");
  auto prev_item_aid_list = context->GetIntListCommonAttr("prev_item_aid_list");
  auto prev_item_type_list = context->GetIntListCommonAttr("prev_item_type_list");
  auto prev_item_key_list = context->GetIntListCommonAttr("prev_item_key_list");
  auto prev_item_ad_list = context->GetIntListCommonAttr("prev_item_ad_list");
  auto prev_item_leaf_list = context->GetIntListCommonAttr("prev_trans_leaf_list");
  auto prev_item_brand_list = context->GetIntListCommonAttr("prev_item_brand_list");
  auto prev_vgs_id_list = context->GetIntListCommonAttr("prev_item_vgs_id_list");
  auto prev_item_rebuy_list = context->GetIntListCommonAttr("prev_item_rebuy_list");
  auto prev_trans_cid1_win_size_list = context->GetIntListCommonAttr("prev_trans_cid1_win_size_list");
  auto prev_trans_cid2_win_size_list = context->GetIntListCommonAttr("prev_trans_cid2_win_size_list");
  auto prev_trans_cid3_win_size_list = context->GetIntListCommonAttr("prev_trans_cid3_win_size_list");
  auto prev_trans_leaf_win_size_list = context->GetIntListCommonAttr("prev_trans_leaf_win_size_list");
  auto prev_pw_id_list = context->GetIntListCommonAttr("prev_item_pw_id_list");
  auto prev_order_c_leaf_list = context->GetIntListCommonAttr("prev_order_c_leaf_list");
  auto prev_sim_cluster_id_list = context->GetIntListCommonAttr("prev_sim_cluster_id_list");

  auto multi_variant_enable = context->GetIntCommonAttr("multi_variant_enable").value_or(0);
  auto multi_cid1_variant_enable = context->GetIntCommonAttr("multi_cid1_variant_enable").value_or(0);

  int64 cid1_len = prev_trans_cid1_list ? (*prev_trans_cid1_list).size() : 0;
  int64 cid2_len = prev_trans_cid2_list ? (*prev_trans_cid2_list).size() : 0;
  int64 cid3_len = prev_trans_cid3_list ? (*prev_trans_cid3_list).size() : 0;
  int64 aid_len = prev_item_aid_list ? (*prev_item_aid_list).size() : 0;
  int64 type_len = prev_item_type_list ? (*prev_item_type_list).size() : 0;
  int64 item_key_len = prev_item_key_list ? (*prev_item_key_list).size() : 0;
  int64 ad_len = prev_item_ad_list ? (*prev_item_ad_list).size() : 0;
  int64 leaf_len = prev_item_leaf_list ? (*prev_item_leaf_list).size() : 0;
  int64 brand_len = prev_item_brand_list ? (*prev_item_brand_list).size() : 0;
  int64 vgs_id_len = prev_vgs_id_list ? (*prev_vgs_id_list).size() : 0;
  int64 rebuy_len = prev_item_rebuy_list ? (*prev_item_rebuy_list).size() : 0;
  int64 cid1_win_len = prev_trans_cid1_win_size_list ? (*prev_trans_cid1_win_size_list).size() : 0;
  int64 cid2_win_len = prev_trans_cid2_win_size_list ? (*prev_trans_cid2_win_size_list).size() : 0;
  int64 cid3_win_len = prev_trans_cid3_win_size_list ? (*prev_trans_cid3_win_size_list).size() : 0;
  int64 leaf_win_len = prev_trans_leaf_win_size_list ? (*prev_trans_leaf_win_size_list).size() : 0;
  int64 pw_id_len = prev_pw_id_list ? (*prev_pw_id_list).size() : 0;
  int64 order_c_leaf_len = prev_order_c_leaf_list ? (*prev_order_c_leaf_list).size() : 0;
  int64 sim_cluster_id_len = prev_sim_cluster_id_list ? (*prev_sim_cluster_id_list).size() : 0;
  int64 max_win_size = 0;
  max_win_size = std::max({cid3_variant_win_size, cid2_variant_win_size, aid_variant_win_size,
                           leaf_variant_win_size, live_variant_win_size, ad_variant_win_size,
                           mmu_variant_win_size, brand_variant_win_size, vgs_id_variant_win_size,
                           multi_cid2_max_win_size, multi_cid3_max_win_size, multi_leaf_max_win_size,
                           multi_cid1_max_win_size, rebuy_variant_win_size, pw_id_variant_win_size,
                           order_c_leaf_len, sim_cluster_id_len});
  ItemScoreInfo score_info;
  for (int64 i = max_win_size; i > 0; --i) {
    if (type_len - i >= 0) {
      score_info.item_type = (*prev_item_type_list)[type_len - i];
      slide_live_counter->Update(&score_info, &diversity_infos_);
    }
    if (cid1_len - i >= 0) {
      score_info.trans_cid1 = (*prev_trans_cid1_list)[cid1_len - i];
      score_info.item_cid1_for_embd = (*prev_trans_cid1_list)[cid1_len - i];
    }
    if (cid2_len - i >= 0) {
      score_info.trans_cid2 = (*prev_trans_cid2_list)[cid2_len - i];
      slide_cid2_counter->Update(&score_info, &diversity_infos_);
    }
    if (cid3_len - i >= 0) {
      score_info.trans_cid3 = (*prev_trans_cid3_list)[cid3_len - i];
      slide_cid3_counter->Update(&score_info, &diversity_infos_);
    }
    if (aid_len - i >= 0) {
      score_info.aid = (*prev_item_aid_list)[aid_len - i];
      slide_aid_counter->Update(&score_info, &diversity_infos_);
    }
    if (item_key_len - i >= 0) {
      score_info.item_key = (*prev_item_key_list)[item_key_len - i];
      slide_mmu_counter->Update(&score_info, &diversity_infos_);
    }
    if (ad_len - i >= 0) {
      score_info.is_ad = (*prev_item_ad_list)[ad_len - i];
      slide_ad_counter->Update(&score_info, &diversity_infos_);
    }
    if (leaf_len - i >= 0) {
      score_info.trans_leaf = (*prev_item_leaf_list)[leaf_len - i];
      slide_leaf_counter->Update(&score_info, &diversity_infos_);
    }
    if (brand_len - i >= 0) {
      score_info.brand_id = (*prev_item_brand_list)[brand_len - i];
      slide_brand_counter->Update(&score_info, &diversity_infos_);
    }
    if (vgs_id_len - i >= 0) {
      score_info.vgs_id = (*prev_vgs_id_list)[vgs_id_len - i];
      slide_vgs_id_counter->Update(&score_info, &diversity_infos_);
    }
    if (pw_id_len - i >= 0) {
      score_info.pw_id = (*prev_pw_id_list)[pw_id_len - i];
      slide_pw_id_counter->Update(&score_info, &diversity_infos_);
    }
    if (order_c_leaf_len - i >= 0) {
      score_info.order_c_leaf = (*prev_order_c_leaf_list)[order_c_leaf_len - i];
      slide_order_c_leaf_counter->Update(&score_info, &diversity_infos_);
    }
    if (sim_cluster_id_len - i >= 0) {
      score_info.sim_cluster_id = (*prev_sim_cluster_id_list)[sim_cluster_id_len - i];
      slide_sim_cluster_id_counter->Update(&score_info, &diversity_infos_);
    }
    if (multi_variant_enable) {
      if (cid2_win_len - i >= 0) {
        score_info.trans_cid2_slide_info.window_size = (*prev_trans_cid2_win_size_list)[cid2_win_len - i];
        slide_multi_cid2_counter->Update(&score_info, &diversity_infos_);
      }
      if (cid3_win_len - i >= 0) {
        score_info.trans_cid3_slide_info.window_size = (*prev_trans_cid3_win_size_list)[cid3_win_len - i];
        slide_multi_cid3_counter->Update(&score_info, &diversity_infos_);
      }
      if (leaf_win_len - i >= 0) {
        score_info.trans_leaf_slide_info.window_size = (*prev_trans_leaf_win_size_list)[leaf_win_len - i];
        slide_multi_leaf_counter->Update(&score_info, &diversity_infos_);
      }
      if (rebuy_len - i >= 0) {
        score_info.rebuy_variant_flag = (*prev_item_rebuy_list)[rebuy_len - i];
        slide_rebuy_counter->Update(&score_info, &diversity_infos_);
      }
    }
    if (multi_cid1_variant_enable) {
      if (cid1_win_len - i >= 0) {
        score_info.trans_cid1_slide_info.window_size = (*prev_trans_cid1_win_size_list)[cid1_win_len - i];
        slide_multi_cid1_counter->Update(&score_info, &diversity_infos_);
      }
    }
  }

  // 优先级 1: AD
  // 优先级 2: SESSION LIVE
  // 优先级 3: LIVE
  // 优先级  : REBUY
  // 优先级  : VGS_ID
  // 优先级  : MULTI_CID2
  // 优先级  : MULTI_CID3
  // 优先级  : MULTI_LEAF
  // 优先级  : RECALL
  // 优先级 4: SPU
  // 优先级 5: LEAF
  // 优先级 6: MMU
  // 优先级 7: AID
  // 优先级 8: cid2
  // 优先级 9: cid3
  // 优先级 10: brand
  // 创建序列多样性规则集合，对应 diversity_rules 中的 index 越小，该规则的优先级越高

  sequence_infos->diversity_rules.clear();
  hard_diversity_rule_type_.clear();
  soft_diversity_rule_type_.clear();
  auto ad_variant_enable = context->GetIntCommonAttr("ad_variant_enable").value_or(0);
  auto ad_hard_diversity_enable = context->GetIntCommonAttr("ad_hard_diversity_enable").value_or(0);
  auto ad_soft_diversity_enable = context->GetIntCommonAttr("ad_soft_diversity_enable").value_or(0);
  if (ad_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_ad_counter);
    if (ad_hard_diversity_enable) {
      hard_diversity_rule_type_.insert(DType::AD);
    }
    if (ad_soft_diversity_enable) {
      soft_diversity_rule_type_.insert(DType::AD);
    }
  }

  auto session_live_variant_enable = context->GetIntCommonAttr("session_live_variant_enable").value_or(0);
  if (session_live_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(session_live_counter);
    hard_diversity_rule_type_.insert(DType::SESSION_LIVE);
  }

  auto live_variant_enable = context->GetIntCommonAttr("live_variant_enable").value_or(0);
  auto live_hard_diversity_enable = context->GetIntCommonAttr("live_hard_diversity_enable").value_or(0);
  auto live_soft_diversity_enable = context->GetIntCommonAttr("live_soft_diversity_enable").value_or(0);
  if (live_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_live_counter);
    if (live_hard_diversity_enable) {
      hard_diversity_rule_type_.insert(DType::LIVE);
    }
    if (live_soft_diversity_enable) {
      soft_diversity_rule_type_.insert(DType::LIVE);
    }
  }
  auto rebuy_variant_enable = context->GetIntCommonAttr("rebuy_variant_enable").value_or(0);
  auto rebuy_hard_diversity_enable = context->GetIntCommonAttr("rebuy_hard_diversity_enable").value_or(0);
  auto rebuy_soft_diversity_enable = context->GetIntCommonAttr("rebuy_soft_diversity_enable").value_or(0);
  if (rebuy_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_rebuy_counter);
    if (rebuy_hard_diversity_enable) {
      hard_diversity_rule_type_.insert(DType::REBUY);
    }
    if (rebuy_soft_diversity_enable) {
      soft_diversity_rule_type_.insert(DType::REBUY);
    }
  }

  auto vgs_id_variant_enable = context->GetIntCommonAttr("vgs_id_variant_enable").value_or(0);
  auto vgs_id_hard_diversity_enable = context->GetIntCommonAttr("vgs_id_hard_diversity_enable").value_or(0);
  auto vgs_id_soft_diversity_enable = context->GetIntCommonAttr("vgs_id_soft_diversity_enable").value_or(0);
  if (vgs_id_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_vgs_id_counter);
    if (vgs_id_hard_diversity_enable) {
      hard_diversity_rule_type_.insert(DType::VGS_ID);
    }
    if (vgs_id_soft_diversity_enable) {
      soft_diversity_rule_type_.insert(DType::VGS_ID);
    }
  }

  if (multi_cid1_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_multi_cid1_counter);
    hard_diversity_rule_type_.insert(DType::MULTI_CID1);
  }

  if (multi_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_multi_cid2_counter);
    sequence_infos->diversity_rules.emplace_back(slide_multi_cid3_counter);
    sequence_infos->diversity_rules.emplace_back(slide_multi_leaf_counter);
    hard_diversity_rule_type_.insert(DType::MULTI_CID2);
    hard_diversity_rule_type_.insert(DType::MULTI_CID3);
    hard_diversity_rule_type_.insert(DType::MULTI_LEAF);
  }

  auto recall_variant_enable = context->GetIntCommonAttr("recall_variant_enable").value_or(0);
  auto recall_hard_diversity_enable = context->GetIntCommonAttr("recall_hard_diversity_enable").value_or(0);
  auto recall_soft_diversity_enable = context->GetIntCommonAttr("recall_soft_diversity_enable").value_or(0);
  if (recall_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_recall_counter);
    if (recall_hard_diversity_enable) {
      hard_diversity_rule_type_.insert(DType::RECALL);
    }
    if (recall_soft_diversity_enable) {
      soft_diversity_rule_type_.insert(DType::RECALL);
    }
  }

  auto spu_variant_enable = context->GetIntCommonAttr("spu_variant_enable").value_or(0);
  auto spu_hard_diversity_enable = context->GetIntCommonAttr("spu_hard_diversity_enable").value_or(0);
  if (spu_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_spu_counter);
    if (spu_hard_diversity_enable) {
      hard_diversity_rule_type_.insert(DType::SPU);
    }
  }

  auto pw_id_variant_enable = context->GetIntCommonAttr("pw_id_variant_enable").value_or(0);
  if (pw_id_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_pw_id_counter);
    hard_diversity_rule_type_.insert(DType::PW_ID);
  }

  auto order_c_leaf_variant_enable = context->GetIntCommonAttr("order_c_leaf_variant_enable").value_or(0);
  if (order_c_leaf_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_order_c_leaf_counter);
    hard_diversity_rule_type_.insert(DType::ORDER_C_LEAF);
  }

  auto sim_cluster_id_variant_enable = context->GetIntCommonAttr("sim_cluster_id_variant_enable").value_or(0);
  if (sim_cluster_id_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_sim_cluster_id_counter);
    hard_diversity_rule_type_.insert(DType::SIM_CLUSTER_ID);
  }

  auto leaf_variant_enable = context->GetIntCommonAttr("leaf_variant_enable").value_or(0);
  auto leaf_hard_diversity_enable = context->GetIntCommonAttr("leaf_hard_diversity_enable").value_or(0);
  auto leaf_soft_diversity_enable = context->GetIntCommonAttr("leaf_soft_diversity_enable").value_or(0);
  if (leaf_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_leaf_counter);
    if (leaf_hard_diversity_enable) {
      hard_diversity_rule_type_.insert(DType::LEAF);
    }
    if (leaf_soft_diversity_enable) {
      soft_diversity_rule_type_.insert(DType::LEAF);
    }
  }

  auto mmu_variant_enable = context->GetIntCommonAttr("mmu_variant_enable").value_or(0);
  auto mmu_hard_diversity_enable = context->GetIntCommonAttr("mmu_hard_diversity_enable").value_or(0);
  auto mmu_soft_diversity_enable = context->GetIntCommonAttr("mmu_soft_diversity_enable").value_or(0);
  if (mmu_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_mmu_counter);
    if (mmu_hard_diversity_enable) {
      hard_diversity_rule_type_.insert(DType::MMU);
    }
    if (mmu_soft_diversity_enable) {
      soft_diversity_rule_type_.insert(DType::MMU);
    }
  }

  auto aid_variant_enable = context->GetIntCommonAttr("aid_variant_enable").value_or(0);
  auto aid_hard_diversity_enable = context->GetIntCommonAttr("aid_hard_diversity_enable").value_or(0);
  auto aid_soft_diversity_enable = context->GetIntCommonAttr("aid_soft_diversity_enable").value_or(0);
  if (aid_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_aid_counter);
    if (aid_hard_diversity_enable) {
      hard_diversity_rule_type_.insert(DType::AID);
    }
    if (aid_soft_diversity_enable) {
      soft_diversity_rule_type_.insert(DType::AID);
    }
  }

  auto cid2_variant_enable = context->GetIntCommonAttr("cid2_variant_enable").value_or(0);
  auto cid2_hard_diversity_enable = context->GetIntCommonAttr("cid2_hard_diversity_enable").value_or(0);
  auto cid2_soft_diversity_enable = context->GetIntCommonAttr("cid2_soft_diversity_enable").value_or(0);
  if (cid2_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_cid2_counter);
    if (cid2_hard_diversity_enable) {
      hard_diversity_rule_type_.insert(DType::CID2);
    }
    if (cid2_soft_diversity_enable) {
      soft_diversity_rule_type_.insert(DType::CID2);
    }
  }

  auto cid3_variant_enable = context->GetIntCommonAttr("cid3_variant_enable").value_or(0);
  auto cid3_hard_diversity_enable = context->GetIntCommonAttr("cid3_hard_diversity_enable").value_or(0);
  auto cid3_soft_diversity_enable = context->GetIntCommonAttr("cid3_soft_diversity_enable").value_or(0);
  if (cid3_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_cid3_counter);
    if (cid3_hard_diversity_enable) {
      hard_diversity_rule_type_.insert(DType::CID3);
    }
    if (cid3_soft_diversity_enable) {
      soft_diversity_rule_type_.insert(DType::CID3);
    }
  }

  auto brand_variant_enable = context->GetIntCommonAttr("brand_variant_enable").value_or(0);
  auto brand_hard_diversity_enable = context->GetIntCommonAttr("brand_hard_diversity_enable").value_or(0);
  auto brand_soft_diversity_enable = context->GetIntCommonAttr("brand_soft_diversity_enable").value_or(0);
  if (brand_variant_enable) {
    sequence_infos->diversity_rules.emplace_back(slide_brand_counter);
    if (brand_hard_diversity_enable) {
      hard_diversity_rule_type_.insert(DType::BRAND_ID);
    }
    if (brand_soft_diversity_enable) {
      soft_diversity_rule_type_.insert(DType::BRAND_ID);
    }
  }
}

int MerchantMixRankGenSeedSequenceBaseEnricher::DiversityRuleHardCheck(
    const SequenceInfos& sequence_infos,
    const ItemScoreInfo* score_info) {
  if (sequence_infos.diversity_rules.size() == 0 ||
      hard_diversity_rule_type_.size() == 0 ||
      score_info == nullptr) {
    return 0;
  }
  int flag = 0;
  for (auto diversity_rule : sequence_infos.diversity_rules) {
    DType d_type = diversity_rule->GetDType();
    if (hard_diversity_rule_type_.find(d_type) !=  hard_diversity_rule_type_.end()) {
      if (score_info->skip_d_types.find(d_type) !=  score_info->skip_d_types.end() ||
        diversity_rule->HardCheck(score_info, sequence_infos.score_infos.size(), &diversity_infos_)) {
        flag = (flag << 1) + 1;
      } else {
        flag = (flag << 1);
      }
    }
  }
  return flag;
}

double MerchantMixRankGenSeedSequenceBaseEnricher::DiversityRuleSoftCheck(
    const SequenceInfos& sequence_infos,
    const ItemScoreInfo* score_info) {
  if (soft_diversity_rule_type_.size() == 0 ||
      sequence_infos.diversity_rules.size() == 0 ||
      score_info == nullptr) {
    return 0;
  }
  double diversity_score = 0;
  for (auto diversity_rule : sequence_infos.diversity_rules) {
    DType d_type = diversity_rule->GetDType();
    if (soft_diversity_rule_type_.find(d_type) !=  soft_diversity_rule_type_.end()) {
      if (score_info->skip_d_types.find(d_type) !=  score_info->skip_d_types.end()) {
        continue;
      }
      diversity_score += diversity_rule->SoftCheck(score_info,
                                                   sequence_infos.score_infos.size(),
                                                   &diversity_infos_);
    }
  }
  return diversity_score;
}

void MerchantMixRankGenSeedSequenceBaseEnricher::DiversityRuleUpdate(const ItemScoreInfo* score_info,
    SequenceInfos* sequence_infos) {
  if (sequence_infos->diversity_rules.size() == 0 || score_info == nullptr) {
    return;
  }
  for (int i = 0; i < sequence_infos->diversity_rules.size(); ++i) {
    sequence_infos->diversity_rules[i]->Update(score_info,
                                               &diversity_infos_,
                                               sequence_infos->score_infos.size() -  1);
  }
  return;
}

void MerchantMixRankGenSeedSequenceBaseEnricher::ProcessOneSequences(
    SequenceInfos* curr_sequences_infos,
    const SequenceInfos& origin_sequence_infos,
    std::unordered_set<uint64>* seq_set,
    std::vector<int64>* retrieve_items,
    MutableRecoContextInterface* context) {
  if (curr_sequences_infos == nullptr || seq_set == nullptr ||
      retrieve_items == nullptr || context == nullptr) {
    return;
  }
  const int origin_item_num = origin_sequence_infos.score_infos.size();
  const int max_length = std::min(sequence_max_length_, origin_item_num);
  if (curr_sequences_infos->selected_flag_list.size() == origin_item_num) {
    if (skip_fill_sequence_early_return_ == 0) {
      GenSeedSequenceAndKey(curr_sequences_infos);
      if (seq_set->find(curr_sequences_infos->item_key) != seq_set->end()) {
        return;
      }
      seq_set->insert(curr_sequences_infos->item_key);
    }
    // 拓展序列
    FillSequenceToMaxLength(curr_sequences_infos, origin_sequence_infos, max_length);
    GenSeedSequenceAndKey(curr_sequences_infos);
    if (seq_set->find(curr_sequences_infos->item_key) == seq_set->end()) {
      seq_set->insert(curr_sequences_infos->item_key);
      retrieve_items->emplace_back(static_cast<int64>(curr_sequences_infos->item_key));
      SetSequenceAttrValue(curr_sequences_infos);
      ConvertSequenceAttrToItemAttr(context, curr_sequences_infos);
    }
  }
  return;
}

std::tuple<int, double> SlideWindowListCalCounter::GetCheckNumAndScore(
    const ItemScoreInfo* score_info,
    const int pos_index,
    DiversityInfos* diversity_infos) {
  double final_score = 0.0;
  int curr_num = 0;
  int pre_max_num = 0;

  if (score_info == nullptr) {
    return std::make_tuple(pre_max_num, final_score);
  }

  // 如果设置了 target_item_type， 同时 curr item_type 未在 set 中，则校验通过。
  if (!target_item_type_.empty() &&
      target_item_type_.find(score_info->item_type) == target_item_type_.end()) {
    return std::make_tuple(pre_max_num, final_score);
  }

  const int64_t key = GetDiversityKey(score_info);
  if (key <= 0) {
    return std::make_tuple(pre_max_num, final_score);
  }

  const auto counter_key_iter = counter_list_.find(key);
  const int64_t curr_item_key = score_info->item_key;
  const auto curr_item_emb_iter = diversity_infos->item_key_2_embedding_map.find(curr_item_key);

  if (counter_key_iter == counter_list_.end() ||
      curr_item_emb_iter == diversity_infos->item_key_2_embedding_map.end()) {
    return std::make_tuple(pre_max_num, final_score);
  }

  // const int64_t cid1 = score_info->trans_cid1;
  double threshold = diversity_infos->default_mmu_cid1_threshold_;
  const auto cid1_threshold_iter = diversity_infos->mmu_cid1_threshold_map.find(key);
  if (cid1_threshold_iter != diversity_infos->mmu_cid1_threshold_map.end()) {
    threshold = cid1_threshold_iter->second;
  }

  const int max_size = data_.size();
  const auto& curr_item_embedding = curr_item_emb_iter->second;
  std::vector<int> key_select_index;
  key_select_index.reserve(counter_key_iter->second.size());
  for (int index = 0; index < max_size; ++index) {
    if (key == data_[index]) {
      key_select_index.emplace_back(index);
    }
  }

  if (key_select_index.size() != counter_key_iter->second.size()) {
    return std::make_tuple(pre_max_num, final_score);
  }

  for (size_t i = 0; i < counter_key_iter->second.size(); ++i) {
    const auto& pre_item_key = std::get<0>(counter_key_iter->second[i]);
    int pre_item_diversity_num = std::get<1>(counter_key_iter->second[i]);
    std::tuple<int64_t, int64_t> item_pair_key = pre_item_key > curr_item_key ?
      std::make_tuple(pre_item_key, curr_item_key) : std::make_tuple(curr_item_key, pre_item_key);

    double inner_value = 0.0;
    auto item_pair_score_iter = diversity_infos->item_pair_mmu_score_map.find(item_pair_key);
    if (item_pair_score_iter != diversity_infos->item_pair_mmu_score_map.end()) {
      inner_value = item_pair_score_iter->second;
    } else {
      const auto pre_item_emb_iter = diversity_infos->item_key_2_embedding_map.find(pre_item_key);
      if (pre_item_emb_iter != diversity_infos->item_key_2_embedding_map.end() &&
          curr_item_embedding.size() == pre_item_emb_iter->second.size()) {
        inner_value = CalculateInnerProduct(curr_item_embedding, pre_item_emb_iter->second);
      }
      diversity_infos->item_pair_mmu_score_map[item_pair_key] = inner_value;
    }
    int pos_gap = max_size - key_select_index[i];
    pos_gap = GetGapMpa(pos_index, pos_gap, diversity_infos->pos_gap_offset_);
    const double this_score = GetGapWeight(diversity_infos->soft_key_to_weight_map, pos_gap) * inner_value;
    if (this_score > threshold) {
      ++curr_num;
      ++pre_item_diversity_num;
    }
    pre_max_num = std::max(pre_item_diversity_num, pre_max_num);
    final_score += this_score;
  }
  pre_max_num = std::max(curr_num, pre_max_num);
  return std::make_tuple(pre_max_num, final_score);
}


bool SlideWindowListCalCounter::HardCheck(const ItemScoreInfo* score_info,
                                          const int pos_index,
                                          DiversityInfos* diversity_infos) {
  if (score_info == nullptr) {
    return false;
  }
  // 如果设置了 target_item_type， 同时 curr item_type 未在 set 中，则校验通过。
  if (target_item_type_.size() > 0 &&
      target_item_type_.find(score_info->item_type) == target_item_type_.end()) {
    return true;
  }
  const int64_t key = GetDiversityKey(score_info);
  // 未查询到 key，则直接校验通过
  if (key <= 0) {
    return true;
  }
  const std::tuple<int, double> max_num_score_pair = GetCheckNumAndScore(
      score_info, pos_index, diversity_infos);
  return std::get<0>(max_num_score_pair) < window_max_num_;
}

double SlideWindowListCalCounter::SoftCheck(const ItemScoreInfo* score_info,
                                            const int pos_index,
                                            DiversityInfos* diversity_infos) {
  double final_score = 0.0;
  if (score_info == nullptr) {
    return final_score;
  }
  // 如果设置了 target_item_type， 同时 curr item_type 未在 set 中，则校验通过。
  if (target_item_type_.size() > 0 &&
      target_item_type_.find(score_info->item_type) == target_item_type_.end()) {
    return final_score;
  }
  const int64_t key = GetDiversityKey(score_info);
  // 未查询到 key，则直接返回 0 分相似分
  if (key <= 0) {
    return final_score;
  }
  const std::tuple<int, double> max_num_score_pair = GetCheckNumAndScore(
    score_info, pos_index, diversity_infos);
  return std::get<1>(max_num_score_pair);
}

int SlideWindowListCalCounter::UpdateCountNum(DiversityInfos* diversity_infos,
                                              const ItemScoreInfo* score_info,
                                              const bool is_pop,
                                              const int pos_index) {
  if (score_info == nullptr) {
    return 0;
  }
  const int64_t key = GetDiversityKey(score_info);
  // const int64_t cid1 = score_info->trans_cid1;

  auto counter_key_iter = counter_list_.find(key);
  const int64_t curr_item_key = score_info->item_key;
  const auto curr_item_emb_iter = diversity_infos->item_key_2_embedding_map.find(curr_item_key);
  if (counter_key_iter == counter_list_.end() ||
      curr_item_emb_iter == diversity_infos->item_key_2_embedding_map.end()) {
    return 0;
  }

  double threshold = diversity_infos->default_mmu_cid1_threshold_;
  const auto cid1_threshold_iter = diversity_infos->mmu_cid1_threshold_map.find(key);
  if (cid1_threshold_iter != diversity_infos->mmu_cid1_threshold_map.end()) {
    threshold = cid1_threshold_iter->second;
  }

  const int max_size = data_.size();
  const auto& curr_item_embedding = curr_item_emb_iter->second;
  std::vector<int> key_select_index;
  key_select_index.reserve(counter_key_iter->second.size());
  for (int index = 0; index < max_size; ++index) {
    if (key == data_[index]) {
      key_select_index.emplace_back(index);
    }
  }

  int curr_num = 0;
  int pos_gap = 0;
  int index = 0;
  int curr_pos_index = 0;
  for (auto& item : counter_key_iter->second) {
    if (is_pop) {
      pos_gap = key_select_index[index] + 1;
      curr_pos_index = (pos_index - max_size) + key_select_index[index];
      curr_pos_index = curr_pos_index >= 0 ? curr_pos_index : -1;
    } else {
      pos_gap = max_size - key_select_index[index];
      curr_pos_index = pos_index;
    }
    ++index;
    const int64_t& pre_item_key = std::get<0>(item);
    int& pre_item_diversity_num = std::get<1>(item);
    std::tuple<int64_t, int64_t> item_pair_key = pre_item_key > curr_item_key ?
    std::make_tuple(pre_item_key, curr_item_key) : std::make_tuple(curr_item_key, pre_item_key);

    double inner_value = 0.0;
    auto item_pair_score_iter = diversity_infos->item_pair_mmu_score_map.find(item_pair_key);

    if (item_pair_score_iter != diversity_infos->item_pair_mmu_score_map.end()) {
      inner_value = item_pair_score_iter->second;
    } else {
      const auto pre_item_emb_iter = diversity_infos->item_key_2_embedding_map.find(pre_item_key);
      if (pre_item_emb_iter != diversity_infos->item_key_2_embedding_map.end() &&
          curr_item_embedding.size() == pre_item_emb_iter->second.size()) {
        inner_value = CalculateInnerProduct(curr_item_embedding, pre_item_emb_iter->second);
      }
      diversity_infos->item_pair_mmu_score_map[item_pair_key] = inner_value;
    }
    pos_gap = GetGapMpa(curr_pos_index, pos_gap, diversity_infos->pos_gap_offset_);
    const double this_score = GetGapWeight(diversity_infos->soft_key_to_weight_map, pos_gap) * inner_value;
    if (this_score > threshold) {
      ++curr_num;
      if (is_pop) {
        --pre_item_diversity_num;
      } else {
        ++pre_item_diversity_num;
      }
    }
  }
  return curr_num;
}

void SlideWindowListCalCounter::Update(const ItemScoreInfo* score_info,
                                       DiversityInfos* diversity_infos,
                                       const int pos_index) {
  if (score_info == nullptr) {
    return;
  }
  int curr_num = 0;
  int64_t curr_item_key = score_info->item_key;
  int64_t key = GetDiversityKey(score_info);
  const auto target_type_iter = target_item_type_.find(score_info->item_type);
  // 如果设置了 target_item_type， 该商品的 item_type 与目标不一致，则直接将 key 置为 0
  if (target_item_type_.size() > 0 && target_type_iter == target_item_type_.end()) {
    key = 0;
    curr_item_key = 0;
  }
  if (data_.size() == window_size_ && !data_.empty()) {
    const int64_t delete_key = data_.front();
    data_.pop_front();
    auto iter = counter_list_.find(delete_key);
    if (iter != counter_list_.end()) {
      if (iter->second.size() == 1) {
        counter_list_.erase(iter);
      } else {
        int64_t delete_item = std::get<0>(iter->second[0]);
        iter->second.pop_front();
        if (window_max_num_ > 1) {
          ItemScoreInfo delete_score_info;
          delete_score_info.item_key = delete_item;
          delete_score_info.item_cid1_for_embd = delete_key;
          UpdateCountNum(diversity_infos, &delete_score_info, true, pos_index);
        }
      }
    }
  }
  if (window_max_num_ > 1 && curr_item_key != 0 && key != 0) {
    curr_num = UpdateCountNum(diversity_infos, score_info, false, pos_index);
  }
  // 无论 key 和 item_key 是否有效，不影响窗口更新
  data_.push_back(key);
  counter_list_[key].push_back(std::make_tuple(curr_item_key, curr_num));
  return;
}

}  // namespace platform
}  // namespace ks
