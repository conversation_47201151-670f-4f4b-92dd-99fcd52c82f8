#include "dragon/src/processor/ext/merchant/arranger/merchant_merge_tunnel_with_earlystop_arranger.h"

#include <algorithm>
#include <limits>
#include <memory>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <stdexcept>
#include <chrono>
#include <thread>
#include <mutex>
#include <iostream>
#include <functional>
#include <set>

namespace ks {
namespace platform {

std::mutex mtx;
std::atomic_int64_t lastTime(0);

std::string GenerateLogId() {
    std::lock_guard<std::mutex> lock(mtx);
    auto now = std::chrono::high_resolution_clock::now().time_since_epoch();
    auto nanoseconds = std::chrono::duration_cast<std::chrono::nanoseconds>(now).count();

    // 确保时间戳不会重复
    while (nanoseconds <= lastTime) {
        std::this_thread::yield();
        now = std::chrono::high_resolution_clock::now().time_since_epoch();
        nanoseconds = std::chrono::duration_cast<std::chrono::nanoseconds>(now).count();
    }

    return std::to_string(nanoseconds);
}

bool MerchantMergeWithPriorityArranger::InitProcessor() {
  debug_ = config()->GetBoolean("debug", false);
  enable_dedup_ = config()->GetBoolean("enable_dedup", true);
  recall_cut_str_attr_name_ = config()->GetString("recall_cut_str_attr_name", \
      "mall_goods_like_recall_cut_num");
  item_id_str_attr_name_ = config()->GetString("item_id_attr_name", "item_id");
  item_source_attr_name_ = config()->GetString("item_source_attr_name", "reason");
  item_multi_sources_attr_name_ = config()->GetString("item_multi_sources_attr_name", \
      "retrieve_reason_list");
  retrieve_kconf_path_ = config()->GetString("retrieve_kconf_path", \
      "reco.distributedIndex.merchantGoodsLeafRecallPriorityAndQuota");
  retrieve_kconf_gray_attr_ = config()->GetString("retrieve_kconf_path_gray_key", "");

  if (retrieve_kconf_path_.empty()) {
    CL_LOG_EVERY_N(ERROR, 100) << "retrieve_kconf_path is empty!";
    return false;
  }

  if (IsDebug()) {
    log_id_ = GenerateLogId();
    for (const auto &q : quotas_) {
      CL_LOG(INFO) << "reason_name: " << q.reason_name << "reason_id: " \
          << q.reason_id << " weight: " << q.weight \
          << " step: " << q.step << " quota: " << q.quota;
    }
  }
  return true;
}

bool MerchantMergeWithPriorityArranger::ParseRecallPriorityQuota(
      MutableRecoContextInterface *context) {
  std::set<MerchantMergeConfig, std::greater<MerchantMergeConfig>> order_quotas;

  auto default_val = std::make_shared<::Json::Value>();
  std::string retrieve_kconf_path_name = retrieve_kconf_path_;
  if (!retrieve_kconf_gray_attr_.empty()) {
    retrieve_kconf_path_name = std::string(context->GetStringCommonAttr(retrieve_kconf_gray_attr_) \
        .value_or(retrieve_kconf_path_).data());
  }
  std::shared_ptr<ks::infra::KsConfig<std::shared_ptr<::Json::Value>>> retireval_config;
  retireval_config = ks::infra::KConf().Get(retrieve_kconf_path_name, default_val);

  if (!retireval_config || !retireval_config->Get()) {
    CL_LOG_EVERY_N(ERROR, 100) << "init kconf data config failed! kconf name is " \
        << retrieve_kconf_path_name;
    return false;
  }

  std::shared_ptr<::Json::Value> json_config = retireval_config->Get();
  std::unordered_set<std::string> quotas_name_set;

  if (json_config->isArray()) {
    try {
      const ::Json::Value& configArray = *json_config;
      if (IsDebug()) {
        CL_LOG(INFO) << "log_id:" << log_id_ << ", kconf_path:" << retrieve_kconf_path_name \
            << ", JSON ITEM configArray.size():" << configArray.size() \
            << ", toStyledString:" << configArray.toStyledString();
      }
      int i = 0;
      for (auto iter : configArray) {
        const ::Json::Value& item = iter;
        if (!item["ConfigType"].empty() && item["ConfigType"].asString() == "QuotaControl") {
          MerchantQuotaConfig recall_assort_config;
          recall_assort_config.recall_assort =
              item["recall_assort"].empty() ? 0 : item["recall_assort"].asInt();
          recall_assort_config.limit_type = merchant_goods_recall_fair_reduce_type_;
          const std::string& limit_type_str = item["limit_type"].asString();
          std::vector<std::string> limit_type_result =
              absl::StrSplit(limit_type_str, ":", absl::SkipWhitespace());
          if (limit_type_result.size() == 2) {
            recall_assort_config.limit_type =
                context->GetIntCommonAttr(limit_type_result[0]).value_or(stoi(limit_type_result[1]));
          }

          recall_assort_config.limit_num = 100;
          const std::string& limit_num_str = item["limit_num"].asString();
          std::vector<std::string> limit_num_result =
              absl::StrSplit(limit_num_str, ":", absl::SkipWhitespace());
          if (limit_num_result.size() == 2) {
            recall_assort_config.limit_num =
                context->GetIntCommonAttr(limit_num_result[0]).value_or(stoi(limit_num_result[1]));
          }
          recall_assort_config_map_[recall_assort_config.recall_assort] = recall_assort_config;
        } else {
          int reason_id = item["reason"].asInt();
          const std::string& reason_name = std::to_string(reason_id);
          if (quotas_name_set.find(reason_name) != quotas_name_set.end()) {
            CL_LOG_EVERY_N(ERROR, 100) << "log_id:" << log_id_ << ", duplicated reason_name: " << reason_name;
            //  return false;
            continue;
          }

          const std::string& priority_ab_name = item["priority"].asString();
          std::vector<std::string> priority_result = absl::StrSplit(priority_ab_name, ":", \
              absl::SkipWhitespace());
          if (priority_result.size() != 2) {
            CL_LOG_EVERY_N(ERROR, 100) << "log_id:" << log_id_ << \
                ", SplitAbDefaultValue fail, priority_ab_name:" << priority_ab_name;
            continue;
          }
          int64 ab_priority = context->GetIntCommonAttr(priority_result[0]).value_or(0);
          if (ab_priority == 0) {
            if (base::StringToInt64(priority_result[1], &ab_priority) == false) {
              CL_LOG_EVERY_N(ERROR, 100) << "log_id:" << log_id_ << \
                  ", SplitAbDefaultValue fail, priority_ab_name:" << priority_ab_name;
            }
          }

          const std::string& step_ab_name = item["step"].asString();
          std::vector<std::string> step_result = absl::StrSplit(step_ab_name, ":", absl::SkipWhitespace());
          if (step_result.size() != 2) {
            CL_LOG_EVERY_N(ERROR, 100)<< "log_id:" << log_id_
                << ", SplitAbDefaultValue fail, step_ab_name:" \
                << step_ab_name;
            continue;
          }
          int64 ab_step = context->GetIntCommonAttr(step_result[0]).value_or(0);
          if (ab_step == 0) {
            if (base::StringToInt64(step_result[1], &ab_step) == false) {
              CL_LOG_EVERY_N(ERROR, 100) << "log_id:" << log_id_  \
                  << ", SplitAbDefaultValue fail, step_ab_name:" << step_ab_name;
            }
          }

          const std::string& quota_ab_name = item["quota"].asString();
          std::vector<std::string> quota_result = absl::StrSplit(quota_ab_name, ":", absl::SkipWhitespace());
          if (quota_result.size() != 2) {
            CL_LOG_EVERY_N(ERROR, 100) << "log_id:" << log_id_ \
                << ", SplitAbDefaultValue fail, quota_ab_name:" << quota_ab_name;
            continue;
          }
          int64 ab_quota = context->GetIntCommonAttr(quota_result[0]).value_or(0);
          if (ab_quota == 0) {
            if (base::StringToInt64(quota_result[1], &ab_quota) == false) {
              CL_LOG_EVERY_N(ERROR, 100) << "log_id:" << log_id_ \
                  << ", SplitAbDefaultValue fail, quota_ab_name:" << quota_ab_name;
            }
          }

          order_quotas.emplace(reason_name, reason_id, 1.0, ab_step, ab_quota, ab_priority);
          quotas_name_set.insert(reason_name);
          if (IsDebug()) {
            CL_LOG_EVERY_N(INFO, 1000)
                << "log_id:" << log_id_ << ", reason_name:" << reason_name << ", reason_id: " \
                << reason_id << ", weight: " << 1.0 << ", step: " << ab_step << ", quota: " << ab_quota \
                << ", priority: " << ab_priority << ", number:" << i << ", order_quotas.size:" \
                << order_quotas.size() << ", quotas_name_set.size():" << quotas_name_set.size();
          }
        }
      }
    } catch(const std::exception& e) {
        CL_LOG_EVERY_N(ERROR, 100) << "log_id:" << log_id_ << ", catch exception:" << e.what();
        return false;
    } catch (...) {  // 捕获所有其他类型的异常
        CL_LOG_EVERY_N(ERROR, 100) << "log_id:" << log_id_ << ", catch unknown exception";
        return false;
    }
  } else {
    CL_LOG_EVERY_N(ERROR, 100) << "log_id:" << log_id_ \
        << ", Error: json_config expected an array but got something else.";
    return false;
  }
  if (order_quotas.empty()) {
    CL_LOG_EVERY_N(ERROR, 100) << "log_id:" << log_id_ \
        << ", Error, order_quotas is empty, retrieve_kconf_path:" \
        << retrieve_kconf_path_name.c_str();
    return false;
  }
  if (recall_assort_config_map_.empty()) {
    // 和 base 保持一样
    recall_assort_config_map_.emplace(0, MerchantQuotaConfig(0, -1, -1));
  }
  quotas_.clear();
  for (auto reason_conf : order_quotas) {
    quotas_.emplace_back(reason_conf);
  }
  return true;
}

void MerchantMergeWithPriorityArranger::RecallAssortQuotaControl(
      const MerchantQuotaConfig &config,
      const std::unordered_set<int64>& reason_list,
      std::unordered_map<int64, std::vector<RecoResultIter>>* result_map_ptr,
      std::unordered_map<int64, int64>* reason_quota_map_ptr) {
  int64 all_item_num = 0;
  if (!result_map_ptr || !reason_quota_map_ptr) {
    return;
  }
  auto& result_map = *result_map_ptr;
  auto& reason_quota_map = *reason_quota_map_ptr;
  for (auto item_list : result_map) {
    all_item_num += item_list.second.size();
  }
  int64 final_limit_num = config.limit_num >= 0 ? config.limit_num : recall_merge_cut_num_;
  int64 final_limit_type = config.limit_type >= 0 ?
      config.limit_type : merchant_goods_recall_fair_reduce_type_;
  int64 need_cut_num = all_item_num - final_limit_num;
  if (need_cut_num <= 0 || all_item_num <= 0) {
    return;
  }

  std::unordered_map<int64, int> recall_count;
  std::unordered_map<int64, int> recall_already_use_count;
  std::unordered_map<int64, bool> high_level_reason_map;
  std::unordered_map<int64, int64> candidated_item_num_map;

  int64 extra_need_cut_num = 0;
  int64 accumulated_candidate_num = 0;

  for (const auto &q : quotas_) {
    if (reason_list.find(q.reason_id) == reason_list.end()) {
      continue;
    }
    recall_count[q.reason_id] = 0;
    recall_already_use_count[q.reason_id] = 0;
    reason_quota_map[q.reason_id] = std::min(q.quota, (int64)(result_map[q.reason_id].size()));
    if (result_map[q.reason_id].size() > q.quota) {
      // 若实际 item 个数大于保量个数，则普通通道减少个数需要降低
      need_cut_num -= (result_map[q.reason_id].size() - q.quota);
    }
    high_level_reason_map[q.reason_id] = true;
    accumulated_candidate_num += reason_quota_map[q.reason_id];
  }
  if (need_cut_num < 0) {
    need_cut_num = 0;
  }
  // 1: 等比例缩减; 2: 等数量缩减; 3:按照 score 求 topk
  if (final_limit_type == 1) {
    for (const auto& iter : result_map) {
      if (reason_quota_map.find(iter.first) == reason_quota_map.end()) {
        int64 reason_need_cut = iter.second.size() * 1.0 / all_item_num * need_cut_num;
        if (reason_need_cut > iter.second.size()) {
          extra_need_cut_num = extra_need_cut_num + reason_need_cut - iter.second.size();
          reason_need_cut = iter.second.size();
        }
        reason_quota_map.emplace(iter.first, iter.second.size() - reason_need_cut);
      }
    }
    // 补充缩量：轮循
    while (extra_need_cut_num > 0) {
      bool break_flag = true;
      for (auto& iter : reason_quota_map) {
        if (!high_level_reason_map[iter.first] && iter.second > 0) {
          iter.second--;
          extra_need_cut_num--;
          break_flag = false;
        }
      }
      if (break_flag) {
        break;
      }
    }
  } else if (final_limit_type == 2) {
    bool has_other_item = true;
    while (has_other_item) {
      has_other_item = false;
      for (const auto& iter : result_map) {
        // 非高优通道循环遍历
        if (high_level_reason_map.find(iter.first) == high_level_reason_map.end()) {
          // candidated_item_num_map 保存每个通道可用的 quota
          if (candidated_item_num_map.find(iter.first) == candidated_item_num_map.end()) {
            candidated_item_num_map.emplace(iter.first, result_map[iter.first].size());
          }
          if (candidated_item_num_map[iter.first] > 0) {
            if (reason_quota_map.find(iter.first) == reason_quota_map.end()) {
              reason_quota_map.emplace(iter.first, 1);
            } else {
              reason_quota_map[iter.first]++;
            }
            candidated_item_num_map[iter.first]--;
            accumulated_candidate_num++;
            has_other_item = true;
          }
          if (accumulated_candidate_num >= final_limit_num) {
            has_other_item = false;
            break;
          }
        }
      }
    }
  } else if (final_limit_type == 3) {
    std::vector<CommonRecoResult> sort_results;
    for (const auto& iter : result_map) {
      if (reason_quota_map.find(iter.first) == reason_quota_map.end()) {
        std::transform(iter.second.begin(), iter.second.begin() + iter.second.size(),
                         std::back_inserter(sort_results),
                         [](const RecoResultIter& iter) { return *iter; });
      }
    }
    int need_sort_item_num = final_limit_num - accumulated_candidate_num;
    if (need_sort_item_num > 0 && need_sort_item_num < sort_results.size()) {
      std::partial_sort(sort_results.begin(), sort_results.begin() + need_sort_item_num, sort_results.end(),
        [](const CommonRecoResult& a, const CommonRecoResult& b) {
          return a.score > b.score;
      });
      for (auto cur_iter : sort_results) {
        if (accumulated_candidate_num++ <= final_limit_num) {
          reason_quota_map[cur_iter.reason]++;
        }
      }
    }
  } else {
    // 和原来逻辑对齐
    for (const auto& iter : result_map) {
      if (reason_quota_map.find(iter.first) == reason_quota_map.end()) {
        reason_quota_map.emplace(iter.first, result_map[iter.first].size());
      }
    }
  }
}

RecoResultIter MerchantMergeWithPriorityArranger::Arrange(
      MutableRecoContextInterface *context, RecoResultIter begin, RecoResultIter end) {
  size_t total_size = std::distance(begin, end);
  if (total_size <= 0) {
    if (IsDebug()) {
      CL_LOG_EVERY_N(ERROR, 100) << "intermix cancelled: empty item list!";
    }
    return end;
  }
  recall_merge_cut_num_ = context->GetIntCommonAttr(recall_cut_str_attr_name_).value_or(9999);
  int64 need_cut_num = total_size < recall_merge_cut_num_ ? 0 : total_size - recall_merge_cut_num_;
  if (need_cut_num == 0) {
    if (IsDebug()) {
      LOG_EVERY_N(ERROR, 100) << "need't cut, total_size:" << total_size \
          << ", cut_num:" << recall_merge_cut_num_;
    }
    return end;
  }
  recall_assort_config_map_.clear();
  ParseRecallPriorityQuota(context);

  std::unordered_map<int64, std::vector<RecoResultIter>> result_map;
  std::unordered_map<int64, std::unordered_set<int64>> recall_assort_map;
  bool enable_earlystop_reason_merge = context->GetIntCommonAttr("enable_earlystop_reason_merge").value_or(1);
  merchant_goods_recall_fair_reduce_type_ = \
      context->GetIntCommonAttr("merchant_goods_recall_fair_reduce_type").value_or(1);

  auto kconf_protect_reason_rule = \
      context->GetStringCommonAttr("open_kconf_protect_reason_rule").value_or("").data();
  std::string u_buyer_effective_type = \
      context->GetStringCommonAttr("uBuyerEffectiveType").value_or("").data();;
  bool enable_kconf_protect_reason = false;
  std::vector<std::string> reason_rule_vec = \
      absl::StrSplit(kconf_protect_reason_rule, ";", absl::SkipWhitespace());
  if (u_buyer_effective_type == "-") {
    u_buyer_effective_type = "U0-none";
  }
  if (!reason_rule_vec.empty() && !u_buyer_effective_type.empty()) {
    for (size_t i = 0; i < reason_rule_vec.size(); ++i) {
      if (reason_rule_vec[i] == u_buyer_effective_type) {
        enable_kconf_protect_reason = true;
        break;
      }
    }
  }
  auto *recall_assort_attr_accessor = context->GetItemAttrAccessor("recall_assort");
  for (auto it = begin; it != end; ++it) {
    int recall_assort = 0;
    if (recall_assort_attr_accessor) {
      recall_assort = context->GetIntItemAttr(*it, recall_assort_attr_accessor).value_or(0);
    }
    result_map[it->reason].push_back(it);
    recall_assort_map[recall_assort].emplace(it->reason);
  }
  std::unordered_map<int64, int64> reason_quota_map;
  for (auto iter : recall_assort_config_map_) {
    auto recall_assort_type = iter.first;
    if (recall_assort_map.find(recall_assort_type) != recall_assort_map.end()) {
      std::unordered_map<int64, std::vector<RecoResultIter>> assort_result_map;
      for (auto reason : recall_assort_map[recall_assort_type]) {
        assort_result_map[reason] = result_map[reason];
      }
      RecallAssortQuotaControl(iter.second, recall_assort_map[recall_assort_type],
          &assort_result_map, &reason_quota_map);
    } else {
      LOG_EVERY_N(ERROR, 1000) << "recall_assort_type:" << recall_assort_type << " not found";
    }
  }
  mix_results_.clear();
  mix_results_.reserve(total_size);
  // 按照 reason_quota_map 的 quota 调整 result
  for (const auto& pair : result_map) {
      const size_t quota = reason_quota_map[pair.first];
      if (quota > 0 && quota <= pair.second.size()) {
          std::transform(pair.second.begin(), pair.second.begin() + quota,
                         std::back_inserter(mix_results_),
                         [](const RecoResultIter& iter) { return *iter; });
      }
  }
  std::copy(mix_results_.begin(), mix_results_.end(), begin);
  int64 after_merge_keep_item_num = \
      recall_merge_cut_num_ > mix_results_.size() ? mix_results_.size() : recall_merge_cut_num_;
  int remove_size = 0;
  auto new_end = std::remove_if(begin, end, \
      [&](const CommonRecoResult &result) { return (++remove_size > after_merge_keep_item_num); });
  return new_end;
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantMergeWithPriorityArranger, MerchantMergeWithPriorityArranger)

}  // namespace platform
}  // namespace ks
