#pragma once

#include <string>
#include <vector>
#include <memory>
#include <unordered_map>
#include <unordered_set>

#include "dragon/src/processor/base/common_reco_base_arranger.h"
#include "ks/serving_util/ab_test_util.h"
#include "ks/base/abtest/abtest_globals.h"
#include "ks/base/abtest/abtest_instance.h"
#include "folly/String.h"
#include "base/strings/string_util.h"


namespace ks {
namespace platform {

struct MerchantMergeConfig {
  explicit MerchantMergeConfig(std::string reason_name = "", int64 reason_id = 0, float weight = 0,
                             int64 step = 0, int64 quota = 0, int64 priority = 0)
      : reason_name(reason_name), reason_id(reason_id), weight(weight),
        step(step), quota(quota), priority(priority) {}

  std::string reason_name;
  int64 reason_id;
  float weight;
  int64 step;
  int64 quota;
  int64 priority;

  MerchantMergeConfig(const MerchantMergeConfig& reason_conf) {
    reason_name = reason_conf.reason_name;
    reason_id = reason_conf.reason_id;
    weight = reason_conf.weight;
    step = reason_conf.step;
    quota = reason_conf.quota;
    priority = reason_conf.priority;
  }

  bool operator > (const MerchantMergeConfig& another) const {
    return priority > another.priority;
  }
};

struct MerchantQuotaConfig {
  int64 recall_assort;
  int64 limit_type;
  int64 limit_num;

  MerchantQuotaConfig() {
    recall_assort = 0;
    limit_type = -1;
    limit_num = -1;
  }

  MerchantQuotaConfig(int64 recall_assort, int64 limit_type, int64 limit_num) {
    recall_assort = recall_assort;
    limit_type = limit_type;
    limit_num = limit_num;
  }
};
class MerchantMergeWithPriorityArranger : public CommonRecoBaseArranger {
 public:
  MerchantMergeWithPriorityArranger() {}

  RecoResultIter Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                         RecoResultIter end) override;

 private:
  bool InitProcessor() override;
  bool IsDebug() const {
    return debug_;
  }
  bool ParseRecallPriorityQuota(MutableRecoContextInterface *context);
  void RecallAssortQuotaControl(const MerchantQuotaConfig &config,
          const std::unordered_set<int64>& reason_list,
          std::unordered_map<int64, std::vector<RecoResultIter>>* result_map,
          std::unordered_map<int64, int64>* reason_quota_map);

 private:
  bool debug_{false};
  bool enable_dedup_{true};
  int64 merchant_goods_recall_fair_reduce_type_;
  int64 recall_merge_cut_num_;
  std::vector<MerchantMergeConfig> quotas_;
  std::vector<CommonRecoResult> mix_results_;
  std::string item_id_str_attr_name_;
  std::string item_source_attr_name_;
  std::string item_multi_sources_attr_name_;
  std::string recall_cut_str_attr_name_;
  std::string retrieve_kconf_path_;
  std::string retrieve_kconf_gray_attr_;
  std::string log_id_ = "";
  std::unordered_map<int64, MerchantQuotaConfig> recall_assort_config_map_;
};

}  // namespace platform
}  // namespace ks
