#include "dragon/src/processor/ext/user_reco/retriever/user_reco_remote_retriever2.h"
#include <utility>
#include "folly/container/F14Map.h"
#include "ks/base/abtest/abtest_instance.h"
#include "base/encoding/base64.h"

namespace ks {
namespace platform {

void UserRecoRemote2Retriever::Retrieve(AddibleRecoContextInterface *context) {
  uint64 user_id = context->GetUserId();
  std::string request_info = "kess_service:" + kess_service_ + std::to_string(user_id);
  InitRelationInfoRequest(context);
  if (need_field_num_list_.size() <= 0) {return;}
  auto pr = relation_api_trigger_client_.AsyncGetUserRelationInfo(kess_service_, timeout_ms_,
                                                                  relation_info_request_, nullptr);
  if (!pr.first) {
    CL_LOG_ERROR_EVERY("UserRecoRemoteRetriever2", "send_req_fail", 100)
      << request_info << RecoUtil::GetRequestInfoForLog(context);
    return;
  }
  auto callback = [this, context, request_info](RelationInfoResponse *response) {
    if (response->status() != ResponseStatus::OK) {return;}
    const RelationInfo &relation_info = response->relation_info();
    std::vector<CommonRecoRetrieveResult> retrieve_items;  // 存召回结果
    retrieve_items.reserve(500);
    folly::F14FastMap<int64, double> relation_score_map;  // 亲密分 map
    relation_score_map.clear();
    AddRetrieveItems(context, relation_info, &retrieve_items, &relation_score_map);
    std::vector<CommonRecoResult> item_results;
    item_results.reserve(500);
    AddToRecoResults(context, retrieve_items, &item_results);  // 写入召回 items
    int64 item_results_index = 0;
    auto *item_type_accessor = context->GetItemAttrAccessor("item_type");
    auto *is_in_new_bifollow_new_bifollow_list
     = context->GetItemAttrAccessor("is_in_new_bifollow_new_bifollow_list");
    auto *is_in_new_bifollow_interact_list
     = context->GetItemAttrAccessor("is_in_new_bifollow_interact_list");
    auto *is_in_new_bifollow_fans_list
     = context->GetItemAttrAccessor("is_in_new_bifollow_fans_list");
    auto *is_in_new_bifollow_follow_list
     = context->GetItemAttrAccessor("is_in_new_bifollow_follow_list");
    auto *is_in_new_interact_new_bifollow_list
     = context->GetItemAttrAccessor("is_in_new_interact_new_bifollow_list");
    auto *is_in_new_interact_new_interact_list
     = context->GetItemAttrAccessor("is_in_new_interact_new_interact_list");
    auto *is_in_new_interact_fans_list
     = context->GetItemAttrAccessor("is_in_new_interact_fans_list");
    auto *is_in_new_interact_follow_list
     = context->GetItemAttrAccessor("is_in_new_interact_follow_list");
    auto *is_in_wifi_aoi_list
     = context->GetItemAttrAccessor("is_in_wifi_aoi_list");
    auto *is_in_follow_follow_push
     = context->GetItemAttrAccessor("is_in_follow_follow_push_list");
    auto *is_in_follow_fans_push
     = context->GetItemAttrAccessor("is_in_follow_fans_push_list");
    auto *is_in_follow_friend_push
     = context->GetItemAttrAccessor("is_in_follow_friend_push_list");
    auto *follow_follow_push_common_size =
      context->GetItemAttrAccessor("follow_follow_push_common_size");
    auto *follow_fans_push_common_size =
      context->GetItemAttrAccessor("follow_fans_push_common_size");
    auto *follow_friend_push_common_size =
      context->GetItemAttrAccessor("follow_friend_push_common_size");
    auto *is_in_recent_be_share_list_accessor =
      context->GetItemAttrAccessor("is_in_recent_be_share_list");
    auto *author_recent_be_share_timestamp =
      context->GetItemAttrAccessor("author_recent_be_share_timestamp");
    auto *author_recent_shared_timestamp =
      context->GetItemAttrAccessor("author_recent_shared_timestamp");
    auto *is_in_recent_shared_list_accessor =
      context->GetItemAttrAccessor("is_in_recent_shared_list");
    auto * is_in_pymk_show_show_list_accessor =
      context->GetItemAttrAccessor("is_in_pymk_show_show_list");
    auto * is_in_pymk_show_follow_list_accessor =
      context->GetItemAttrAccessor("is_in_pymk_show_follow_list");
    auto * is_in_pymk_show_friend_list_accessor =
      context->GetItemAttrAccessor("is_in_pymk_show_friend_list");
    auto * is_in_pymk_show_fans_list_accessor =
      context->GetItemAttrAccessor("is_in_pymk_show_fans_list");
    auto * is_in_pymk_be_showed_show_list_accessor =
      context->GetItemAttrAccessor("is_in_pymk_be_showed_show_list");
    auto * is_in_pymk_be_showed_follow_list_accessor =
      context->GetItemAttrAccessor("is_in_pymk_be_showed_follow_list");
    auto * is_in_pymk_be_showed_friend_list_accessor =
      context->GetItemAttrAccessor("is_in_pymk_be_showed_friend_list");
    auto * is_in_pymk_be_showed_fans_list_accessor =
      context->GetItemAttrAccessor("is_in_pymk_be_showed_fans_list");
    auto * is_in_comment_bidirection_list_accessor =
      context->GetItemAttrAccessor("is_in_comment_bidirection_list");
    auto * is_in_message_bidirection_list_accessor =
      context->GetItemAttrAccessor("is_in_message_bidirection_list");
    auto * is_in_high_interact_bidirection_list_accessor =
      context->GetItemAttrAccessor("is_in_high_interact_bidirection_list");
    auto * is_in_single_high_interact_list_accessor =
      context->GetItemAttrAccessor("is_in_single_high_interact_list");
    auto * is_in_reverse_single_high_interact_list_accessor =
      context->GetItemAttrAccessor("is_in_reverse_single_high_interact_list");
    auto * is_in_low_interact_bidirection_list_accessor =
      context->GetItemAttrAccessor("is_in_low_interact_bidirection_list");
    auto * is_in_pymk_mix_relation_all_list_accessor =
      context->GetItemAttrAccessor("is_in_pymk_mix_relation_all_list");
    auto * is_in_pymk_mix_relation_fans_list_accessor =
      context->GetItemAttrAccessor("is_in_pymk_mix_relation_fans_list");
    auto * is_in_message_message_list_accessor =
      context->GetItemAttrAccessor("is_in_message_message_list_accessor");
    auto * is_in_message_follow_list_accessor =
      context->GetItemAttrAccessor("is_in_message_follow_list_accessor");
    auto * is_in_message_friend_list_accessor =
      context->GetItemAttrAccessor("is_in_message_friend_list_accessor");
    auto * is_in_message_fans_list_accessor =
      context->GetItemAttrAccessor("is_in_message_fans_list_accessor");
    auto * is_in_pymk_simple_fusion_graph_list_accessor =
      context->GetItemAttrAccessor("is_in_pymk_simple_fusion_graph_list_accessor");
    auto * is_in_pymk_show_show_interact_list_accessor =
      context->GetItemAttrAccessor("is_in_pymk_show_show_interact_list_accessor");
    auto * is_in_pymk_show_fans_interact_list_accessor =
      context->GetItemAttrAccessor("is_in_pymk_show_fans_interact_list_accessor");
    auto * is_in_pymk_fof_interact_list_accessor =
      context->GetItemAttrAccessor("is_in_pymk_fof_interact_list_accessor");
    auto * is_in_pymk_low_fof_interact_list_accessor =
      context->GetItemAttrAccessor("is_in_pymk_low_fof_interact_list_accessor");
    auto * is_in_pymk_merge_relation_graph_accessor =
      context->GetItemAttrAccessor("is_in_pymk_merge_relation_graph_accessor");

    for (auto need_field_num : need_field_num_list_) {
      if (item_results_index >= item_results.size()) {
        ks::infra::PerfUtil::CountLogStashAndLocal(1, "UserRecoRemoteRetriever2", "error.index.qps");
        break;}
      if (need_field_num == 156) {
        for (const auto &relation_unit : relation_info.new_bifollow_new_bifollow_list()) {
          context->SetIntItemAttr(item_results[item_results_index], item_type_accessor, 1);
          context->SetIntItemAttr(item_results[item_results_index], is_in_new_bifollow_new_bifollow_list, 1);
          item_results_index++;
        }
      } else if (need_field_num == 157) {
        for (const auto &relation_unit : relation_info.new_bifollow_interact_list()) {
          context->SetIntItemAttr(item_results[item_results_index], item_type_accessor, 1);
          context->SetIntItemAttr(item_results[item_results_index], is_in_new_bifollow_interact_list, 1);
          item_results_index++;
        }
      } else if (need_field_num == 158) {
        for (const auto &relation_unit : relation_info.new_bifollow_fans_list()) {
          context->SetIntItemAttr(item_results[item_results_index], item_type_accessor, 1);
          context->SetIntItemAttr(item_results[item_results_index], is_in_new_bifollow_fans_list, 1);
          item_results_index++;
        }
      } else if (need_field_num == 159) {
        for (const auto &relation_unit : relation_info.new_bifollow_follow_list()) {
          context->SetIntItemAttr(item_results[item_results_index], item_type_accessor, 1);
          context->SetIntItemAttr(item_results[item_results_index], is_in_new_bifollow_follow_list, 1);
          item_results_index++;
        }
      } else if (need_field_num == 160) {
        for (const auto &relation_unit : relation_info.new_interact_new_bifollow_list()) {
          context->SetIntItemAttr(item_results[item_results_index], item_type_accessor, 1);
          context->SetIntItemAttr(item_results[item_results_index], is_in_new_interact_new_bifollow_list, 1);
          item_results_index++;
        }
      } else if (need_field_num == 161) {
        for (const auto &relation_unit : relation_info.new_interact_new_interact_list()) {
          context->SetIntItemAttr(item_results[item_results_index], item_type_accessor, 1);
          context->SetIntItemAttr(item_results[item_results_index], is_in_new_interact_new_interact_list, 1);
          item_results_index++;
        }
      } else if (need_field_num == 162) {
        for (const auto &relation_unit : relation_info.new_interact_fans_list()) {
          context->SetIntItemAttr(item_results[item_results_index], item_type_accessor, 1);
          context->SetIntItemAttr(item_results[item_results_index], is_in_new_interact_fans_list, 1);
          item_results_index++;
        }
      } else if (need_field_num == 163) {
        for (const auto &relation_unit : relation_info.new_interact_follow_list()) {
          context->SetIntItemAttr(item_results[item_results_index], item_type_accessor, 1);
          context->SetIntItemAttr(item_results[item_results_index], is_in_new_interact_follow_list, 1);
          item_results_index++;
        }
      } else if (need_field_num == 165) {
        for (const auto &relation_unit : relation_info.wifi_aoi_list()) {
          context->SetIntItemAttr(item_results[item_results_index], item_type_accessor, 1);
          context->SetIntItemAttr(item_results[item_results_index], is_in_wifi_aoi_list, 1);
          item_results_index++;
        }
      } else if (need_field_num == 172) {
        for (const auto &relation_unit : relation_info.follow_follow_list_push()) {
          context->SetIntItemAttr(item_results[item_results_index], item_type_accessor, 1);
          context->SetIntItemAttr(item_results[item_results_index], is_in_follow_follow_push, 1);
          context->SetIntItemAttr(item_results[item_results_index], follow_follow_push_common_size,
            relation_unit.common_ids_size());
          item_results_index++;
        }
      } else if (need_field_num == 173) {
        for (const auto &relation_unit : relation_info.follow_fans_list_push()) {
          context->SetIntItemAttr(item_results[item_results_index], item_type_accessor, 1);
          context->SetIntItemAttr(item_results[item_results_index], is_in_follow_fans_push, 1);
          context->SetIntItemAttr(item_results[item_results_index], follow_fans_push_common_size,
            relation_unit.common_ids_size());
          item_results_index++;
        }
      } else if (need_field_num == 174) {
        for (const auto &relation_unit : relation_info.follow_friend_list_push()) {
          context->SetIntItemAttr(item_results[item_results_index], item_type_accessor, 1);
          context->SetIntItemAttr(item_results[item_results_index], is_in_follow_friend_push, 1);
          context->SetIntItemAttr(item_results[item_results_index], follow_friend_push_common_size,
            relation_unit.common_ids_size());
          item_results_index++;
        }
      } else if (need_field_num == 134) {
        for (const auto &relation_unit : relation_info.recent_be_share_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_recent_be_share_list_accessor, 1);
          context->SetIntCommonAttr("recent_be_share_timestamp", relation_unit.timestamp());
          context->SetIntItemAttr(result, author_recent_be_share_timestamp, relation_unit.timestamp());}
      } else if (need_field_num == 135) {
        for (const auto &relation_unit : relation_info.recent_shared_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_recent_shared_list_accessor, 1);
          context->SetIntCommonAttr("recent_shared_timestamp", relation_unit.timestamp());
          context->SetIntItemAttr(result, author_recent_shared_timestamp, relation_unit.timestamp());}
      } else if (need_field_num == 175) {
        for (const auto &relation_unit : relation_info.pymk_show_show_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_pymk_show_show_list_accessor, 1);}
      } else if (need_field_num == 176) {
        for (const auto &relation_unit : relation_info.pymk_show_follow_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_pymk_show_follow_list_accessor, 1);}
      } else if (need_field_num == 177) {
        for (const auto &relation_unit : relation_info.pymk_show_friend_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_pymk_show_friend_list_accessor, 1);}
      } else if (need_field_num == 178) {
        for (const auto &relation_unit : relation_info.pymk_show_fans_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_pymk_show_fans_list_accessor, 1);}
      }  else if (need_field_num == 179) {
        for (const auto &relation_unit : relation_info.pymk_be_showed_show_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_pymk_be_showed_show_list_accessor, 1);}
      } else if (need_field_num == 180) {
        for (const auto &relation_unit : relation_info.pymk_be_showed_follow_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_pymk_be_showed_follow_list_accessor, 1);}
      } else if (need_field_num == 181) {
        for (const auto &relation_unit : relation_info.pymk_be_showed_friend_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_pymk_be_showed_friend_list_accessor, 1);}
      } else if (need_field_num == 182) {
        for (const auto &relation_unit : relation_info.pymk_be_showed_fans_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_pymk_be_showed_fans_list_accessor, 1);}
      } else if (need_field_num == 185) {
        for (const auto &relation_unit : relation_info.comment_bidirection_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_comment_bidirection_list_accessor, 1);}
      } else if (need_field_num == 186) {
        for (const auto &relation_unit : relation_info.message_bidirection_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_message_bidirection_list_accessor, 1);}
      } else if (need_field_num == 187) {
        for (const auto &relation_unit : relation_info.high_interact_bidirection_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_high_interact_bidirection_list_accessor, 1);}
      } else if (need_field_num == 188) {
        for (const auto &relation_unit : relation_info.single_high_interact_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_single_high_interact_list_accessor, 1);}
      } else if (need_field_num == 189) {
        for (const auto &relation_unit : relation_info.reverse_single_high_interact_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_reverse_single_high_interact_list_accessor, 1);}
      } else if (need_field_num == 190) {
        for (const auto &relation_unit : relation_info.low_interact_bidirection_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_low_interact_bidirection_list_accessor, 1);}
      } else if (need_field_num == 195) {
        for (const auto &relation_unit : relation_info.pymk_mix_relation_all_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_pymk_mix_relation_all_list_accessor, 1);}
      } else if (need_field_num == 196) {
        for (const auto &relation_unit : relation_info.pymk_mix_relation_fans_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_pymk_mix_relation_fans_list_accessor, 1);}
      } else if (need_field_num == 197) {
        for (const auto &relation_unit : relation_info.message_message_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_message_message_list_accessor, 1);}
      } else if (need_field_num == 198) {
        for (const auto &relation_unit : relation_info.message_follow_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_message_follow_list_accessor, 1);}
      } else if (need_field_num == 199) {
        for (const auto &relation_unit : relation_info.message_friend_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_message_friend_list_accessor, 1);}
      } else if (need_field_num == 200) {
        for (const auto &relation_unit : relation_info.message_fans_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_message_fans_list_accessor, 1);}
      } else if (need_field_num == 201) {
        for (const auto &relation_unit : relation_info.pymk_simple_fusion_graph_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_pymk_simple_fusion_graph_list_accessor, 1);}
      } else if (need_field_num == 202) {
        for (const auto &relation_unit : relation_info.pymk_show_show_interact_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_pymk_show_show_interact_list_accessor, 1);}
      } else if (need_field_num == 203) {
        for (const auto &relation_unit : relation_info.pymk_show_fans_interact_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_pymk_show_fans_interact_list_accessor, 1);}
      } else if (need_field_num == 204) {
        for (const auto &relation_unit : relation_info.pymk_fof_interact_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_pymk_fof_interact_list_accessor, 1);}
      } else if (need_field_num == 205) {
        for (const auto &relation_unit : relation_info.pymk_low_fof_interact_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_pymk_low_fof_interact_list_accessor, 1);}
      } else if (need_field_num == 206) {
        for (const auto &relation_unit : relation_info.pymk_merge_relation_graph_list()) {
          const auto &result = item_results[item_results_index];
          item_results_index++;
          context->SetIntItemAttr(result, item_type_accessor, 1);  // item 类型，1 表示用户
          context->SetIntItemAttr(result, is_in_pymk_merge_relation_graph_accessor, 1);}
      }
    }
  };
  // 注册 callback 函数
  RegisterAsyncCallback(context, std::move(pr.second), std::move(callback), request_info);
}

void UserRecoRemote2Retriever::AddRetrieveItems(AddibleRecoContextInterface *context,
                                               const RelationInfo &relation_info,
                                               std::vector<CommonRecoRetrieveResult> *retrieve_items,
                                               folly::F14FastMap<int64, double> *relation_score_map) {
    for (auto need_field_num : need_field_num_list_) {
      if (need_field_num == 156) {
        for (const auto &relation_unit : relation_info.new_bifollow_new_bifollow_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 157) {
        for (const auto &relation_unit : relation_info.new_bifollow_interact_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 158) {
        for (const auto &relation_unit : relation_info.new_bifollow_fans_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 159) {
        for (const auto &relation_unit : relation_info.new_bifollow_follow_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 160) {
        for (const auto &relation_unit : relation_info.new_interact_new_bifollow_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 161) {
        for (const auto &relation_unit : relation_info.new_interact_new_interact_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 162) {
        for (const auto &relation_unit : relation_info.new_interact_fans_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 163) {
        for (const auto &relation_unit : relation_info.new_interact_follow_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 165) {
        for (const auto &relation_unit : relation_info.wifi_aoi_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 172) {
        for (const auto &relation_unit : relation_info.follow_follow_list_push()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 173) {
        for (const auto &relation_unit : relation_info.follow_fans_list_push()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 174) {
        for (const auto &relation_unit : relation_info.follow_friend_list_push()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 134) {
        for (const auto &relation_unit : relation_info.recent_be_share_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 135) {
        for (const auto &relation_unit : relation_info.recent_shared_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 175) {
        for (const auto &relation_unit : relation_info.pymk_show_show_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 176) {
        for (const auto &relation_unit : relation_info.pymk_show_follow_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 177) {
        for (const auto &relation_unit : relation_info.pymk_show_friend_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 178) {
        for (const auto &relation_unit : relation_info.pymk_show_fans_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 179) {
        for (const auto &relation_unit : relation_info.pymk_be_showed_show_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 180) {
        for (const auto &relation_unit : relation_info.pymk_be_showed_follow_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 181) {
        for (const auto &relation_unit : relation_info.pymk_be_showed_friend_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 182) {
        for (const auto &relation_unit : relation_info.pymk_be_showed_fans_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 185) {
        for (const auto &relation_unit : relation_info.comment_bidirection_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 186) {
        for (const auto &relation_unit : relation_info.message_bidirection_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 187) {
        for (const auto &relation_unit : relation_info.high_interact_bidirection_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 188) {
        for (const auto &relation_unit : relation_info.single_high_interact_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 189) {
        for (const auto &relation_unit : relation_info.reverse_single_high_interact_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 190) {
        for (const auto &relation_unit : relation_info.low_interact_bidirection_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 195) {
        for (const auto &relation_unit : relation_info.pymk_mix_relation_all_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 196) {
        for (const auto &relation_unit : relation_info.pymk_mix_relation_fans_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 197) {
        for (const auto &relation_unit : relation_info.message_message_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 198) {
        for (const auto &relation_unit : relation_info.message_follow_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 199) {
        for (const auto &relation_unit : relation_info.message_friend_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 200) {
        for (const auto &relation_unit : relation_info.message_fans_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      }  else if (need_field_num == 201) {
        for (const auto &relation_unit : relation_info.pymk_simple_fusion_graph_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 202) {
        for (const auto &relation_unit : relation_info.pymk_show_show_interact_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 203) {
        for (const auto &relation_unit : relation_info.pymk_show_fans_interact_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 204) {
        for (const auto &relation_unit : relation_info.pymk_fof_interact_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 205) {
        for (const auto &relation_unit : relation_info.pymk_low_fof_interact_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      } else if (need_field_num == 206) {
        for (const auto &relation_unit : relation_info.pymk_merge_relation_graph_list()) {
          (*retrieve_items).emplace_back(relation_unit.user_id(), need_field_num + 1000);
        }
      }
    }
}

void UserRecoRemote2Retriever::InitRelationInfoRequest(AddibleRecoContextInterface *context) {
  relation_info_request_.clear_user_id();
  relation_info_request_.set_user_id(context->GetUserId());
  relation_info_request_.clear_need_field();
  for (auto field : need_field_num_list_) {
    relation_info_request_.add_need_field(field);
  }
  relation_info_request_.clear_limit();
  relation_info_request_.set_limit(remote_recall_limit_);
  relation_info_request_.clear_request_biz_type();
  if (request_biz_type_for_relation_api_ == "USER_RECO_GUIDE_COMMON_LEAF_FOR_LOW_FRIEND") {
    relation_info_request_.set_request_biz_type(RequestBizType::USER_RECO_GUIDE_COMMON_LEAF_FOR_LOW_FRIEND);
  } else if (request_biz_type_for_relation_api_ == "USER_RECO_GUIDE_COMMON_LEAF_FOR_HIGH_FRIEND") {
    relation_info_request_.set_request_biz_type(RequestBizType::USER_RECO_GUIDE_COMMON_LEAF_FOR_HIGH_FRIEND);
  } else {
    relation_info_request_.set_request_biz_type(RequestBizType::INVALIDE_BIZ);
  }

  relation_info_request_.clear_params();
  relation_info_request_.mutable_params()->insert({"llsid", context->GetRequestId()});
  relation_info_request_.mutable_params()->insert({"did", context->GetDeviceId()});
  relation_info_request_.mutable_params()->insert({"request_type", context->GetRequestType()});
  // 添加用户画像
  const auto *user_reco_request_pb =
    context->GetProtoMessagePtrCommonAttr<UserRecoRequest>("user_reco_request_pb");
  if (user_reco_request_pb) {
    std::string encoded_msg;
    std::string visitor_string;
    std::string abtest_mapping_id_string;
    std::string encoded_ab_mapping_id;
    user_reco_request_pb->visitor().SerializeToString(&visitor_string);
    base::Base64Encode(visitor_string.data(), visitor_string.size(), &encoded_msg);
    user_reco_request_pb->abtest_mapping_id().SerializeToString(&abtest_mapping_id_string);
    base::Base64Encode(abtest_mapping_id_string.data(), abtest_mapping_id_string.size(),
                       &encoded_ab_mapping_id);
    relation_info_request_.mutable_params()->insert({"visitor_string", encoded_msg});
    relation_info_request_.mutable_params()->insert({"abtest_mapping_id", encoded_ab_mapping_id});
  }

  auto portal_string = context->GetStringCommonAttr("portal_string");
  if (portal_string) {
    relation_info_request_.mutable_params()->insert({"portal_string", std::string(*portal_string)});
  }
  relation_info_request_.clear_browse_set();

  if (true) {
    auto user_block_list = context->GetIntListCommonAttr("user_block_list");
    if (user_block_list) {
      for (auto &fid : *user_block_list) {
        relation_info_request_.mutable_browse_set()->add_browsed_ids(fid);
      }
    }
    auto user_profile_follow_list = context->GetIntListCommonAttr("user_profile_follow_list");
    if (user_profile_follow_list) {
      for (auto &fid : *user_profile_follow_list) {
        relation_info_request_.mutable_browse_set()->add_browsed_ids(fid);
      }
    }
    auto user_server_filtered_list = context->GetIntListCommonAttr("user_server_filtered_list");
    if (user_server_filtered_list) {
      for (auto &fid : *user_server_filtered_list) {
        relation_info_request_.mutable_browse_set()->add_browsed_ids(fid);
      }
    }
    auto user_hate_author_list = context->GetIntListCommonAttr("user_hate_author_list");
    if (user_hate_author_list) {
      for (auto &fid : *user_hate_author_list) {
        relation_info_request_.mutable_browse_set()->add_browsed_ids(fid);
      }
    }
    int feedback_filter_bad_enable_retrieve_24q2 = 0;
    auto feedback_list_filter_bad = context->GetIntListCommonAttr("feedback_list_filter_bad");
    auto abtest_common_param_accessor1 =
        context->GetIntCommonAttr("feedback_filter_bad_enable_retrieve_2024q2");
    if (abtest_common_param_accessor1) {
      feedback_filter_bad_enable_retrieve_24q2 = *abtest_common_param_accessor1;
    }
    if (feedback_filter_bad_enable_retrieve_24q2 > 0 && feedback_list_filter_bad) {
      for (auto &fid : *feedback_list_filter_bad) {
        relation_info_request_.mutable_browse_set()->add_browsed_ids(fid);
      }
    }

    if (portal_string) {
      if (std::string(*portal_string) == "RECO_PORTAL_SELECTED_DIALOG"
          || std::string(*portal_string) == "RECO_PORTAL_FRIEND_TAB") {
          for (auto &fid : (*(context->GetRequest())).browse_set().browsed_ids()) {
            relation_info_request_.mutable_browse_set()->add_browsed_ids(fid);
          }
      }
    }
    if (context->GetRequestType() == "notify_red_low_friend"
        || context->GetRequestType() == "notify_red_high_friend") {
      auto user_follow_list = context->GetIntListCommonAttr("user_follow_list");
      if (user_follow_list) {
        for (auto &fid : *user_follow_list) {
          relation_info_request_.mutable_browse_set()->add_browsed_ids(fid);
        }
      }
      for (auto &fid : (*(context->GetRequest())).browse_set().browsed_ids()) {
        relation_info_request_.mutable_browse_set()->add_browsed_ids(fid);
      }
    }
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, UserRecoRemote2Retriever, UserRecoRemote2Retriever);
}  // namespace platform
}  // namespace ks
