#pragma once

#include <random>
#include <string>
#include <memory>
#include <vector>
#include <utility>

#include "dragon/src/processor/base/common_reco_base_enricher.h"
#include "dragon/src/processor/ext/explore/common/rerank_candidate_collection.h"

namespace ks {
namespace platform {

class ExploreRerankRandomEnsembleCandidateEnricher : public CommonRecoBaseEnricher {
 public:
  ExploreRerankRandomEnsembleCandidateEnricher() = default;

  bool InitProcessor() override;
  void Enrich(MutableRecoContextInterface *context, RecoResultConstIter begin,
              RecoResultConstIter end) override;

 private:
  struct QueueConfigItem {
    std::string score_attr;
    bool enable = false;
    bool reverse_order = false;
    double weight = 0.0;
    double disturbance_range = 0.0;
    double pow_alpha = 0.0;
    double pow_beta = 0.0;
    int boost_topk_threshold = 0;
    double boost_topk_coef = 0.0;
    const base::Json *enable_config = nullptr;
    const base::Json *weight_config = nullptr;
    const base::Json *pow_alpha_config = nullptr;
    const base::Json *pow_beta_config = nullptr;
    const base::Json *disturbance_range_config = nullptr;
    const base::Json *boost_topk_threshold_config = nullptr;
    const base::Json *boost_topk_coef_config = nullptr;
    const ItemAttr *score_attr_accessor = nullptr;
  };

  class EnsembleAlgorithm {
   public:
    virtual ~EnsembleAlgorithm() = default;
    virtual void InitEnsembleScoreList(std::vector<double> *score_list, const int count) const = 0;
    virtual double AddQueueScore(
        double *ensemble_score, const int index, const double score, const double weight,
        const QueueConfigItem &queue_config) const = 0;
    virtual void BoostTopKScore(
        double *ensemble_score, const int index, const double score, const int boost_topk_threshold,
        const double boost_topk_coef) const = 0;
  };

  class Algorithm1 : public EnsembleAlgorithm {
   public:
    Algorithm1() = default;
    ~Algorithm1() = default;

    void InitEnsembleScoreList(std::vector<double> *score_list, const int count) const override {
      if (!score_list) {
        return;
      }
      score_list->clear();
      score_list->resize(count, 0.0);
    }

    double AddQueueScore(
        double *ensemble_score, const int index, const double score, const double weight,
        const QueueConfigItem &queue_config) const override {
      double raw_score = std::pow(queue_config.pow_alpha * score + 1.0, queue_config.pow_beta);
      double queue_score = weight / (index + 10.0) * raw_score;
      *ensemble_score += queue_score;
      return queue_score;
    }

    void BoostTopKScore(
        double *ensemble_score, const int index, const double score, const int boost_topk_threshold,
        const double boost_topk_coef) const override {
      if (boost_topk_threshold > 0 && index < boost_topk_threshold) {
        *ensemble_score += score * boost_topk_coef;
      }
    }
  };

  class Algorithm2 : public EnsembleAlgorithm {
   public:
    Algorithm2() = default;
    ~Algorithm2() = default;

    void InitEnsembleScoreList(std::vector<double> *score_list, const int count) const override {
      if (!score_list) {
        return;
      }
      score_list->clear();
      score_list->resize(count, 1.0);
    }

    double AddQueueScore(
        double *ensemble_score, const int index, const double score, const double weight,
        const QueueConfigItem &queue_config) const override {
      double raw_score = std::pow(queue_config.pow_alpha * score + 1.0, queue_config.pow_beta);
      double queue_score = std::pow(1.0 / (index + 10.0) * raw_score, weight);
      *ensemble_score *= queue_score;
      return queue_score;
    }

    void BoostTopKScore(
        double *ensemble_score, const int index, const double score, const int boost_topk_threshold,
        const double boost_topk_coef) const override {
      if (boost_topk_threshold > 0 && index < boost_topk_threshold) {
        *ensemble_score *= (1 + boost_topk_coef);
      }
    }
  };

  static const std::vector<std::shared_ptr<const EnsembleAlgorithm>> ALGORITHM_LIST;
  static constexpr double EPS = 1e-9;

  static inline const auto descending_cmp_func_ =
      [](const std::pair<int, double>& left, const std::pair<int, double>& right) {
    return left.second > right.second;
  };
  static inline const auto increasing_cmp_func_ =
      [](const std::pair<int, double> &left, const std::pair<int, double> &right) {
    return left.second < right.second;
  };

  void GenRandomEnsembleCandidates(
    MutableRecoContextInterface *context,
    RecoResultConstIter begin,
    RecoResultConstIter end,
    const int target_candidate_num);

  void GetSortedPairLists(
    std::vector<std::vector<std::pair<int, double>>>* es_pair_lists,
    const int item_count, const int beam_size);

  void GenTopKShuffleListsBasedOnEnsembleCandidates(
    MutableRecoContextInterface *context,
    const int item_count,
    const int beam_size);

  void GenTopKResortedListsBasedOnEnsembleCandidates(
    MutableRecoContextInterface *context,
    RecoResultConstIter begin,
    RecoResultConstIter end,
    const int item_count,
    const int beam_size);

  std::mt19937 engine_{std::random_device()()};
  std::uniform_real_distribution<double> rand_{ 0.0, 1.0 };

  std::string source_;
  std::string candidate_attr_;
  std::vector<QueueConfigItem> queue_config_list_;
  explore::RerankCandidateCollection candidate_collection_;
  std::shared_ptr<const EnsembleAlgorithm> ensemble_algorithm_;
  std::string topk_resort_score_attr_;
  std::default_random_engine random_engine_;

  DISALLOW_COPY_AND_ASSIGN(ExploreRerankRandomEnsembleCandidateEnricher);
};

}  // namespace platform
}  // namespace ks
