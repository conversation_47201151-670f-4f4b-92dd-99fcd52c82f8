#include "dragon/src/processor/ext/explore/enricher/explore_rerank_random_ensemble_candidate_enricher.h"

#include <algorithm>
#include <memory>

namespace ks {
namespace platform {

const std::vector<std::shared_ptr<const ExploreRerankRandomEnsembleCandidateEnricher::EnsembleAlgorithm>>
    ExploreRerankRandomEnsembleCandidateEnricher::ALGORITHM_LIST = {
  std::make_shared<const Algorithm1>(),
  std::make_shared<const Algorithm2>(),
};

bool ExploreRerankRandomEnsembleCandidateEnricher::InitProcessor() {
  source_ = config()->GetString("source");
  if (source_.empty()) {
    return false;
  }
  const auto *queues = config()->Get("queues");
  if (queues && queues->IsArray() && !queues->array().empty()) {
    for (const auto *item : queues->array()) {
      if (item && item->IsObject()) {
        QueueConfigItem config_item;
        config_item.score_attr = item->GetString("score_attr");
        config_item.enable_config = item->Get("enable");
        config_item.reverse_order = item->GetBoolean("reverse_order", false);
        config_item.weight_config = item->Get("weight");
        config_item.disturbance_range_config = item->Get("disturbance_range");
        config_item.pow_alpha_config = item->Get("pow_alpha");
        config_item.pow_beta_config = item->Get("pow_beta");
        config_item.boost_topk_threshold_config = item->Get("boost_topk_threshold");
        config_item.boost_topk_coef_config = item->Get("boost_topk_coef");
        queue_config_list_.emplace_back(std::move(config_item));
      }
    }
  } else {
    return false;
  }

  candidate_attr_ = config()->GetString("save_candidate_to_attr");
  topk_resort_score_attr_ = config()->GetString("topk_resort_score_attr");

  random_engine_ = std::default_random_engine(base::GetTimestamp());

  return true;
}

void ExploreRerankRandomEnsembleCandidateEnricher::Enrich(
    MutableRecoContextInterface *context, RecoResultConstIter begin, RecoResultConstIter end) {
  int item_count = std::distance(begin, end);
  if (item_count == 0) {
    return;
  }

  int ensemble_algorithm_index = GetIntProcessorParameter(context, "ensemble_algorithm", 0);
  if (ensemble_algorithm_index == 0 || ensemble_algorithm_index > ALGORITHM_LIST.size()) {
    return;
  }

  int target_candidate_num = GetIntProcessorParameter(context, "target_candidate_num", 0);

  ensemble_algorithm_ = ALGORITHM_LIST[ensemble_algorithm_index - 1];

  candidate_collection_.Clear();
  candidate_collection_.SetSource(source_);
  candidate_collection_.Reserve(target_candidate_num);

  for (auto &config_item : queue_config_list_) {
    config_item.enable = GetBoolProcessorParameter(context, config_item.enable_config, false);
    config_item.weight = GetDoubleProcessorParameter(context, config_item.weight_config, 0.0);
    config_item.disturbance_range =
        GetDoubleProcessorParameter(context, config_item.disturbance_range_config, 0.0);
    config_item.pow_alpha = GetDoubleProcessorParameter(context, config_item.pow_alpha_config, 0.0);
    config_item.pow_beta = GetDoubleProcessorParameter(context, config_item.pow_beta_config, 0.0);
    config_item.boost_topk_threshold =
        GetIntProcessorParameter(context, config_item.boost_topk_threshold_config, 0);
    config_item.boost_topk_coef =
        GetDoubleProcessorParameter(context, config_item.boost_topk_coef_config, 0.0);
  }

  GenRandomEnsembleCandidates(context, begin, end, target_candidate_num);


  bool enable_topk_shuffle = GetBoolProcessorParameter(context, "enable_topk_shuffle", false);
  bool enable_topk_resort = GetBoolProcessorParameter(context, "enable_topk_resort", false);
  if (enable_topk_shuffle) {
    int topk_shuffle_target_candidate_num = GetIntProcessorParameter(context,
        "topk_shuffle_target_candidate_num", 1);
    topk_shuffle_target_candidate_num = std::min(target_candidate_num, topk_shuffle_target_candidate_num);
    GenTopKShuffleListsBasedOnEnsembleCandidates(context, item_count, topk_shuffle_target_candidate_num);
  }
  if (enable_topk_resort) {
    int topk_resort_target_candidate_num = GetIntProcessorParameter(context,
        "topk_resort_target_candidate_num", 1);
    topk_resort_target_candidate_num = std::min(target_candidate_num, topk_resort_target_candidate_num);
    GenTopKResortedListsBasedOnEnsembleCandidates(context, begin, end, item_count,
        topk_resort_target_candidate_num);
  }

  context->SetPtrCommonAttr(candidate_attr_, &candidate_collection_);
}

void ExploreRerankRandomEnsembleCandidateEnricher::GenRandomEnsembleCandidates(
    MutableRecoContextInterface *context,
    RecoResultConstIter begin,
    RecoResultConstIter end,
    const int target_candidate_num) {
  int item_count = std::distance(begin, end);
  std::vector<std::pair<int, double>> score_pair_list;
  score_pair_list.reserve(item_count);

  std::vector<std::vector<double>> target_score_list;
  target_score_list.reserve(target_candidate_num);
  for (int i = 0; i < target_candidate_num; ++i) {
    target_score_list.emplace_back();
    ensemble_algorithm_->InitEnsembleScoreList(&target_score_list.back(), item_count);
  }

  for (const auto &queue_config : queue_config_list_) {
    if (!queue_config.enable) {
      continue;
    }
    auto *score_attr_accessor = context->GetItemAttr(queue_config.score_attr);
    if (!score_attr_accessor) {
      continue;
    }

    score_pair_list.clear();
    int index = 0;
    for (auto iter = begin; iter != end; ++iter) {
      score_pair_list.emplace_back(
          index, context->GetDoubleItemAttr(*iter, score_attr_accessor).value_or(0.0));
      ++index;
    }

    const auto cmp_func = queue_config.reverse_order ? increasing_cmp_func_ : descending_cmp_func_;
    std::sort(score_pair_list.begin(), score_pair_list.end(), cmp_func);

    double min_weight = std::max(queue_config.weight - queue_config.disturbance_range, 0.0);
    double max_weight = std::max(queue_config.weight + queue_config.disturbance_range, 0.0);
    for (int j = 0; j < target_candidate_num; ++j) {
      double weight = rand_(engine_) * (max_weight - min_weight) + min_weight;
      int rank;
      for (int i = 0; i < score_pair_list.size(); ++i) {
        rank = i == 0 || std::abs(score_pair_list[i].second - score_pair_list[i - 1].second) > EPS
            ? i + 1 : rank;
        double score = ensemble_algorithm_->AddQueueScore(
          &target_score_list[j][score_pair_list[i].first], rank,
          score_pair_list[i].second, weight, queue_config);
        ensemble_algorithm_->BoostTopKScore(&target_score_list[j][score_pair_list[i].first], rank,
            score, queue_config.boost_topk_threshold, queue_config.boost_topk_coef);
      }
    }
  }

  for (int j = 0; j < target_candidate_num; ++j) {
    score_pair_list.clear();
    for (int i = 0; i < item_count; ++i) {
      score_pair_list.emplace_back(i, target_score_list[j][i]);
    }
    std::sort(score_pair_list.begin(), score_pair_list.end(), descending_cmp_func_);
    for (int i = 0; i < item_count; ++i) {
      target_score_list[j][score_pair_list[i].first] = 1.0 - (1.0 + i) / item_count + 0.001;
    }
    candidate_collection_.AddScoreVec(std::move(target_score_list[j]));
  }
}

void ExploreRerankRandomEnsembleCandidateEnricher::GenTopKShuffleListsBasedOnEnsembleCandidates(
    MutableRecoContextInterface *context,
    const int item_count,
    const int beam_size) {
  // 先存一份 sort 后的结果,  后面可能会修改值
  std::vector<std::vector<std::pair<int, double>>> es_pair_lists;
  es_pair_lists.reserve(beam_size);
  GetSortedPairLists(&es_pair_lists, item_count, beam_size);
  if (es_pair_lists.size() != beam_size) {
    return;
  }

  int topk_shuffle_window_size = GetIntProcessorParameter(context, "topk_shuffle_window_size", 10);
  if (topk_shuffle_window_size > item_count) {
    return;
  }
  std::vector<std::vector<double>> topk_shuffle_score_list(beam_size,
      std::vector<double>(item_count, 0.0));
  for (int j = 0; j < beam_size; ++j) {
    std::vector<int> topk_indices;
    topk_indices.reserve(topk_shuffle_window_size);
    for (int i = 0; i < topk_shuffle_window_size; ++i) {
      topk_indices.emplace_back(es_pair_lists[j][i].first);  // topk item indices
    }
    std::shuffle(topk_indices.begin(), topk_indices.end(), random_engine_);
    for (int i = 0; i < topk_shuffle_window_size; ++i) {
      es_pair_lists[j][i].first = topk_indices[i];  // only change topk indices
    }
    for (int i = 0; i < item_count; ++i) {
      topk_shuffle_score_list[j][es_pair_lists[j][i].first] = 1.0 - (1.0 + i) / item_count + 0.001;
    }
    candidate_collection_.AddScoreVec(std::move(topk_shuffle_score_list[j]));
  }
}

void ExploreRerankRandomEnsembleCandidateEnricher::GenTopKResortedListsBasedOnEnsembleCandidates(
    MutableRecoContextInterface *context,
    RecoResultConstIter begin,
    RecoResultConstIter end,
    const int item_count,
    const int beam_size) {
  // 先存一份 sort 后的结果,  后面可能会修改值
  std::vector<std::vector<std::pair<int, double>>> es_pair_lists;
  es_pair_lists.reserve(beam_size);
  GetSortedPairLists(&es_pair_lists, item_count, beam_size);
  if (es_pair_lists.size() != beam_size) {
    return;
  }

  int topk_resort_window_size = GetIntProcessorParameter(context, "topk_resort_window_size", 10);
  auto *score_attr_accessor = context->GetItemAttr(topk_resort_score_attr_);
  if (!score_attr_accessor || topk_resort_window_size > item_count) {
    return;
  }
  std::vector<std::pair<int, double>> resort_score_pair_list;
  resort_score_pair_list.reserve(item_count);
  int index = 0;
  for (auto iter = begin; iter != end; ++iter) {
    resort_score_pair_list.emplace_back(
        index, context->GetDoubleItemAttr(*iter, score_attr_accessor).value_or(0.0));
    ++index;
  }
  std::vector<std::vector<double>> topk_resort_score_list(beam_size,
      std::vector<double>(item_count, 0.0));
  for (int j = 0; j < beam_size; ++j) {
    std::vector<std::pair<int, double>> topk_pairs(
        es_pair_lists[j].begin(),
        es_pair_lists[j].begin() + topk_resort_window_size);  // copy topk pairs to be sorted
    for (int i = 0; i < topk_pairs.size(); ++i) {  // topk use resort_score
      topk_pairs[i].second = resort_score_pair_list[topk_pairs[i].first].second;
    }
    std::sort(topk_pairs.begin(), topk_pairs.end(), descending_cmp_func_);  // topk resort
    for (int i = 0; i < item_count; ++i) {
      if (i < topk_pairs.size()) {
        topk_resort_score_list[j][topk_pairs[i].first] =
            1.0 - (1.0 + i) / item_count + 0.001;  // topk index
      } else {
        topk_resort_score_list[j][es_pair_lists[j][i].first] =
            1.0 - (1.0 + i) / item_count + 0.001;  // rest
      }
    }
    candidate_collection_.AddScoreVec(std::move(topk_resort_score_list[j]));
  }
}

void ExploreRerankRandomEnsembleCandidateEnricher::GetSortedPairLists(
    std::vector<std::vector<std::pair<int, double>>>* es_pair_lists,
    const int item_count, const int beam_size) {
  const auto &score_vec_list = candidate_collection_.GetScoreVecList();
  if (beam_size > score_vec_list.size() || !es_pair_lists) {
    return;
  }
  std::vector<std::pair<int, double>> score_pair_list;
  score_pair_list.reserve(item_count);
  for (int j = 0; j < beam_size; ++j) {
    score_pair_list.clear();
    for (int i = 0; i < item_count; ++i) {
      score_pair_list.emplace_back(i, score_vec_list[j][i]);
    }
    std::sort(score_pair_list.begin(), score_pair_list.end(), descending_cmp_func_);
    es_pair_lists->emplace_back(std::move(score_pair_list));
  }
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, ExploreRerankRandomEnsembleCandidateEnricher,
    ExploreRerankRandomEnsembleCandidateEnricher);

}  // namespace platform
}  // namespace ks
