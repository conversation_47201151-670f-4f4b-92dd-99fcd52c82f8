#include "dragon/src/processor/ext/follow_leaf/arranger/follow_leaf_filter_arranger.h"

#include <algorithm>
#include <map>
#include <utility>

#include "ks/base/perfutil/perfutil_wrapper.h"

#include "dragon/src/processor/ext/follow_leaf/arranger/common_photo_info.h"
#include "dragon/src/processor/ext/follow_leaf/context/context.h"

namespace ks {
namespace platform {

RecoResultIter FollowLeafFilterArranger::Arrange(MutableRecoContextInterface *context, RecoResultIter begin,
                                                 RecoResultIter end) {
  if (item_accessor_map_.empty()) {
    for (const auto &pair : item_attr_map_config_->objects()) {
      std::string attr = pair.second->StringValue();
      if (!attr.empty()) {
        item_accessor_map_[pair.first] = context->GetItemAttrAccessor(attr);
      }
    }
  }

  for (auto &filter_op : filter_op_vec_) {
    bool enable = GetBoolProcessorParameter(context, filter_op->GetConfig()->Get("enable"));
    if (enable) {
      enable = filter_op->InitContext(context, begin, end, is_check_follow_context_);
    }
    filter_op->SetEnable(enable);
  }

  new_result_vec_.clear();
  // 保存过滤后的 item 用于补齐队列
  std::map<int, std::vector<CommonRecoResult>> filter_item_map;
  std::map<std::string, uint32_t> filter_reason_map;
  follow::CommonPhotoInfo photo_info(item_accessor_map_);
  std::for_each(begin, end, [this, context, &photo_info, &filter_item_map, &filter_reason_map]
      (const CommonRecoResult &result) {
    // 遍历每个 Op 进行过滤
    photo_info.ResetPhoto(context, result);
    for (auto &filter_op : filter_op_vec_) {
      if (filter_op->IsFilter(result, photo_info)) {
        // TODO(liyun) 记录过滤原因
        filter_reason_map[filter_op->name()]++;
        int type = filter_op->GetFilterType();
        context->SetTracebackFilterReason(result, filter_op->name());
        // 只有大于 0 的才进入补齐队列
        if (type > 0) {
          filter_item_map[type].push_back(std::move(result));
        }
        return;
      }
    }
    new_result_vec_.push_back(std::move(result));
  });

  if (DebugLogEnabled()) {
    std::string buffer;
    for (auto &cp : filter_reason_map) {
      buffer += cp.first + ":" + std::to_string(cp.second) + ",";
    }
    LOG(ERROR) << "user_id:" << context->GetUserId() << ",debug_log_filter:" << buffer
               << ",origin size:" << end - begin;
  }

  // Perf
  for (auto &cp : filter_reason_map) {
    base::perfutil::PerfUtilWrapper::IntervalLogStash(cp.second, kPerfNs, "follow.filter",
                                                      GlobalHolder::GetServiceIdentifier(),
                                                      context->GetRequestType(), cp.first);
  }
  CompleteResult(context, filter_item_map);
  return std::move(new_result_vec_.begin(), new_result_vec_.end(), begin);
}

void FollowLeafFilterArranger::CompleteResult(
    MutableRecoContextInterface *context,
    const std::map<int, std::vector<CommonRecoResult>> &filter_item_map) {
  if (low_limit_ <= 0) {
    return;
  }
  int old_size = new_result_vec_.size();
  auto *is_complete_accessor = context->GetItemAttrAccessor("is_complete");
  // 按优先级补齐, 数值越大, 优先级越高
  for (auto it = filter_item_map.rbegin(); it != filter_item_map.rend(); ++it) {
    for (const auto &item : it->second) {
      if (new_result_vec_.size() >= low_limit_) {
        return;
      }
      context->SetIntItemAttr(item, is_complete_accessor, 1);
      new_result_vec_.push_back(item);
    }
  }

  // Perf
  base::perfutil::PerfUtilWrapper::IntervalLogStash(new_result_vec_.size() - old_size, kPerfNs,
                                                    "follow.filter", GlobalHolder::GetServiceIdentifier(),
                                                    context->GetRequestType(), "complete_size");
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, FollowLeafFilterArranger, FollowLeafFilterArranger)

}  // namespace platform
}  // namespace ks
