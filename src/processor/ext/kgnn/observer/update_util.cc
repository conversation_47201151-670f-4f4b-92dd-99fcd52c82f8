#include "dragon/src/processor/ext/kgnn/observer/update_util.h"
#include <string>
#include "dragon/src/core/common_reco_context_interface.h"
#include "infra/btq_client/dynamic_bt_queue_client.h"
#include "serving_base/utility/timer.h"
#include "teams/reco-arch/kgnn/src/proto/gnn_kv_service.pb.h"

namespace ks {
namespace platform {

template <>
void Extractor<int64>::Extract(ReadableRecoContextInterface *context, const std::string &attr_name) {
  item = context->GetIntCommonAttr(attr_name);
  items = context->GetIntListCommonAttr(attr_name);
}

template <>
void Extractor<int64>::Extract(reco_result_iterator iter, ReadableRecoContextInterface *context,
                               const std::string &attr_name) {
  auto accessor = context->GetItemAttrAccessor(attr_name);
  item = iter->GetIntAttr(accessor);
  items = iter->GetIntListAttr(accessor);
}

template <>
void Extractor<double>::Extract(ReadableRecoContextInterface *context, const std::string &attr_name) {
  item = context->GetDoubleCommonAttr(attr_name);
  items = context->GetDoubleListCommonAttr(attr_name);
}

template <>
void Extractor<double>::Extract(reco_result_iterator iter, ReadableRecoContextInterface *context,
                                const std::string &attr_name) {
  auto accessor = context->GetItemAttrAccessor(attr_name);
  item = iter->GetDoubleAttr(accessor);
  items = iter->GetDoubleListAttr(accessor);
}

template struct Extractor<int64>;
template struct Extractor<double>;

bool ExtractCommonSideInfo(SideInfoConfig *side_info_config, ReadableRecoContextInterface *context,
                           std::string *attr_value) {
  attr_value->resize(side_info_config->attr_op_->MaxSize());
  side_info_config->attr_op_->InitialBlock(const_cast<char *>(attr_value->data()),
                                           side_info_config->attr_op_->MaxSize());
  auto int_ext = Extractor<int64>();
  auto float_ext = Extractor<double>();

  int offset = 0;
  // 有一个成功就算成功了，万一提供的字段不存在, 用户最好自己保证存在
  bool has_side_info = false;
  std::vector<int64> int_list_attr_value;
  for (const auto &schema : side_info_config->int64_attr_items_schema_) {
    auto max_size = std::get<2>(schema);
    base::ScopeExit a([&offset, &max_size]() { offset += max_size; });
    // is list
    if (std::get<1>(schema)) {
      if (!int_ext.ExtractFill(context, std::get<0>(schema), &int_list_attr_value, true)) {
        continue;
      }
      has_side_info = true;
      for (int i = 0; i < std::min(int_list_attr_value.size(), max_size); ++i) {
        side_info_config->attr_op_->SetIntX(const_cast<char *>(attr_value->data()), offset + i,
                                            int_list_attr_value.at(i));
      }
    } else {
      if (!int_ext.ExtractFill(context, std::get<0>(schema), &int_list_attr_value, false)) {
        continue;
      }
      has_side_info = true;
      side_info_config->attr_op_->SetIntX(const_cast<char *>(attr_value->data()), offset,
                                          int_list_attr_value.back());
    }
  }

  offset = 0;
  std::vector<float> float_list_attr_value;
  for (const auto &schema : side_info_config->float_attr_items_schema_) {
    auto max_size = std::get<2>(schema);
    base::ScopeExit a([&offset, &max_size]() { offset += max_size; });
    // is list
    if (std::get<1>(schema)) {
      if (!float_ext.ExtractFill(context, std::get<0>(schema), &float_list_attr_value, true)) {
        continue;
      }
      has_side_info = true;
      for (int i = 0; i < std::min(float_list_attr_value.size(), max_size); ++i) {
        side_info_config->attr_op_->SetFloatX(const_cast<char *>(attr_value->data()), offset + i,
                                              float_list_attr_value.at(i));
      }
    } else {
      if (!float_ext.ExtractFill(context, std::get<0>(schema), &float_list_attr_value, false)) {
        continue;
      }
      has_side_info = true;
      side_info_config->attr_op_->SetFloatX(const_cast<char *>(attr_value->data()), offset,
                                            float_list_attr_value.back());
    }
  }
  return has_side_info;
}

bool ExtractItemSideInfo(SideInfoConfig *side_info_config, reco_result_iterator iter,
                         ReadableRecoContextInterface *context, std::string *attr_value) {
  attr_value->resize(side_info_config->attr_op_->MaxSize());
  side_info_config->attr_op_->InitialBlock(const_cast<char *>(attr_value->data()),
                                           side_info_config->attr_op_->MaxSize());
  auto int_ext = Extractor<int64>();
  auto float_ext = Extractor<double>();

  int offset = 0;
  // 有一个成功就算成功了，万一提供的字段不存在, 用户最好自己保证存在
  bool has_side_info = false;
  std::vector<int64> int_list_attr_value;
  for (const auto &schema : side_info_config->int64_attr_items_schema_) {
    auto max_size = std::get<2>(schema);
    base::ScopeExit a([&offset, &max_size]() { offset += max_size; });
    // is list
    if (std::get<1>(schema)) {
      if (!int_ext.ExtractFill(iter, context, std::get<0>(schema), &int_list_attr_value, true)) {
        continue;
      }
      has_side_info = true;
      for (int i = 0; i < std::min(int_list_attr_value.size(), max_size); ++i) {
        side_info_config->attr_op_->SetIntX(const_cast<char *>(attr_value->data()), offset + i,
                                            int_list_attr_value.at(i));
      }
    } else {
      if (!int_ext.ExtractFill(iter, context, std::get<0>(schema), &int_list_attr_value, false)) {
        continue;
      }
      has_side_info = true;
      side_info_config->attr_op_->SetIntX(const_cast<char *>(attr_value->data()), offset,
                                          int_list_attr_value.back());
    }
  }

  offset = 0;
  std::vector<float> float_list_attr_value;
  for (const auto &schema : side_info_config->float_attr_items_schema_) {
    auto max_size = std::get<2>(schema);
    base::ScopeExit a([&offset, &max_size]() { offset += max_size; });
    // is list
    if (std::get<1>(schema)) {
      if (!float_ext.ExtractFill(iter, context, std::get<0>(schema), &float_list_attr_value, true)) {
        continue;
      }
      has_side_info = true;
      for (int i = 0; i < std::min(float_list_attr_value.size(), max_size); ++i) {
        side_info_config->attr_op_->SetFloatX(const_cast<char *>(attr_value->data()), offset + i,
                                              float_list_attr_value.at(i));
      }
    } else {
      if (!float_ext.ExtractFill(iter, context, std::get<0>(schema), &float_list_attr_value, false)) {
        continue;
      }
      has_side_info = true;
      side_info_config->attr_op_->SetFloatX(const_cast<char *>(attr_value->data()), offset,
                                            float_list_attr_value.back());
    }
  }
  return has_side_info;
}

bool BatchInsertToBtq(const std::string &btq_prefix, int shard_num,
                      const gnn::BatchInsertInfo &batch_insert_info, gnn::RelationType relation_type) {
  static ks::infra::btq::DynamicBTQueueClient *client = ks::infra::btq::DynamicBTQueueClient::Singleton();

  thread_local std::vector<gnn::BatchInsertInfo> sharding_infos;
  sharding_infos.resize(shard_num);
  for (int i = 0; i < shard_num; ++i) {
    sharding_infos[i].Clear();
    sharding_infos[i].iewa = batch_insert_info.iewa;
    sharding_infos[i].timeout_ms = batch_insert_info.timeout_ms;
  }
  bool has_attr = !batch_insert_info.src_attrs.empty();
  bool has_edge = !batch_insert_info.dest_ids.empty();
  bool has_weight = !batch_insert_info.dest_weights.empty();
  bool has_ts = !batch_insert_info.dst_ts.empty();
  bool has_dest_attr = !batch_insert_info.dest_attrs.empty();
  for (int i = 0; i < batch_insert_info.src_ids.size(); ++i) {
    auto src_id = batch_insert_info.src_ids[i];
    int shard_id = src_id % shard_num;
    sharding_infos[shard_id].src_ids.emplace_back(src_id);
    if (has_attr) {
      sharding_infos[shard_id].src_attrs.emplace_back(batch_insert_info.src_attrs[i]);
    }
    if (has_edge) {
      sharding_infos[shard_id].dest_ids.emplace_back(batch_insert_info.dest_ids[i]);
    }
    if (has_weight) {
      sharding_infos[shard_id].dest_weights.emplace_back(batch_insert_info.dest_weights[i]);
    }
    if (has_ts) {
      sharding_infos[shard_id].dst_ts.emplace_back(batch_insert_info.dst_ts[i]);
    }
    if (has_dest_attr) {
      sharding_infos[shard_id].dest_attrs.emplace_back(batch_insert_info.dest_attrs[i]);
    }
  }

  thread_local std::vector<gnn::BatchInsertRequest> requests;
  requests.clear();
  requests.resize(shard_num);
  for (int i = 0; i < shard_num; ++i) {
    serving_base::Timer timer;
    requests[i].Clear();
    requests[i].set_iewa(sharding_infos[i].iewa);
    size_t total_src_id_size = sharding_infos[i].src_ids.size();
    requests[i].mutable_src_ids()->Reserve(total_src_id_size);
    requests[i].mutable_relation_types()->Reserve(total_src_id_size);
    for (int j = 0; j < total_src_id_size; ++j) {
      requests[i].mutable_src_ids()->AddAlreadyReserved(sharding_infos[i].src_ids[j]);
      requests[i].mutable_relation_types()->AddAlreadyReserved(relation_type);
    }

    for (int j = 0; j < total_src_id_size; ++j) {
      if (sharding_infos[i].src_attrs.empty()) {
        break;
      }
      requests[i].mutable_attrs()->Reserve(total_src_id_size);
      if (j == total_src_id_size - 1) {
        *(requests[i].mutable_attrs()->Add()) = sharding_infos[i].src_attrs[j];
      } else {
        *(requests[i].mutable_attrs()->Add()) =
            (sharding_infos[i].src_ids[j] != sharding_infos[i].src_ids[j + 1])
                ? sharding_infos[i].src_attrs[j]
                : "";
      }
    }

    for (int j = 0; j < total_src_id_size; ++j) {
      if (sharding_infos[i].dest_ids.empty()) {
        break;
      }
      requests[i].mutable_dest_ids()->Reserve(total_src_id_size);
      if (sharding_infos[i].dest_weights.size() > 0) {
        requests[i].mutable_dest_weights()->Reserve(total_src_id_size);
        requests[i].mutable_dest_weights()->AddAlreadyReserved(sharding_infos[i].dest_weights[j]);
      }
      if (sharding_infos[i].dst_ts.size() > 0) {
        requests[i].mutable_dst_ts()->Reserve(total_src_id_size);
        requests[i].mutable_dst_ts()->AddAlreadyReserved(sharding_infos[i].dst_ts[j]);
      }
      if (sharding_infos[i].dest_attrs.size() > 0) {
        requests[i].mutable_edge_attrs()->Reserve(total_src_id_size);
        *(requests[i].mutable_edge_attrs()->Add()) = sharding_infos[i].dest_attrs[j];
      }
      requests[i].mutable_dest_ids()->AddAlreadyReserved(sharding_infos[i].dest_ids[j]);
    }

    timer.AppendCostMs("build request");

    std::string message;
    requests[i].SerializeToString(&message);
    if (message.length() == 0) {
      continue;
    }
    std::string btq_name = btq_prefix + "-" + std::to_string(i);
    if (!client->Push(btq_name, message, sharding_infos[i].timeout_ms)) {
      LOG(ERROR) << "Push message to btq " << btq_name << " failed!";
      return false;
    }
    timer.AppendCostMs("push to btq");
    FB_LOG_EVERY_MS(INFO, 10000) << "Push " << total_src_id_size << " records to btq " << btq_name
                                 << ", message size: " << message.length()
                                 << ", time profile: " << timer.display();
  }
  return true;
}

bool BatchInsertToBtq(const std::string &btq_prefix, int btq_shard_num,
                      const colossusdb::ps::biz::kgnn::BatchUpdateInfo &batch_insert_info,
                      const std::string &table_name) {
  if (btq_shard_num <= 0) {
    FB_LOG_EVERY_MS(ERROR, 5000) << "BatchInsert with btq_shard_num <= 0. "
                                 << "btq_shard_num: " << btq_shard_num;
    return false;
  }

  // 先获取需要预留的长度
  std::vector<int> reserved_num;
  reserved_num.resize(btq_shard_num);
  for (int i = 0; i < batch_insert_info.src_ids.size(); ++i) {
    auto src_id = batch_insert_info.src_ids[i];
    reserved_num[src_id % btq_shard_num]++;
  }

  bool has_attr = !batch_insert_info.src_attrs.empty();
  bool has_edge = !batch_insert_info.dest_ids.empty();
  bool has_weight = !batch_insert_info.dest_weights.empty();
  bool has_ts = !batch_insert_info.dst_ts.empty();
  bool has_dest_attr = !batch_insert_info.dest_attrs.empty();
  bool has_expire_ts_update_flags = !batch_insert_info.expire_ts_update_flags.empty();
  bool has_use_item_ts_for_expires = !batch_insert_info.use_item_ts_for_expires.empty();

  thread_local std::vector<colossusdb::ps::proto::biz::kgnn::BatchUpdateRequest> requests;
  requests.resize(btq_shard_num);
  for (int i = 0; i < btq_shard_num; ++i) {
    requests[i].Clear();
    requests[i].set_iewa(batch_insert_info.iewa);
    requests[i].set_table_name(table_name);
    requests[i].mutable_src_ids()->Reserve(reserved_num[i]);
    if (has_edge) {
      requests[i].mutable_dest_ids()->Reserve(reserved_num[i]);
    }
    if (has_attr) {
      requests[i].mutable_attrs()->Reserve(reserved_num[i]);
    }
    if (has_weight) {
      requests[i].mutable_dest_weights()->Reserve(reserved_num[i]);
    }
    if (has_ts) {
      requests[i].mutable_dst_ts()->Reserve(reserved_num[i]);
    }
    if (has_dest_attr) {
      requests[i].mutable_edge_attrs()->Reserve(reserved_num[i]);
    }
    if (has_expire_ts_update_flags) {
      requests[i].mutable_expire_ts_update_flags()->Reserve(reserved_num[i]);
    }
    if (has_use_item_ts_for_expires) {
      requests[i].mutable_use_item_ts_for_expires()->Reserve(reserved_num[i]);
    }
  }

  for (int i = 0; i < batch_insert_info.src_ids.size(); ++i) {
    auto src_id = batch_insert_info.src_ids[i];
    int shard_id = src_id % btq_shard_num;
    requests[shard_id].mutable_src_ids()->AddAlreadyReserved(src_id);
    if (has_attr) {
      *requests[shard_id].mutable_attrs()->Add() =
          (i == batch_insert_info.src_ids.size() - 1 || src_id != batch_insert_info.src_ids[i + 1])
              ? std::move(batch_insert_info.src_attrs[i])
              : "";
    }
    if (!has_edge) {
      continue;
    }
    requests[shard_id].mutable_dest_ids()->AddAlreadyReserved(batch_insert_info.dest_ids[i]);
    if (has_weight) {
      requests[shard_id].mutable_dest_weights()->AddAlreadyReserved(batch_insert_info.dest_weights[i]);
    }
    if (has_ts) {
      requests[shard_id].mutable_dst_ts()->AddAlreadyReserved(batch_insert_info.dst_ts[i]);
    }
    if (has_dest_attr) {
      *(requests[shard_id].mutable_edge_attrs()->Add()) = std::move(batch_insert_info.dest_attrs[i]);
    }
    if (has_expire_ts_update_flags) {
      requests[shard_id].mutable_expire_ts_update_flags()->AddAlreadyReserved(
          batch_insert_info.expire_ts_update_flags[i]);
    }
    if (has_use_item_ts_for_expires) {
      requests[shard_id].mutable_use_item_ts_for_expires()->AddAlreadyReserved(
          batch_insert_info.use_item_ts_for_expires[i]);
    }
  }

  for (int i = 0; i < btq_shard_num; ++i) {
    if (requests[i].src_ids_size() <= 0) {
      continue;
    }
    std::string message;
    requests[i].SerializeToString(&message);
    std::string btq_topic_name = absl::StrFormat("%s%d", btq_prefix.c_str(), i);
    if (!ks::infra::btq::DynamicBTQueueClient::Singleton()->Push(btq_topic_name, message,
                                                                 batch_insert_info.timeout_ms)) {
      FB_LOG_EVERY_MS(ERROR, 5000) << "Push message to btq " << btq_topic_name
                                   << " failed! Check btq log for more details";
      continue;
    }

    FB_LOG_EVERY_MS(INFO, 60000) << "Push " << requests[i].src_ids_size() << " records to btq "
                                 << btq_topic_name << ", message size: " << message.length();
  }

  return true;
}

bool BatchDeleteToBtq(const std::string &btq_prefix, int btq_shard_num,
                      const colossusdb::ps::biz::kgnn::BatchUpdateInfo &batch_delete_info,
                      const std::string &table_name) {
  if (btq_shard_num <= 0) {
    FB_LOG_EVERY_MS(ERROR, 5000) << "BatchDelete with btq_shard_num <= 0. "
                                 << "btq_shard_num: " << btq_shard_num;
    return false;
  }

  bool delete_whole_node = batch_delete_info.dest_ids.empty();

  std::vector<colossusdb::ps::proto::biz::kgnn::BatchUpdateRequest> requests(btq_shard_num);
  for (int i = 0; i < btq_shard_num; ++i) {
    requests[i].Clear();
    requests[i].set_table_name(table_name);
    requests[i].set_is_delete(true);
  }

  for (int i = 0; i < batch_delete_info.src_ids.size(); ++i) {
    int shard_id = batch_delete_info.src_ids[i] % btq_shard_num;
    requests[shard_id].add_src_ids(batch_delete_info.src_ids[i]);
    if (!delete_whole_node) {
      requests[shard_id].add_dest_ids(batch_delete_info.dest_ids[i]);
    }
  }

  for (int i = 0; i < btq_shard_num; ++i) {
    if (requests[i].src_ids_size() <= 0) {
      continue;
    }
    std::string message;
    requests[i].SerializeToString(&message);
    std::string btq_topic_name = absl::StrFormat("%s%d", btq_prefix.c_str(), i);
    if (!ks::infra::btq::DynamicBTQueueClient::Singleton()->Push(btq_topic_name, message,
                                                                 batch_delete_info.timeout_ms)) {
      FB_LOG_EVERY_MS(ERROR, 5000) << "Push message to btq " << btq_topic_name
                                   << " failed! Check btq log for more details";
      continue;
    }

    FB_LOG_EVERY_MS(INFO, 60000) << "Push " << requests[i].src_ids_size() << " records to btq "
                                 << btq_topic_name << ", message size: " << message.length();
  }

  return true;
}

}  // namespace platform
}  // namespace ks
