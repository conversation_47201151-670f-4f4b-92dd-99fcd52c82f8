#pragma once

#include <math.h>
#include <algorithm>
#include <ctime>
#include <functional>
#include <limits>
#include <random>
#include <set>
#include <string>
#include <tuple>
#include <unordered_map>
#include <utility>
#include <vector>
#include <memory>
#include <unordered_set>
#include "base/strings/string_number_conversions.h"
#include "base/strings/string_split.h"
#include "base/time/timestamp.h"
#include "dragon/src/module/common_reco_light_function.h"
#include "folly/String.h"
#include "folly/container/F14Map.h"
#include "ks/reco_pub/reco/util/util.h"
#include "folly/container/F14Set.h"
#include "absl/strings/str_split.h"

namespace ks {
namespace platform {

class SlideLightFunctionSet : public CommonRecoBaseLightFunctionSet {
 public:
  SlideLightFunctionSet() {
    REGISTER_LIGHT_FUNCTION(CoinLightGenXtrsKey);
    REGISTER_LIGHT_FUNCTION(CoinLightParseXtrsString);
    REGISTER_LIGHT_FUNCTION(GetTriggerItemsFromActionItemList);
    REGISTER_LIGHT_FUNCTION(FunctionSignalsTransStr2List);
    REGISTER_LIGHT_FUNCTION(FunctionSignalsGetItemAttr);
    REGISTER_LIGHT_FUNCTION(FunctionSignalsGetAttrFromMemoryData);
    REGISTER_LIGHT_FUNCTION(GetIntCrowdItemAttrFromMemoryData);
    REGISTER_LIGHT_FUNCTION(ProfileCrowdGetAttrFromMemoryDataByOneCommonOneItem);
    REGISTER_LIGHT_FUNCTION(ProfileCrowdGetAttrFromMemoryDataByTwoCommonOneItem);
    REGISTER_LIGHT_FUNCTION(ProfileCrowdGetAttrFromMemoryDataByThreeCommonOneItem);
    REGISTER_LIGHT_FUNCTION(ProfileCrowdGetAttrFromMemoryDataByFourCommonOneItem);
    REGISTER_LIGHT_FUNCTION(ProfileCrowdGetAttrFromMemoryDataByThreeCommon);
    REGISTER_LIGHT_FUNCTION(ProfileCrowdGetAttrFromMemoryDataByCombinedAttr);
    REGISTER_LIGHT_FUNCTION(GetAttrFromStringDoubleMapMemoryDataByCombinedAttr);
    REGISTER_LIGHT_FUNCTION(GetValueFromIntListByItemAttr);
    REGISTER_LIGHT_FUNCTION(GetValueFromMemoryDataListByItemAttr);
    REGISTER_LIGHT_FUNCTION(CoinLightParseClusterId632);
    REGISTER_LIGHT_FUNCTION(ExtractUserTopClusters);
    REGISTER_LIGHT_FUNCTION(FindSatisfiedHetuTagIdByMinMax);
    REGISTER_LIGHT_FUNCTION(YoungCrowdGetAttrFromMemoryDataBySixCommonOneItem);
  }

  // [chenjiahao05] for coin project fetching redis
  static bool CoinLightGenXtrsKey(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    // get
    auto union_tag = context.GetStringCommonAttr("coin_crowd_union_tag").value_or("");
    std::vector<absl::string_view> union_tag_vec = absl::StrSplit(union_tag, "_", absl::SkipWhitespace());

    // process
    if (context.GetIntCommonAttr("is_gamora_user").value_or(0) > 0) {
      if (union_tag_vec.size() >= 5) {
        context.SetStringCommonAttr("coin_if_watch", absl::StrCat("if_watch_KUAISHOU_", union_tag_vec[0]));
        context.SetStringCommonAttr("coin_rfm", absl::StrCat("rfm_KUAISHOU_", union_tag_vec[1]));
        context.SetStringCommonAttr("coin_rfm_ubf", absl::StrCat("rfm_ubf_KUAISHOU_",
                                                                  union_tag_vec[1], "_",
                                                                  union_tag_vec[2], "_",
                                                                  union_tag_vec[3], "_",
                                                                  union_tag_vec[4]));
      }
      if (union_tag_vec.size() >= 6) {
        context.SetStringCommonAttr("coin_rfm_ten", absl::StrCat("rfm_ten_KUAISHOU_",
                                                                  union_tag_vec[1], "_",
                                                                  union_tag_vec[5]));
      }
    } else {
      if (union_tag_vec.size() >= 5) {
        context.SetStringCommonAttr("coin_if_watch", absl::StrCat("if_watch_NEBULA_", union_tag_vec[0]));
        context.SetStringCommonAttr("coin_rfm", absl::StrCat("rfm_NEBULA_", union_tag_vec[1]));
        context.SetStringCommonAttr("coin_rfm_ubf", absl::StrCat("rfm_ubf_NEBULA_",
                                                                  union_tag_vec[1], "_",
                                                                  union_tag_vec[2], "_",
                                                                  union_tag_vec[3], "_",
                                                                  union_tag_vec[4]));
      }
      if (union_tag_vec.size() >= 6) {
        context.SetStringCommonAttr("coin_rfm_ten", absl::StrCat("rfm_ten_NEBULA_",
                                                                  union_tag_vec[1], "_",
                                                                  union_tag_vec[5]));
      }
    }

    return true;
  }

  // [chenjiahao05] for coin project fetching redis
  static bool CoinLightParseXtrsString(
      const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    // getter
    auto raw_string = context.GetStringCommonAttr("coin_crowd_cross_cluster_xtrs_string").value_or("");
    std::vector<absl::string_view> raw_vec = absl::StrSplit(raw_string, "_", absl::SkipWhitespace());
    // accessor
    int64 num_debias_xtrs = context.GetIntCommonAttr("coin_num_debias_xtrs").value_or(1);
    std::vector<std::vector<double>> xtrs_list(1001, std::vector<double>(num_debias_xtrs, 0.0));

    // process
    for (absl::string_view &kv_str : raw_vec) {
      int key = 0;
      std::vector<absl::string_view> kv = absl::StrSplit(kv_str, ":", absl::SkipWhitespace());
      if (kv.size() < 2) continue;
      std::vector<absl::string_view> xtrs = absl::StrSplit(kv[1], ",", absl::SkipWhitespace());
      if (!absl::SimpleAtoi(kv[0], &key) && xtrs.size() != num_debias_xtrs) continue;
      for (int i = 0; i < xtrs.size() && i < num_debias_xtrs; ++i) {
        if (!absl::SimpleAtod(xtrs[i], &xtrs_list[key == -124 ? 1000 : key][i])) {
          CL_LOG_EVERY_N(WARNING, 803)
          << "[Coin Crowd Debias] fetching_redis's light function failed to convert xtr string to double!";
        }
      }
    }

    // dispatch to items
    auto cluster_id_getter = context.GetIntItemAttr("hetu_sim_cluster_id");
    auto xtrs_setter = context.SetDoubleListItemAttr("coin_crowd_cross_cluster_xtrs");
    for (auto iter = begin; iter != end; ++iter) {
      if (cluster_id_getter(*iter).has_value()) {
        auto cluster_id = cluster_id_getter(*iter).value();
        std::vector<double> temp_vec(xtrs_list[cluster_id]);
        xtrs_setter(*iter, temp_vec);
      }
    }

    return true;
  }

  // [wangmingda] 把 hetu_sim_cluster_id 映射成 632，输出变量为 hetu_sim_cluster_id_632
  static bool CoinLightParseClusterId632(
    const CommonRecoLightFunctionContext &context, RecoResultConstIter begin, RecoResultConstIter end) {
    // kconf 读取 hetu cluster 映射信息
    auto hetu_cluster_map_ptr =
        ks::infra::KConf()
            .GetList("reco.interestExplore.remapClusterId632", std::make_shared<std::vector<int>>())
            ->Get();
    if (hetu_cluster_map_ptr == nullptr) {
      LOG_EVERY_N(WARNING, 10000)
          << "CoinLightParseClusterId632. failed to get cluster_632 kconf";
      return false;
    }

    // dispatch to items
    auto cluster_id_getter = context.GetIntItemAttr("hetu_sim_cluster_id");
    auto xtrs_setter = context.SetIntItemAttr("hetu_sim_cluster_id_632");
    for (auto iter = begin; iter != end; ++iter) {
      if (cluster_id_getter(*iter).has_value()) {
        auto cluster_id = cluster_id_getter(*iter).value();
        int mapping_cluster_id = cluster_id;
        if (hetu_cluster_map_ptr && cluster_id >= 0 && cluster_id < hetu_cluster_map_ptr->size()) {
          mapping_cluster_id = hetu_cluster_map_ptr->at(cluster_id);
        }

        LOG_EVERY_N(INFO, 10000) << "CoinLightParseClusterId632"
                                      << ", cluster_id:" << cluster_id
                                      << ", mapping_cluster_id:" << mapping_cluster_id;

        xtrs_setter(*iter, mapping_cluster_id);
      }
    }
    return true;
  }

  static bool GetTriggerItemsFromActionItemList(const CommonRecoLightFunctionContext &context,
                                                RecoResultConstIter begin, RecoResultConstIter end) {
    const auto photo_id_list = context.GetIntListCommonAttr("photo_id_list");
    const auto time_ms_list = context.GetIntListCommonAttr("time_ms_list");
    // 允许 trigger_items_list 为 nullptr
    const auto trigger_items_list = context.GetIntListCommonAttr("trigger_items_list");
    const auto max_cnt = context.GetIntCommonAttr("max_cnt").value_or(0);
    const auto recent_day = context.GetIntCommonAttr("recent_day").value_or(0);
    if (!photo_id_list || !time_ms_list ||
        photo_id_list->size() == 0 || time_ms_list->size() == 0 ||
        photo_id_list->size() != time_ms_list->size()) {
      CL_LOG_EVERY_N(WARNING, 1000) << "GetTriggerItemsFromActionItemList input common_attr "
                                    << "photo_id_list or time_ms_list check failed";
      return true;
    }
    if (trigger_items_list && trigger_items_list->size() >= max_cnt) {
      CL_LOG_EVERY_N(WARNING, 1000) << "GetTriggerItemsFromActionItemList input common_attr "
                                    << "trigger_items_list size >= "
                                    << max_cnt;
      return true;
    }
    std::vector<int64> new_trigger_items_list;
    folly::F14FastSet<int64> trigger_items_set;
    if (trigger_items_list && trigger_items_list->size() > 0) {
      folly::F14FastSet<int64> temp_set(trigger_items_list->begin(), trigger_items_list->end());
      trigger_items_set.swap(temp_set);
    }
    int64 recent_day_ms = (base::GetTimestamp() - recent_day * base::Time::kMicrosecondsPerDay) / 1e3;
    for (int index = 0; index < photo_id_list->size(); ++index) {
      auto photo_id = (*photo_id_list)[index];
      auto time_ms = (*time_ms_list)[index];
      if (photo_id <= 0) {
        continue;
      }
      if (trigger_items_set.count(photo_id)) {
        continue;
      }
      if (time_ms >= recent_day_ms && trigger_items_set.size() < max_cnt) {
        trigger_items_set.insert(photo_id);
        new_trigger_items_list.emplace_back(photo_id);
      } else {
        break;
      }
    }
    for (const auto &item : new_trigger_items_list) {
      context.AppendIntListCommonAttr("trigger_items_list", item);
    }
    return true;
  }

  static bool FunctionSignalsTransStr2List(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    int64 group_num = context.GetIntCommonAttr("group_num").value_or(-1);
    int64 redis_data_lines = context.GetIntCommonAttr("redis_data_lines").value_or(-1);
    if (group_num <= 0 || redis_data_lines <= 0) {
      CL_LOG_EVERY_N(WARNING, 1) << "FunctionSignalsTransStr2List invalid params.";
      return true;
    }
    std::vector<std::string> output_str_list(group_num);

    for (int line_ind = 0; line_ind < redis_data_lines; ++line_ind) {
      auto raw_string = context.GetStringCommonAttr(absl::StrCat(
                                    "raw_concat_str_", std::to_string(line_ind))).value_or("");
      std::vector<absl::string_view> raw_vec = absl::StrSplit(raw_string, "-", absl::SkipWhitespace());
      for (absl::string_view &kv_str : raw_vec) {
        if (kv_str.empty()) {
          CL_LOG_EVERY_N(WARNING, 1000) << "FunctionSignalsTransStr2List empty key-value pair.";
          continue;
        }
        size_t split_ind = kv_str.find('_');
        if (split_ind == absl::string_view::npos) {
          CL_LOG_EVERY_N(WARNING, 1000) << "FunctionSignalsTransStr2List invalid key-value pair.";
          continue;
        }
        absl::string_view key = kv_str.substr(0, split_ind);
        absl::string_view value = kv_str.substr(split_ind + 1);

        int ind = 0;
        if (absl::SimpleAtoi(key, &ind)) {
          if (0 <= ind < group_num) {
            output_str_list[ind] = std::string(value);
          } else {
            CL_LOG_EVERY_N(WARNING, 1000) << "FunctionSignalsTransStr2List index out of range, ind: " << ind;
          }
        } else {
          CL_LOG_EVERY_N(WARNING, 1000) <<
                "FunctionSignalsTransStr2List failed to convert string to int, string: " << key;
        }
      }
    }

    context.SetStringListCommonAttr("output_str_list", std::move(output_str_list));
    return true;
  }

  static bool FunctionSignalsGetItemAttr(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    int64 group_num = context.GetIntCommonAttr("group_num").value_or(0);
    if (group_num <=0) {
      CL_LOG_EVERY_N(WARNING, 1) << "FunctionSignalsGetItemAttr group_num less than 0";
      return true;
    }
    auto concat_list = context.GetStringListCommonAttr("concat_list");
    if (!concat_list.has_value() || concat_list->size() != group_num) {
      CL_LOG_EVERY_N(WARNING, 1000) << "FunctionSignalsGetItemAttr concat_list not exist";
      return true;
    }

    auto photoid_getter = context.GetIntItemAttr("photo_id");
    auto output_setter = context.SetStringItemAttr("output_click_cnts_str");
    for (auto iter = begin; iter != end; ++iter) {
      int64 photoid = photoid_getter(*iter).value_or(0);
      if (photoid <= 0) {
        continue;
      }
      int64 index = photoid % group_num;
      if (index < 0 || index >= group_num) {
        continue;
      }

      absl::string_view concat_str = concat_list->at(index);
      size_t pos = 0;
      while (pos < concat_str.size()) {
        size_t end_pos = concat_str.find('_', pos);
        if (end_pos == absl::string_view::npos) {
            end_pos = concat_str.size();
        }

        absl::string_view kv_str = concat_str.substr(pos, end_pos - pos);
        pos = end_pos + 1;

        size_t colon_pos = kv_str.find(':');
        if (colon_pos == absl::string_view::npos) {
          CL_LOG_EVERY_N(WARNING, 1000) << "FunctionSignalsGetItemAttr invalid key-value pair";
          continue;
        }
        absl::string_view key = kv_str.substr(0, colon_pos);
        absl::string_view value = kv_str.substr(colon_pos + 1);

        int64 key_int = 0;
        if (absl::SimpleAtoi(key, &key_int) && key_int == photoid) {
          output_setter(*iter, std::string(value));
          break;
        }
      }
    }
    return true;
  }

  static bool FunctionSignalsGetAttrFromMemoryData(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    const folly::F14FastMap<std::string, std::vector<uint64>> *func_memory_data =
      context.GetPtrCommonAttr<folly::F14FastMap<std::string, std::vector<uint64>>>(
        "feature_map__memory_data");
    if (!func_memory_data || func_memory_data->size() == 0) {
      CL_LOG_EVERY_N(WARNING, 1000) <<
        "FunctionSignalsGetAttrFromMemoryData failed to get memory data ptr";
      return true;
    }

    std::string prod = context.GetIntCommonAttr("is_gamora_user").value_or(0) > 0 ? "KS" : "NE";
    auto user_crowd = context.GetStringCommonAttr("ten_crowds").value_or("");

    auto photoid_getter = context.GetIntItemAttr("photo_id");
    auto output_setter_all_user = context.SetIntListItemAttr("click_cnts_all");
    auto output_setter_crowd = context.SetIntListItemAttr("click_cnts_crowd");

    for (auto iter = begin; iter != end; ++iter) {
      int64 photoid = photoid_getter(*iter).value_or(0);
      auto target_pair_all_user = func_memory_data->find(
        absl::StrCat(prod, "_", "all_", std::to_string(photoid)));
      if (target_pair_all_user != func_memory_data->end() && !target_pair_all_user->second.empty()) {
        std::vector<int64> value_all_user;
        value_all_user.reserve(target_pair_all_user->second.size());
        for (const auto& val : target_pair_all_user->second) {
            value_all_user.push_back(static_cast<int64>(val));
        }
        output_setter_all_user(*iter, value_all_user);
      }

      auto target_pair_crowd = func_memory_data->find(
        absl::StrCat(prod, "_", user_crowd, "_", std::to_string(photoid)));
      if (target_pair_crowd != func_memory_data->end() && !target_pair_crowd->second.empty()) {
        std::vector<int64> value_crowd;
        value_crowd.reserve(target_pair_crowd->second.size());
        for (const auto& val : target_pair_crowd->second) {
            value_crowd.push_back(static_cast<int64>(val));
        }
        output_setter_crowd(*iter, value_crowd);
      }
    }
    return true;
  }

  static bool GetIntCrowdItemAttrFromMemoryData(const CommonRecoLightFunctionContext &context,
                                            RecoResultConstIter begin, RecoResultConstIter end) {
    const folly::F14FastMap<std::string, uint64> *func_memory_data =
      context.GetPtrCommonAttr<folly::F14FastMap<std::string, uint64>>(
        "feature_map__memory_data");
    if (!func_memory_data || func_memory_data->size() == 0) {
      CL_LOG_EVERY_N(WARNING, 1000) <<
        "GetIntCrowdItemAttrFromMemoryData failed to get memory data ptr";
      return true;
    }

    std::string prod = context.GetIntCommonAttr("is_gamora_user").value_or(0) > 0 ? "KS" : "NE";
    auto user_crowd = context.GetStringCommonAttr("ten_crowds").value_or("");

    auto photoid_getter = context.GetIntItemAttr("photo_id");
    auto output_setter_all_user = context.SetIntItemAttr("ret_int_all");
    auto output_setter_crowd = context.SetIntItemAttr("ret_int_crowd");

    for (auto iter = begin; iter != end; ++iter) {
      int64 photoid = photoid_getter(*iter).value_or(0);
      auto target_pair_all_user = func_memory_data->find(
        absl::StrCat(prod, "_", "all_", std::to_string(photoid)));
      if (target_pair_all_user != func_memory_data->end() && target_pair_all_user->second >= 0) {
        output_setter_all_user(*iter, target_pair_all_user->second);
      }

      auto target_pair_crowd = func_memory_data->find(
        absl::StrCat(prod, "_", user_crowd, "_", std::to_string(photoid)));
      if (target_pair_crowd != func_memory_data->end() && target_pair_crowd->second >= 0) {
        output_setter_all_user(*iter, target_pair_crowd->second);
      }
    }
    return true;
  }

  // [chenjiahao05] - Optimized version (without cache)
  static bool ProfileCrowdGetAttrFromMemoryDataByOneCommonOneItem(
    const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end
  ) {
    const folly::F14FastMap<std::string, std::vector<double>> *memory_data =
      context.GetPtrCommonAttr<folly::F14FastMap<std::string, std::vector<double>>>(
        "feature_map__memory_data");
    if (!memory_data || memory_data->size() == 0) {
      CL_LOG_EVERY_N(WARNING, 803) <<
        "ProfileCrowdGetAttrFromMemoryDataByOneCommonOneItem failed to get memory data ptr";
      return true;
    }

    std::string prod = context.GetIntCommonAttr("is_gamora_user").value_or(0) > 0 ? "KS" : "NE";
    auto common_attr_1 = context.GetIntCommonAttr("common_attr_1").value_or(0);
    std::string key_prefix = absl::StrCat(prod, "_", common_attr_1, "_");

    auto item_attr_accessor = context.GetIntItemAttr("item_attr");
    auto output_setter = context.SetDoubleListItemAttr("pxtr_statistics");

    for (auto iter = begin; iter != end; ++iter) {
      int64 item_attr = item_attr_accessor(*iter).value_or(0);
      auto target_pair = memory_data->find(absl::StrCat(key_prefix, item_attr));
      if (target_pair != memory_data->end() && !target_pair->second.empty()) {
        std::vector<double> pxtr_vec(target_pair->second);
        output_setter(*iter, pxtr_vec);
      }
    }
    return true;
  }

  // [chenjiahao05]
  static bool ProfileCrowdGetAttrFromMemoryDataByTwoCommonOneItem(
    const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end
  ) {
    const folly::F14FastMap<std::string, std::vector<double>> *memory_data =
      context.GetPtrCommonAttr<folly::F14FastMap<std::string, std::vector<double>>>(
        "feature_map__memory_data");
    if (!memory_data || memory_data->size() == 0) {
      CL_LOG_EVERY_N(WARNING, 803) <<
        "ProfileCrowdGetAttrFromMemoryDataByTwoCommonOneItem failed to get memory data ptr";
      return true;
    }

    std::string prod = context.GetIntCommonAttr("is_gamora_user").value_or(0) > 0 ? "KS" : "NE";
    auto common_attr_1 = context.GetIntCommonAttr("common_attr_1").value_or(0);
    auto common_attr_2 = context.GetIntCommonAttr("common_attr_2").value_or(0);
    std::string key_prefix = absl::StrCat(prod, "_", common_attr_1, "_", common_attr_2, "_");

    auto item_attr_accessor = context.GetIntItemAttr("item_attr");
    auto output_setter = context.SetDoubleListItemAttr("pxtr_statistics");

    for (auto iter = begin; iter != end; ++iter) {
      int64 item_attr = item_attr_accessor(*iter).value_or(0);
      auto target_pair = memory_data->find(absl::StrCat(key_prefix, item_attr));
      if (target_pair != memory_data->end() && !target_pair->second.empty()) {
        std::vector<double> pxtr_vec(target_pair->second);
        output_setter(*iter, pxtr_vec);
      }
    }
    return true;
  }

  // [chenjiahao05]
  static bool ProfileCrowdGetAttrFromMemoryDataByThreeCommonOneItem(
    const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end
  ) {
    const folly::F14FastMap<std::string, std::vector<double>> *memory_data =
      context.GetPtrCommonAttr<folly::F14FastMap<std::string, std::vector<double>>>(
        "feature_map__memory_data");
    if (!memory_data || memory_data->size() == 0) {
      CL_LOG_EVERY_N(WARNING, 803) <<
        "ProfileCrowdGetAttrFromMemoryDataByThreeCommonOneItem failed to get memory data ptr";
      return true;
    }

    std::string prod = context.GetIntCommonAttr("is_gamora_user").value_or(0) > 0 ? "KS" : "NE";
    auto common_attr_1 = context.GetIntCommonAttr("common_attr_1").value_or(0);
    auto common_attr_2 = context.GetIntCommonAttr("common_attr_2").value_or(0);
    auto common_attr_3 = context.GetIntCommonAttr("common_attr_3").value_or(0);
    std::string key_prefix = absl::StrCat(
      prod, "_", common_attr_1, "_", common_attr_2, "_", common_attr_3, "_");

    auto item_attr_accessor = context.GetIntItemAttr("item_attr");
    auto output_setter = context.SetDoubleListItemAttr("pxtr_statistics");

    for (auto iter = begin; iter != end; ++iter) {
      int64 item_attr = item_attr_accessor(*iter).value_or(0);
      auto target_pair = memory_data->find(absl::StrCat(key_prefix, item_attr));
      if (target_pair != memory_data->end() && !target_pair->second.empty()) {
        std::vector<double> pxtr_vec(target_pair->second);
        output_setter(*iter, pxtr_vec);
      }
    }
    return true;
  }

  // [chenjiahao05]
  static bool ProfileCrowdGetAttrFromMemoryDataByFourCommonOneItem(
    const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end
  ) {
    const folly::F14FastMap<std::string, std::vector<double>> *memory_data =
      context.GetPtrCommonAttr<folly::F14FastMap<std::string, std::vector<double>>>(
        "feature_map__memory_data");
    if (!memory_data || memory_data->size() == 0) {
      CL_LOG_EVERY_N(WARNING, 803) <<
        "ProfileCrowdGetAttrFromMemoryDataByFourCommonOneItem failed to get memory data ptr";
      return true;
    }

    std::string prod = context.GetIntCommonAttr("is_gamora_user").value_or(0) > 0 ? "KS" : "NE";
    auto common_attr_1 = context.GetIntCommonAttr("common_attr_1").value_or(0);
    auto common_attr_2 = context.GetIntCommonAttr("common_attr_2").value_or(0);
    auto common_attr_3 = context.GetIntCommonAttr("common_attr_3").value_or(0);
    auto common_attr_4 = context.GetIntCommonAttr("common_attr_4").value_or(0);
    std::string key_prefix = absl::StrCat(
      prod, "_", common_attr_1, "_", common_attr_2, "_", common_attr_3, "_", common_attr_4, "_");

    auto item_attr_accessor = context.GetIntItemAttr("item_attr");
    auto output_setter = context.SetDoubleListItemAttr("pxtr_statistics");

    for (auto iter = begin; iter != end; ++iter) {
      int64 item_attr = item_attr_accessor(*iter).value_or(0);
      auto target_pair = memory_data->find(absl::StrCat(key_prefix, "_", item_attr));
      if (target_pair != memory_data->end() && !target_pair->second.empty()) {
        std::vector<double> pxtr_vec(target_pair->second);
        output_setter(*iter, pxtr_vec);
      }
    }
    return true;
  }

  // [chenjiahao05]
  static bool ProfileCrowdGetAttrFromMemoryDataByThreeCommon(
    const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end
  ) {
    const folly::F14FastMap<std::string, std::vector<double>> *memory_data =
      context.GetPtrCommonAttr<folly::F14FastMap<std::string, std::vector<double>>>(
        "feature_map__memory_data");
    if (!memory_data || memory_data->size() == 0) {
      CL_LOG_EVERY_N(WARNING, 803) <<
        "ProfileCrowdGetAttrFromMemoryDataByThreeCommon failed to get memory data ptr";
      return true;
    }

    std::string prod = context.GetIntCommonAttr("is_gamora_user").value_or(0) > 0 ? "KS" : "NE";
    auto common_attr_1 = context.GetIntCommonAttr("common_attr_1").value_or(0);
    auto common_attr_2 = context.GetIntCommonAttr("common_attr_2").value_or(0);
    auto common_attr_3 = context.GetIntCommonAttr("common_attr_3").value_or(0);
    std::string key = absl::StrCat(prod, "_", common_attr_1, "_", common_attr_2, "_", common_attr_3);

    auto target_pair = memory_data->find(key);
    if (target_pair != memory_data->end() && !target_pair->second.empty()) {
      std::vector<double> pxtr_vec(target_pair->second);
      context.SetDoubleListCommonAttr("pxtr_statistics", std::move(pxtr_vec));
    }

    return true;
  }

  // [chenjiahao05]
  static bool ProfileCrowdGetAttrFromMemoryDataByCombinedAttr(
    const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end
  ) {
    const folly::F14FastMap<std::string, std::vector<double>> *memory_data =
      context.GetPtrCommonAttr<folly::F14FastMap<std::string, std::vector<double>>>(
        "feature_map__memory_data");
    if (!memory_data || memory_data->size() == 0) {
      CL_LOG_EVERY_N(WARNING, 803) <<
        "ProfileCrowdGetAttrFromMemoryDataByCombinedAttr failed to get memory data ptr";
      return true;
    }

    std::string prod = context.GetIntCommonAttr("is_gamora_user").value_or(0) > 0 ? "KS" : "NE";
    auto common_attr = context.GetStringCommonAttr("common_attr").value_or("UNKNOWN");
    std::string key_prefix = absl::StrCat(prod, "_", common_attr, "_");

    auto item_attr_accessor = context.GetIntItemAttr("item_attr");
    auto output_setter = context.SetDoubleListItemAttr("pxtr_statistics");

    for (auto iter = begin; iter != end; ++iter) {
      int64 item_attr = item_attr_accessor(*iter).value_or(0);
      auto target_pair = memory_data->find(absl::StrCat(key_prefix, item_attr));
      if (target_pair != memory_data->end() && !target_pair->second.empty()) {
        std::vector<double> pxtr_vec(target_pair->second);
        output_setter(*iter, pxtr_vec);
      }
    }
    return true;
  }

  // [chenjiahao05]
  static bool GetAttrFromStringDoubleMapMemoryDataByCombinedAttr(
    const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end
  ) {
    const folly::F14FastMap<std::string, double> *memory_data =
      context.GetPtrCommonAttr<folly::F14FastMap<std::string, double>>(
        "feature_map__memory_data");
    if (!memory_data || memory_data->size() == 0) {
      CL_LOG_EVERY_N(WARNING, 803) <<
        "GetAttrFromStringDoubleMapMemoryDataByCombinedAttr failed to get memory data ptr";
      return true;
    }

    std::string prod = context.GetIntCommonAttr("is_gamora_user").value_or(0) > 0 ? "KS" : "NE";
    auto common_attr = context.GetStringCommonAttr("common_attr").value_or("UNKNOWN");

    auto item_attr_accessor = context.GetIntItemAttr("item_attr");
    auto output_setter = context.SetDoubleItemAttr("pxtr_statistics");

    for (auto iter = begin; iter != end; ++iter) {
      int64 item_attr = item_attr_accessor(*iter).value_or(0);
      auto target_pair = memory_data->find(
        absl::StrCat(prod, "_", common_attr, "_", item_attr));
      if (target_pair != memory_data->end()) {
        output_setter(*iter, target_pair->second);
      }
    }
    return true;
  }

  // [chenjiahao05]
  static bool GetValueFromIntListByItemAttr(
    const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end
  ) {
    auto input_list = context.GetIntListCommonAttr("input_common_attr");
    if (!input_list.has_value()) {
      CL_LOG_EVERY_N(WARNING, 803) <<
        "GetValueFromIntListByItemAttr failed to get input_common_attr list";
      return true;
    }
    if (input_list.value().size() == 0) {
      CL_LOG_EVERY_N(WARNING, 803) <<
        "GetValueFromIntListByItemAttr get a zero-length input_common_attr list";
      return true;
    }

    auto item_attr_accessor = context.GetIntItemAttr("input_item_attr");
    auto output_setter = context.SetIntItemAttr("output_item_attr");

    for (auto iter = begin; iter != end; ++iter) {
      int64 res = -124;
      int64 item_attr = item_attr_accessor(*iter).value_or(-124);
      if (item_attr >= 0 && item_attr < input_list.value().size()) {
        res = input_list.value()[item_attr];
      }
      output_setter(*iter, res);
    }
    return true;
  }

  // [chenjiahao05]
  static bool GetValueFromMemoryDataListByItemAttr(
    const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end
  ) {
    const std::vector<uint64> *memory_data =
      context.GetPtrCommonAttr<std::vector<uint64>>("feature_map__memory_data");
    if (!memory_data || memory_data->size() == 0) {
      CL_LOG_EVERY_N(WARNING, 803) <<
        "GetValueFromMemoryDataListByItemAttr failed to get memory data ptr";
      return true;
    }

    auto item_attr_accessor = context.GetIntItemAttr("input_item_attr");
    auto output_setter = context.SetIntItemAttr("output_item_attr");

    for (auto iter = begin; iter != end; ++iter) {
      int64 res = -124;
      int64 item_attr = item_attr_accessor(*iter).value_or(-124);
      if (item_attr >= 0 && item_attr < memory_data->size()) {
        res = memory_data->at(item_attr);
      }
      output_setter(*iter, res);
    }
    return true;
  }

  // [chenjiahao05]
  static bool ExtractUserTopClusters(
    const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end
  ) {
    absl::string_view user_to_cluster_list_str =
        context.GetStringCommonAttr("user_to_cluster_list_str").value_or("");
    std::vector<absl::string_view> user_to_cluster_list_med =
        absl::StrSplit(user_to_cluster_list_str, ",", absl::SkipWhitespace());

    std::vector<std::pair<int, int>> user_to_cluster_list;
    user_to_cluster_list.reserve(user_to_cluster_list_med.size());

    for (int i = 0; i < user_to_cluster_list_med.size(); ++i) {
      std::vector<absl::string_view> kv =
          absl::StrSplit(user_to_cluster_list_med[i], ":", absl::SkipWhitespace());

      int cluster_id = -124, score = 0;
      if (kv.size() <= 1 ||
          !absl::SimpleAtoi(kv[0], &cluster_id) ||
          !absl::SimpleAtoi(kv[1], &score)) continue;

      if (cluster_id > 0 && score > 0) {
        user_to_cluster_list.emplace_back(std::make_pair(cluster_id, score));
      }
    }

    std::sort(user_to_cluster_list.begin(), user_to_cluster_list.end(),
              [](const std::pair<int, int> &lhs, const std::pair<int, int> &rhs) {
                  return (lhs.second) > (rhs.second);});

    std::vector<int64> user_top5_cluster_id, user_top10_cluster_id;
    user_top5_cluster_id.reserve(5);
    user_top10_cluster_id.reserve(10);

    int user_to_cluster_list_size = static_cast<int>(user_to_cluster_list.size());
    for (int i = 0; i < std::min(5, user_to_cluster_list_size); ++i) {
      user_top5_cluster_id.push_back(user_to_cluster_list[i].first);
      user_top10_cluster_id.push_back(user_to_cluster_list[i].first);
    }
    for (int i = 5; i < std::min(10, user_to_cluster_list_size); ++i) {
      user_top10_cluster_id.push_back(user_to_cluster_list[i].first);
    }

    context.SetIntListCommonAttr("user_top5_cluster_id", std::move(user_top5_cluster_id));
    context.SetIntListCommonAttr("user_top10_cluster_id", std::move(user_top10_cluster_id));

    return true;
  }

  // [denghaoyuan] 找到第一个满足条件的 hetu_tag_id
  static bool FindSatisfiedHetuTagIdByMinMax(
    const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end
  ) {
    auto common_attr_min_value = context.GetIntCommonAttr("common_attr_1").value_or(500000);
    auto common_attr_max_value = context.GetIntCommonAttr("common_attr_2").value_or(4000000);
    auto hetu_tag_lists = context.GetIntListItemAttr("input_item_attr");
    auto output_setter = context.SetIntItemAttr("output_item_attr");

    std::for_each(begin, end, [&](const CommonRecoResult &result) {
      auto hetu_tag_list = hetu_tag_lists(result);
      if (!hetu_tag_list) return;
      for (size_t i = 0; i < hetu_tag_list->size(); i++) {
        int64 hetu_tag_id = hetu_tag_list->at(i);
        if (hetu_tag_id >= common_attr_min_value && hetu_tag_id < common_attr_max_value) {
          output_setter(result, hetu_tag_id);
          break;
        }
      }
    });
    return true;
  }

  // [linjie09]
  static bool YoungCrowdGetAttrFromMemoryDataBySixCommonOneItem(
    const CommonRecoLightFunctionContext &context,
    RecoResultConstIter begin, RecoResultConstIter end
  ) {
    const folly::F14FastMap<std::string, std::vector<double>> *memory_data =
      context.GetPtrCommonAttr<folly::F14FastMap<std::string, std::vector<double>>>(
        "feature_map__memory_data");
    if (!memory_data || memory_data->size() == 0) {
      CL_LOG_EVERY_N(WARNING, 803) <<
        "YoungCrowdGetAttrFromMemoryDataBySixCommonOneItem failed to get memory data ptr";
      return true;
    }

    auto common_attr_1 = context.GetIntCommonAttr("common_attr_1").value_or(0);
    auto common_attr_2 = context.GetIntCommonAttr("common_attr_2").value_or(0);
    auto common_attr_3 = context.GetIntCommonAttr("common_attr_3").value_or(0);
    auto common_attr_4 = context.GetIntCommonAttr("common_attr_4").value_or(0);
    auto common_attr_5 = context.GetIntCommonAttr("common_attr_5").value_or(0);
    auto common_attr_6 = context.GetIntCommonAttr("common_attr_6").value_or(0);
    std::string key_prefix = absl::StrCat(
      common_attr_1, "_", common_attr_2, "_", common_attr_3, "_", common_attr_4, \
      "_", common_attr_5, "_", common_attr_6);

    auto item_attr_accessor = context.GetIntItemAttr("item_attr");
    auto output_setter = context.SetDoubleListItemAttr("pxtr_statistics");

    for (auto iter = begin; iter != end; ++iter) {
      int64 item_attr = item_attr_accessor(*iter).value_or(0);
      auto target_pair = memory_data->find(absl::StrCat(key_prefix, "_", item_attr));
      if (target_pair != memory_data->end() && !target_pair->second.empty()) {
        std::vector<double> pxtr_vec(target_pair->second);
        output_setter(*iter, pxtr_vec);
      }
    }
    return true;
  }

 private:
  DISALLOW_COPY_AND_ASSIGN(SlideLightFunctionSet);
};

}  // namespace platform
}  // namespace ks
